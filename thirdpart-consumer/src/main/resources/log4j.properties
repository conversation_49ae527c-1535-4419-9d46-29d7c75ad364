log4j.rootLogger=info,console, logFile,error


log4j.appender.console=org.apache.log4j.ConsoleAppender
log4j.appender.console.layout=org.apache.log4j.PatternLayout
log4j.appender.console.layout.ConversionPattern=[mail-consumer] %d{yyyy-MM-dd HH\:mm\:ss} [%5p] (%F\:%L) - %m%n

log4j.appender.logFile=org.apache.log4j.DailyRollingFileAppender
log4j.appender.logFile.File=../../logs/mail-consumer.log
log4j.appender.logFile.Threshold = INFO
log4j.appender.logFile.Append=true
log4j.appender.logFile.layout=org.apache.log4j.PatternLayout
log4j.appender.logFile.layout.ConversionPattern=%d{yyyy-MM-dd HH\:mm\:ss} [%5p] (%F\:%L) - %m%n

log4j.appender.error=org.apache.log4j.DailyRollingFileAppender
log4j.appender.error.File=../../logs/mail-consumer.error.log
log4j.appender.error.Threshold = ERROR
log4j.appender.error.Append=true
log4j.appender.error.layout=org.apache.log4j.PatternLayout
log4j.appender.error.layout.ConversionPattern=%d{yyyy-MM-dd HH\:mm\:ss} [%5p] (%F\:%L) - %m%n


log4j.logger.freemarker=ERROR
log4j.logger.com.mchange=ERROR