<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
		xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xmlns:context="http://www.springframework.org/schema/context"
		xmlns:jms="http://www.springframework.org/schema/jms"

		xsi:schemaLocation="http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
        http://www.springframework.org/schema/context 
        http://www.springframework.org/schema/context/spring-context-3.2.xsd
        http://www.springframework.org/schema/jms
        http://www.springframework.org/schema/jms/spring-jms-4.0.xsd"

	default-lazy-init="false">


	<!-- ActiveMQ 连接工厂 -->
    <!-- 真正可以产生Connection的ConnectionFactory，由对应的 JMS服务厂商提供-->
    <!-- 如果连接网络：tcp://ip:61616；未连接网络：tcp://localhost:61616 以及用户名，密码-->
    <bean id="targetConnectionFactory" class="org.apache.activemq.ActiveMQConnectionFactory">
	    <property name="brokerURL" value="${mq.brokerURL}"/>
	    <property name="trustAllPackages" value="true"/>
	     <property name="redeliveryPolicy">
	             <bean name="defaultRedeliveryPolicy" class="org.apache.activemq.RedeliveryPolicy">
                 <property name="maximumRedeliveries" value="10"/>
                 <property name="redeliveryDelay" value="60000"/>
             </bean>
         </property>
	</bean>

	 <!-- Spring Caching连接工厂    Spring用于管理真正的ConnectionFactory的ConnectionFactory 有缓存功能  -->
    <bean id="connectionFactory" class="org.springframework.jms.connection.CachingConnectionFactory">
        <property name="targetConnectionFactory" ref="targetConnectionFactory"></property>
         <property name="reconnectOnException" value="true"/>
         <property name="cacheConsumers" value="false"/>
         <property name="cacheProducers" value="false"/>
         <property name="sessionCacheSize" value="50"/>
    </bean>

	    <!--
    	ActiveMQ为我们提供了一个PooledConnectionFactory，通过往里面注入一个ActiveMQConnectionFactory
    	可以用来将Connection、Session和MessageProducer池化，这样可以大大的减少我们的资源消耗。
    	要依赖于 activemq-pool包
	<bean id="pooledConnectionFactory" class="org.apache.activemq.pool.PooledConnectionFactory">
		<property name="connectionFactory" ref="targetConnectionFactory" />
		<property name="maxConnections" value="${mq.pool.maxConnections}" />
	</bean> -->


    <!-- Spring用于管理真正的ConnectionFactory的ConnectionFactory
	<bean id="connectionFactory" class="org.springframework.jms.connection.SingleConnectionFactory">
	    <property name="targetConnectionFactory" ref="pooledConnectionFactory"/>
	</bean>   -->

    <!-- Spring JmsTemplate 的消息生产者 start-->

    <!-- Spring提供的JMS工具类，它可以进行消息发送、接收等 -->
	<bean id="jmsTemplate" class="org.springframework.jms.core.JmsTemplate">
	    <property name="connectionFactory" ref="connectionFactory"/>
	</bean>

	<!--这个是队列目的地，点对点的-->
	<bean id="queueDestinationOrderNotice" class="org.apache.activemq.command.ActiveMQQueue">
		<constructor-arg>
			<value>vp-orderNotice-queue</value>
		</constructor-arg>
	</bean>
	
    <!-- 消息监听器 -->
	<bean id="orderNoticeListener" class="com.vispractice.vpshop.activemq.OrderListener"/>


	<bean id="orderNoticeJmsContainer" class="org.springframework.jms.listener.DefaultMessageListenerContainer">
	    <property name="connectionFactory" ref="connectionFactory" />
	    <property name="destination" ref="queueDestinationOrderNotice" />
	    <property name="messageListener" ref="orderNoticeListener" />
	</bean>
	
	<!-- Payment Notice Queue -->
	<bean id="queueDestinationPaymentNotice" class="org.apache.activemq.command.ActiveMQQueue">
		<constructor-arg>
			<value>vp-paymentNotice-queue</value>
		</constructor-arg>
	</bean>
	
    <!-- Payment Notice Listener -->
	<bean id="paymentNoticeListener" class="com.vispractice.vpshop.activemq.PaymentListener"/>

	<!-- Payment Notice JMS Container -->
	<bean id="paymentNoticeJmsContainer" class="org.springframework.jms.listener.DefaultMessageListenerContainer">
	    <property name="connectionFactory" ref="connectionFactory" />
	    <property name="destination" ref="queueDestinationPaymentNotice" />
	    <property name="messageListener" ref="paymentNoticeListener" />
	</bean>


	<!--这个是队列目的地，点对点的-->
	<bean id="queueDestination" class="org.apache.activemq.command.ActiveMQQueue">
		<constructor-arg>
			<value>vp-payment-queue</value>
		</constructor-arg>
	</bean>
	<bean id="queueDestinationMail" class="org.apache.activemq.command.ActiveMQQueue">
		<constructor-arg>
			<value>vp-mail-queue</value>
		</constructor-arg>
	</bean>
	<bean id="queueDestinationMailNow" class="org.apache.activemq.command.ActiveMQQueue">
		<constructor-arg>
			<value>vp-mailnow-queue</value>
		</constructor-arg>
	</bean>

	<bean id="queueDestinationCheckJd" class="org.apache.activemq.command.ActiveMQQueue">
		<constructor-arg>
			<value>vp-checkJd-queue</value>
		</constructor-arg>
	</bean>

	<bean id="queueDestinationLoginLog" class="org.apache.activemq.command.ActiveMQQueue">
		<constructor-arg>
			<value>vp-loginLog-queue</value>
		</constructor-arg>
	</bean>

	<bean id="queueDestinationBeanPointLog" class="org.apache.activemq.command.ActiveMQQueue">
		<constructor-arg>
			<value>vp-beanPointLog-queue</value>
		</constructor-arg>
	</bean>

	<bean id="queueDestinationHitLog" class="org.apache.activemq.command.ActiveMQQueue">
		<constructor-arg>
			<value>vp-hitLog-queue</value>
		</constructor-arg>
	</bean>

	<bean id="queueDestinationSearchLog" class="org.apache.activemq.command.ActiveMQQueue">
		<constructor-arg>
			<value>vp-searchLog-queue</value>
		</constructor-arg>
	</bean>

	<!--这个是队列目的地，点对点的-->
	<bean id="hotelQueueDestination" class="org.apache.activemq.command.ActiveMQQueue">
		<constructor-arg>
			<value>vp-hotel-queue</value>
		</constructor-arg>
	</bean>

	<bean id="queueDestinationCoinCardLog" class="org.apache.activemq.command.ActiveMQQueue">
		<constructor-arg>
			<value>vp-coinCardLog-queue</value>
		</constructor-arg>
	</bean>

	<bean id="orderLogQueueDestination" class="org.apache.activemq.command.ActiveMQQueue">
		<constructor-arg>
			<value>vp-orderLog-queue</value>
		</constructor-arg>
	</bean>

	<bean id="sendCoinNoticequeueDestination" class="org.apache.activemq.command.ActiveMQQueue">
		<constructor-arg>
			<value>vp-sendCoinNotice-queue</value>
		</constructor-arg>
	</bean>

	<bean id="sendCarePlanNoticequeueDestination" class="org.apache.activemq.command.ActiveMQQueue">
		<constructor-arg>
			<value>vp-sendCarePlanNotice-queue</value>
		</constructor-arg>
	</bean>
	<bean id="openSearchQueueDestination" class="org.apache.activemq.command.ActiveMQQueue">
		<constructor-arg>
			<value>vp-opensearch-queue</value>
		</constructor-arg>
	</bean>

	<!-- 用户消息  -->
	<bean id="apiMessageQueueDestination" class="org.apache.activemq.command.ActiveMQQueue">
		<constructor-arg>
			<value>vp-apiMessage-queue</value>
		</constructor-arg>
	</bean>

	<!-- 红包消息  -->
	<bean id="redEnvelopeMessageQueueDestination" class="org.apache.activemq.command.ActiveMQQueue">
		<constructor-arg>
			<value>vp-redEnvelopeMessage-queue</value>
		</constructor-arg>
	</bean>

	<bean id="queueDestinationCheckProductDetail" class="org.apache.activemq.command.ActiveMQQueue">
		<constructor-arg>
			<value>vp-checkProductDetail-queue</value>
		</constructor-arg>
	</bean>

</beans>