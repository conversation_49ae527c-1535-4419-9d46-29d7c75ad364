<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" 
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:tx="http://www.springframework.org/schema/tx" 
	xmlns:cache="http://www.springframework.org/schema/cache" 
	xmlns:task="http://www.springframework.org/schema/task"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
	http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.2.xsd
	http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.2.xsd
	http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache-3.2.xsd
	http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.2.xsd"
	default-lazy-init="true">

	<context:property-placeholder location="classpath*:/easyshopping.properties" ignore-resource-not-found="true" ignore-unresolvable="true" />

	<context:component-scan base-package="com.vispractice.vpshop">
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	
	<bean id="dataSource" class="com.alibaba.druid.pool.DruidDataSource" destroy-method="close">  
        <property name="driverClassName" value="${jdbc.driver}"/>
        <property name="url" value="${jdbc.url}"/>
        <property name="username" value="${jdbc.username}"/>
        <property name="password" value="${jdbc.password}"/>
        <!-- 配置初始化大小、最小、最大 -->
        <property name="initialSize" value="5"/>
        <property name="minIdle" value="5"/>
        <property name="maxActive" value="1000"/>
        <!-- 配置获取连接等待超时的时间 -->
        <property name="maxWait" value="60000"/>
        <!-- 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 -->
        <property name="timeBetweenEvictionRunsMillis" value="60000"/>
        <!-- 配置一个连接在池中最小生存的时间，单位是毫秒 -->
        <property name="minEvictableIdleTimeMillis" value="300000"/>
        <!-- 校验语句 -->
        <property name="validationQuery" value="SELECT 1"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="false"/>
        <property name="testOnReturn" value="false"/>
        <!-- 配置监控统计拦截的filters -->
        <property name="filters" value="stat"/>
	</bean>  

<!-- 	<bean id="dataSource" class="com.mchange.v2.c3p0.ComboPooledDataSource" destroy-method="close">
		<property name="driverClass" value="${jdbc.driver}" />
		<property name="jdbcUrl" value="${jdbc.url}" />
		<property name="user" value="${jdbc.username}" />
		<property name="password" value="${jdbc.password}" />
		<property name="initialPoolSize" value="${connection_pools.initial_pool_size}" />
		<property name="minPoolSize" value="${connection_pools.min_pool_size}" />
		<property name="maxPoolSize" value="${connection_pools.max_pool_size}" />
		<property name="maxIdleTime" value="${connection_pools.max_idle_time}" />
		<property name="acquireIncrement" value="${connection_pools.acquire_increment}" />
		<property name="checkoutTimeout" value="${connection_pools.checkout_timeout}" />
		<property name="preferredTestQuery" value="${connection_pools.preferred_test_query}" />
		<property name="idleConnectionTestPeriod" value="${connection_pools.idle_connection_test_period}"/>
	</bean> -->

	<bean id="entityManagerFactory" class="org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean">
		<property name="dataSource" ref="dataSource" />
		
		<property name="persistenceXmlLocation" value="classpath*:/persistence.xml" />
		
		<property name="packagesToScan">
			<list>
				<value>com.vispractice.vpshop.*</value>
			</list>
		</property>
		<!--  
		<property name="persistenceUnitName" value="persistenceUnit" />
		 -->
		<property name="jpaVendorAdapter">
			<bean class="org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter">
				<property name="showSql" value="false" />
				<property name="generateDdl" value="false" />
			</bean>
		</property>
		<property name="jpaProperties">
			<props>
				<prop key="hibernate.dialect">${hibernate.dialect}</prop>
				<prop key="hibernate.ejb.naming_strategy">org.hibernate.cfg.ImprovedNamingStrategy</prop>
				<prop key="hibernate.cache.use_second_level_cache">${hibernate.cache.use_second_level_cache}</prop>
				<prop key="hibernate.cache.region.factory_class">${hibernate.cache.region.factory_class}</prop>
				<prop key="hibernate.cache.use_query_cache">${hibernate.cache.use_query_cache}</prop>
				<prop key="hibernate.jdbc.fetch_size">${hibernate.jdbc.fetch_size}</prop>
				<prop key="hibernate.jdbc.batch_size">${hibernate.jdbc.batch_size}</prop>
				<prop key="hibernate.connection.isolation">2</prop>
				<prop key="javax.persistence.validation.mode">none</prop>
				<prop key="hibernate.search.default.directory_provider">org.hibernate.search.store.FSDirectoryProvider</prop>
				<prop key="hibernate.search.default.indexBase">${java.io.tmpdir}/${system.project_name}/index</prop>
			</props>
		</property>
	</bean>

	<bean id="transactionManager" class="org.springframework.orm.jpa.JpaTransactionManager">
		<property name="entityManagerFactory" ref="entityManagerFactory" />
	</bean>

	<tx:annotation-driven transaction-manager="transactionManager" />

	<cache:annotation-driven cache-manager="cacheManager" />

	<bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
		<property name="targetClass" value="java.lang.System" />
		<property name="targetMethod" value="setProperty" />
		<property name="arguments">
			<list>
				<value>system.project_name</value>
				<value>${system.project_name}</value>
			</list>
		</property>
	</bean>
	
	<bean id="jedisPoolConfig" class="redis.clients.jedis.JedisPoolConfig">
		<property name="maxTotal" value="${redis.pool.maxTotal}" />
		<property name="maxIdle" value="${redis.pool.maxIdle}" />
		<property name="timeBetweenEvictionRunsMillis" value="${redis.pool.timeBetweenEvictionRunsMillis}" />
		<property name="minEvictableIdleTimeMillis" value="${redis.pool.minEvictableIdleTimeMillis}" />
		<property name="testOnBorrow" value="${redis.pool.testOnBorrow}" />
	</bean>	

	<!-- redis的连接池pool，不是必选项：timeout/password -->
	<bean id="jedisPool" class="redis.clients.jedis.JedisPool">
		<constructor-arg index="0" ref="jedisPoolConfig" />
		<constructor-arg index="1" value="${redis.hostname}" />
		<constructor-arg index="2" value="${redis.port}" type="int" />
		<constructor-arg index="3" value="60000" type="int" />
		<constructor-arg index="4" value="${redis.password}" />
	</bean>

	<bean id="ehCacheManager" class="org.springframework.cache.ehcache.EhCacheManagerFactoryBean">
		<property name="configLocation" value="classpath:/ehcache.xml" />
		<property name="shared" value="true" />
	</bean>

	<bean id="cacheManager" class="org.springframework.cache.ehcache.EhCacheCacheManager">
		<property name="cacheManager" ref="ehCacheManager" />
	</bean>

	<bean id="freeMarkerConfigurer" class="org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer">
		<property name="templateLoaderPaths" value="${template.loader_path}" />
		<property name="freemarkerSettings">
			<props>
				<prop key="defaultEncoding">${template.encoding}</prop>
				<prop key="url_escaping_charset">${url_escaping_charset}</prop>
				<prop key="locale">${locale}</prop>
				<prop key="template_update_delay">${template.update_delay}</prop>
				<prop key="tag_syntax">auto_detect</prop>
				<prop key="whitespace_stripping">true</prop>
				<prop key="classic_compatible">true</prop>
				<prop key="number_format">${template.number_format}</prop>
				<prop key="boolean_format">${template.boolean_format}</prop>
				<prop key="datetime_format">${template.datetime_format}</prop>
				<prop key="date_format">${template.date_format}</prop>
				<prop key="time_format">${template.time_format}</prop>
				<prop key="object_wrapper">freemarker.ext.beans.BeansWrapper</prop>
			</props>
		</property>
	</bean>

	<bean id="messageSource" class="org.springframework.context.support.ReloadableResourceBundleMessageSource">
		<property name="cacheSeconds" value="${message.cache_seconds}" />
		<property name="useCodeAsDefaultMessage" value="true" />
		<property name="basenames">
			<list>
				<value>${message.common_path}</value>
				<value>${message.shop_path}</value>
				<value>${message.admin_path}</value>
			</list>
		</property>
	</bean>

	<bean id="localeResolver" class="org.springframework.web.servlet.i18n.FixedLocaleResolver">
		<property name="defaultLocale" value="${locale}" />
	</bean>

	<bean id="imageCaptchaService" class="com.octo.captcha.service.image.DefaultManageableImageCaptchaService">
		<property name="captchaEngine">
			<bean class="com.vispractice.vpshop.CaptchaEngine" />
		</property>
		<property name="minGuarantedStorageDelayInSeconds" value="3600" />
	</bean>
	
	<bean id="javaMailSender" class="org.springframework.mail.javamail.JavaMailSenderImpl">
		<property name="javaMailProperties">
			<props>
				<prop key="mail.smtp.auth">${mail.smtp.auth}</prop>
				<prop key="mail.smtp.timeout">${mail.smtp.timeout}</prop>
				<prop key="mail.smtp.starttls.enable">${mail.smtp.starttls.enable}</prop>
				<!--
				<prop key="mail.smtp.socketFactory.class">javax.net.ssl.SSLSocketFactory</prop>
				-->
			</props>
		</property>
	</bean>

	<bean id="taskExecutor" class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
		<property name="corePoolSize" value="${task.core_pool_size}" />
		<property name="maxPoolSize" value="${task.max_pool_size}" />
		<property name="queueCapacity" value="${task.queue_capacity}" />
		<property name="keepAliveSeconds" value="${task.keep_alive_seconds}" />
	</bean>

	<task:annotation-driven />

</beans>