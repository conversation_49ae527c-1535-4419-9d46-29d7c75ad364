import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.http.impl.cookie.DateUtils;

import com.alibaba.fastjson.JSONObject;
import com.vispractice.vpshop.api.ctrip.hotel.request.CreateOrderRequest;
import com.vispractice.vpshop.api.plane.bean.Passenger;
import com.vispractice.vpshop.api.plane.bean.PlaneTicket;
import com.vispractice.vpshop.api.train.bean.OrderDetailPassengerBean;
import com.vispractice.vpshop.api.train.response.OrderDetailResponse;
import com.vispractice.vpshop.home.service.vo.HomeHotelInfoVO;
import com.vispractice.vpshop.lytrain.vo.LyTrainVo;
import com.vispractice.vpshop.util.CommUtil;

import bean.HotelExcelVo;
import bean.PlaneExecelVo;
import bean.TaxiExcelVo;
import bean.TrainExcelVo;
import tool.ExcelUtils;

/**
 * 版权所有：版权所有(C) 2016，远行科技
 * 文件编号：M01_TravelTest.java
 * 文件名称：TravelTest.java
 * 系统编号：远行福利plus
 * 设计作者：wudingchao
 * 完成日期：2020-11-25 09:03:58
 * 设计文档：
 * 内容摘要：TODO
 * 系统用例：
 * 界面原型：
 */

/**
* 
* 类 编 号：
* 类 名 称：TravelTest.java
* 内容摘要：
* 完成日期：2020年12月15日 下午7:10:21
* 编码作者: wudingchao
*/
public class TravelTest {

	//通过脚本（vp-service/src/test/java/sql/travel_pay.sql）到处的的数据
	public static final String TRIP_ORDER_ALL_PATH = "E:\\福利PLUS\\差旅订单\\差旅订单.xlsx";
	
	//通过脚本（vp-service/src/test/java/sql/travel_refunds.sql）到处的的数据
	public static final String TRIP_RETURN_ALL_PATH = "E:\\福利PLUS\\差旅订单\\差旅订单 _退改签手续费.xlsx";
	
	//处理结果到处目录
	public static final String EXCEL_OUT_PUT_PATH = "E:\\福利PLUS\\差旅订单\\";
	
	public static void main(String[] args) {
		
		doAll();
		
	}
	
	public static void doAll() {
		InputStream input = null ;    // 准备好一个输入的对象
		try {
			File file = new File(TRIP_ORDER_ALL_PATH) ;    // 声明File对象
			input = new FileInputStream(file)  ;
			List<List<String>> datas = ExcelUtils.readExcelContent(input, TRIP_ORDER_ALL_PATH);
			if(CollectionUtils.isNotEmpty(datas)) {
				List<Object> taxis = new ArrayList<Object>();
				List<Object> trains = new ArrayList<Object>();
				List<Object> planes = new ArrayList<Object>();
				List<Object> hotels = new ArrayList<Object>();
				for(List<String> data : datas) {
					String supplierId = data.get(13);//供应商
					if(supplierId.equals("142")) {//滴滴打车——142
						TaxiExcelVo thisvo = getData_142(data);
						if(thisvo != null) {
							taxis.add(thisvo);
						}
					} else if(supplierId.equals("275")) {//火车票——275
						TrainExcelVo thisvo = getData_275(data);
						if(thisvo != null) {
							trains.add(thisvo);
						}
					} else if(supplierId.equals("144")) {//去哪儿机票——144
						PlaneExecelVo thisvo = getData_144(data);
						if(thisvo != null) {
							planes.add(thisvo);
						}
					} else if(supplierId.equals("293")) {//携程酒店——293
						HotelExcelVo thisvo = getData_239(data);
						if(thisvo != null) {
							hotels.add(thisvo);
						}
					} else if(supplierId.equals("295")) {//如家酒店——295
						HotelExcelVo thisvo = getData_295(data);
						if(thisvo != null) {
							hotels.add(thisvo);
						}
					} else if(supplierId.equals("433")) {//美团酒店——433
						HotelExcelVo thisvo = getData_433(data);
						if(thisvo != null) {
							hotels.add(thisvo);
						}
					} else {
						System.out.println(supplierId + "_供应商没有找到...");
					}
				}
				
				Calendar cal = Calendar.getInstance();
				String yyyymmdd = DateUtils.formatDate(cal.getTime(), "yyyyMMdd");
				
				ExcelUtils.writeExcel(EXCEL_OUT_PUT_PATH + yyyymmdd + "\\滴滴订单_" + CommUtil.randomInt(4) + ".xlsx", taxis);
				ExcelUtils.writeExcel(EXCEL_OUT_PUT_PATH + yyyymmdd + "\\火车票订单_" + CommUtil.randomInt(4) + ".xlsx", trains);
				ExcelUtils.writeExcel(EXCEL_OUT_PUT_PATH + yyyymmdd + "\\机票订单_" + CommUtil.randomInt(4) + ".xlsx", planes);
				ExcelUtils.writeExcel(EXCEL_OUT_PUT_PATH + yyyymmdd + "\\酒店订单_" + CommUtil.randomInt(4) + ".xlsx", hotels);
				
			}
		} catch (FileNotFoundException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} finally {
			if(input != null) {
				try {
					input.close();
				} catch (IOException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
		}
	}
	
	/**
	 * 
	 * 方法: 滴滴打车酒店<br/>
	 * 实现流程  : <br/>
	 * @return
	 * <AUTHOR>
	 * @serialData 2020年12月16日 下午3:43:58
	 */
	public static TaxiExcelVo getData_142(List<String> data){
		TaxiExcelVo thisvo = null;
		if(data != null) {
			JSONObject order = JSONObject.parseObject(data.get(14));
			JSONObject didiOrderStatus = order.getJSONObject("orderStatus");
			JSONObject didiFliOrder = order.getJSONObject("fliOrder");
			JSONObject didiQygjOrder = order.getJSONObject("qygjOrder");
			
			String rule = didiFliOrder.getString("rule");//计价模型分类，201(专车)； 301(快车)
			String carType = didiFliOrder.getString("carType");//车型代码:600 普通快车; 900 优享快车; 100 舒适型; 400 六座商务; 200 行政级;
		    StringBuffer cardType = new StringBuffer();
		    if(carType.equals("600")) {
		    	cardType.append("普通快车");
		    } else if(carType.equals("900")) {
		    	cardType.append("优享快车");
		    } else if(carType.equals("100")) {
		    	cardType.append("舒适型");
		    } else if(carType.equals("400")) {
		    	cardType.append("六座商务");
		    } else if(carType.equals("200")) {
		    	cardType.append("行政级");
		    }
		    if(rule.equals("301")) {
		    	cardType.append("(快车)");
		    } else {
		    	cardType.append("(专车)");
		    }
		  //(String orderSn, String createDate, String orderMan, String stayMen, String cardType, String from,
			//String to, String orderAmount, String fee, String companyName, String structureName, String applySn,
			//String projectName, String breakReason, String memo)
			thisvo = new TaxiExcelVo(data.get(0), data.get(1), data.get(3), data.get(3), cardType.toString(),
					didiOrderStatus.getString("from"), didiOrderStatus.getString("to"), data.get(5), "0", data.get(7),
					data.get(8), data.get(9), data.get(10), data.get(11), data.get(12));
			
		}
		return thisvo;
	}
	
	/**
	 * 
	 * 方法: 携程酒店<br/>
	 * 实现流程  : <br/>
	 * @return
	 * <AUTHOR>
	 * @serialData 2020年12月16日 下午3:43:58
	 */
	public static HotelExcelVo getData_239(List<String> data){
		HotelExcelVo thisvo = null;
		if(data != null) {
			CreateOrderRequest order = JSONObject.parseObject(data.get(14), CreateOrderRequest.class);
			
			thisvo = new HotelExcelVo(data.get(0), data.get(1), data.get(3), data.get(3), order.getHotelAddress(),
					order.getHotelName(), order.getRoomTypeName(), order.getRoomNums().toString(),
					order.getArrivalDate(), order.getDiffDays().toString(), data.get(5), data.get(5), "0", data.get(7),
					data.get(8), data.get(9), data.get(10), data.get(11), data.get(12));
		}
		return thisvo;
	}
	
	/**
	 * 
	 * 方法: 如家酒店<br/>
	 * 实现流程  : <br/>
	 * @return
	 * <AUTHOR>
	 * @serialData 2020年12月16日 下午3:43:58
	 */
	public static HotelExcelVo getData_295(List<String> data){
		HotelExcelVo thisvo = null;
		if(data != null) {
			HomeHotelInfoVO order = JSONObject.parseObject(data.get(14), HomeHotelInfoVO.class);
			
			thisvo = new HotelExcelVo(data.get(0), data.get(1), data.get(3), data.get(3), order.getAddress(),
					order.getHotelName(), order.getRoomTypeName(), order.getRmNum().toString(), order.getArrDate(),
					order.getDiffDays().toString(), data.get(5), data.get(5), "0", data.get(7), data.get(8),
					data.get(9), data.get(10), data.get(11), data.get(12));
		}
		return thisvo;
	}
	
	/**
	 * 
	 * 方法: 美团酒店<br/>
	 * 实现流程  : <br/>
	 * @return
	 * <AUTHOR>
	 * @serialData 2020年12月16日 下午3:43:58
	 */
	public static HotelExcelVo getData_433(List<String> data){
		return getData_295(data);//和如家一样的
	}
	
	/**
	 * 
	 * 方法: 同城火车票<br/>
	 * 实现流程  : <br/>
	 * @return
	 * <AUTHOR>
	 * @serialData 2020年12月16日 下午3:43:58
	 */
	public static TrainExcelVo getData_275(List<String> data){
		TrainExcelVo thisvo = null;
		if(data != null) {
			LyTrainVo order = JSONObject.parseObject(data.get(14), LyTrainVo.class);
			OrderDetailResponse orderDetail = order.getOrderDetailResponse();
			StringBuffer seatType = new StringBuffer();
			StringBuffer stayMens = new StringBuffer();
			for(OrderDetailPassengerBean passenger : orderDetail.getPassengers()) {
				seatType.append(",").append(passenger.getSeatClassName()).append("(").append(passenger.getSeatNo()).append(")");
				stayMens.append(",").append(passenger.getPassengerName());
			}
			if(seatType.length() > 1) {
				seatType.deleteCharAt(0);
				stayMens.deleteCharAt(0);
			}
			String station = orderDetail.getFromStation() + "--" + orderDetail.getToStation();
			thisvo = new TrainExcelVo(data.get(0), data.get(1), data.get(3), stayMens.toString(),
					orderDetail.getTicketNo(), orderDetail.getTicketNo(), seatType.toString(), station,
					orderDetail.getDepartureTime(), orderDetail.getArrivalTime(), "", order.getTicketPrice().toString(),
					"0", data.get(6), order.getServiceCharge().toString(), data.get(7), data.get(8), data.get(9), data.get(10),
					data.get(11), data.get(12));
			
		}
		return thisvo;
	}
	/**
	 * 
	 * 方法: 机票<br/>
	 * 实现流程  : <br/>
	 * @return
	 * <AUTHOR>
	 * @serialData 2020年12月16日 下午3:43:58
	 */
	public static PlaneExecelVo getData_144(List<String> data){
		PlaneExecelVo thisvo = null;
		if(data != null) {
			PlaneTicket	planeTicket = JSONObject.parseObject(data.get(14), PlaneTicket.class);
			StringBuffer consign = new StringBuffer();
			StringBuffer cabinName = new StringBuffer();
			StringBuffer ticketNo = new StringBuffer();
			int ticketNums = 0;
			for(Passenger passenger : planeTicket.getPassengers()) {
				consign.append(",").append(passenger.getName());
				cabinName.append(",").append(passenger.getCabinName());
				ticketNo.append(",").append(passenger.getTicketNo());
				ticketNums ++;
			}
			if(consign.length() > 1) {
				consign.deleteCharAt(0);
				cabinName.deleteCharAt(0);
				ticketNo.deleteCharAt(0);
			}
			String dptDate = planeTicket.getDptDate() + " " + planeTicket.getDptTime();
			String flightNum = planeTicket.getFlightNum();
			String carrierName = planeTicket.getCarrierName();
			String dpt2arr = planeTicket.getDptCity() + "--" + planeTicket.getArrCity();
			String arrTime = planeTicket.getArrDate() + " " + planeTicket.getArrTime();
			String ticketPrice = planeTicket.getToatlAmount().toString();
			String arfNtof = (planeTicket.getArf() + planeTicket.getTof()) + "";
			if(planeTicket.getSuccessChangeInfo() != null) {//改签单
				
			}
			
			thisvo = new PlaneExecelVo(data.get(0), data.get(1), data.get(3), consign.toString(), data.get(15),
					ticketNo.toString(), ticketNums + "", dptDate, carrierName, flightNum, cabinName.toString(), dpt2arr, dptDate,
					arrTime, "", ticketPrice, arfNtof, "0", "0", ticketPrice, "0", data.get(7), data.get(8),
					data.get(9), data.get(10), data.get(11), data.get(12));
		}
		return thisvo;
	}
	
}
