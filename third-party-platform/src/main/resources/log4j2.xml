<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN" monitorInterval="60">
	<Properties>
		<property name="baseName">
			vpshop-buyer
		</property>
        <property name="logPattern">
            [${baseName}] %d{yyyy-MM-dd HH:mm:ss} [%t] [%5p] (%F\:%L) - %m%n
        </property>
        <property name="logLevel">
            INFO
        </property>
	</Properties>
	
    <Appenders>
    	<!-- 控制台日志 -->
        <Console name="CONSOLE" target="SYSTEM_OUT">
            <PatternLayout pattern="${logPattern}"/> 
        </Console>
        
        <!-- 所有日志  按天切分-->
        <RollingFile name="LOG_FILE" fileName="../../logs/${baseName}.log" filePattern="../../logs/${baseName}-%d{yyyy-MM-dd}.log">
            <PatternLayout pattern="${logPattern}" />
            <Policies>
    			<TimeBasedTriggeringPolicy modulate="true" interval="1"/>
    		</Policies>
        </RollingFile>
        <!-- 错误日志 按天切分-->
        <RollingFile name="ERROR_FILE" fileName="../../logs/${baseName}.error.log" filePattern="../../logs/${baseName}.error-%d{yyyy-MM-dd}.log">
            <PatternLayout pattern="${logPattern}" />
            <ThresholdFilter level="ERROR" onMatch="ACCEPT" />
            <Policies>
    			<TimeBasedTriggeringPolicy modulate="true" interval="1"/>
    		</Policies>
        </RollingFile>
    </Appenders>
    
    <Loggers>
    	<Logger name="freemarker" level="ERROR"></Logger>
        <Logger name="com.mchange" level="ERROR"></Logger>
        <Root level="${logLevel}"> 
            <AppenderRef ref="CONSOLE"/>
            <AppenderRef ref="LOG_FILE"/>
            <AppenderRef ref="ERROR_FILE"/>
        </Root>
    </Loggers>
</Configuration>