/*
 * 

 * 
 */
package com.vispractice.vpshop.controller.admin;

import com.vispractice.vpshop.Filter;
import com.vispractice.vpshop.Message;
import com.vispractice.vpshop.entity.*;
import com.vispractice.vpshop.service.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.annotation.Resource;
import java.util.*;

/**
 * 
 * 类 编 号：UI_PU010401_ProductCategoryController
 * 类 名 称：ProductCategoryController.java 
 * 内容摘要：商品分类
 * 完成日期：2018年12月3日 下午6:05:04 
 * 编码作者: 许亮
 */
@Controller("adminProductCategoryController")
@RequestMapping("/admin/product_category")
public class ProductCategoryController extends BaseController {
	final static Logger LOGGER = LoggerFactory.getLogger(ProductCategoryController.class);
	@Resource(name = "productCategoryServiceImpl")
	private ProductCategoryService productCategoryService;
	@Resource(name = "brandServiceImpl")
	private BrandService brandService;
	@Resource(name = "productTypeServiceImpl")
	private ProductTypeService productTypeService;
	@Resource(name = "productServiceImpl")
	private ProductService productService;
	@Resource(name = "supplierServiceImpl")
	private SupplierService supplierService;
	/**
	 * 添加
	 */
	@RequestMapping(value = "/add", method = RequestMethod.GET)
	public String add(ModelMap model) {
		model.addAttribute("productTypes", productTypeService.findAll());
		// model.addAttribute("brands", brandService.findAll());
		List<Filter> filters1=new ArrayList<Filter>();
		filters1.add(Filter.eq("status", true));
		filters1.add(Filter.eq("supplierType", 0));
		filters1.add(Filter.eq("enabledFlag", true));
		filters1.add(Filter.eq("isOweCatege", true));
		List<Supplier> supplierList = supplierService.findList(null, filters1, null);
		model.addAttribute("supplierList", supplierList);
		if(supplierList!=null && !supplierList.isEmpty()){
			//福利平台
			model.addAttribute("productCategoryTree",productCategoryService.findPlatformList());
		}else{
			model.addAttribute("productCategoryTree",productCategoryService.findTree());
		}
		return "/admin/product_category/add";
	}
	
	/**
	 *方法:根据供应商找分类
	 *作者:sangyj
	 * @param supplierId
	 * @return
	 */
	@RequestMapping(value = "/findBySupplier", method = RequestMethod.POST)
	@ResponseBody
	public List<Map<String,Object>> findBySupplier(Long supplierId){
		List<ProductCategory> categoryList = null;
		if(supplierId!=null){
			Supplier supplier = supplierService.find(supplierId);
			categoryList = productCategoryService.findBySupplier(supplier);
		}else{
			//福利平台
			categoryList = productCategoryService.findPlatformList();
		}
		List<Map<String,Object>> result = new ArrayList<Map<String,Object>>();
		if(categoryList!=null && !categoryList.isEmpty()){
			for(ProductCategory pc : categoryList){
				Map<String,Object> pcMap = new HashMap<String, Object>();
				pcMap.put("id", pc.getId());
				pcMap.put("name", pc.getName());
				pcMap.put("grade", pc.getGrade());
				result.add(pcMap);
			}
		}
		return result;
	}
	
	

	/**
	 * 保存
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public String save(ProductCategory productCategory, Long parentId,
			Long productTypeId, Long[] brandIds,
			RedirectAttributes redirectAttributes) {
		productCategory.setParent(productCategoryService.find(parentId));
		ProductType productType = productTypeService.find(productTypeId);
		// 保存前，应判断绑定的类型是否存在
		if (!productType.getEnabledFlag()) {
			addFlashMessage(redirectAttributes,
					Message.error("新增商品分类失败，绑定类型已被删除"));
			return "redirect:add.jhtml";
		}
		productCategory.setProductType(productTypeService.find(productTypeId));
		productCategory.setBrands(new HashSet<Brand>(brandService
				.findList(brandIds)));
		if (!isValid(productCategory)) {
			return ERROR_VIEW;
		}
		productCategory.setTreePath(null);
		productCategory.setGrade(null);
		productCategory.setChildren(null);
		productCategory.setProducts(null);
		productCategory.setParameterGroups(null);
		productCategory.setAttributes(null);
		productCategory.setPromotions(null);
		productCategory.setEnabledFlag(true);
		productCategoryService.save(productCategory);
		addFlashMessage(redirectAttributes, SUCCESS_MESSAGE);
		return "redirect:list.jhtml";
	}

	/**
	 * 编辑
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.GET)
	public String edit(Long id, ModelMap model) {
		ProductCategory productCategory = productCategoryService.find(id);
		model.addAttribute("productTypes", productTypeService.findAll());
		model.addAttribute("brands", brandService.getBrandsBySupplier(supplierService.find(productCategory.getSupplierId())));
		model.addAttribute("productCategory", productCategory);
		if(productCategory.getSupplierId()!=null){
			Supplier supplier = supplierService.find(productCategory.getSupplierId());
			model.addAttribute("supplier",supplier);
			model.addAttribute("productCategoryTree",productCategoryService.findBySupplier(supplier));
		}else{
			//福利平台
			model.addAttribute("productCategoryTree",productCategoryService.findPlatformList());
		}
		List<Filter> filters1=new ArrayList<Filter>();
		filters1.add(Filter.eq("status", true));
		filters1.add(Filter.eq("supplierType", 0));
		filters1.add(Filter.eq("enabledFlag", true));
		filters1.add(Filter.eq("isOweCatege", true));
		model.addAttribute("children",productCategoryService.findChildren(productCategory));
		return "/admin/product_category/edit";
	}
	
	/**
	 * 检查同一父类，同一层级下的商品名是否相同
	 */
	@RequestMapping(value = "/check", method = RequestMethod.POST)
	public @ResponseBody int check(Long productId,String name,Long parentCategoryId) {
		ProductCategory productCategory=productCategoryService.find(productId);
		if (productCategory.getParent() != null) {
			ProductCategory parent = productCategory.getParent();
			if (parent.equals(productCategory)) {
				return 0;
			}
			List<ProductCategory> children = productCategoryService
					.findChildren(parent);
			if (children != null && children.contains(parent)) {
				return 1;
			}
			for (int i = 0; i < children.size(); i++) {
					if (children.get(i).getGrade().intValue() == productCategory.getGrade().intValue()
							&& children.get(i).getName()
									.equals(name)) {
						return 2;
					}
			}
		}else{
			if(parentCategoryId!=null){//选中上级分类下拉框触发
				ProductCategory parentProductCategory=productCategoryService.find(parentCategoryId);
				List<ProductCategory> children = productCategoryService
						.findChildren(parentProductCategory);
				for(int i=0;i<children.size();i++){
					if(children.get(i).getGrade()==productCategory.getGrade()+1 && 
							children.get(i).getName().equals(productCategory.getName())){
						return 2;
					}
				}
			}else{
				List<ProductCategory> allProductCategory=productCategoryService.findChildren(null, null);
				for(int i=0;i<allProductCategory.size();i++){
			    	if(allProductCategory.get(i).getGrade()==0 &&
			    			name.equals(allProductCategory.get(i).getName())){
			    		return 2;
			    	}
			    }
			}
		}
		return 3;
	}

	/**
	 * 更新
	 */
	@RequestMapping(value = "/update", method = RequestMethod.POST)
	public String update(ProductCategory productCategory, Long parentId,
			Long productTypeId, Long[] brandIds,
			RedirectAttributes redirectAttributes) {
		ProductCategory productCategoryTemp = productCategoryService.find(productCategory.getId());
		productCategoryTemp.setParent(productCategoryService.find(parentId));
		productCategoryTemp.setBrands(new HashSet<>(brandService.findList(brandIds)));
		productCategoryTemp.setIcon(productCategory.getIcon());
		productCategoryTemp.setOrder(productCategory.getOrder());

		if (!isValid(productCategoryTemp)) {
			return ERROR_VIEW;
		}
		if (productCategoryTemp.getParent() != null) {
			ProductCategory parent = productCategoryTemp.getParent();
			if (parent.equals(productCategoryTemp)) {
				return ERROR_VIEW;
			}
			List<ProductCategory> children = productCategoryService.findChildren(parent);
			if (children != null && children.contains(parent)) {
				return ERROR_VIEW;
			}
		}
		productCategoryService.update(productCategoryTemp, "treePath", "grade","supplierId",
				"children", "products", "parameterGroups", "attributes",
				"promotions", "companys", "suppliers", "coinTypes");
		addFlashMessage(redirectAttributes, SUCCESS_MESSAGE);
		return "redirect:list.jhtml";
	}

	/**
	 * 列表
	 */
	@RequestMapping(value = "/list", method = RequestMethod.GET)
	public String list(ModelMap model,Long supplierId) {
		List<Filter> filters1=new ArrayList<Filter>();
		filters1.add(Filter.eq("status", true));
		filters1.add(Filter.eq("supplierType", 0));
		filters1.add(Filter.eq("enabledFlag", true));
		filters1.add(Filter.eq("isOweCatege", true));
		List<Supplier> supplierList = supplierService.findList(null, filters1, null);
		if(supplierList!=null && !supplierList.isEmpty()){
			if(supplierId!=null){
				Supplier supplier=supplierService.find(supplierId);
				model.addAttribute("supplier",supplier);
				model.addAttribute("productCategoryTree",productCategoryService.findBySupplier(supplier));
			}else{
				//福利平台
				model.addAttribute("productCategoryTree",productCategoryService.findPlatformList());
			}
		}else{
			model.addAttribute("productCategoryTree",productCategoryService.findTree());
		}
		model.addAttribute("supplierList", supplierList);
		return "/admin/product_category/list";
	}

	/**
	 * 删除
	 */
	@RequestMapping(value = "/delete", method = RequestMethod.POST)
	public @ResponseBody
	Message delete(Long id) {
		ProductCategory productCategory = productCategoryService.find(id);
		if (productCategory == null) {
			return ERROR_MESSAGE;
		}
		Set<ProductCategory> children = productCategory.getChildren();
		if (children != null && !children.isEmpty()) {
			return Message
					.error("admin.productCategory.deleteExistChildrenNotAllowed");
		}
		Set<Product> products = productCategory.getProducts();
		if (products != null && !products.isEmpty()) {
			return Message.error("admin.productCategory.deleteExistProductNotAllowed");
		}
		try {
			productCategory.setEnabledFlag(false);
			productCategoryService.update(productCategory);
		} catch (Exception e) {
			e.printStackTrace();
			return Message.error("该分类存在引用不允许删除！");
		}

		return SUCCESS_MESSAGE;
	}

	/**
	 * 自有分类供应商查询该供应商下品牌，否则查询平台下品牌
	 * @param id 供应商id
	 * @author: GanSiquan
	 * @date: 2024-12-24 17:32
	 */
	@RequestMapping("/getBrands")
	public @ResponseBody List<Brand> getBrands(Long id) {
		return brandService.getBrandsBySupplier(supplierService.find(id));
	}

}