#------------ System ------------
system.name=\u8FDC\u884C\u4E91\u5546
system.version=1.0 RELEASE
system.description=\u5168\u7403\u6700\u5927\u4E91\u5546\u57CE
system.show_powered=true
system.project_name=vpshop

#------------ Common ------------
locale=zh_CN
url_escaping_charset=UTF-8

#------------ Template ------------
template.encoding=UTF-8
template.update_delay=0
template.number_format=0.######
template.boolean_format=true,false
template.datetime_format=yyyy-MM-dd
template.date_format=yyyy-MM-dd
template.time_format=HH:mm:ss
template.loader_path=/WEB-INF/template,classpath:/
template.suffix=.ftl

#------------ Message ------------
message.cache_seconds=3600
message.common_path=/WEB-INF/language/common/message
message.shop_path=/WEB-INF/language/shop/message
message.admin_path=/WEB-INF/language/admin/message

#------------ Sn ------------
sn.machine.no =1
sn.product.prefix={.now?string('yyyyMMdd')}
sn.product.maxLo=100
sn.order.prefix={.now?string('yyyyMMdd')}
sn.order.maxLo=100
sn.payment.prefix={.now?string('yyyyMMdd')}
sn.payment.maxLo=100
sn.refunds.prefix={.now?string('yyyyMMdd')}
sn.refunds.maxLo=100
sn.shipping.prefix={.now?string('yyyyMMdd')}
sn.shipping.maxLo=100
sn.returns.prefix={.now?string('yyyyMMdd')}
sn.returns.maxLo=100
sn.coin.prefix={.now?string('yyyyMMdd')}
sn.coin.maxLo=100
sn.rechargeRecord.prefix={.now?string('yyyyMMdd')}
sn.rechargeRecord.maxLo=100
sn.rechargeRecordOpt.prefix={.now?string('yyyyMMdd')}
sn.rechargeRecordOpt.maxLo=100
sn.returnsItem.prefix={.now?string('yyyyMMdd')}
sn.returnsItem.maxLo=100
sn.carePlan.prefix={.now?string('yyyyMMdd')}
sn.carePlan.maxLo=100
sn.squRewardPublish.prefix={.now?string('yyyyMMdd')}
sn.squRewardPublish.maxLo=100
sn.coinRecharge.maxLo=100
sn.tripApply.maxLo=100
#------------ Mail ------------
mail.smtp.auth=true
mail.smtp.timeout=25000
mail.smtp.starttls.enable=false

#------------ Task ------------
task.core_pool_size=5
task.max_pool_size=50
task.queue_capacity=1000
task.keep_alive_seconds=60

#------------ Job ------------
job.static_build.cron=0 0 1 * * ?
job.cart_evict_expired.cron=0 0 1 * * ?
job.order_release_stock.cron=0 0/30 * * * ?
job.weixin_token_refresher.cron=0 */58 * * * ?
job.order_confirm_class.cron=0 0 1 * * ?
job.book_course_class.cron=0 0 2 * * ?
job.freecourse_release_stock.cron=0 0 3 * * ?
job.jd_token_refresher.cron=0 0 5 ? * SUN



#------------ ConnectionPools ------------
connection_pools.initial_pool_size=5
connection_pools.min_pool_size=5
connection_pools.max_pool_size=400
connection_pools.max_idle_time=600
connection_pools.acquire_increment=5
connection_pools.checkout_timeout=60000
connection_pools.preferred_test_query=SELECT 1
connection_pools.idle_connection_test_period=900


#------------ JDBC ------------
#jdbc.driver=com.mysql.jdbc.Driver
#jdbc.url=jdbc\:mysql\://**************\:3306/vpshop_test?useUnicode\=true&characterEncoding\=UTF-8
#jdbc.username=root
#jdbc.password=aihetao

jdbc.driver=com.mysql.jdbc.Driver
jdbc.url=jdbc\:mysql\://**************\:3306/pre_fliplus?useUnicode\=true&characterEncoding\=UTF-8
jdbc.username=fliplus_pre
jdbc.password=flipluspre_123
#jdbc.driver=com.mysql.jdbc.Driver
#jdbc.url=jdbc\:mysql\://**************\:13306/pre_fliplus?useUnicode\=true&characterEncoding\=UTF-8
#jdbc.username=dev
#jdbc.password=123456

#------------ Hibernate ------------
hibernate.dialect=org.hibernate.dialect.MySQLDialect
hibernate.cache.use_second_level_cache=false
hibernate.cache.region.factory_class=org.hibernate.cache.impl.NoCachingRegionFactory
hibernate.cache.use_query_cache=false
hibernate.jdbc.fetch_size=50
hibernate.jdbc.batch_size=30

#------------mq url----------------
mq.brokerURL=tcp://**************:61616
mq.pool.maxConnections=300

#---------------supplier default authorities-----------
authorities.default=seller:product_add,seller:product_manage,seller:product_photo,seller:order,seller:evaluate,seller:after_sale,seller:financial_reconciliation,seller:sales_statistics,seller:sub_account

#------------ wechat pay ------------
appId=wxe92da7aab459b577
appSecret=f8dd42dc18a52aed57127085c311582e
appKey=IjOMYPYJqP15K56cta63TtUeSs2pN87w
mchId=**********
payNotifyUrl=http://pre.fliplus.com/vp-web-buyer/notifies/paid.jhtml
cancel_unpay_order_minutes=40

#------------ alipay ------------
alipay.merchantId=****************
alipay.secret=748o92aymtmhpekufbgfnv8q55uxesl6
alipay.payNotifyUrl=http://pre.fliplus.com/vp-web-buyer/notifies/backend.jhtml
alipay.refundNotifyUrl=http://pre.fliplus.com/vp-web-buyer/notifies/refund.jhtml
alipay.webReturnUrl=http://pre.fliplus.com/vp-web-buyer/member/order/paymentSuccess.jhtml
alipay.wapReturnUrl=http://pre.fliplus.com/vp-web-buyer/notifies/wap/frontend.jhtml

#------------ redis ------------
redis.hostname=**************
redis.port=6379
redis.database=0
redis.pool.maxIdle=300   
redis.pool.maxTotal=600
redis.pool.timeBetweenEvictionRunsMillis=30000  
redis.pool.minEvictableIdleTimeMillis=30000 
redis.pool.testOnBorrow=true 

#------------ wechattenplateId ------------
shipping_templateid=qcf-k5DIlT6O8Q67KKaH5DSQXgSlaCkrQXxxRqcXhDM
coinchange_templateid=oTFqMHasqXeBT7zRYYovQLFC6pwqUGZYTTNZl8J8A1Y
appointmentremind_templateid=ltVViLB7dZZ0IP6w3wk1UfSwvq0lWTTSZltT__VUXvU
payorder_templateid=5uaCBj-itMKOwnJcDHl7Uyj8y60Fiuj9RhpeBLUvZeA
coinexpire_templateid=gDBRkrc3wnpbWebmENpO3JmvavEyqk3fR4_W_NXU2PI
refunds_templateid=fRN6jvwokbef9WuKxLP9mpgxIyX6D1LA80lNqIhS8bs
cointoaccount_templateid=cKXuYPSETznOHCleYhYdqyRTFQEeAFf4P0xRoL37sLk

#-------------sms---------------------
smsCode=131436
smsBook=131772
smsRegister=132810

#------------ jd ------------
jd.appKey=6343355b83d74427af85e7a34c8d8427
jd.appSecret=893d0a883df747bb9b8fe08302ca66b9
jd.username=\u8FDC\u884C\u4E91\u5546
jd.password=820C569E4B6B299D6D7D6EB8E19401DA32434
jd.order.confirm.time=30

