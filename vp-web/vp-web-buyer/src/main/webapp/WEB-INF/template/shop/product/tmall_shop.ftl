<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="renderer" content="webkit"/>
		<title>天猫商城</title>
		<link rel="stylesheet" type="text/css" href="${base}/resources/shop/css/common.css"/>
        <link rel="stylesheet" href="${base}/resources/shop/css/swiper.min.css" />
        <link rel="stylesheet" type="text/css" href="${base}/resources/shop/css/taobao.css"/>
		<script src="${base}/resources/shop/plugins/jquery/jquery-1.9.1.min.js"></script>
		<script type="text/javascript" src="${base}/resources/shop/js/common.js"></script>
        <script type="text/javascript" src="${base}/resources/shop/js/hotWordSearch.js"></script>
        <script type="text/javascript" src="${base}/resources/shop/plugins/swiper/swiper.jquery.min.js"></script>
		<script src="${base}/resources/shop/js/vue/vue.js"></script>
	</head>
	<body class="taobao-page">
		<div class="public_page">
			<!--此处引入公共头部 开始-->
				[#include "/shop/include/header.ftl"]
			<!--此处引入公共头部 结束-->
				<div class="main-content" id="rechargeForm" v-cloak >
					<div class="main-content-header">
						<div class="common_width flexbox align-items-c justify-content-space-between">
							<img src="${base}/resources/shop/img/taobao/logo.png"/>
                            <!-- 搜索 -->
                            <div class="fli_shop_search_section">
                                <form id="productSearchForm" action="${base}/product/search.jhtml" method="get">
                                    <input type="hidden" name="supplierId" value="${supplier.id}" />
                                    <input type="hidden" name="orderType" value="salesDesc" />
                                    <input type="hidden" name="keyword" id="actual-keyword" value=""  >
                                    <input type="hidden" name="from" value="boutique" />
                                    <div class="search_section">
                                        <div>
                                            <input type="text" name="keyword1" :placeholder="hotWord.length>0?'':'请输入搜索内容'" onkeydown="keydownenter()" id="autocomplete-ajax" value="">
                                            <a class="icon_search" href="javascript:void(0)" id="submitBtn">搜索</a>
                                        </div>
                                        <div id="hotWordSearchBox" style="display: none;"  class="hotWordSearchBox ">
                                            <div id="announcementsearchBox" class="announcementsearchBox" style="position: relative;overflow: hidden">
                                                <ul id="announcement3">
                                                </ul>
                                                <div id="announcement4"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <ul class="fli_shop_keywords height-limited text-ellipsis">
                                        <a v-for="(item,index) in hotWord"  >{{item}}</a>
                                    </ul>
                                </form>
                            </div>
							<img src="${base}/resources/shop/img/taobao/header-right.png"/>
						</div>
					</div>
					<div class="common_width main-content-content">
						<!--banner-->
						<div class="fli_shop_header clearfix">
                            <div class="nav_section">
								<p class="allClass">全部分类</p>
                                <ul class="side-nav">
                                    [#list groupProductCategories as groupProductCategory]
                                    [#assign twoProductCategoryName=""]
                                    [#list groupProductCategory as twoProductCategory]
                                        [#if twoProductCategoryName?length>0]
                                            [#assign twoProductCategoryName=twoProductCategoryName+"/"]
                                        [/#if]
                                        [#assign twoProductCategoryName=twoProductCategoryName+twoProductCategory.name]
                                    [/#list]
                                    <li class="flexbox align-items-c justify-content-c">
                                        <a href="javascript:;">${twoProductCategoryName}</a>
                                        <ul>
                                            <li>
                                                [#list groupProductCategory as twoProductCategory]
                                                    <div class="clearfix">
                                                        <a class="right-title" href="/vp-web-buyer/product/list/${twoProductCategory.id}.jhtml" target="_blank">${twoProductCategory.name}</a>
                                                        <ul>
                                                            [#list twoProductCategory.children as threeProductCategory]
                                                                <li class="right-item">
                                                                    <a class="right-content" href="/vp-web-buyer/product/list/${threeProductCategory.id}.jhtml" target="_blank">${threeProductCategory.name}</a>
                                                                </li>
                                                            [/#list]
                                                        </ul>
                                                    </div>
                                                [/#list]
                                            </li>
                                        </ul>
                                    </li>
                                    [/#list]
                                </ul>
                            </div>
                            <div class="swiper-container-content">
                                <div class="swiper-container swiper-container-fade swiper-container-initialized swiper-container-horizontal">
                                    <!--推荐尺寸 1920X480-->
                                    <div class="swiper-wrapper" >
                                    	[@ad_position id = 713/]
                                    </div>
                                    <div class="swiper-pagination"></div>
                                    <!-- Add Arrows -->
                                    <div class="swiper-button-next"></div>
                                    <div class="swiper-button-prev"></div>
								</div>
							</div>
							<div class="data-list flexbox">
                                [#if act??&&act.actProducts??&&act.actProducts?size>0]
                                [#assign i = 0]
                                [#list act.actProducts as actProduct]
        						[#assign p=actProduct.products]
                                [#if p.isList && i<4]
                                    [#assign i = i+1]
                                    [#assign productImage = setting.siteUrlImg + setting.defaultThumbnailProductImage]
                                    [#if (p.supplierId.id == 68 || p.supplierId.id == 181) && p.attributeValue8 != null]
                                        [#assign productImage = p.attributeValue8]
                                    [#elseif p.image!=null]
                                        [#assign productImage = setting.siteUrlImg + p.image]
                                    [#elseif com_person_product_default_img != null]
                                        [#assign productImage = com_person_product_default_img]
                                    [/#if]
                                    <div class="item flexbox flex-column justify-content-space-between align-items-c"  @click="goDetail('${p.path}')">
                                        <div class="image-box flexbox align-items-c justify-content-c">
                                            <img src="${productImage}"/>
                                        </div>

                                        <p>￥<span>${p.price}</span></p>
                                    </div>
                                [/#if]
                                [/#list]
                                [/#if]
							</div>
						</div>

                        <div class="nav  nav_list swiper-container">
                            <div class="swiper-wrapper">
                                <div class=" swiper-slide "  @click="changeNav(item,index)"  :class="{ 'active': activeIndex === index }"  v-for="(item,index) in navList" :key="'nav'+item.id">
                                    <p class="item flexbox align-items-c justify-content-c">
                                        <img :src="item.icon"/><span>{{item.name}}</span>
                                    </p>

                                </div>
                            </div>
                        </div>
                        <div class="product-list-content clearfix">
                            <div class="item" v-for="(item,index) in productList" @click="goDetail(item.path)">
                                <div class="img-box">
                                    <img :src="item.image"/>
                                    <span  class="no_stock" style="display: none" :id="'p-stock_'+item.id">区域无货</span>
                                </div>
                                <div class="title">
                                    {{item.name}}
                                </div>
                                <div class="price-box flexbox justify-content-space-between">
                                            <span class="price-content">
                                                    ￥<span>{{item.price}}</span>
                                            </span>
                                </div>
                            </div>

                        </div>
                        <div  v-if="loading"  class="loadingbox flexbox align-items-c justify-content-c" ><img src="${base}/resources/shop/img/data_loading.gif"/>加载中</div>
                        <div v-if="!noMore" class="no-more">没有更多数据了</div>
                    </div>

               </div>
           <!--此处引入公共尾部 开始-->
				[#include "/shop/include/footer.ftl"]
			<!--此处引入公共尾部 结束-->
			</div>
			
		</div>
		<script>
			var vm = new Vue({
				el: '#rechargeForm',
                computed: {
                    leftWidth: function () {
                        return this.activeIndex1*135+73
                    },
                    leftWidth2:function () {
                        return this.activeIndex2*135+73
                    }
                },
				data: {
				    navList:[
                        [#list productCategories as productCategory]
                        [#if productCategory_index<10]
                            {
                                id: ${productCategory.id},
                                icon:"${base}${productCategory.icon}",
                                name: "${productCategory.name}"
                            }[#if productCategory_index+1<productCategories?size],[/#if]
                        [/#if]
                        [/#list]
                    ],
                    hotWord:[],
                    activeIndex:0,
                    productList:[],
                    totalPage:1,
                    currentPage:1,
                    loading: false, // 加载状态
                    noMore: true,  // 是否有更多数据
                    isFirst:true,
                    scrollLock: false, // 滚动锁，防止重复加载
                    activePcId: null,// 选中的分类id
				},
				created:function(){
                    this.hotWord=   querySearchList(9);//获取搜索热词

				},
				mounted(){
                    //拼团抢购滚动效果
                   var swiper1= new Swiper('.nav_list.swiper-container', {
                       navigation: {
                           nextEl: '.nav_list .swiper-button-next',
                           prevEl: '.nav_list .swiper-button-prev',
                       },
                       slidesPerView: 6,
                       slidesPerGroup: 4,
                       allowTouchMove: true,      // 允许触摸/鼠标拖动
                       simulateTouch: true,       // 在 PC 上模拟触摸事件
                       preventClicks: false,      // 允许点击
                       preventClicksPropagation: false, // 允许事件冒泡
                    });
                    $('.nav_list .swiper-button-prev').on('click', function(e){
                        e.preventDefault();

                        swiper1.slidePrev();
                    })

                    $('.nav_list .swiper-button-next').on('click', function(e){
                        e.preventDefault();
                        swiper1.slideNext();
                    })
                   var  swiper = new Swiper('.swiper-container-content .swiper-container', {
                       pagination: '.swiper-container-content  .swiper-pagination',
                       nextButton: '.swiper-container-content  .swiper-button-next',
                       prevButton: '.swiper-container-content .swiper-button-prev',
                       paginationClickable: true,
                       effect: 'fade',
                       spaceBetween: 30,
                       centeredSlides: true,
                       autoplay: 2500,
                       speed: 500,
                       preventClicks: false,
                       loop: true,
                       autoplayDisableOnInteraction: false
                    });
                    this.activePcId = this.navList[0].id
                    this.onLoad(this.activePcId)
                    window.addEventListener('scroll', this.handleScroll);
				},
                beforeDestroy() {
                    // 使用保存的引用移除监听
                    window.removeEventListener('scroll', this.handleScroll);
                },
				methods: {
                    goDetail(url){
                        window.open("${base}"+url, "_blank");
                    },
                    handleScroll(event) {

                        if (this.scrollLock || this.loading || !this.noMore) return;
                        const scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;
                        const windowHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
                        const documentHeight = document.documentElement.scrollHeight || document.body.scrollHeight;
                        const distanceToBottom = documentHeight - (scrollTop + windowHeight);
                        // 如果距离小于100像素，并且还有更多数据，并且当前没有在加载中，则加载更多
                        if (distanceToBottom < 100 ) {
                            this.scrollLock = true; // 锁定滚动
                            this.onLoad(this.activePcId);
                        }
                    },
                    //切换请求商品
                    changeNav(data,idx){
                        this.activeIndex = idx;
                        this.currentPage = 1;
                        this.totalPage = 1;
                        this.isFirst = true;
                        this.productList = [];
                        this.scrollLock = false;
                        this.noMore = true;
                        this.activePcId = data.id
                        this.onLoad(this.activePcId)
                    },
                    //查询是否有货
                    listStock(data){
                        var ids = new Array();
                        data.map(item=>{
                            ids.push(item.id)
                        })
                        var areaId = getCookie("areaIdCookie"); //
                        $.ajax({
                            url: '${base}/product/listStock.jhtml',
                            type: "POST",
                            data: {ids: ids.toString(), areaId: areaId},
                            dataType: "json",
                            cache: false,
                            success: function(data) {
                                $.each(data, function(index, obj) {
                                    if(obj){
                                        $("#p-stock_" + index).show();
                                    }
                                    else{
                                        $("#p-stock_" + index).hide();
                                    }

                                });
                            }
                        });
                    },
                        onLoad(pcId) {
                        this.loading = true;
                        var that = this;
                        var pageSize = 10;
                        // if(this.listCurPageSS>1&&this.currentPage===1){
                        //     pageSize=this.listCurPageSS*10;
                        // }
                        // 异步更新数据
                        $.ajax({
                            url: "${base}/product/tmallProductList.jhtml",
                            type: "POST",
                            data: {pageNumber:this.currentPage,pageSize:pageSize,productCategoryId:pcId},
                            success: function (data) {
                                var arr =[];
                                if( that.isFirst ){
                                    that.productList = [];
                                    that.isFirst = false;
                                }
                                if(data.content&&data.content.length>0){
                                    that.productList =  that.productList.concat(data.content);
                                    that.listStock(data.content);
                                }

                                that.totalPage = data.totalPages;
                                // 数据全部加载完成
                                if(that.currentPage >= that.totalPage){
                                    that.noMore = false;
                                    that.scrollLock = true;
                                }else{
                                    that.currentPage = that.currentPage + 1;
                                    that.scrollLock = false;
                                }
                                // 加载状态结束
                                that.loading = false;
                            },
                            error: function (xhr, type) {
                                //加载数据失败提示
                                if( that.isFirst ){
                                    that.productList = [];
                                    that.isFirst = false;
                                }
                            }
                        });
                    },
				}
			})

			//检查模块权限
            checkModule();
		</script>
	</body>
</html>
