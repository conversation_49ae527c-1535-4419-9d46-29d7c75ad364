[#assign shiro=JspTaglibs["/WEB-INF/tld/shiro.tld"] /]
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("admin.productCategory.edit")} - ${setting.siteName}</title>
<meta name="author" content="${setting.siteName} Team" />
<meta name="copyright" content="${setting.siteName}" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<meta name="renderer" content="webkit" />
<link href="${base}/resources/admin/css/extend.css" rel="stylesheet" type="text/css" />
<link href="${base}/resources/admin/css/common.css" rel="stylesheet" type="text/css" />
<link href="${base}/resources/admin/css/YS_style.css" rel="stylesheet" type="text/css" />
<link href="${base}/resources/admin/plugin/searchableSelect/jquery.searchableSelect.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="${base}/resources/admin/js/jquery-1.9.1.min.js"></script>
<script type="text/javascript" src="${base}/resources/admin/plugin/searchableSelect/jquery.searchableSelect.js" ></script>
<script type="text/javascript" src="${base}/resources/admin/js/jquery.validate.js"></script>
<script type="text/javascript" src="${base}/resources/admin/js/common.js"></script>
<script type="text/javascript" src="${base}/resources/admin/js/input.js"></script>
<script type="text/javascript" src="${base}/resources/admin/plugin/layer/layer.js" ></script>

<link rel="stylesheet" href="${base}/resources/admin/plugin/element-ui-2.13.2/lib/theme-chalk/index.css">
<style type="text/css">
.brands label {
	width: 150px;
	display: block;
	float: left;
	padding-right: 6px;
}
</style>
<script type="text/javascript">
$().ready(function() {
//var $browserButton = $("#browserButton");
//$browserButton.browser();
	var $inputForm = $("#inputForm");
	
    var $browserButton = $("#browserButton");
	
	[@flash_message /]
	
	$browserButton.browser();

	// 表单验证
	$inputForm.validate({
		rules: {
			name: "required",
			order: {
				required:true,
				digits:true
			}
		}
	});
	
});

function check(){
  var grade=$("#parentId1").find("option:selected").attr("grade");
  if(grade>=2){
    layer.msg("新增分类时最多只能有三个层级", {icon: 1});
    $("#parentId1").val("");
    $("#parentId1").siblings().find(".searchable-select-holder").html("顶级分类");
  }
}


</script>
</head>
<body>
[#assign current = "product" /]
	[#assign curnt = "productCategory" /]
		[#include "/admin/include/header.ftl"]
		<div class="public_body productEditPage">
			<div class="common_width">
				<div class="left_nav">
					<div class="header">商品</div>
					[#include "/admin/include/menus4goods.ftl"]
				</div>
				
				<div class="main_section" id="vueApp">
					<!--面包屑导航 开始-->
					<div class="breadcrumb_section">
						<ul class="breadcrumb">
							<li><a href="../product_category/list.jhtml">商品分类</a></li>
							<li><a href="#">编辑商品分类</a></li>
						</ul>
					</div>
					<!--面包屑导航 结束-->
					
					<!--表单区域  开始-->
					<form id="inputForm" action="update.jhtml" method="post">
					<input type="hidden" name="id" value="${productCategory.id}" />
					<div class="form_section">
						<div class="items">
							<div class="item"> 
						 		<div class="label"><span class="requiredField">*</span>${message("ProductCategory.name")}</div>
						 		<div class="inputs"> 
						 			<input type="text" autocomplete="off" id="name" name="name" class="text" value="${productCategory.name}" maxlength="200" />
						 		</div>
						 	</div>
						 	
						 	<div class="item"> 
						 		<div class="label">供应商</div>
						 		<div class="inputs">
						 			<input type="hidden" autocomplete="off" name="supplierId" id="supplierId" value="${(supplier.id)!''}" />
						 			${(supplier.supplierName)!'福利平台'}
						 		</div>
						 	</div>
						 	
						 	<div class="item"> 
						 		<div class="label">${message("ProductCategory.parent")}</div>
						 		<div class="inputs"> 
						 			<select name="parentId" id="parentId1"  onchange="check()" class="searchable">
						                  <option value="">${message("admin.productCategory.root")}</option>
						             [#list productCategoryTree as category]
							            [#if category.grade<2 && category != productCategory && !children?seq_contains(category)]
								          <option value="${category.id}" grade="${category.grade}" [#if category == productCategory.parent] selected="selected"[/#if]>
										      [#if category.grade == 1]
									            &nbsp;&nbsp;
							                  [/#if]
							                  ${category.name}
								           </option>
							            [/#if]
						             [/#list]
					             </select>
						 		</div>
						 	</div>
						 	
						 	<div class="item"> 
						 		<div class="label">${message("ProductCategory.productType")}</div>
								<div class="inputs">
									[#if productCategory.productType??]
										<input type="text" autocomplete="off" class="text" value="${productCategory.productType.name}" disabled="disabled" />
									[/#if]
								</div>
						 	</div>
						 	
						 	 <div class="item"> 
						        <div class="label">${message("Tag.icon")}</div>
						 		<div class="inputs">
						 		    <input type="text" autocomplete="off" name="icon" class="text" value="${productCategory.icon}" maxlength="200" />
					                <input type="button" id="browserButton" class="btn_theme btn_links" value="${message("admin.browser.select")}" />
					                [#if productCategory.icon??]
						               <a href="${base}${productCategory.icon}" target="_blank">${message("admin.common.view")}</a>
					                [/#if]
                                 </div>
						  </div>
						 	
						 	<div class="item"> 
						 		<div class="label">${message("ProductCategory.brands")}</div>
						 		 <el-select style="vertical-align: middle;width:436px" v-model="brandIds" multiple clearable filterable placeholder="请选择">
                                    <el-option
                                    v-for="item in brandList"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                    </el-option>
                                </el-select>
                                <input type="hidden" id="brandIds" name="brandIds" :value="brandIds" />
						 	</div>
						 	
						 	<div class="item"> 
						 		<div class="label">${message("ProductCategory.seoTitle")}</div>
						 		<div class="inputs"> 
						 			<input type="text" autocomplete="off" name="seoTitle" class="text" value="${productCategory.seoTitle}" maxlength="200" />
						 		</div>
						 	</div>
						 	
						 	<div class="item"> 
						 		<div class="label">${message("ProductCategory.seoKeywords")}</div>
						 		<div class="inputs"> 
						 			<input type="text" autocomplete="off" name="seoKeywords" class="text" value="${productCategory.seoKeywords}" maxlength="200" />
						 		</div>
						 	</div>
						 	
						 	<div class="item"> 
						 		<div class="label">${message("ProductCategory.seoDescription")}</div>
						 		<div class="inputs"> 
						 			<input type="text" autocomplete="off" name="seoDescription" class="text" value="${productCategory.seoDescription}" maxlength="200" />
						 		</div>
						 	</div>
						 	
						 	
						 	<div class="item"> 
						 		<div class="label"><span class="requiredField">*</span>${message("admin.common.order")}</div>
						 		<div class="inputs"> 
						 			<input type="text" autocomplete="off" name="order" class="text" value="${productCategory.order}" maxlength="9" /><span>0表示最前面，数字越大排名越靠后</span>
						 		</div>
						 	</div>
						 	
				<!--<tr>
				<th>
					分类展示图片:
				</th>
				<td>
					<span class="fieldSet">
						<input type="text" autocomplete="off" id="categoryImg" name="categoryImg" value="${productCategory.categoryImg}" class="text" maxlength="200"  />
						<input type="button" id="browserButton" class="button" value="${message("admin.browser.select")}" />
					</span>
					   [#if productCategory.categoryImg??]
							<a href="${base}${productCategory.categoryImg}" target="_blank">${message("admin.common.view")}</a>
						[/#if]
				</td>
			</tr>-->
						 	
						</div>
					 	 
					 	
					 	<div class="btns">
					 		<input type="submit" name="" id="" value="提交" class="btn_theme btn_submit"/> 
					 	</div>
					</div>  
					<!--表单区域  结束-->
				</div>
				
			</div>
			
		</div>
		[#include "/admin/include/footer.ftl"]
		[#--<script src="https://cdn.jsdelivr.net/npm/vue@2.6.11"></script>--]
		<script src="${base}/resources/admin/plugin/vue/vue.min.js"></script>
        <script src="${base}/resources/admin/plugin/element-ui-2.13.2/lib/index.js"></script>
		<script>
            
            var brandIds = [
                [#list productCategory.brands as brand]
                      ${brand.id},
                [/#list]
            ];
            var brandList = [
            [#list brands as brand]
            {
                value:${brand.id},
                label:"${brand.name}"
            },
            [/#list]
            ];
            

            new Vue({
                el: '#vueApp',
                data:function(){
                    return {
                        brandList:brandList,
                        brandIds:brandIds,
                    }
                }
            })

        
        </script>
	</body>
</html>
