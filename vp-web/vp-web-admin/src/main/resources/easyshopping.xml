<?xml version="1.0" encoding="UTF-8"?>
<easyshopping>
	<setting name="siteName" value="福利PLUS" />
   <setting name="siteUrl" value="http://**************/vp-web-admin/" /><!-- 本系统网址 -->
	<setting name="siteUrlImage" value="http://**************/vp-web-seller/" /><!-- 图片网址 -->
	<setting name="siteUrlAdmin" value="http://**************/vp-web-admin/" /><!-- 运营平台网址 -->
	<setting name="siteUrlSeller" value="http://**************/vp-web-seller/" /><!-- 供应商平台网址 -->
	<setting name="siteUrlHr" value="http://**************/vp-web-hr/" /><!-- 企业平台网址 -->
	<setting name="siteUrlBuyer" value="http://**************/vp-web-buyer/" /><!-- 员工平台网址 -->
	<setting name="siteUrlwechat" value="http://**************/vp-web-buyer-wechat/" /><!-- 员工平台网址 -->
	<setting name="servicePhone" value="0755-86212290" />
	<setting name="serviceTime" value="工作日 9:00-18:00" />
	<setting name="freight" value="6" />
	<setting name="freeFreight" value="99" />
	<setting name="logo" value="/resources/image/logo.png" />
	<setting name="hotSearch" value="衬衫, T恤, 春夏新款, 牛仔裙" />
	<setting name="address" value="深圳经济特区" />
	<setting name="phone" value="" />
	<setting name="zipCode" value="" />
	<setting name="email" value="" />
	<setting name="certtext" value="" />
	<setting name="isSiteEnabled" value="true" />
	<setting name="siteCloseMessage" value="&lt;dl&gt;&lt;dt&gt;网站维护中，暂时无法访问！&lt;/dt&gt;&lt;dd&gt;本网站正在进行系统维护和技术升级，网站暂时无法访问，敬请谅解！&lt;/dd&gt;&lt;/dl&gt;" />
	<setting name="largeProductImageWidth" value="800" />
	<setting name="largeProductImageHeight" value="800" />
	<setting name="mediumProductImageWidth" value="300" />
	<setting name="mediumProductImageHeight" value="300" />
	<setting name="thumbnailProductImageWidth" value="170" />
	<setting name="thumbnailProductImageHeight" value="170" />
	<setting name="defaultLargeProductImage" value="/resources/image/default_large.jpg" />
	<setting name="defaultMediumProductImage" value="/resources/image/default_medium.jpg" />
	<setting name="defaultThumbnailProductImage" value="/resources/image/default_thumbnail.jpg" />
	<setting name="watermarkAlpha" value="50" />
	<setting name="watermarkImage" value="/resources/image/watermark.png" />
	<setting name="watermarkPosition" value="bottomRight" />
	<setting name="priceScale" value="2" />
	<setting name="priceRoundType" value="roundHalfUp" />
	<setting name="isShowMarketPrice" value="true" />
	<setting name="defaultMarketPriceScale" value="1.2" />
	<setting name="isRegisterEnabled" value="true" />
	<setting name="isDuplicateEmail" value="false" />
	<setting name="disabledUsername" value="admin,管理员" />
	<setting name="usernameMinLength" value="2" />
	<setting name="usernameMaxLength" value="20" />
	<setting name="passwordMinLength" value="4" />
	<setting name="passwordMaxLength" value="20" />
	<setting name="registerPoint" value="0" />
	<setting name="registerAgreement" value="&lt;p&gt;尊敬的用户欢迎您注册成为本网站会员。请用户仔细阅读以下全部内容。如用户不同意本服务条款任意内容，请不要注册或使用本网站服务。如用户通过本网站注册程序，即表示用户与本网站已达成协议，自愿接受本服务条款的所有内容。此后，用户不得以未阅读本服务条款内容作任何形式的抗辩。&lt;/p&gt;
&lt;p&gt;一、本站服务条款的确认和接纳&lt;br /&gt;本网站涉及的各项服务的所有权和运作权归本网站所有。本网站所提供的服务必须按照其发布的服务条款和操作规则严格执行。本服务条款的效力范围及于本网站的一切产品和服务，用户在享受本网站的任何服务时，应当受本服务条款的约束。&lt;/p&gt;
&lt;p&gt;二、服务简介&lt;br /&gt;本网站运用自己的操作系统通过国际互联网络为用户提供各项服务。用户必须:  1. 提供设备，如个人电脑、手机或其他上网设备。 2. 个人上网和支付与此服务有关的费用。&lt;/p&gt;
&lt;p&gt;三、用户在不得在本网站上发布下列违法信息&lt;br /&gt;1. 反对宪法所确定的基本原则的； 2. 危害国家安全，泄露国家秘密，颠覆国家政权，破坏国家统一的； 3. 损害国家荣誉和利益的； 4. 煽动民族仇恨、民族歧视，破坏民族团结的； 5. 破坏国家宗教政策，宣扬邪教和封建迷信的； 6. 散布谣言，扰乱社会秩序，破坏社会稳定的； 7. 散布淫秽、色情、赌博、暴力、凶杀、恐怖或者教唆犯罪的； 8. 侮辱或者诽谤他人，侵害他人合法权益的； 9. 含有法律、行政法规禁止的其他内容的。&lt;/p&gt;
&lt;p&gt;四、有关个人资料&lt;br /&gt;用户同意:  1. 提供及时、详尽及准确的个人资料。 2. 同意接收来自本网站的信息。 3. 不断更新注册资料，符合及时、详尽准确的要求。所有原始键入的资料将引用为注册资料。 4. 本网站不公开用户的姓名、地址、电子邮箱和笔名。除以下情况外:  a) 用户授权本站透露这些信息。 b) 相应的法律及程序要求本站提供用户的个人资料。&lt;/p&gt;
&lt;p&gt;五、服务条款的修改&lt;br /&gt;本网站有权在必要时修改服务条款，一旦条款及服务内容产生变动，本网站将会在重要页面上提示修改内容。如果不同意所改动的内容，用户可以主动取消获得的本网站信息服务。如果用户继续享用本网站信息服务，则视为接受服务条款的变动。&lt;/p&gt;
&lt;p&gt;六、用户隐私制度&lt;br /&gt;尊重用户个人隐私是本网站的一项基本政策。所以，本网站一定不会在未经合法用户授权时公开、编辑或透露其注册资料及保存在本网站中的非公开内容，除非有法律许可要求或本网站在诚信的基础上认为透露这些信息在以下四种情况是必要的:  1. 遵守有关法律规定，遵从本网站合法服务程序。 2. 保持维护本网站的商标所有权。 3. 在紧急情况下竭力维护用户个人和社会大众的隐私安全。 4. 符合其他相关的要求。&lt;/p&gt;
&lt;p&gt;七、用户的帐号、密码和安全性&lt;br /&gt;用户一旦注册成功，将获得一个密码和用户名。用户需谨慎合理的保存、使用用户名和密码。如果你不保管好自己的帐号和密码安全，将负全部责任。另外，每个用户都要对其帐户中的所有活动和事件负全责。你可随时根据指示改变你的密码。用户若发现任何非法使用用户帐号或存在安全漏洞的情况，请立即通告本网站。   八、 拒绝提供担保 用户明确同意信息服务的使用由用户个人承担风险。本网站不担保服务不会受中断，对服务的及时性，安全性，出错发生都不作担保，但会在能力范围内，避免出错。&lt;/p&gt;
&lt;p&gt;九、有限责任&lt;br /&gt;如因不可抗力或其它本站无法控制的原因使本站销售系统崩溃或无法正常使用导致网上交易无法完成或丢失有关的信息、记录等本站会尽可能合理地协助处理善后事宜，并努力使客户免受经济损失，同时会尽量避免这种损害的发生。&lt;/p&gt;
&lt;p&gt;十、用户信息的储存和限制&lt;br /&gt;本站有判定用户的行为是否符合国家法律法规规定及本站服务条款权利，如果用户违背本网站服务条款的规定，本网站有权中断对其提供服务的权利。&lt;/p&gt;
&lt;p&gt;十一、用户管理&lt;br /&gt;用户单独承担发布内容的责任。用户对服务的使用是根据所有适用于本站的国家法律、地方法律和国际法律标准的。用户必须遵循:  1. 使用网络服务不作非法用途。 2. 不干扰或混乱网络服务。 3. 遵守所有使用网络服务的网络协议、规定、程序和惯例。 用户须承诺不传输任何非法的、骚扰性的、中伤他人的、辱骂性的、恐性的、伤害性的、庸俗的，淫秽等信息资料。另外，用户也不能传输何教唆他人构成犯罪行为的资料；不能传输助长国内不利条件和涉及国家安全的资料；不能传输任何不符合当地法规、国家法律和国际法律的资料。未经许可而非法进入其它电脑系统是禁止的。 若用户的行为不符合以上提到的服务条款，本站将作出独立判断立即取消用户服务帐号。用户需对自己在网上的行为承担法律责任。用户若在本站上散布和传播反动、色情或其它违反国家法律的信息，本站的系统记录有可能作为用户违反法律的证据。&lt;/p&gt;
&lt;p&gt;十二、通告&lt;br /&gt;所有发给用户的通告都可通过重要页面的公告或电子邮件或常规的信件传送。服务条款的修改、服务变更、或其它重要事件的通告都会以此形式进行。&lt;/p&gt;
&lt;p&gt;十三、信息内容的所有权&lt;br /&gt;本网站定义的信息内容包括: 文字、软件、声音、相片、录象、图表；在广告中全部内容；本网站为用户提供的其它信息。所有这些内容受版权、商标、标签和其它财产所有权法律的保护。所以，用户只能在本网站和广告商授权下才能使用这些内容，而不能擅自复制、再造这些内容、或创造与内容有关的派生产品。本站所有的文章版权归原文作者和本站共同所有，任何人需要转载本站的文章，必须征得原文作者或本站授权。&lt;/p&gt;
&lt;p&gt;十四、法律&lt;br /&gt;本协议的订立、执行和解释及争议的解决均应适用中华人民共和国的法律。用户和本网站一致同意服从本网站所在地有管辖权的法院管辖。如发生本网站服务条款与中华人民共和国法律相抵触时，则这些条款将完全按法律规定重新解释，而其它条款则依旧保持对用户的约束力。&lt;/p&gt;"/> 
	<setting name="isEmailLogin" value="true" />
	<setting name="captchaTypes" value="memberLogin,memberRegister,adminLogin,review,consultation,findPassword,resetPassword,other" />
	<setting name="accountLockTypes" value="member,admin" />
	<setting name="accountLockCount" value="5" />
	<setting name="accountLockTime" value="30" />
	<setting name="safeKeyExpiryTime" value="15" />
	<setting name="sendVerificationCodeTime" value="1" />
	<setting name="uploadMaxSize" value="10" />
	<setting name="uploadImageExtension" value="jpg,jpeg,bmp,gif,png" />
	<setting name="uploadFlashExtension" value="swf,flv" />
	<setting name="uploadMediaExtension" value="swf,flv,mp3,wav,avi,rm,rmvb" />
	<setting name="uploadFileExtension" value="zip,rar,7z,doc,docx,xls,xlsx,ppt,pptx,pdf" />
	<setting name="imageUploadPath" value="/upload/image/${.now?string('yyyyMM')}/" />
	<setting name="flashUploadPath" value="/upload/flash/${.now?string('yyyyMM')}/" />
	<setting name="mediaUploadPath" value="/upload/media/${.now?string('yyyyMM')}/" />
	<setting name="fileUploadPath" value="/upload/file/${.now?string('yyyyMM')}/" />
	<setting name="serviceMail" value="<EMAIL>" />
	<setting name="smtpFromMail" value="<EMAIL>" />
	<setting name="smtpHost" value="smtp.exmail.qq.com" />
	<setting name="smtpPort" value="25" />
	<setting name="smtpUsername" value="<EMAIL>" />
	<setting name="smtpPassword" value="Test246" />
	<setting name="smsname" value="云通讯" />
	<setting name="httpaddr" value="app.cloopen.com" />
	<setting name="httpmothod" value="8883" />
	<setting name="userpwdparam" value="8aaf07085805254b0158196e0f150bd0" />
	<setting name="usernameparam" value="698fe54c3e664d93a7c0b248ed295d70" />
	<setting name="userpwdvalue" value="8aaf07085805254b0158196e112a0bd7" />
	<setting name="usernamevalue" value="usernamevalue" />
	<setting name="mobileparam" value="destmobile" />
	<setting name="contentparam" value="msgText" />
	<setting name="encodevalue" value="utf_8" />
	<setting name="tomd5" value="" />
	<setting name="returnstyle" value="JSON" />
	<setting name="returnparam" value="" />
	<setting name="returnvalue" value="1" />
	<setting name="messgetail" value="【福利PLUS】" />
	<setting name="currencySign" value="￥" />
	<setting name="currencyUnit" value="元" />
	<setting name="stockAlertCount" value="5" />
	<setting name="stockAllocationTime" value="order" />
	<setting name="defaultPointScale" value="1" />
	<setting name="isDevelopmentEnabled" value="false" />
	<setting name="isReviewEnabled" value="true" />
	<setting name="isReviewCheck" value="true" />
	<setting name="reviewAuthority" value="anyone" />
	<setting name="isConsultationEnabled" value="true" />
	<setting name="isConsultationCheck" value="true" />
	<setting name="consultationAuthority" value="member" />
	<setting name="isInvoiceEnabled" value="true" />
	<setting name="isTaxPriceEnabled" value="true" />
	<setting name="taxRate" value="0.06" />
	<setting name="cookiePath" value="/" />
	<setting name="cookieDomain" value="" />
	<setting name="kuaidi100Key" value="" />
	<setting name="isCnzzEnabled" value="true" />
	<setting name="cnzzSiteId" value="test" />
	<setting name="cnzzPassword" value="test" />
	
	<!-- 优惠券发放条件 -->
	<setting name="couponIssueCondition" value="WeiXinBinding:微信绑定,shareOrder:分享红包,HolidayWelfare:节日福利,ActCoupon:活动领券,BirthCoupon:生日券" />
	<setting name="weiXinBinding" value="WeiXinBinding" />
	<setting name="holidayWelfare" value="HolidayWelfare" />
	<setting name="shareOrder" value="shareOrder" />
	<setting name="shareOrderCount" value="5" />
	<setting name="ActCoupon" value="ActCoupon" />
	<setting name="BirthCoupon" value="BirthCoupon" />
	
	<logConfig operation="系统设置" urlPattern="/admin/setting/update.jhtml" />
	<logConfig operation="添加管理员" urlPattern="/admin/admin/save.jhtml" />
	<logConfig operation="编辑管理员" urlPattern="/admin/admin/update.jhtml" />
	<logConfig operation="删除管理员" urlPattern="/admin/admin/delete.jhtml" />
	<logConfig operation="添加会员" urlPattern="/admin/member/save.jhtml" />
	<logConfig operation="编辑会员" urlPattern="/admin/member/update.jhtml" />
	<logConfig operation="删除会员" urlPattern="/admin/member/delete.jhtml" />
	<logConfig operation="添加商品" urlPattern="/admin/product/save.jhtml" />
	<logConfig operation="编辑商品" urlPattern="/admin/product/update.jhtml" />
	<logConfig operation="删除商品" urlPattern="/admin/product/delete.jhtml" />
	<logConfig operation="添加文章" urlPattern="/admin/article/save.jhtml" />
	<logConfig operation="编辑文章" urlPattern="/admin/article/update.jhtml" />
	<logConfig operation="删除文章" urlPattern="/admin/article/delete.jhtml" />
	<logConfig operation="编辑订单" urlPattern="/admin/order/update.jhtml" />
	<logConfig operation="删除订单" urlPattern="/admin/order/delete.jhtml" />
	<logConfig operation="订单确认" urlPattern="/admin/order/confirm.jhtml" />
	<logConfig operation="订单完成" urlPattern="/admin/order/complete.jhtml" />
	<logConfig operation="订单取消" urlPattern="/admin/order/cancel.jhtml" />
	<logConfig operation="订单支付" urlPattern="/admin/order/payment.jhtml" />
	<logConfig operation="订单退款" urlPattern="/admin/order/refunds.jhtml" />
	<logConfig operation="订单发货" urlPattern="/admin/order/shipping.jhtml" />
	<logConfig operation="订单退货" urlPattern="/admin/order/returns.jhtml" />
	
	
	<template id="shopCommonJs" type="page" name="shop_common_js" templatePath="/shop/js/common.ftl" staticPath="/resources/shop/js/common.js" />
	<template id="adminCommonJs" type="page" name="admin_common_js" templatePath="/admin/js/common.ftl" staticPath="/resources/admin/js/common.js" />
	<template id="sitemapIndex" type="page" name="sitemap索引" templatePath="/shop/xml/sitemap_index.ftl" staticPath="/sitemap/index.xml" />
	<template id="sitemap" type="page" name="sitemap" templatePath="/shop/xml/sitemap.ftl" staticPath="/sitemap/${index}.xml" />
	<template id="index" type="page" name="首页" templatePath="/shop/index.ftl" staticPath="/index.html" />
	
	<template id="articleContent" type="page" name="文章内容" templatePath="/shop/article/content.ftl" staticPath="/article/content/${createDate?string('yyyyMM')}/${id}/${pageNumber!1}.html" />
	<template id="productContent" type="page" name="商品内容" templatePath="/shop/product/content.ftl" staticPath="../vp-web-buyer/product/content/${createDate?string('yyyyMM')}/${id}.html" />
	<template id="articleList" type="page" name="文章列表" templatePath="/shop/article/list.ftl" />
	<template id="productList" type="page" name="商品列表" templatePath="/shop/product/list.ftl" />
	<template id="brandList" type="page" name="品牌列表" templatePath="/shop/brand/list.ftl" />
	<template id="brandContent" type="page" name="品牌内容" templatePath="/shop/brand/content.ftl" />
	<template id="promotionContent" type="page" name="促销内容" templatePath="/shop/promotion/content.ftl" />
	<template id="productCategory" type="page" name="商品分类" templatePath="/shop/product_category/index.ftl" />
	<template id="friendLink" type="page" name="友情链接" templatePath="/shop/friend_link/index.ftl" />
	<template id="happyBirthday" type="page" name="生日" templatePath="wechat/member/card/happyBirthday/index.ftl" />
	<template id="newYear" type="page" name="元旦" templatePath="wechat/member/card/newYear/index.ftl" />
	<template id="springFestival" type="page" name="春节" templatePath="wechat/member/card/springFestival/index.ftl" />
	<template id="testMail" type="mail" name="邮件测试" templatePath="/admin/setting/mail.ftl" />
	<template id="productNotifyMail" type="mail" name="到货通知" templatePath="/shop/product_notify/mail.ftl" />
	<template id="findPasswordMail" type="mail" name="找回密码" templatePath="/shop/password/mail.ftl"/>
	<template id="setNewEmail" type="mail" name="设置新邮箱" templatePath="/shop/mailTemplate/setNewEmail.ftl"/>
	<template id="promotionBookEmail" type="mail" name="促销提醒邮箱" templatePath="/admin/promotion/mail.ftl"/>
	<template id="careNotifyMail" type="mail" name=" 员工关怀通知" templatePath="/hr/care/mail.ftl" />
	<template id="couponNotifyMail" type="mail" name=" 优惠券发放通知" templatePath="/admin/coupon/mail.ftl" />
	<template id="orderPrint" type="print" name="订单打印" templatePath="/admin/print/order.ftl"/>
	<template id="productPrint" type="print" name="购物单打印" templatePath="/admin/print/product.ftl"/>
	<template id="shippingPrint" type="print" name="配送单打印" templatePath="/admin/print/shipping.ftl"/>
	
	
</easyshopping>