# http configuration
http.timeout=60000
http.defaultCharset=utf-8
http.connection.maxTotal=200
http.connection.defaultMaxPerRoute=50

# channel configuration
channel.appKey=2a0d772536b049d7864f6bbbdcf41c10
channel.appSecret=38026c337b8749008c3b54511ec6ee02

# 需要在host中添加下面映射
# ***************  openapi-test.you.163.com
yx.openApi.host=http://openapi-test.you.163.com
#yx.openApi.host=http://openapi.you.163.com
yx.openApi.path=/channel/api.json
yx.openApi.payedOrder.method=yanxuan.order.paid.create
yx.openApi.getOrder.method=yanxuan.order.paid.get
yx.openApi.confirmOrder.method=yanxuan.order.received.confirm
yx.openApi.cancelOrder.method=yanxuan.order.paid.cancel

yx.openApi.getIds.method=yanxuan.item.id.batch.get
yx.openApi.getItems.method=yanxuan.item.batch.get

yx.openApi.getInventory.method=yanxuan.inventory.count.get

yx.openApi.getCallBackMethods.method=yanxuan.callback.method.list
yx.openApi.registerCallbackMethod.method=yanxuan.callback.method.register

yx.openApi.callback.orderExceptional.method=yanxuan.notification.order.exceptional
yx.openApi.callback.skuClose.method=yanxuan.notification.sku.close
yx.openApi.callback.skuReopen.method=yanxuan.notification.sku.reopen
yx.openApi.callback.orderDelivered.method=yanxuan.notification.order.delivered

yx.openApi.mockapi.path.prefix=/mock/api/v1
yx.openApi.mockapi.callbackAuditCancelOrder.method=/callbackAuditCancelOrder
yx.openApi.mockapi.callbackCancelOrder.method=/callbackCancelOrder
yx.openApi.mockapi.callbackDeliveryOrder.method=/callbackDeliveryOrder
yx.openApi.mockapi.callbackTransfer.method=/callbackTransfer

yx.yanxuan_create_time=1