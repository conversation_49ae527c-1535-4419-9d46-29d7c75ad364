<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.2.xsd"
	default-lazy-init="true">

	<bean id="shiroFilter" class="org.apache.shiro.spring.web.ShiroFilterFactoryBean">
		<property name="securityManager" ref="securityManager" />
		<property name="loginUrl" value="/seller/login.jsp" />
		<property name="successUrl" value="/seller/common/index.jhtml" />
		<property name="unauthorizedUrl" value="/seller/common/unauthorized.jhtml" />
		<property name="filterChainDefinitions">
			<value>
				/seller/ = anon
				/seller/index.jsp = anon
				/seller/login.jsp = authc
				/seller/logout.jsp = logout
				/seller/common/captcha.jhtml = anon
				/seller/agent/getVerifyFlag.jhtml=anon
				/seller/agent/sendVerifyFlagSms.jhtml=anon
				/seller/agent/verificationSmsCode.jhtml=anon
				/seller/agent/getPic.jhtml=anon
				/seller/agent/checkcapcode.jhtml=anon
				/seller/product/add** = perms["seller:product_add"]
				/seller/product/list** = perms["seller:product_manage"]
				/seller/product/photo** = perms["seller:product_photo"]
				/seller/order/** = perms["seller:order"]
				/seller/evaluate/** = perms["seller:evaluate"]
				/seller/after_sale/** = perms["seller:after_sale"]
				/seller/financial_reconciliation/** = perms["seller:financial_reconciliation"]
				/seller/sales/** = perms["seller:sales_statistics"]
				/seller/sub_account/** = perms["seller:sub_account"]

				/seller/** = authc
			</value>
		</property>
		<property name="filters">
			<map>
				<entry key="authc" value-ref="authenticationFilter" />
				<entry key="logout" value-ref="logout" />
			</map>
		</property>
	</bean>
	<bean id="logout" class="org.apache.shiro.web.filter.authc.LogoutFilter">
		<property name="redirectUrl" value="/seller/login.jsp" />
	</bean>
		<bean id="securityManager" class="org.apache.shiro.web.mgt.DefaultWebSecurityManager">
		<property name="realm" ref="authenticationRealm" />
		<property name="cacheManager" ref="rediacacheManager" />
	</bean>

   <bean id="redisManager" class="org.crazycake.shiro.RedisManager">
	    <property name="host" value="${redis.hostname}"/>
	    <property name="port" value="${redis.port}"/>
	    <property name="timeout" value="10000"/>
	    <property name="password" value="${redis.password}"/>
	</bean>

   <bean id="rediacacheManager" class="org.crazycake.shiro.RedisCacheManager">
	    <property name="redisManager" ref="redisManager" />
<!--	    <property name="expire" value="1800"/>-->
	</bean>

	<bean id="authenticationRealm" class="com.vispractice.vpshop.AuthenticationRealm">
		<property name="authorizationCacheName" value="authorization" />
	</bean>

	<bean id="shiroCacheManager" class="org.apache.shiro.cache.ehcache.EhCacheManager">
		<property name="cacheManager" ref="ehCacheManager" />
	</bean>

	<bean id="authenticationFilter" class="com.vispractice.vpshop.filter.AuthenticationFilter" />

	<bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
		<property name="staticMethod" value="org.apache.shiro.SecurityUtils.setSecurityManager" />
		<property name="arguments" ref="securityManager" />
	</bean>

</beans>