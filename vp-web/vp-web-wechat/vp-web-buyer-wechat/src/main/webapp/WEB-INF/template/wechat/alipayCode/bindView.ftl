<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta
            name="viewport"
            content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no"
    />
    <title>绑定支付宝账户</title>
    <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
  <link rel="stylesheet" href="${base}/resources/wechat/css/vant/index.css">
    <link  type="text/css" rel="stylesheet" href="${base}/resources/wechat/css/common.css">
    <link  type="text/css" rel="stylesheet" href="${base}/resources/wechat/css/checkingMobile.css">
 <script src="${base}/resources/wechat/plugins/vue/vue.min.js"></script>

    <script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
    <script src="${base}/resources/wechat/js/vant.min.js"></script>
</head>
<body class="loginPage corporate-code-open">
<div id="app"  @click.stop="hideHomeList">
    <van-sticky>
        <van-nav-bar
                title="绑定支付宝账户"
                left-text=""
                left-arrow
                @click-left="onClickLeft"
                @click-right.stop="showJumplist"
        >
            <template #right>
                <van-icon  name="ellipsis" />
                <ul class="showHomeList  modify_public_right_nav " v-if="showHomeList">
                    <li>
                        <a href="${base}/index.jhtml">
                            <span class="icon_home"></span>
                            <p>首页</p>
                        </a>
                    </li>
                    <li>
                        <a href="${base}/alipayCode/index.jhtml">
                            <span class="wechaticon_home"></span>
                            <p>企业码首页</p>
                        </a>
                    </li>
                    <li>
                        <a href="[#if my_home_link?? && my_home_link != ""]${my_home_link}[#else]${base}/member/index.jhtml[/#if]">
                            <span class="icon_user"></span>
                            <p>个人中心</p>
                        </a>
                    </li>
                </ul>
            </template>
        </van-nav-bar>
    </van-sticky>
    <div class="content">
        <van-steps :active="active" active-color="#1c77f9">
            <van-step v-for="(item,index) in titleList" :key="'titleList'+index" v-cloak> {{ item.title }}</van-step>
        </van-steps>
        [#--<div class="steps flexbox">--]
            [#--<div class="item" :class="[{active:activeStep ==item.id}]" v-for="(item,index) in titleList" :key="'titleList'+index">--]
                [#--{{ item.id }} <span>{{ item.title }}</span>--]
            [#--</div>--]
        [#--</div>--]
        <div class="content-main" >
            <van-form @submit="onSubmit" v-if=" active===0">
                <van-field
                        type="text"
                        v-model="account"
                        placeholder="请输入支付宝账号"
                        :border="false"
                >
                </van-field>
                <van-field
                        type="text"
                        v-model="name"
                        placeholder="请输入支付宝对应的真实姓名"
                        :border="false"
                >
                </van-field>
                <div class="btnoption">
                    <van-button class="borderR" :loading="loading" :disabled="loading" loading-text="提交中..." color="rgb(20,143,240)" block  native-type="submit" v-cloak>{{btnText}}</van-button>
                </div>
            </van-form>
            <div v-if="active===1">
                <div class="infomation">
                    <div class="kaitong-img" >
                        <img src="${base}/resources/wechat/img/corporate/doing.png"/>
                    </div>
                    <div class="info-content">
                        <p class="labelText flexbox align-items-c">
                            <label class="des">
                                支付宝账号
                                <span></span>
                            </label>
                            :
                            <span  class="value" v-cloak>
                                {{account}}
                            </span>
                            <van-icon class="edit" @click="edit" name="edit" />
                        </p>
                        <p class="labelText flexbox align-items-c">
                            <label class="des">
                                姓名
                                <span></span>
                            </label>
                            :
                            <span class="value" v-cloak>
                                 {{name}}
                            </span>
                        </p>
                    </div>
           
                </div>
                <div class="btnoption">
                    <van-button class="borderR" :loading="loading" :disabled="loading" loading-text="提交中..." color="rgb(20,143,240)" @click="goZhifubao" block  native-type="button" v-cloak>{{btnText2}}</van-button>
                </div>
            </div>
            <div class="msg">
                    <p class="msgtitle">
                        绑定说明
                    </p>
                    <p class="msg-content">
                        1.请填写您本人已实名认证的支付宝账号（手机或邮箱）。<br/>

                        2.填写您本人的真实姓名。<br/>
                        
                        3.手机号对应多个支付宝账户，请填写邮箱地址的账号名称(登录支付宝APP-我的-个人信息-支付宝账号)<br/>

                    </p>
            </div>

        </div>
    </div>
    <van-dialog
            width="80%"
            v-model="show"
            :title="dialogTitle"
            class="PhoneCodeDialog"
            :before-close="confirmOption"
            @cancel="cancelOption"
            :confirm-button-text="confirmButtonText"
            :show-cancel-button="showCancelbutton">
        <div class="content " :class="status===1?'flexbox align-items-c justify-content-c':''">
            <template v-if="status===0">
                <p>
                    您填写的支付宝账号已绑定其他账号，<br/>
                    如需继续绑定，请通过以下验证
                </p>
                <van-field
                        v-model="code"
                        placeholder="请填写验证码"
                        :border="false"
                        type="digit"
                        maxlength="6"
                >
                    <template slot="button">
                        <van-count-down
                                ref="countDown"
                                :auto-start="false"
                                @finish="countFinished"
                                :time="time"
                                format="ss"
                                v-show="isOn"
                                class="i-text"
                        >
                        </van-count-down>
                        <span @click="handleCode" class="i-text" v-show="!isOn"
                        >发送验证码</span
                        >
                    </template>
                </van-field>
                <template>
                    <p class="line-breaks" v-cloak>接收验证码{{typeName}}：{{tel}}</p>
                </template>
            </template>
          <template  v-if="status===1">
                    <p v-html="msgContent"></p>
            </template>
        </div>
    </van-dialog>
</div>
<script>
    let vm = new Vue({
        el: "#app",
        data() {
            return {
                dialogTitle:'温馨提示',//弹窗标题
                confirmButtonText:"确定",//弹窗的确认按钮文字
                showCancelbutton:true,//是否展示取消按钮
                active:0,//开通的步骤 0 1 2
                btnText:"绑定",//按钮文字
                btnText2:'去支付宝激活', //按钮文字
                account: '',//支付宝账户账户
                name: '',//姓名
                oldAccount:'', //保留之前的支付宝账户和姓名，对比是否有修改
                oldName:'',
                code:'',//验证码
                tel:'',//支付宝绑定手机号或邮箱
                time: 60 * 1000, //60秒
                isOn: false, //是否显示倒计时
                loading:false,//按钮的loading
                showHomeList:false,//是否显示右侧的
                sourceCancel:false,
                show:false,//是否展示弹框
                activeStep : 1,
                titleList : [
                    {
                        title:'绑定支付宝',
                        id:1
                    },
                    {
                        title:'支付宝激活',
                        id:2
                    },
                    {
                        title:'获得消费码',
                        id:3
                    }
                ],
                status:1, //0是支付宝账户被其他账户绑定，需要输入手机验证码更换    1是其他错误信息
                msgContent:'支付宝账号不存在或未实名', //返回的信息提示
                creditCardId:'',//更换支付宝账户的时候需要回传给后端
                typeName:'邮箱'//重新绑定验证时,判断是邮箱校验还是手机号校验
            };
        },
        computed:{

        },
        mounted(){
            var bool = false;
            window.addEventListener('pageshow',function(){
                bool = false;
                setTimeout(function(){
                    bool = true;
                },1000)//延迟1秒
                pushHistory();
            });
            window.addEventListener(//监听浏览器回退
                    "popstate",
                    function(e) {
                        if (bool){
                            location.href ='${base}/index.jhtml';
                        }
                        pushHistory();
                    },
                    false
            );

            function pushHistory() {
                var state = {
                    title: "title",
                    url: "#"
                };
                window.history.pushState(state, state.title, state.url);
            }
          this.inintData();
          //在第二步时,轮询查询是否激活
          if (this.active!==undefined && this.active===1){
              this.timer = setInterval(()=>{
                  this.askActived();
              },5000);
          }
        },
        beforeDestory(){
            if(this.timer!==null){
                clearInterval(this.timer);
            }
        },
        methods: {
            //提示文字3s消失
            durationToast(msg){
                this.$toast({
                    duration: 3000,
                    message: msg,
                });
            },
            //去支付宝激活
            goZhifubao(){ //去支付宝激活
                let $that = this;
                $that.loading = true;
                this.check().then((res)=>{
                        if(res.type==="success"){
                            let sig = res.data.signUrl;//去支付宝签约加入地址
                            window.location.href = "${base}/alipayCode/alipayView.jhtml?signUrl="+sig;
                        }else {
                            $that.durationToast(res.content)
                        }
                    $that.loading = false;
                }).catch(err=>{
                    $that.loading = false;
                    $that.durationToast("操作失败！")
                })
            },
            //初始化数据
            inintData(){
                [#if creditCard??&&creditCard.bankAccountNo??]
                    this.active = 1;
                    this.account = '${creditCard.bankAccountNoDec!''}';
                    this.name = '${creditCard.bankAccountName!''}';
                [/#if]

            },
            //修改账户
            edit(){
                this.active = 0;
                this.oldAccount = this.account;
                this.oldName = this.name;

            },
            //倒计时结束
            countFinished() {
                this.isOn = false;
                this.$refs.countDown.reset();
            },
            //发送验证码
            handleCode() {
                let $that =this;
                if (this.tel == "") {
                    $that.durationToast("手机号码不能为空！");
                    return false;
                }else {
                    this.isOn=true;
                    this.$refs.countDown.start();
                    $.get(
                            "${base}" + "/alipayCode/sendPhoneCode.jhtml",
                            {mobile:$that.tel},
                            function(data){
                                //0-失败，1-成功，2-达到当天上限次数
                                let str = '发送验证失败，请联系管理员'
                                if(data == 0){
                                } else if(data == 1) {
                                    str = "验证码发送成功";
                                } else if(data == 2) {
                                    str = "验证码发送达到当天上限次数";
                                }
                                $that.durationToast(str);
                            });
                }
            },
            //关闭弹窗之前
            confirmOption(action, done){

                let that = this;
                //点击的是取消则直接关闭
                if(this.sourceCancel){
                    done();

                    return false
                }else {
                    if(this.code===""){
                        that.durationToast('验证码为空');
                        done(false);
                        return false
                    }
                    this.updateBind().then(function (res) {
                        //0//验证码超过15分钟，已失效 1 //验证码不正确 2 //手机号码不正确 3//修改失败 4   //修改成功
                        let status = res.data.flag;
                        if(status===4||status===0){
                            location.href= '${base}/alipayCode/index.jhtml';
                            done();
                        }else {
                            that.durationToast(res.content);
                            done(false);
                        }

                    }).catch(function (err) {
                        that.durationToast("操作失败！");
                        done(false);
                    })
                }
            },
            updateBind(){//解绑
                let $that = this;
                return new  Promise(function (resolve,reject) {
                    $.ajax({
                        type: "POST",
                        url: "checkPhoneCode.jhtml",
                        contentType: "application/x-www-form-urlencoded",
                        data:{
                            mobile:$that.tel,
                            code:$that.code,
                            creditCardId:$that.creditCardId
                        },
                        success: function(data){
                            resolve(data)
                        },
                        error: function(XMLHttpRequest, textStatus, errorThrown){
                            $that.durationToast("解绑失败！")
                        }
                    });
                })

            },
            //点击取消
            cancelOption(){
                this.sourceCancel=true;
                this.show=false;
            },
            hideHomeList(){
                this.showHomeList=false
            },
            showJumplist(){
                this.showHomeList=!this.showHomeList;
            },
            onClickLeft() {
                history.back();
            },
            check(){//验证账户是否绑定
                let $that = this;
                return new  Promise(function (resolve,reject) {
                    $.ajax({
                        type: "POST",
                        url: "checkAccount.jhtml",
                        data:{
                            account:$that.account,
                            name:$that.name
                        },
                        contentType: "application/x-www-form-urlencoded",
                        success: function(data){
                            resolve(data)
                        },
                        error: function(XMLHttpRequest, textStatus, errorThrown){
                            $that.durationToast("绑定失败！")
                        }
                    });
                })

            },
            askActived(){//轮询请求查询是否激活
                $.ajax({
                    type: "POST",
                    url: "askActived.jhtml",
                    contentType: "application/x-www-form-urlencoded",
                    success: function(data){
                        if(data!=null&&data){
                            location.href="index.jhtml";
                        }
                    },
                    error: function(err){
                        $that.durationToast("激活失败！")
                    }
                });
            },
            //绑定
            onSubmit(){
                let $that = this;
                if (this.account === "") {
                    $that.durationToast("账户不能为空");
                    return false;

                }
                if(this.name===""){
                    $that.durationToast("姓名不能为空");
                    return false;
                }
                if ($that.account===this.oldAccount&&$that.name===this.oldName){ //点击编辑 没有修改
                    $that.active = 1;
                    return false
                }
                this.loading = true;
                this.check().then((res)=>{
                    $that.loading = false;
                    let flag = res.data.flag;
                    $that.msgContent = res.content;
                        if(flag===0||flag===4){
                            // 0添加员工成功 1输入的账号或姓名为空 2参数不合法,校验返回 3账号非本人绑定 4已被本人绑定 5添加员工失败
                            $that.active = 1;
                            if( !$that.timer==null){

                            }else {
                                $that.timer = setInterval(()=>{
                                    $that.askActived();
                                },5000);
                            }
                        }else if(flag===3){ //非本人绑定
                            $that.show = true;
                            $that.code = "";
                            $that.tel = res.data.mobileOrEmail;
                            //邮箱校验
                            if ($that.tel.indexOf('@')>-1){
                                $that.typeName = "邮箱";
                            }else {
                                $that.typeName = "手机";
                            }
                            $that.creditCardId = res.data.creditCardId;
                            $that.showCancelbutton = true;
                            $that.confirmButtonText = '确定';
                            $that.sourceCancel = false;
                            $that.status = 0;
                        }else{  //其他错误情况
                            let bindResult = {
                                "PARAMETER_ERROR":"参数错误，请检查参数",
                                "SYSTEM_ERROR":"系统繁忙，请稍后重试",
                                "ENTERPRISE_NOT_EXIST":"企业不存在，绑定失败",
                                "USER_NOT_EXIST":"用户不存在，绑定失败<br/>建议填写您的支付宝邮箱地址账号",
                                "USER_UNREAL_NAME":"用户未实名，无法使用该功能，绑定失败",
                                "INVITE_MEMBER_FAIL":"邀请发起失败，绑定失败",
                                "ENTERPRISE_MEMBER_NUM_EXCEED_MAX":"企业人数已达上限，绑定失败",
                                "JOINED_ACCOUNT_NUM_EXCEED_MAX":"用户加入的企业已经超过上限，绑定失败",
                                "NO_AGREEMENT":"产品未签约或合约状态异常，绑定失败",
                                "EMPLOYEE_HAS_ACTIVATED":"员工已激活",
                                "ENTERPRISE_NOT_SIGNED":"企业未签约因公付，绑定失败",
                                "EMPLOYEE_NO_EXIST":"员工工号已存在",
                                "EMPLOYEE_ALIPAY_OCCUPIED":"支付宝账号已被其他员工绑定，绑定失败",
                                "EMPLOYEE_MOBILE_EXIST":"手机号与已有员工重复，绑定失败",
                                "EMPLOYEE_FREQUENCY_OVER_LIMIT":"初级认证企业添加员工操作频率超出上限，绑定失败",
                                "EMPLOYEE_IS_SUPER_ADMIN":"员工为超管不允许该操作，绑定失败",
                                "EMPLOYEE_OVER_LIMIT":"初级认证企业添加员工个数超出上限，绑定失败",
                                "NODE_NOT_EXIST":"部门不存在，绑定失败",
                                "NEED_TO_SET_PPW":"请设置支付密码，绑定失败",
                                "PRE_PAY_NOT_BIND":"该账号已被绑定"
                            }
                            
                            $that.sourceCancel = true; //点击知道了可直接关闭
                            if (bindResult[res.content]){
                                $that.msgContent=bindResult[res.content];
                            }
                            $that.showCancelbutton = false;
                            $that.confirmButtonText = '知道了';
                            $that.show = true;
                            $that.status = 1;
                        }
                }).catch((err)=>{
                    $that.loading = false;
                    $that.durationToast("绑定失败!");
                })
            }
        },
    });
</script>
</body>
</html>
