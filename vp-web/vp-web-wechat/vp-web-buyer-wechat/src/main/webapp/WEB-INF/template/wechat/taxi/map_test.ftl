<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<title>打车出行-POI搜索</title>
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">
		<script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/didiTravel.css" />
		<!-- <base href="//webapi.amap.com/ui/1.0/ui/misc/PoiPicker/examples/" /> -->
    	<link rel="stylesheet" href="http://cache.amap.com/lbs/static/main1119.css"/>
    	 <script type="text/javascript" src="http://cache.amap.com/lbs/static/addToolbar.js"></script>
    	<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
    <style>
    html,
    body {
        width: 100%;
        height: 100%;
        margin: 0px;
        padding: 0;
        font-size: 13px;
    }
    
    #outer-box {
        height: 100%;
        padding-right: 300px;
    }
    
    #container {
        height: 100%;
        width: 100%;
    }
    
    #panel {
        position: absolute;
        top: 0;
        bottom: 0;
        right: 0;
        height: 100%;
        overflow: auto;
        width: 300px;
        z-index: 999;
        border-left: 1px solid #eaeaea;
        background: #fff;
    }
    
    #searchBar {
        height: 30px;
        background: #ccc;
    }
    
    #searchInput {
        width: 100%;
        height: 30px;
        line-height: 30%;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        border: none;
        border-bottom: 1px solid #ccc;
        padding: 0 5px;
    }
    
    #searchResults {
        overflow: auto;
        height: calc(100% - 30px);
    }
    
    .amap_lib_placeSearch,
    .amap-ui-poi-picker-sugg-container {
        border: none!important;
    }
    
    .amap_lib_placeSearch .poibox.highlight {
        background-color: #CAE1FF;
    }
    
    .poi-more {
        display: none!important;
    }
    </style>
	
	</head>
	
	<body class="no_fixed_top">
    <div id="outer-box">
        <div id="container" class="map" tabindex="0"></div>
        <div id="panel" class="scrollbar1">
           	<div id="searchBar">
                <input id="searchInput" placeholder="输入关键字搜素起始地址" />
            </div>
            <div id="searchResults"></div>
        </div>
    </div>
    <script type="text/javascript" src='//webapi.amap.com/maps?v=1.4.2&key=e3b1ebe6fb1111c03c864dda26a876c2'></script>
    <!-- UI组件库 1.0 -->
    <script src="//webapi.amap.com/ui/1.0/main.js?v=1.0.11"></script>
    <script type="text/javascript">
    
    /*****定位******/
    
    /***************************************
	    由于Chrome、IOS10等已不再支持非安全域的浏览器定位请求，
	    为保证定位成功率和精度，请尽快升级您的站点到HTTPS。
    ***************************************/
        var map, geolocation;
    	var poiName;
        //加载地图，调用浏览器定位服务
        map = new AMap.Map('container', {
            resizeEnable: true,
            zoom: 10
        });
        map.plugin('AMap.Geolocation', function() {
            geolocation = new AMap.Geolocation({
                enableHighAccuracy: true,//是否使用高精度定位，默认:true
                timeout: 10000,          //超过10秒后停止定位，默认：无穷大
                buttonOffset: new AMap.Pixel(10, 20),//定位按钮与设置的停靠位置的偏移量，默认：Pixel(10, 20)
                zoomToAccuracy: true,      //定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：false
                buttonPosition:'RB'
            });
            map.addControl(geolocation);
            geolocation.getCurrentPosition();
            AMap.event.addListener(geolocation, 'complete', onComplete);//返回定位信息
            AMap.event.addListener(geolocation, 'error', onError);      //返回定位出错信息
        });
        //解析定位结果
        function onComplete(data) {
            // var str=['定位成功'];
          console.log(data);
          poiName = data.addressComponent.building; // addressComponent.building  addressComponent.businessAreas[0].name  addressComponent.formattedAddress
           // if(data.accuracy){
                // str.push('精度：' + data.accuracy + ' 米');
           // }//如为IP精确定位结果则没有精度信息
            //str.push('是否经过偏移：' + (data.isConverted ? '是' : '否'));
            //document.getElementById('tip').innerHTML = str.join('<br>');
        }
        //解析定位错误信息
        function onError(data) {
            //document.getElementById('tip').innerHTML = '定位失败';
        }
        /*****定位******/
        
        
        
   /*  var map = new AMap.Map('container', {
        zoom: 10
    }); */

    AMapUI.loadUI(['misc/PoiPicker'], function(PoiPicker) {

        var poiPicker = new PoiPicker({
            input: 'searchInput',
            placeSearchOptions: {
                map: map,
                pageSize: 10
            },
            searchResultsContainer: 'searchResults'
        });
        poiDataChange();
        poiPicker.on('poiPicked', function(poiResult) {

            poiPicker.hideSearchResults();

            var source = poiResult.source,
                poi = poiResult.item;

            if (source !== 'search') {

                //suggest来源的，同样调用搜索
                poiPicker.searchByKeyword(poi.name);
                
            } else {

                console.log(poi);
            }
        });

        poiPicker.onCityReady(function() {
            poiPicker.searchByKeyword(poiName);//'美食'
        });
    });
    
    function poiDataChange(){
    	$(".poibox").each(function(i){
    		$(this).find(".poi-img").remove();
    		$(this).find(".poi-info").remove();
    	});
    }
    
    
    </script>
</body>
	
</html>
