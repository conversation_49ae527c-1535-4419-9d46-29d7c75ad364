<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>确认订单</title>
		<meta name="apple-mobile-web-app-capable" content="yes" />
		<meta name="apple-mobile-web-app-status-bar-style" content="black" />
		<meta http-equiv="Expires" content="0" />
		<meta http-equiv="Cache-Control" content="no-cache" />
		<meta http-equiv="Pragma" content="no-cache" />
		<script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/plusWap.css" />
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/lifeService.css" />
		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

		<script type="text/javascript" src="${base}/resources/wechat/js/card.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/jquery.validate.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>

		<script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js"></script>

		<script type="text/javascript">
			var $orderRefreshForm;
			var $memo;
			var $memoMsg;

			$().ready(function() {
				$orderRefreshForm = $("#orderRefreshForm");
				$memo = $("#memo");
				$memoMsg = $("#memoMsg");
				var $submit = $("#submit");
				var $orderForm = $("#orderForm");
				var isOverseas = "${isOverseas}"; //海外商品标识
				var $receiverIdCard = $("#receiverIdCard");
				var $idcardNoOrder = $("#idcardNoOrder");
				var $saveIdcardNo = $("#saveIdcardNo");
				var $receiverId = $("#receiverId");
                var payType=$("#payType").val();
                var guarantee=$("#guarantee").val();
				function submitOrder() {
				     if (repeatFlag) {
                         return false;
                     }
                     repeatFlag = true;
					var url = "${base}/ctrip_hotel/submitOrder.jhtml";
					$.ajax({
						url: url,
						type: "POST",
						data: $orderForm.serialize(),
						dataType: "json",
						cache: false,
						beforeSend: function() {
							$submit.prop("disabled", true);
							repeatFlag = true;
							$submit.val("提交中，请稍候");
						},
						success: function(message) {
							if(message.type == "success") {
							    repeatFlag = true;
							    //	location.href = "${base}/member/order/payment.jhtml?sn=" + message.content + "&type=0";
								location.href = "${base}/paysRedirect/payment.jhtml?sn=" + message.content + "&type=0";
							}
							if(message.type == "warn") {
							    repeatFlag = false;
                                if (payType == 'PP') {
                                    $submit.val("确认支付");
                                } else {
                                    if (guarantee == 'true') {
                                        $submit.val("确认担保");
                                    } else {
                                        $submit.val("确认提交");
                                    }
                                }
								layer.open({
									content: message.content,
									skin: 'msg',
									time: 2 //2秒后自动关闭
								});
							} else if(message.type == "error") {
							    repeatFlag = false;
								if (payType == 'PP') {
                                    $submit.val("确认支付");
                                } else {
                                    if (guarantee == 'true') {
                                        $submit.val("确认担保");
                                    } else {
                                        $submit.val("确认提交");
                                    }
                                }
								var msg="支付失败";
								if(message.content!=null && message.content!=""){
								    msg=message.content;
								}
								layer.open({
									content: msg,
									skin: 'msg',
									time: 2 //2秒后自动关闭
								});

							}
						}
					});
				}

				//输入密码事件
				$("#password").keyup(function() {
					var l_pwd = this.value.length;
					if(l_pwd >= 1 && l_pwd <= 6 && event.keyCode != 8) {
						var _input = document.getElementById("number" + l_pwd);
						_input.value = this.value.charAt(l_pwd - 1);
						if(l_pwd == 6) { //输入完成动作
							var _pwd = this.value;
							$("#paymentPop").hide();
							this.blur();
							var device = openDevice();
							if(device == "android") {
								vpshop_android.callHideSoftInput();
							}

							var $checkedIds = $("#rechargeRecords input[name='coinIds']:enabled:checked");
							$.ajax({
								url: "${base}/ctrip_hotel/check_paypassword.jhtml",
								type: "POST",
								data: {
									sn: $("#orderSn").val(),
									coinIds: $("#coinIds").val(),
                                    whiteBarIds: $("#whiteBarIds").val(),
									paypassword: _pwd
								},
								dataType: "json",
								cache: false,
								success: function(message) {
									$(".password_section input").val("");
									if(message.type == "success") {
										submitOrder();
									} else {
										layer.open({
											content: message.content,
											skin: 'msg',
											time: 2 //2秒后自动关闭
										});
									}
								}
							});

						}
					} else if(event.keyCode == 8) { //退格键删除
						var _input = document.getElementById("number" + (l_pwd + 1));
						_input.value = '';
					}
				})





				var $payPasswordSetBtn = $("#payPasswordSetBtn");



				//保存身份证号
				$saveIdcardNo.click(function() {
					var receiverId = $receiverId.val();
					var idcardNo = $receiverIdCard.val();
					if(!idCardNoUtil.checkIdCardNo(idcardNo)) {
						layer.open({
							content: '请正确填写身份证号！',
							skin: 'msg',
							time: 2 //2秒后自动关闭
						});
						return false;
					}
					$.ajax({
						url: "saveReviceIdcard.jhtml",
						type: "POST",
						data: {
							receiverId: receiverId,
							idcardNo: idcardNo
						},
						dataType: "json",
						cache: false,
						success: function(msg) {
							if(msg == true) {
								layer.open({
									content: '身份证号码保存成功!',
									skin: 'msg',
									time: 2 //2秒后自动关闭
								});
							} else {
								layer.open({
									content: '身份证号码保存失败',
									skin: 'msg',
									time: 2 //2秒后自动关闭
								});
							}
						}
					});

				});

				// 订单提交
                //提交订单(防止重复提交)
			   var repeatFlag = false;
				$submit.click(function() {
				    var guarantee=$(this).attr("guarantee"); //担保方式
                    var payType=$(this).attr("payType");  //支付方式
                    
                    var rechargeRecordAmount = "${rechargeRecordAmount}"; //勾选了积分支付
                    var whiteBarYAmount = "${whiteBarYAmount}"; //勾选了白条支付

					if(rechargeRecordAmount > 0 && whiteBarYAmount > 0){
                           $("#isCreditCardGuarantee1").val(3);
                           $("#isCreditCardGuarantee").val(3);
					}
					if(rechargeRecordAmount > 0 && whiteBarYAmount <= 0){
                            $("#isCreditCardGuarantee1").val(0);
                            $("#isCreditCardGuarantee").val(0);
					}
					if(whiteBarYAmount > 0 && rechargeRecordAmount <= 0){
                            $("#isCreditCardGuarantee1").val(2);
                            $("#isCreditCardGuarantee").val(2);
					}
					
                    if (payType == "FG") {
                        if (guarantee == "false") { //到店付，非担保
                        	$("#isCreditCardGuarantee1").val("");
                            $("#isCreditCardGuarantee").val("");
                            submitOrder();
                        } else {
                            //现付订单
                            if ($("#isCreditCardGuarantee").val() == null || $("#isCreditCardGuarantee").val() == "") {
                                layer.open({
                                    content: "请先选择担保方式",
                                    skin: 'msg',
                                    time: 2 //2秒后自动关闭
                                });
                                return false;
                            }
                            $("#paymentPop").show().find("#password").focus();
                        }
                    }else{

						[#if member.companyId.isFreePay ]
							submitOrder();
						[#else]
						var cashPayFlag="${member.companyId.cashPayFlag}";
					    
					    if(cashPayFlag!=null && cashPayFlag!="" && cashPayFlag==0){ //不允许使用现金支付
							   if($("#_amountPayable").val() > 0) {
					             layer.open({
					                 content: '积分余额不足！',
					                 skin: 'msg',
					                 time: 2 //2秒后自动关闭
					             });
					             return false;
					         }
						   }
                        if (rechargeRecordAmount > 0) { //可用积分大于0，弹出密码框
                            $("#paymentPop").show().find("#password").focus();
                        } else if (whiteBarYAmount > 0) {
                            $("#paymentPop").show().find("#password").focus();
                        } else {
                            submitOrder();
                        }
						[/#if]
					}

				});
			});



			//选择积分页面跳转
			function selectCoin() {
                var url = "groupSelectCoin.jhtml";
				$("#orderRefreshForm").attr("action", url);
				$("#orderRefreshForm").submit();
			}

			//选择积分页面跳转
			function selectWhiteBar() {
                var url = "groupSelectWhiteBar.jhtml";
				$("#orderRefreshForm").attr("action", url);
				$("#orderRefreshForm").submit();
			}

			function selectCreditCard(){
                var sn=$("#orderSn").val();
                location.href="${base}/ctrip_hotel/creditCardList.jhtml?sn="+sn;
			}
			
			// 验证手机号
			jQuery.validator.addMethod("isTel", function(value,element) {   
			    var length = value.length;   
			    var mobile = /^(((13[0-9]{1})|14[57]{1}|(15[0-9]{1})|17[378]{1}|(18[0-9]{1}))+\d{8})$/;
			    if(this.optional(element) || (length==11 && mobile.test(value))){
			    	$("#midMobile").val(value);
			    }	    
			    return this.optional(element) || (length==11 && mobile.test(value));   
			   }, "请正确填写您的联系方式"); 
			
			$("#orderForm").validate({  
			    rules : {  
			        mobile : {  
			        	isTel:"#mobile",
			            required : true,  
			            minlength : 11 
						
			        }
			    },  
			    messages : {  
			    	mobile : {  
			            required : "请输入手机号",  
			            minlength : "确认手机不能小于11个字符",  
			            isMobile : "请正确填写您的手机号码"  
			        }
			    },  
				errorPlacement: function(error, element) {
					error.appendTo(element.parent());
				}				
			}); 
			
		</script>
	</head>

	<body id="myOrderPage">
		<div class="myOrderPage hotel_order_page" style="margin-top:0;">
			<div class="public_top_header bg_theme">
				<a href="javascript:history.back();" class="return_back"></a>
				确认订单
				[#include "./wechat/include/head_nav.ftl" /]
			</div>

			<div class="page_header_bg hotel">
                <header>
                    <div class="base">
                        <div class="img">
                            [#if hotelInfoVo.url!=null]
                                <img src="${hotelInfoVo.url}"/>
                            [#else]
                                <img src="${base}/resources/wechat/img/hotel/hotel_default.jpg"/>
                            [/#if]
                        </div>
                        <div class="info">
                            <p class="title">${hotelInfoVo.hotelName}</p>
                            <p class="time">${hotelInfoVo.arrivalDate}至${hotelInfoVo.departureDate}<span>共${hotelInfoVo.diffDays}晚</span></p>
                            <p class="bed_type">${hotelInfoVo.roomTypeName}</p>
                            <div class="attr">
                                <span>
                                    ${hotelInfoVo.bedType}
                                </span>

                                [#if hotelInfoVo.breakfast!=null]
                                    <span>${hotelInfoVo.breakfast}</span>
                                [/#if]

								 [#if hotelInfoVo.floor!=null]
                                    <span>${hotelInfoVo.floor}楼</span>
                                [/#if]
                            </div>
                        </div>
                    </div>
                    <div class="tips_section">
                        <span class="icon_hotel"></span>${hotelInfoVo.cancelRuleDescription}
                    </div>
                </header>
			</div>
			
			<section class="favourable_section">
				<div class="lines_Section_all">
					<a href="javascript:void(0)">
						房客姓名
						<div class="right_text">
							<span>${hotelInfoVo.contactName}</span>
							
						</div>
					</a>
				</div>
				<div class="lines_Section_all">
					<a href="javascript:void(0)">
						联系电话
						<div class="right_text">
							${hotelInfoVo.contactMobile}
						</div>
					</a>
				</div>
			</section>

			[#if hotelInfoVo.payType=="FG" &&  hotelInfoVo.guarantee!=null && hotelInfoVo.guarantee=='true']
            <section class="favourable_section">
                <div class="header">
                    担保金额
                    <span class="num_span"><span>${currency(order.toatlAmount, true, false)}</span></span>
                    <span class="right_text_gray">担保说明</span>
                    <p>${hotelInfoVo.cancelRuleDescription}，若未入住或过时取消担保费用将不予退还。平台会根据您的担保方式，暂扣RMB${currency(order.toatlAmount, false, false)}或积分用于担保。订单需等酒店或供应商确认后生效，订单确认结果以平台短信通知为准。<p>
                </div>

				<div class="lines_Section_all">
					<a href="javascript:void(0)"  onclick="selectCreditCard()" class="fli_link_line">
						    信用卡担保<span class="text_gray">[#if creditCard!=null]${creditCard.bankName} ****${creditCard.bankAccountNoDec?substring((creditCard.bankAccountNoDec)?length-4)}[/#if]</span>
					</a>
				</div>
			</section>
            [/#if]

 			<form id="orderRefreshForm" action="" method="post">
			[#list cartItems as cartItem]
                <input type="hidden" name="ids" value="${cartItem.id}"/> [/#list]
                <input type="hidden" name="coinIds" value="${coinIds}" id="coinIds"/>
                <input type="hidden" name="whiteBarIds" value="${whiteBarIds}" id="whiteBarIds"/>
                <input type="hidden" id="receiverId" name="receiverId" [#if receiver??] value="${receiver.id}" [/#if]/>
                <input type="hidden" id="productId" name="productId" value="${productId}"/>
                <input type="hidden" name="couponCodeId" value="${couponCodeId}"/>
                <input type="hidden" name="pId" value="${productId}"/>
                <input type="hidden" name="quantity" value="${quantity}"/>
                <input type="hidden" name="toatlAmount" value="[#if order??]${order.toatlAmount}[#else]000[/#if]"/>
                <input type="hidden" name="coinAmount" value="${order.coinAmount}"/>
                <input type="hidden" name="amountPaid" value="${order.amountPaid}"/>
                <input type="hidden" name="sku" value="ctrip_hotel_ticket"/>
			[#--	<input type="hidden" id="" name="type" value="${type}" />--]
                <input type="hidden" name="arrivalDate" id="arrivalDate" value="${hotelInfoVo.arrivalDate}"/>
                <input type="hidden" name="departureDate" id="departureDate" value="${hotelInfoVo.departureDate}"/>
                <input type="hidden" name="hotelId" value="${hotelInfoVo.hotelId}"/>
                <input type="hidden" name="hotelName" value="${hotelInfoVo.hotelName}"/>
                <input type="hidden" name="contactName" value="${hotelInfoVo.contactName}"/>
                <input type="hidden" name="contactMobile" value="${hotelInfoVo.contactMobile}"/>
                <input type="hidden" name="roomNums" value="${hotelInfoVo.roomNums}"/>
                <input type="hidden" name="roomTypeId" value="${hotelInfoVo.roomTypeId}"/>
                <input type="hidden" name="roomID" value="${hotelInfoVo.roomID}"/>
                <input type="hidden" name="bedType" value="${hotelInfoVo.bedType}"/>
                <input type="hidden" name="totalCost" id="totalCost" value="${hotelInfoVo.totalCost}"/>
                <input type="hidden" id="orderAmountHi" name="totalPrice" value="${hotelInfoVo.totalPrice}"/><!--订单总额-->
                <input type ="hidden" id="guestCount" name="guestCount" value="${hotelInfoVo.roomNums}"/><!--总人数-->
                <input type="hidden" id="freightAmountHi" value="0"/>
                <input type="hidden" id="actSaveAmount" value="0"/>
                <input type="hidden" id="paymentMethodId" name="paymentMethodId" maxlength="200" value="1"/>
                <!--支付方式-->
                <input type="hidden" id="shippingMethodId" name="shippingMethodId" maxlength="200" value="1"/>
                <!--支付方式-->
                <input type="hidden" name="customNames" value="${hotelInfoVo.customNames}"/>
                <input type="hidden" name="coinBalance" value="${coinBalance}"/>
				 <input type="hidden" name="hotelAddress"  value="${hotelInfoVo.hotelAddress}"/>
                <input type="hidden" name="roomTypeName"  value="${hotelInfoVo.roomTypeName}"/>
				<input type="hidden" name="ratePlanCategory"   value="${hotelInfoVo.ratePlanCategory}"/>  <!-- 标准预付房型-->
			    <input type="hidden" name="fromCityName"  value="${hotelInfoVo.fromCityName}"/>
				<input type="hidden" name="coinTypeIdsEcho" value="${coinTypeIdsEcho}" />
				<input type="hidden" name="coinTypeIds" value="${coinTypeIds}" />
				<!-- 下面参数为了计算金额 -->
				<input type="hidden" id="price"  name="price" value="${order.price}"/>
				<input type="hidden" id="freight"  name="freight" value="${order.freight}"/>
				<input type="hidden" id="couponDiscount"  name="couponDiscount" value="${order.couponDiscount}"/>
				<input type="hidden" id="promotionDiscount"  name="promotionDiscount" value="${order.promotionDiscount}"/>
				<input type="hidden" name="thirdCoinAmount" value="${thirdCoinAmount}" />
			[#if tripSnapshot?? ]
                <input type="hidden" name="applyId" value="${tripSnapshot.applyId}"/>
                <input type="hidden" name="tripType" value="${tripSnapshot.tripType!"hotel"}"/>
				<input type="hidden" name="serviceInfo" value="${tripSnapshot.serviceInfo}"/>
				<input type="hidden" name="tripReasonCode" value="${tripSnapshot.tripReasonCode}"/>
				<input type="hidden" name="tripRuleBroken" id="tripRuleBroken" value="${tripSnapshot.tripRuleBroken}"/>
				<input type="hidden" name="tripReason" value="${tripSnapshot.tripReason}"/>
				<input type="hidden" name="tripMemo" value="${tripSnapshot.tripMemo}"/>
				<input type="hidden" name="tripCityName" id="tripCityName" value="${tripSnapshot.tripCityName}"/>
				<input type="hidden" name="isBreak" id="isBreak" value="${tripSnapshot.isBreak}"/>
			[/#if]
				<input type="hidden" name="url" value="${hotelInfoVo.url}"/>
				<input type="hidden" name="lateArrivalTime"  value="${hotelInfoVo.lateArrivalTime}" />


				<input type="hidden" name="breakfast" value="${hotelInfoVo.breakfast}"/>
				<input type="hidden" name="floor" value="${hotelInfoVo.floor}"/>
				<input type="hidden" name="cancelRuleDescription" value="${hotelInfoVo.cancelRuleDescription}"/>
				<input type="hidden" name="guarantee"  value="${hotelInfoVo.guarantee}" id="guarantee"/>
				<input type="hidden" name="payType"   value="${hotelInfoVo.payType}" id="payType"/>  <!-- 标准预付房型-->
				<input type="hidden" name="sn"   value="${order.sn}" id="orderSn"/>  <!-- 标准预付房型-->
				<input type="hidden" name="isCreditCardGuarantee" value="${isCreditCardGuarantee}" id="isCreditCardGuarantee">
				<input type="hidden" id="agentMemberId" name="agentMemberId" value="${agentMemberId}">

			</form>

				
 			<form id="orderForm" action="${base}/ctrip_hotel/submitOrder.jhtml" method="post">
				<input type="hidden" name="sn" value="${order.sn}">
				 <input type="hidden" name="coinIds" value="${coinIds}" id="coinIds"/>
				<input type="hidden" name="whiteBarIds" value="${whiteBarIds}" id="whiteBarIds"/>
				<input type="hidden" name="isCreditCardGuarantee" value="${isCreditCardGuarantee}" id="isCreditCardGuarantee1">
				<input type="hidden" name="thirdCoinAmount" value="${thirdCoinAmount}" />
			</form>
 		
 						
			
            [#if hotelInfoVo.payType=='PP'  ||  (hotelInfoVo.payType=='FG' &&  (hotelInfoVo.guarantee!=null && hotelInfoVo.guarantee=='true') && (isContainCompanyCreditCard!=null && isContainCompanyCreditCard==true))] <!-- 到店付，担保-->
			<section class="favourable_section">
				<div class="lines_Section_all">
					<a href="javascript:void(0)" onclick="selectCoin()" class="fli_link_line">
						[#if hotelInfoVo.payType=='PP']
						     积分支付
						 [#else]
                             积分担保
						[/#if]

						<div class="like_btn_box">
							[#list coins as coin] [#if coin.coinTypeId.isCredit]
							<span class="like_btn">	${coin.coinTypeId.name?substring(0,1)}${coinConvert(coin.balance, coinShowRate!1)}[#if isUnifiedDeduct](企)[/#if]</span> [/#if] [/#list]
							[#if thirdCoin??]
							<span class="like_btn">${thirdCoin.coinName?substring(0,1)}${thirdCoin.amount}</span>
							[/#if]
						</div>
						[#if order.coinAmount > 0]
						<div class="right_text red">-${coinConvert(order.coinAmount, coinShowRate!1)}</div>
						[#else]
						<div class="right_text gray">未使用</div>
						[/#if]
					</a>
				</div>

				<div class="lines_Section_all" [#if (member.companyId?? && member.companyId.hasWhitebar?? && member.companyId.hasWhitebar)] [#else]style="display:none;" [/#if]>
					<a href="javascript:void(0)" onclick="selectWhiteBar()" class="fli_link_line">

						[#if hotelInfoVo.payType=='PP']
						     白条支付
						 [#else]
                             白条担保
						[/#if]
						[#list whiteBarCoins as coin] [#if coin.coinTypeId.isCredit && coin.enabledFlag && coin.isFreeze]
						<span class="like_btn">	${coin.coinTypeId.name?substring(0,1)}${coinConvert(coin.balance, coinShowRate!1)}[#if isUnifiedDeduct](企)[/#if]</span> [/#if] [/#list] [#if order.whiteBarAmount gte 0]
						<div class="right_text red">-${coinConvert(order.whiteBarAmount, coinShowRate!1)}</div>
						[#else]
						<div class="right_text gray">未使用</div>
						[/#if]
					</a>
				</div>
			</section>
			[/#if]

			<section class="amount_section">
                [#assign  serviceCharge=order.orderItems[0].getVirtualProductOrderInfoMapValue('serviceCharge')]
				[#if hotelInfoVo.payType=='PP']
                    <p>
					     订单总额
					    <span>[#if productShowRate??]${coinConvert(order.toatlAmount, productShowRate, true, false)}[#else]${currency(order.toatlAmount, true, false)}[/#if]</span>
				    </p>
					[#if serviceCharge!=null && serviceCharge!=""]
                        <p>
                            服务费
                            <span>[#if productShowRate??]${coinConvert(serviceCharge, productShowRate, true, false)}[#else]${currency(serviceCharge, true, false)}[/#if]</span>
                        </p>
					[/#if]
				    <p>
					    积分抵扣
					    <span>-[#if productShowRate??]${coinConvert(order.coinAmount, productShowRate, true, false)}[#else]${currency(order.coinAmount, true, false)}[/#if]</span>
				    </p>
				    <p [#if (member.companyId?? && member.companyId.hasWhitebar?? && member.companyId.hasWhitebar)] [#else]style="display:none;" [/#if]>
					    白条抵扣
					   <span>-[#if productShowRate??]${coinConvert(order.whiteBarAmount!0, productShowRate, true, false)}[#else]${currency(order.whiteBarAmount, true, false)}[/#if]</span>
				    </p>
				    <p [#if member.companyId?? && member.companyId.cashPayFlag?? && member.companyId.cashPayFlag == 0] style="display: none;" [/#if]>
						实付款
                        <span>${currency(order.amountPayable, true, false)}</span>
					    <input type="hidden" id="_amountPayable" value="${order.amountPayable}">
				    </p>
				    [#if discountAmount??]
				    <p>
					    下单立减
					  <span>-${currency(discountAmount, true, false)}</span>
				    </p>
					[/#if]
                [#else]
                    [#if hotelInfoVo.guarantee=='false']
					      <p>
					       订单总额
					       <span>${currency(order.toatlAmount, true, false)}</span>
				       </p>
					    <p>
					       到店付款
					       <span>${currency(order.toatlAmount, true, false)}</span>
				       </p>
				       [#if discountAmount??]
                       <p>
					       离店返积分
					       <span>${currency(discountAmount, true, false)}</span>
				       </p>
				       [/#if]
					[#if serviceCharge!=null && serviceCharge!=""]
                        <p>
                            服务费
                            <span>-${currency(serviceCharge, true, false)}</span>
                        </p>
					[/#if]
					[#else]
					   <p>
					       订单总额
					       <span>${currency(order.toatlAmount, true, false)}</span>
				       </p>
					   <p>
					       到店付款
					       <span>${currency(order.toatlAmount, true, false)}</span>
				       </p>
                       <p>
					       积分担保
					       <span>-${currency(order.coinAmount, true, false)}</span>
				       </p>
					    <p [#if (member.companyId?? && member.companyId.hasWhitebar?? && member.companyId.hasWhitebar)] [#else]style="display:none;" [/#if]>
					     白条担保
					   <span>-${currency(order.whiteBarAmount, true, false)}</span>
				       </p>
				       [#if discountAmount??]
					   <p>
					       离店返积分
					       <span>${currency(discountAmount, true, false)}</span>
				       </p>
						[/#if]
					[#if serviceCharge!=null && serviceCharge!=""]
                        <p>
                            服务费
                            <span>-${currency(serviceCharge, true, false)}</span>
                        </p>
					[/#if]


					[/#if]
				[/#if]
			</section>



			<div class="bottom">

				 <input type="button" name="" id="submit"
						[#if hotelInfoVo.payType=='PP']
						    value="确认支付"
                        [#else]
                            [#if hotelInfoVo.guarantee=='true']
                                 value="确认担保"
						     [#else]
						         value="确认提交"
                            [/#if]
						[/#if]

						class="btn_submit_long" guarantee="${hotelInfoVo.guarantee}"  payType="${hotelInfoVo.payType}"/>
			</div>
		</div>

		[#include "./wechat/include/set_payPasswords.ftl" /]

        <script>
        	[#include "./wechat/include/categoryCheck.ftl" /]
            $(function(){
                $(".header .right_text_gray").on("click",function(){
                    $(this).next("p").toggle()
                })

            })
        
        </script>
	</body>

</html>