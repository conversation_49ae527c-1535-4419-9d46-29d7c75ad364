<!DOCTYPE html>
<html>
	<head>
		<title>选择日期</title>
		<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">
		<link rel="stylesheet" href="${base}/resources/wechat/css/jquery-weui.css">
		<!--http://jqweui.com/extends#calendar-->
	</head>
	<body style="padding: 0;margin: 0;">
		<input id="date3" type="hidden">
		<div id="inline-calendar"></div>
		<script src="${base}/resources/wechat/plugins/jquery/jquery-2.1.4.js"></script>
		<script src="${base}/resources/wechat/js/jquery-weui.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>

		<script>
		Date.prototype.Format = function(fmt){ //author: meizz   
	  		var o = {   
	    		"M+" : this.getMonth()+1,                 //月份   
	    		"d+" : this.getDate(),                    //日   
	    		"h+" : this.getHours(),                   //小时   
	    		"m+" : this.getMinutes(),                 //分   
	    		"s+" : this.getSeconds(),                 //秒   
	    		"q+" : Math.floor((this.getMonth()+3)/3), //季度   
	    		"S"  : this.getMilliseconds()             //毫秒   
	  		};  
	  		
	  		if(/(y+)/.test(fmt)){
	  			fmt=fmt.replace(RegExp.$1, (this.getFullYear()+"").substr(4 - RegExp.$1.length));
	  		}
	       
	  		for(var k in o){
	  			if(new RegExp("("+ k +")").test(fmt)){
	  				fmt = fmt.replace(RegExp.$1, (RegExp.$1.length==1) ? (o[k]) : (("00"+ o[k]).substr((""+ o[k]).length)));
	  			}
	  		}
	  		return fmt;   
		}
		var d=new Date(); 
	    d.setDate(d.getDate()+14);
		var start=new Date(); 
		start.setDate(start.getDate()-1); 
		start=start.Format("yyyy-MM-dd");
		$("#inline-calendar").calendar({
			value: ["${date!.now}"],
			container: "#inline-calendar",
			input:"#date3",
			minDate:start,
			maxDate:d.Format("yyyy-MM-dd")
		});
		
		$('#date3').bind('change', function() {
			var d=new Date($(this).val());
			
			[#if type??&&type=="query"]
				var url ="ticketQuery.jhtml?to=${to}&from=${from}&orderNo=${orderNo}&passengerId=${passengerId}&sn=${sn}&date="+d.Format("yyyy-MM-dd");
			[#else]
				var url ="index.jhtml?to=${to}&from=${from}&orderNo=${orderNo}&passengerId=${passengerId}&sn=${sn}&date="+d.Format("yyyy-MM-dd");
			[/#if]
			[#if applyId?? ]
				url += "&applyId=${applyId}";
			[/#if]
			[#if agentMemberId?? ]
				url += "&agentMemberId=${agentMemberId}";
			[/#if]
			[#if isPublic??]
				url += "&isPublic=${isPublic}";
			[/#if]
			location.href = url;
		});
		</script>
	</body>
</html>