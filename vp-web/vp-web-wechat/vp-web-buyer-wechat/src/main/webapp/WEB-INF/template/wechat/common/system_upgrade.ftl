<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0,minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="black">
		<title>福利平台正在升级中...</title>
		<style>
			* {
			  padding: 0;
			  margin: 0;
			}
			html,
			body {
			  position: relative;
			  height: 100%;
			  background: #f5f5f5;
			}
			.upgradeSection {
			  position: relative;
			  width: 45rem;
			  width: 900px\9;
			  margin: 0 auto;
			  padding-top: 5rem;
			}
			.upgradeSection .img {
			  text-align: center;
			}
			.upgradeSection .img img {
			  display: inline-block;
			  width: 100%;
			  max-width: 26.9rem;
			  max-width: 538px\9;
			  max-height: 536px\9;
			  height: auto;
			}
			.upgradeSection .text {
			  color: #444;
			  font-size: 1.2rem;
			  font-family: "微软雅黑";
			  line-height: 1.7rem;
			}
			.upgradeSection .text p {
			  text-align: center;
			  margin-left: -0.7rem;
			}
			@media screen and (min-width: 1366px) {
			  html {
			    font-size: 20px;
			  }
			}
			@media screen and (min-width: 900px) and (max-width: 1366px) {
			  html {
			    font-size: 14px;
			  }
			  .upgradeSection .text p {
			    margin-left: 0;
			  }
			}
			@media screen and (max-width: 900px) {
			  html {
			    font-size: 12px;
			  }
			  .upgradeSection {
			    width: 100%;
			    padding: 0;
			    position: absolute;
			    top: 50%;
			    left: 0;
			    -ms-transform: translate(0, -50%);
			    -moz-transform: translate(0, -50%);
			    -o-transform: translate(0, -50%);
			    -webkit-transform: translate(0, -50%);
			    transform: translate(0, -50%);
			  }
			  .upgradeSection .img img {
			    width: 50%;
			  }
			  .upgradeSection .text {
			    width: 80%;
			    margin-top: 1rem;
			    font-size: 1rem;
			    line-height: 1.7rem;
			    margin-left: auto;
			    margin-right: auto;
			  }
			  .upgradeSection .text p {
			    text-align: left;
			    margin-left: 0;
			    text-indent: 2em;
			  }
			}
		</style>
	</head>
	<body>
		<div class="upgradeSection">
			<div class="img">
				<img src="http://www.fliplus.com/errorpng/sys_upgrade.png"/>
			</div>
			<div class="text">
                hi 兄弟，<p>很高兴在积分商城遇见您！但非常抱歉，因此刻燕兄正在进行二维码系统升级，9月1日0:00~8:00积分商城将暂时停止服务，欢迎您8点以后再过来选购哦！</p>
                <p>燕兄听说今天商城上了不少优质折扣商品，走过路过千万别错过了哟~</p>
			</div>
		</div>
	</body>
</html>
