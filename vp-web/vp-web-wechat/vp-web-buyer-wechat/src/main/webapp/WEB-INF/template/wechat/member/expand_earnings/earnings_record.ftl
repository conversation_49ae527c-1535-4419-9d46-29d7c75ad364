<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="viewport"
        content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">
    <title>奖励积分-收益明细</title>
    <script src="${base}/resources/wechat/plugins/flexible/flexible.js"></script>
    <link rel="stylesheet" href="${base}/resources/wechat/css/common.css">
    <link rel="stylesheet" href="${base}/resources/wechat/css/cIndexStyle.css">
    <link rel="stylesheet" href="${base}/resources/wechat/css/vant/index.css">
</head>

<body class="coinRecordPage has_fixed_footer">
    <div id="coinRecordPage">
        <!-- 顶部固定区域  开始 -->
        <van-nav-bar fixed title="收益明细" left-arrow @click-left="javascript:location.href='${base}/member/expandEarnings/index.jhtml';">
        </van-nav-bar>
        <!-- 顶部固定区域  结束 -->

        <div class="selectButton" @click="showPicker = true;temp = currentDate" v-cloak>
            {{time}}
            <van-icon name="arrow-down" v-if="!showPicker"></van-icon>
            <van-icon name="arrow-up" v-else></van-icon>
        </div>
        <template v-if="totalPages&&totalPages>0">
		<van-list v-model="loading" :finished="finished" finished-text="没有更多了"  @load="onLoad">
		        <van-cell v-for="(item,index) in list" :key="'item'+index" :class="{coin:item.type === 0,trans:item.type === 1}">
		            <div slot="title">
		                <p v-if="item.type == 1">月结奖励</p>
		                <p v-else>转换积分</p>
		            </div>
		            <div class="right">
		                <p>
		                    <span v-cloak>{{item.amount}} </span>
		                </p>
		                <p class="suc">结余</p>
		            </div>
		            <div class="label" slot="label">
		                <template v-if="item.type">
		                    <p class="add" v-cloak>+{{item.changAmount}}</p>
		                </template>
		                <template v-else>
		                    <p class="reduce" v-cloak>-{{item.changAmount}}</p>
		                </template>
		                <p v-cloak>{{item.time}}</p>
		            </div>
		        </van-cell>
		     
		</van-list>
		</template>
		<template v-else>
            <div class="public_empty_section_list">
                <p>暂无推广收益</p>
				<a href="${base}/index.jhtml" class="pub_link_theme">去推广</a>
			</div>
	    </template>
        <van-popup v-model="showPicker" position="bottom">
            <van-datetime-picker v-model="currentDate" type="year-month" :min-date="minDate" :max-date="maxDate"
                :formatter="formatter" @cancel="handleTimeCancel" @confirm="handleTimeConfirm"></van-datetime-picker>
        </van-popup>


    </div>

    <script src="${base}/resources/wechat/plugins/vue/vue.min.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

    <script src="${base}/resources/wechat/js/common.js"></script>
    <script src="${base}/resources/wechat/js/vant.min.js"></script>
    <script type="text/javascript">
    	var base="${base}"
    	[#if date?? ]
    	var date=new Date("${date?string('yyyy/MM/dd')!}")
    	[#else]
    	var date=new Date()
    	[/#if]
    </script>
    <script src="${base}/resources/wechat/es6/coinRecordsList.js"></script>
</body>

</html>