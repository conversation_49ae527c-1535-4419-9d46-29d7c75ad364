<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no"
    />
    <title>加油</title>
    <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
  <link rel="stylesheet" href="${base}/resources/wechat/css/vant/index.css">
    <link type="text/css" rel="stylesheet" href="${base}/resources/wechat/css/common.css">
    <link type="text/css" rel="stylesheet" href="${base}/resources/wechat/css/gasStation.css">
 <script src="${base}/resources/wechat/plugins/vue/vue.min.js"></script>

    <script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
    <script src="${base}/resources/wechat/js/vant.min.js"></script>
      <script type="text/javascript">
          var hostname=window.location.protocol+"//"+document.location.hostname;
          window._AMapSecurityConfig = {
              serviceHost:hostname+'/_AMapService'
          }
      </script>
     <script type="text/javascript" src='https://webapi.amap.com/maps?v=1.4.2&key=af778b7f852561b0c1028ba36eaf6c68&plugin=AMap.Driving,AMap.Geocoder'></script>
      <script src="https://webapi.amap.com/ui/1.0/main.js?v=1.0.11"></script>

  </head>
  <body class="gasStationPage wechatConsumePage">
    <div id="app" @click.stop="hideHomeList">
        <van-sticky>
            <div>
                <div class="headerTop">
                    <van-nav-bar
                            title="加油"
                            left-text=""
                            left-arrow
                            @click-left.stop="onClickLeft"
                            @click-right.stop="showJumplist"
                    >
                        <template #right>
                                <van-icon name="ellipsis" />
                                <ul class="showHomeList" v-if="showHomeList">
                                    <li>
                                        <a href="${base}/index.jhtml">
                                            <span class="icon_home"></span>
                                            <p>首页</p>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="${base}/brightoilonline/index.jhtml">
                                            <span class="wechaticon_home"></span>
                                            <p>消费卡首页</p>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="[#if my_home_link?? && my_home_link != ""]${my_home_link}[#else]${base}/member/index.jhtml[/#if]">
                                            <span class="icon_user"></span>
                                            <p>个人中心</p>
                                        </a>
                                    </li>
                                </ul>

                        </template>
                    </van-nav-bar>
                    <div class="searchBox flexbox justify-content-space-between ">

                        <div @click="Jump('/brightoilonline/selectAddress')" class="flexbox addressName align-items-c ">
                            <van-icon name="location-o"  color="#ffffff" size="0.4rem"></van-icon>
                            <p class="flex1 detailedAddress"  v-cloak>{{address}}</p>
                        </div>

                        <div class="search_section ">
                            <van-field
                                    v-model="searchValue"
                                    right-icon="search"
                                    placeholder="请输入油站名称"
                                    @click-right-icon.stop="search"
                            >
                            </van-field>
                        </div>

                    </div>
                </div>
                <div class="screen">
                    <!--<div class="selectType1">油号：</div>-->
                    <!--<div class="selectType2">品牌：</div>-->
                    <van-dropdown-menu :close-on-click-overlay="false">
                        <div class="droptitle-box">
                            <van-dropdown-item  ref="oilItem" :title="oilType" >
                                <ul class="flexbox oilBox">
                                    <li v-cloak v-for="(item,index) in oilTypeList" @click.stop.prevent="selectedOil(item)" :class="item.active" v-if="item!=null" :key="'oilType_list_'+index">
                                        {{item.text}}
                                    </li>
                                </ul>
                            </van-dropdown-item>
                        </div>
                        <!--
                        <div class="droptitle-box">
                            <van-dropdown-item ref="gasItem" :title="gasStationType"  >
                                <ul class="flexbox  oilBox">
                                    <li :class="item.active" @click.stop.prevent="selectedGas(item)" v-for="(item,index) in gasStationTypeList" v-if="item!=null" :key="'gasStationType_list_'+index">
                                        {{item.text}}
                                    </li>
                                    <div class="determineBox">
                                        <van-button class="determineBtn" @click.stop.prevent="comfirmGas" type="info">确定</van-button>
                                    </div>
                                </ul>
                            </van-dropdown-item>
                        </div>
                        -->
                    </van-dropdown-menu>
                </div>
            </div>
        </van-sticky>
        <div class="paddingB">
          <!--筛选-->
          <ul class="gasStationList" v-if="(gasSationList.length>0)">
              <li  v-for="(item,index) in gasSationList" v-if="item!=null" :key="'gasSation_list_'+index" @click.stop="jumpToInfo(item)">
                  <span class="tag_recommond" v-if="item.type==1">特惠</span>
                  <van-row class="contenTop" type="flex" justify="space-between" align="center">
                      <van-col span="18">
                          <div class="gasInfo">
                              <span class="gasName" v-cloak>
                                  {{item.stationName}}
                              </span>
                              <!--<span class="gasType">
                                    {{item.gasType}}
                              </span>-->
                          </div>
                      </van-col>
                      <van-col span="6">
                          <p class="distance" v-cloak>
                              {{item.distance}}km
                          </p>
                      </van-col>
                  </van-row>
                  <div class="address" v-cloak>
                      {{item.addr}}
                  </div>
                  <div class="flexbox price">
                      <p >
                          ￥
                          <span class="priceOne" v-cloak>
                            {{item.discountPrice}}
                            <span v-if="item.discountPrice == item.nationalPrice">(仅供参考，具体以油站优惠为准)</span>
                          </span>
                      </p>
                      <p v-if="item.discountPrice != item.nationalPrice">
                          ￥<span class="priceTwo" v-cloak>{{item.nationalPrice}}</span>
                      </p>
                  </div>
              </li>
          </ul>
          <van-empty v-else :description="descriptionText" >
          </van-empty>
         </div>
    </div>
  </body>
</html>
<script>
    let vm = new Vue({
        el: "#app",
        data() {
            return {
                searchValue:'',//搜索内容
                oilType:'95#',
                gasStationType:'全部',
                descriptionText:"正在加载中...",//是否在请求数据
                address: "${address!''}",//位置：省市区街道
                oilTypeList:[
                    {
                        text: '92#', value: 0,
                        active:''
                    },
                    {
                        text: '95#', value: 1,
                        active:'active'
                    },
                    {
                        text: '98#', value: 2,
                        active:''
                    },
                    {
                        text: '0#', value: 3,
                        active:''
                    },
                ],
                gasStationTypeList:[
                    {
                        text: '全部', value: 0,
                        active:'active'
                    },
                    {
                        text: '中国石化', value: 1,
                        active:'active'
                    },
                    {
                        text: '中国石油', value:2,
                        active:'active'
                    },
                    {
                        text: '中国海油', value: 3,
                        active:'active'
                    },
                    {
                        text: 'BP', value: 4,
                        active:'active'
                    },
                    {
                        text: '壳牌', value: 5,
                        active:'active'
                    },
                    {
                        text: '加德士', value:6,
                        active:'active'
                    },
                    {
                        text: '其他', value: 7,
                        active:'active'
                    }
                ],
                gasSationList:[],
                latitude : ${latitude!0},//纬度
                longitude : ${longitude!0},//经度
                showHomeList:false
            };
        },
        computed:{

        },
        mounted:function(){
          // this.initStation();
        },
        methods: {
            hideHomeList(){
                this.showHomeList=false
            },
            onClickLeft() {
                location.href="${base}/brightoilonline/index.jhtml";

                // history.back();
            },
            //点击搜索图标
            search(){
                this.findStation();
            },
            Jump(data){
                location.href="${base}" + data+".jhtml";
                if(data==='/brightoilonline/index'||data==='/index'){
                    this.showHomeList=false;
                }
            },
            //选择油号
            selectedOil(data){
                this.oilTypeList.map(item=>{
                    item.active='';
                })
                data.active='active';
                this.oilType=data.text;
                this.$refs.oilItem.toggle();
                this.findStation()
            },
            //确定选中的加油站
            comfirmGas(){
                if(this.gasStationTypeList[0].active==='active'){ //如果是全部
                    this.gasStationType=this.gasStationTypeList[0].text;
                    this.$refs.gasItem.toggle();
                    this.findStation()
                    return
                }
                let arr=[]
                this.gasStationTypeList.map(item=>{
                    if( item.active==='active'){
                        arr.push(item.text)
                    }
                })
                if(arr.length===0){
                    this.gasStationTypeList.map(item=>{
                        item.active='active'
                    })
                    this.gasStationType="全部";
                }else {
                    this.gasStationType=arr.join('、');
                }
                this.$refs.gasItem.toggle();
                this.findStation()
            },
            //选择加油站
            selectedGas(data){
                //点击全部选中或者全部不选中
                if(data.value===0){
                    if(data.active===''){ //全部选中
                        this.gasStationTypeList.map(item=>{
                            item.active='active';
                        })
                    }else { //全部不选中
                        this.gasStationTypeList.map(item=>{
                            item.active='';
                        })
                    }
                    return
                }
                if(data.active===''){
                    data.active='active';
                    let num=0;
                    this.gasStationTypeList.map(item=>{
                        if(item.value!==0){
                            if(item.active==='active'){
                                num++;
                            }
                        }
                    })
                    if(num===this.gasStationTypeList.length-1){
                        this.gasStationTypeList[0].active='active';
                    }
                }else {
                    data.active='';
                    //如果是全选，取消全选
                    if(this.gasStationTypeList[0].active==='active'){
                        this.gasStationTypeList[0].active='';
                    }
                }
            },
            findStation(){
                this.descriptionText="正在加载中...";
                this.gasSationList=[];
                let dto = {};
                dto.oilCode = this.oilType;//油号
                dto.latitude = this.latitude;//纬度
                dto.longitude = this.longitude;//经度
                dto.stationName = this.searchValue;//油站名
                dto.brandIds = new Array();//品牌
                for(let i in this.gasStationTypeList) {
                    if(this.gasStationTypeList[i].value == 0 && this.gasStationTypeList[i].active == 'active') {
                        break;
                    } else if(this.gasStationTypeList[i].value != 0 && this.gasStationTypeList[i].active == 'active') {
                        dto.brandIds.push(this.gasStationTypeList[i].value);
                    }
                }
                let that=this
                $.ajax({
                    type: "GET",
                    url: "${base}" + "/brightoilonline/nearbyGasStation.jhtml",
                    contentType: "application/x-www-form-urlencoded",
                    data: dto,
                    async: false,
                    traditional: true,//数组不带[]
                    success: function(data){
                        if("200" == data.code) {
                            if(data.DataList&&data.DataList!==null){
                                that.gasSationList = data.DataList;
                                if(that.gasSationList.length===0){
                                    that.descriptionText="暂无数据";
                                }
                                //把type字符串转为number类型
                                for(let i = 0; i < that.gasSationList.length; i++) {
                                    that.gasSationList[i].type = Number(that.gasSationList[i].type);
                                    that.gasSationList[i].distance = Number(that.gasSationList[i].distance).toFixed(2);//油站距离保留两位数
                                }
                            }

                        }else {
                            that.descriptionText="加载失败";
                        }
                    }
                });
            },
            initStation(){
                let that=this;
                //加载地图，调用浏览器定位服务
                var map = new AMap.Map('container', {
                    resizeEnable: true
                });
                if(this.address == '' || this.address == null) {
                    map.plugin('AMap.Geolocation', function () {
                        geolocation = new AMap.Geolocation({
                            enableHighAccuracy: true,//是否使用高精度定位，默认:true
                            timeout: 10000,          //超过10秒后停止定位，默认：无穷大
                            buttonOffset: new AMap.Pixel(10, 20),//定位按钮与设置的停靠位置的偏移量，默认：Pixel(10, 20)
                            zoomToAccuracy: true,      //定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：false
                            buttonPosition: 'RB'
                        });
                        map.addControl(geolocation);
                        geolocation.getCurrentPosition(function (result, data) {
                            if (result == "complete") {
                                if(that.address == '' || that.address == null) { //从主页过的加载位置信息
                                    that.latitude = data.position.lat;
                                    that.longitude = data.position.lng;
                                    //地址：深市区街道data.addressComponent.province + data.addressComponent.city +
                                    that.address =  data.addressComponent.district + data.addressComponent.township + data.formattedAddress.replace(data.addressComponent.building,"");
                                    //加载附件油站
                                    that.findStation();
                                }


                            }else {
                                that.descriptionText="定位失败，请手动选择地址"
                            }
                        });
                    });
                } else {
                    //加载附件油站
                    this.findStation();
                }
            },
            jumpToInfo(item) {
                if(item.type == 1) {//合作油站
                    let url = "${base}/brightoilonline/gasStationInfo.jhtml?stationId=" + item.stationId + "&distance=" + item.distance;
                    for(let i = 0; i < this.oilTypeList.length; i++) {
                        if(this.oilTypeList[i].active == 'active') {
                            url += "&oilType=" + this.oilTypeList[i].text;
                        }
                    }
                    location.href = url;
                } else {//非合作油站
                    let url = "${base}/brightoilonline/toWechatConsumer.jhtml?type=1&stationId=" +item.stationId + "&distance=" + item.distance;
                    for(let i = 0; i < this.oilTypeList.length; i++) {
                        if(this.oilTypeList[i].active == 'active') {
                            url += "&oilType=" + this.oilTypeList[i].text;
                        }
                    }
                    location.href =  url;
                }
            },
            showJumplist(){
               this.showHomeList=!this.showHomeList;
            },

        },
    });



</script>
<script type="text/javascript">
    window.onload=function(){
            initStation();
    }


    function findStation(){
        vm.descriptionText="正在加载中...";
        vm.gasSationList=[];
        let dto = {};
        dto.oilCode = vm.oilType;//油号
        dto.latitude = vm.latitude;//纬度
        dto.longitude = vm.longitude;//经度
        dto.stationName = vm.searchValue;//油站名
        dto.brandIds = new Array();//品牌
        for(let i in vm.gasStationTypeList) {
            if(vm.gasStationTypeList[i].value == 0 && vm.gasStationTypeList[i].active == 'active') {
                break;
            } else if(vm.gasStationTypeList[i].value != 0 && vm.gasStationTypeList[i].active == 'active') {
                dto.brandIds.push(vm.gasStationTypeList[i].value);
            }
        }
        $.ajax({
            type: "GET",
            url: "${base}" + "/brightoilonline/nearbyGasStation.jhtml",
            contentType: "application/x-www-form-urlencoded",
            data: dto,
            async: false,
            traditional: true,//数组不带[]
            success: function(data){
                if("200" == data.code) {
                    if(data.DataList&&data.DataList!==null){
                        vm.gasSationList = data.DataList;
                        if(vm.gasSationList.length===0){
                            vm.descriptionText="暂无数据";
                        }
                        //把type字符串转为number类型
                        for(let i = 0; i < vm.gasSationList.length; i++) {
                            vm.gasSationList[i].type = Number(vm.gasSationList[i].type);
                            vm.gasSationList[i].distance = Number(vm.gasSationList[i].distance).toFixed(2);//油站距离保留两位数
                        }
                    }

                }else {
                    vm.descriptionText="加载失败";
                }
            }
        });
    };
    function  initStation(){
        var map;
        var geolocation;
        //加载地图，调用浏览器定位服务
         map = new AMap.Map('container', {
            resizeEnable: true
        });
        if(vm.address == '' || vm.address == null) {
                map.plugin('AMap.Geolocation', function () {
                geolocation = new AMap.Geolocation({
                    enableHighAccuracy: true,//是否使用高精度定位，默认:true
                    timeout: 10000,          //超过10秒后停止定位，默认：无穷大
                    buttonOffset: new AMap.Pixel(10, 20),//定位按钮与设置的停靠位置的偏移量，默认：Pixel(10, 20)
                    zoomToAccuracy: true,      //定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：false
                    buttonPosition: 'RB'
                });
                map.addControl(geolocation);
                geolocation.getCurrentPosition(function (result, data) {
                    console.log(result,'result');
                    console.log(data,'data');
                    if (result == "complete") {
                        if(vm.address == '' || vm.address == null) { //从主页过的加载位置信息
                            vm.latitude = data.position.lat;
                            vm.longitude = data.position.lng;
                            //地址：深市区街道data.addressComponent.province + data.addressComponent.city +
                            vm.address =  data.addressComponent.district + data.addressComponent.township + data.formattedAddress.replace(data.addressComponent.building,"");
                            //加载附件油站
                            findStation();
                        }


                    }else {
                        vm.descriptionText="定位失败，请手动选择地址"
                    }
                });
            });
        } else {
            //加载附件油站
            findStation();
        }
    };
</script>