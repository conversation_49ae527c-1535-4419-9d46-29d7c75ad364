<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta
            name="viewport"
            content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no"
    />
    <title>加油支付</title>
    <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
    <link rel="stylesheet" href="${base}/resources/wechat/css/vant/index.css">
    <link type="text/css"  rel="stylesheet" href="${base}/resources/wechat/css/common.css">
    <link type="text/css"  rel="stylesheet" href="${base}/resources/wechat/css/rechargePay.css">
    <!--<script type="text/javascript"src="https://cdn.jsdelivr.net/npm/vue@2.6/dist/vue.min.js"></script>-->
    <script src="${base}/resources/wechat/plugins/vue/vue.min.js"></script>
    <script type="text/javascript"src="${base}/resources/wechat/plugins/jquery/jquery-1.9.1.min.js"></script>
    <script type="text/javascript"src="${base}/resources/wechat/js/common.js"></script>
    <!--<script type="text/javascript"src="https://cdn.jsdelivr.net/npm/vant@2.8/lib/vant.min.js"></script>-->
    <script src="${base}/resources/wechat/js/vant.min.js"></script>
</head>
<body class="rechargPayPage oilPayPage wechatConsumePage">
<div id="app" @click.stop="hideHomeList">
    <van-sticky>
        <van-nav-bar
                title="加油支付"
                left-text=""
                left-arrow
                @click-left.stop="onClickLeft"
                @click-right.stop="onClickRight"
        >
            <template #right>
                <van-icon name="ellipsis" />
                <ul class="showHomeList" v-if="showHomeList">
                    <li>
                        <a href="${base}/index.jhtml">
                            <span class="icon_home"></span>
                            <p>首页</p>
                        </a>
                    </li>
                    <li>
                        <a href="${base}/brightoilonline/index.jhtml">
                            <span class="wechaticon_home"></span>
                            <p>消费卡首页</p>
                        </a>
                    </li>
                    <li>
                        <a href="[#if my_home_link?? && my_home_link != ""]${my_home_link}[#else]${base}/member/index.jhtml[/#if]">
                            <span class="icon_user"></span>
                            <p>个人中心</p>
                        </a>
                    </li>
                </ul>
            </template>
        </van-nav-bar>
    </van-sticky>
    <div class="content  oilPayPageContent">
       <div class="titile borderR">
           加油订单支付
       </div>
        <div class="borderR informationForm">
                <van-field
                        v-model="payInfo.gasStationName"
                        placeholder=""
                        label="油站名称"
                        input-align="right"
                        readonly
                >
                </van-field>
                <!--<van-field
                        v-model="payInfo.wechatCardNum"
                        placeholder=""
                        label="支付卡号"
                        input-align="right"
                        readonly
                >-->
                </van-field>
                <van-field
                        placeholder=""
                        input-align="right"
                        label="汽油编号"
                        readonly
                >
                    <template #input >
                        <div class="oilType" v-cloak>
                            {{payInfo.oilType}}
                        </div>
                    </template>
                </van-field>
                <van-field
                        placeholder=""
                        label="加油升数"
                        input-align="right"
                        readonly
                >
                    <template #input >
                        <div v-cloak>
                            {{payInfo.totalOil}}L
                        </div>
                    </template>
                </van-field>
                <van-field
                        placeholder=""
                        label="油枪号"
                        input-align="right"
                        readonly
                >
                    <template #input >
                        <div v-cloak>
                           {{payInfo.oilGun}}号
                        </div>
                    </template>
                </van-field>
                <van-field
                        v-model="payInfo.cashback"
                        placeholder=""
                        label="预计立返金额"
                        input-align="right"
                        readonly
                >
                    <template #input >
                        <div v-cloak>
                            {{payInfo.cashback}}元
                        </div>
                    </template>
                </van-field>
        </div>
        <div  class="borderR informationForm">
            <van-field
                    placeholder=""
                    label="订单总金额"
                    input-align="right"
                    readonly
            >
                <template #input >
                    <div v-cloak>
                        {{payInfo.totoalMoney}}元
                    </div>
                </template>
            </van-field>
        </div>
        <div class="btnoption">
            <van-button class="borderR" :loading="loading" :disabled="loading" loading-text="提交中..." color="rgb(20,143,240)" block  @click.stop="onSubmit">立即支付</van-button>
        </div>
        <van-dialog
                v-model="showTips"
                title="温馨提示"
                class="PhoneCodeDialog"
                confirm-button-text="我已了解,继续支付"
                :show-cancel-button="showCancelFlag"
                @confirm="confirmPay"
                   >
            <p class="content">
                支付成功后，请在光汇云油小票机打印加油小票出示小票加油，或咨询工作人员请说明通过‘光汇云油’支付
            </p>
        </van-dialog>
    </div>
</div>
<script>
    let vm = new Vue({
        el: "#app",
        data() {
            return {
                showCancelFlag:true,
                showHomeList:false,
                loading:false,
                payInfo:{
                    gasStationId:${stationId!}, // 油站id
                    gasStationName:'${stationName!}',//油站名称
                    //wechatCardNum:'62949494949494942000',//三类卡卡号
                    oilId:'${oilId!}',
                    oilType:'${oilName!}#',//油号 如0#，92#,
                    totalOil:'${oilVolume!}',//油体积（L）
                    oilGun:'${gun!}',//油枪
                    cashback:'${backAmount!}',
                    totoalMoney:'${fee!}',//订单总金额
                    longitude:'113.94849', //经度
                    latitude: '22.54435', //纬度


                },//开户信息
                whiteNotePayment:"0.00",//白条支付
                pointPayment:"105.00",//积分支付
                showTips:false,//支付提示
                showPassword:false,
                showKeyboard:false,
                value: '123',
            };
        },
        computed:{

        },
        methods: {
            onClickLeft() {
                history.back();
            },
            hideHomeList(){
                this.showHomeList=false;
            },
            onClickRight() {
              this.showHomeList=!this.showHomeList;
            },
            Jump(data){
                location.href="${base}" + data+".jhtml";
            },
            //点击立即支付
            onSubmit(){
                this.showTips=true;
            },
            //确定支付
            confirmPay(){
                this.loading = true;
                let dto = new Object();
                dto.stationId = this.payInfo.gasStationId ; // 油站id
                dto.stationName = this.payInfo.gasStationName; // 油站名称
                dto.oilId = this.payInfo.oilId; //油品id
                dto.gunName = this.payInfo.oilGun; //油枪号名称
                dto.latitude = this.payInfo.latitude; //纬度
                dto.longitude = this.payInfo.longitude ; //经度
                dto.cashBack = this.payInfo.cashback; // 预计立返金额
                dto.orderAmount = this.payInfo.totoalMoney ; // 加油金额
                let that = this;
                $.ajax({
                    type: "POST",
                    url: "${base}" + "/brightoilonline/createOilOrder.jhtml",
                    contentType: "application/x-www-form-urlencoded",
                    data: dto,
                    success: function(data){

                        if(data.type == "warn" || data.type == "error") {//支付密码错误，弹出提示
                            that.$toast(data.content);
                            that.loading = false;
                        } else { //跳转到结果页面
                              location.href = data.content;
                       //     location.href = "${base}" + "/brightoilonline/toRechargePayResult.jhtml?type=" + data.type + "&content=" + data.content;
                        }
                    },
                    error: function(XMLHttpRequest, textStatus, errorThrown){
                        that.loading=false;
                    }
                });
            }
        },
    });
</script>
</body>
</html>
