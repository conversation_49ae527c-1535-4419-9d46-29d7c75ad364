<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>[#if tripReimburseApply?? && tripReimburseApply.id??]编辑[#else]新增[/#if]报销-报销明细</title>
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="format-detection" content="telephone=no, email=no" />
    <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/chailvStyle.css" />

    <script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js"></script>
</head>

<body>
    <div class="chailvPage has_fixed_footer">
        <div class="public_top_header">
            <a href="javascript:goBack();" class="return_back"></a>
            选择收款账户
            <span class="text text_right"  onclick="javascript:confirm();">确定</span>
        </div>
        <ul class="bank_list">
        	[#if creditCards?? && creditCards?size>0]
            [#list creditCards as creditCard]
            <li id="cardInfo_${creditCard.id}" bankAccountName="${creditCard.bankAccountName}" bankAccountNo="${creditCard.bankAccountNoDec}" bankName="${creditCard.bankName}">
                <div class="fli_checkbox_blue">
                    <input type="radio" name="cards" id="card_${creditCard.id}" value="${creditCard.id}">
                    <label for="card_${creditCard.id}"></label>
                </div>
                <div class="info">
                    <p class="title">${creditCard.bankAccountName}</p>
                    <p>${creditCard.bankAccountNoDec}</p>
                    <p>${creditCard.bankName}</p>
                    <p>${creditCard.bankProvince}&nbsp;${creditCard.bankCity}</p>
                </div>
                <a href="javascript:deleteCard(this, ${creditCard.id});" class="icon_del"></a>
            </li>
            [/#list]
            [/#if]
        </ul>

        <div class="fixed_bottom_option_line">
            <a href="javascript:newAccount();">新增收款账户</a>
        </div>
    </div>
    <form action="paymentInfo.jhtml" id="reimburseForm" method="post">
    	<input type="hidden" name="id" value="${tripReimburseApply.id}" />
        	<input type="hidden" name="applySn" value="${tripReimburseApply.applySn}" />
        	<input type="hidden" name="applyStatus" id="applyStatus" value="submited" />
        <input type="hidden" name="memberId.id" value="${tripReimburseApply.memberId.id}" /> 
        <input type="hidden" value="[#if reimburseAccount?? && reimburseAccount.tripLevelConfig??]${reimburseAccount.tripLevelConfig.tripAllowancePrice}[#else]0[/#if]" class="input" readonly />
		<input type="hidden" name="reimburseMemberId.id" value="${tripReimburseApply.reimburseMemberId.id}" /> 
        <input type="hidden" name="applyType" value="${tripReimburseApply.applyType}"/>
        <input type="hidden" name="bussinessType" value="${tripReimburseApply.bussinessType}"/>
        <input type="hidden" name="feeCompanyStructure.id" value="${tripReimburseApply.feeCompanyStructure.id}"/>
        <input type="hidden" name="projectId.id" value="${tripReimburseApply.projectId.id}"/>
        <input type="hidden" name="receiptNumbers" value="${tripReimburseApply.receiptNumbers}"/>
        <input type="hidden" name="memo" value="${tripReimburseApply.memo}"/>
        <input type="hidden" name="reimburseMemberId.name" id="reimburseMemberIdName" value="${tripReimburseApply.reimburseMemberId.name}" />
           <input type="hidden" name="feeCompanyStructure.name" id="feeCompanyStructureName" value="${tripReimburseApply.feeCompanyStructure.name}"/>
           <input type="hidden" name="projectId.name" id="projectIdName" value="${tripReimburseApply.projectId.name}"/>
           <input type="hidden" name="receiveType" id="receiveType" value="${tripReimburseApply.receiveType}" />
        <input type="hidden" name="receiveAccount" id="receiveAccount" value="${tripReimburseApply.receiveAccount}" /> 
        [#list tripReimburseApply.tripReimburseRecords as record]
	        <input type="hidden" name="tripReimburseRecords[${record_index }].id" value="${record.id }"/>
			<input type="hidden" name="tripReimburseRecords[${record_index }].tripApply.id" class="tripApplyId" value="[#if record.tripApply??]${record.tripApply.id }[/#if]"/>
			<input type="hidden" name="tripReimburseRecords[${record_index }].tripApply.applySn" class="tripApplySn" value="[#if record.tripApply??]${record.tripApply.applySn }[/#if]"/>
			<input type="hidden" name="tripReimburseRecords[${record_index }].startDate" value="${record.startDate }"/>
			<input type="hidden" name="tripReimburseRecords[${record_index }].endDate" value="${record.endDate }"/>
			<input type="hidden" name="tripReimburseRecords[${record_index }].tripDays" value="${record.tripDays }" />
			<input type="hidden" name="tripReimburseRecords[${record_index }].fromCity" value="${record.fromCity }"/>
			<input type="hidden" name="tripReimburseRecords[${record_index }].toCity" value="${record.toCity }"/>
			<input type="hidden" name="tripReimburseRecords[${record_index }].reimburseDays" value="${record.reimburseDays }" />
			[#list record.tripReimburseAmounts as amount]
			<input type="hidden" class="tripReimburseAmount_enabledFlag" name="tripReimburseRecords[${record_index }].tripReimburseAmounts[${amount_index }].enabledFlag" value="[#if amount.id?? && !amount.enabledFlag]false[#else]true[/#if]"/>
			<input type="hidden" name="tripReimburseRecords[${record_index }].tripReimburseAmounts[${amount_index }].id" value="${amount.id }"/>
			<input type="hidden" class="tripReimburseAmount_costType" name="tripReimburseRecords[${record_index }].tripReimburseAmounts[${amount_index }].costType" value="${amount.costType }"/>
			<input type="hidden" class="tripReimburseAmount_amount" name="tripReimburseRecords[${record_index }].tripReimburseAmounts[${amount_index }].amount" value="${currency(amount.amount) }"/>
			<input type="hidden" class="tripReimburseAmount_invoiceType" name="tripReimburseRecords[${record_index }].tripReimburseAmounts[${amount_index }].invoiceType" value="${amount.invoiceType }"/>
			<input type="hidden" class="tripReimburseAmount_chargeFlag" name="tripReimburseRecords[${record_index }].tripReimburseAmounts[${amount_index }].chargeFlag" value="[#if amount.chargeFlag?? && amount.chargeFlag]1[#else]0[/#if]"/>
			<input type="hidden" class="tripReimburseAmount_memo" name="tripReimburseRecords[${record_index }].tripReimburseAmounts[${amount_index }].memo" value="${amount.memo }"/>
			[/#list]		
        [/#list]
    </form>
    <script>
    	//返回上一页
    	function goBack(){
    		$("#reimburseForm").attr("action", "paymentInfo.jhtml");
			$("#reimburseForm").submit();
        }
    	//返回上一页
    	function newAccount(){
    		$("#reimburseForm").attr("action", "newAccount.jhtml");
			$("#reimburseForm").submit();
        }

        function confirm(){
        	var cardId = $("input[name='cards']:checked").val();
        	if(cardId != null && cardId != ""){
				var $cardInfo = $("#cardInfo_" + cardId);
				var bankAccountName = $cardInfo.attr("bankAccountName");
				var bankAccountNo = $cardInfo.attr("bankAccountNo");
				var bankName = $cardInfo.attr("bankName");
				$("#receiveType").val("1");
				$("#receiveAccount").val(bankAccountNo + ";" + bankAccountName + ";" + bankName);
				$("#reimburseForm").attr("action", "paymentInfo.jhtml");
				$("#reimburseForm").submit();
            } else {
            	layer.open({
                    content: "请选择收款账户"
                    ,skin: 'msg'
                    ,time: 2 //2秒后自动关闭
                });
                return false;
            }
        }

    	//删除卡片
        function deleteCard(obj, id){
            var $obj = $(obj);
            $obj.prop("disabled", true);
        	layer.open({
                content: '确认删除该账户？',
                skin:"footer",
                btn: ["确认", "取消"],
                yes: function (index, layero) { //确定按钮事件
					$.ajax({
						url: "deleteCard.jhtml",
						type: "POST",
						dataType: "json",
						data: {id:id},
						cache: false,
						success: function(msg) {
							if(msg){
								if(msg.type=="success"){
									layer.open({
				                       content: "删除成功"
				                       ,skin: 'msg'
				                       ,time: 2 //2秒后自动关闭
				                   });
									$("#cardInfo_" + id).remove();
								}else{
									layer.open({
					                       content: msg.content
					                       ,skin: 'msg'
					                       ,time: 2 //2秒后自动关闭
					                   });
									$obj.prop("disabled", false);
								}
							}else{
								layer.open({
				                       content: '删除失败'
				                       ,skin: 'msg'
				                       ,time: 2 //2秒后自动关闭
				                   });
								$obj.prop("disabled", false);
							}
						}
					});
                    
                    layer.close(index);
                }
            });
        }
        $(function () {
        })
    </script>
</body>

</html>