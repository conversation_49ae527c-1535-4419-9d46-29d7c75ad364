<input type="hidden" id="totalPages" value="${page.totalPages}"/>
[#if page.total > 0]
    [#list page.content as message]
        [#if message.messageContentId.messageType == "notice"]
             <li class="item_notice"
                 onclick="getDetail('${message.messageContentId.contentId}',0)">
                 <header>
                     <div class="header">
                         <p class="title">${escapeHtml(message.messageContentId.title)}</p>
                         <p class="info">
                             公司公告 <span>${message.messageContentId.createDate}</span>
                         </p>
                     </div>
                 </header>
                 <article>
                     <div>${escapeHtml(message.messageContentId.content)}</div>
                 </article>
             </li>
        [/#if]
        [#if message.messageContentId.messageType == "promotion"]
            <li class="item_sale" onclick="getDetail('${message.messageContentId.contentId}',2)">
                <header>
                    <div class="header">
                        <p class="title">${message.messageContentId.title}</p>
                        <p class="info">
                            开始时间 <span>${message.messageContentId.ip}</span>
                        </p>
                    </div>
                </header>
                <article class="text-ellipsis">
                    ${message.messageContentId.content}
                </article>
                <div class="btns">
                    <input type="button" name="" id="" value="立即抢购" class="btn_index_long"/>
                </div>
            </li>
        [/#if]
        [#if message.messageContentId.messageType == "birdthday"]
            <li class="item_birthday" onclick="getDetail('${message.messageContentId.contentId}',4)">
                <header>
                    <div class="header">
                        <p class="title">${message.messageContentId.title}</p>
                        <p class="info">
                            员工生日 <span>${message.messageContentId.createDate?string("yyyy-MM-dd HH:mm:ss")}</span>
                        </p>
                    </div>
                </header>
                <article>
                    <div>${message.messageContentId.content}</div>
                    <a href="${base}/buyer/birthdayreminder/index.jhtml"
                       class="btn_submit_long">为“TA”集福</a>
                </article>
            </li>
        [/#if]
        [#if message.messageContentId.messageType == "topicPush"]
            <li class="item_notice"
                onclick="getDetail('${message.messageContentId.contentId}',5)">
                <header>
                    <div class="header">
                        <p class="title">${message.messageContentId.title}</p>
                        <p class="info">
                            互动社区 <span>${message.messageContentId.createDate}</span>
                        </p>
                    </div>
                </header>
                <article class="community-article">
                    <div>${message.messageContentId.content}</div>
                </article>
            </li>
        [/#if]
        [#if message.messageContentId.messageType == "order"]
            <li class="item_order"
                onclick="getDetail('${message.messageContentId.contentId}',6)"
                messageId="${message.id}">
                <header>
                    <div class="header">
                        <p class="title">${message.messageContentId.title}</p>
                        <p class="info">
                            订单通知
                            <span>${message.messageContentId.createDate?string('yyyy-MM-dd HH:mm:ss')}</span>
                        </p>
                    </div>
                    <!-- <div [#if message.receiverRead==false]class="switch"[/#if] messageId="${message.id}">
                            <span>[#if message.receiverRead==false]未读[/#if]</span>
                            [#if message.receiverRead==false]<div class="sButton"></div>[/#if]
                            <input type="hidden" name="" id="" value="" />
                         </div> -->

                    [#if message.receiverRead==false]
                        <span class="unRead" messageId="${message.id}">未读</span>
                    [/#if]
                </header>
                <article>
                    <div>${message.messageContentId.content}</div>
                </article>
            </li>
        [/#if]
        [#if message.messageContentId.messageType == "carecard"]
            <li class="item_carecard"
                onclick="getDetail('${message.messageContentId.contentId}',7)"
                messageId="${message.id}">
                <header>
                    <div class="header">
                        <p class="title">${message.messageContentId.title}</p>
                        <p class="info">
                            贺卡
                            <span>${message.messageContentId.createDate?string('yyyy-MM-dd HH:mm:ss')}</span>
                        </p>
                    </div>
                    <!--<div [#if message.receiverRead==false]class="switch"[/#if] messageId="${message.id}">
                        <span>[#if message.receiverRead==false]未读[/#if]</span>
                        [#if message.receiverRead==false]<div class="sButton"></div>[/#if]
                        <input type="hidden" name="" id="" value="" />
                    </div>-->

                    [#if message.receiverRead==false]
                    <span class="unRead" messageId="${message.id}">未读</span>
                    [/#if]
                </header>
                <article>
                    <div>${message.messageContentId.content}</div>
                </article>
            </li>
        [/#if]
        [#if message.messageContentId.messageType == "coupon"]
            <li class="item_coupon"
                onclick="getDetail('${message.messageContentId.contentId}',8)"
                messageId="${message.id}">
                <header>
                    <div class="header">
                        <p class="title">${message.messageContentId.title}</p>
                        <p class="info">
                            福利券
                            <span>${message.messageContentId.modifyDate?string('yyyy-MM-dd HH:mm:ss')}</span>
                        </p>
                    </div>
                    <!--	<div [#if message.receiverRead==false]class="switch"[/#if] messageId="${message.id}">
                        <span>[#if message.receiverRead==false]未读[/#if]</span>
                        [#if message.receiverRead==false]<div class="sButton"></div>[/#if]
                        <input type="hidden" name="" id="" value="" />
                    </div>-->

                    [#if message.receiverRead==false]
                    <span class="unRead" messageId="${message.id}">未读</span>
                    [/#if]
                </header>
                <article>
                    <div>${message.messageContentId.content}</div>
                </article>
            </li>
        [/#if]
        [#if message.messageContentId.messageType == "birthGood" || message.messageContentId.messageType == "birthBlessed"]
            <li class="item_birthday2"
                onclick="getDetail('${message.messageContentId.contentId}',9)"
                messageId="${message.id}">
                <header>
                    <div class="header">
                        <p class="title">${message.messageContentId.title}</p>
                        <p class="info">
                            ${message.messageContentId.contentId}
                            <span>${message.messageContentId.modifyDate?string('yyyy-MM-dd HH:mm:ss')}</span>
                        </p>
                    </div>
                    [#if message.receiverRead==false]
                    <span class="unRead" messageId="${message.id}">未读</span>
                    [/#if]
                </header>
                <article>
                    <div>${message.messageContentId.content}</div>
                </article>
            </li>
        [/#if]
        [#if message.messageContentId.messageType == "coinReward"]
            <li class="item_hongbao"
                onclick="getDetail('${message.messageContentId.contentId}',9)"
                messageId="${message.id}">
                <header>
                    <div class="header">
                        <p class="title">${message.messageContentId.title}</p>
                        <p class="info">
                            ${message.messageContentId.contentId}
                            <span>${message.messageContentId.modifyDate?string('yyyy-MM-dd HH:mm:ss')}</span>
                        </p>
                    </div>
                    [#if message.receiverRead==false]
                    <span class="unRead" messageId="${message.id}">未读</span>
                    [/#if]
                </header>
                <article>
                    <div>${message.messageContentId.content}</div>
                </article>
            </li>
        [/#if]
        [#if message.messageContentId.messageType == "giftGiving"]
            <li class="item_hongbao"
                onclick="getDetail('${message.messageContentId.contentId}',10)"
                messageId="${message.id}">
                <header>
                    <div class="header">
                        <p class="title">${message.messageContentId.title}</p>
                        <p class="info">
                            礼物
                            <span>${message.messageContentId.modifyDate?string('yyyy-MM-dd HH:mm:ss')}</span>
                        </p>
                    </div>
                    [#if message.receiverRead==false]
                    <span class="unRead" messageId="${message.id}">未读</span>
                    [/#if]
                </header>
                <article>
                    <div>${message.messageContentId.content}</div>
                </article>
            </li>
        [/#if]
    [/#list]
[#else]
<div class="public_empty_section [#if comPersonConf && com_person_EmptyBg != null && com_person_EmptyBg != ""] ${com_person_EmptyBg!}[#else] Public_Bg[/#if]">
    <p class="tip">这里空空如也，先去逛逛吧~</p>
    [#--企业未选配全部分类 或 企业个性化空空如也配置index--]
    [#if (company?? && !company.categoryAll) || (com_person_moreAtts_empty!=null && com_person_moreAtts_empty=="index") ]
        <a href="${base}/index.jhtml" class="pub_link_theme">去逛逛</a>
    [#else]
        <a href="${base}/product/index.jhtml" class="pub_link_theme">去逛逛</a>
    [/#if]
</div>
[/#if]
