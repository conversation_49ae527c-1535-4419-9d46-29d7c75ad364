		 			<input type="hidden" id="totalPages" value="${page.totalPages}"/>
		 			[#if page.total > 0]
			 			[#list page.content as order]
						[#assign orderItem=order.orderItems[0]]
						
					 			<li>
					 				<header>
					 					<span class="no">订单编号：${order.sn}</span>
										<span class="status complete">
											[#if order.orderStatus == "unpaid"]
										                      待支付
									        [#elseif order.orderStatus == "unconfirmed"]
										                       交易中
											[#elseif order.orderStatus == "confirmed"]
												预订成功
											[#elseif order.orderStatus == "shipped"]
												等待入住
											[#elseif order.orderStatus == "completed"]
									           	已完成
											[#elseif order.orderStatus == "cancelled"]
									         	 已取消
											[/#if]	
										</span>
					 				</header>
					 				
					 				<a href="#" class="item">
					 					<div class="img">
					 					   <img src="${base}/resources/wechat/img/recharge/img_hotel.png">
								 			
								 		</div>
								 		<div class="title">
								 			<p>
								 			   ${orderItem.getVirtualProductOrderInfoMapValue('hotelName')}
								 			   [#if orderItem.getVirtualProductOrderInfoMapValue('address')!=null]
								 			     (${orderItem.getVirtualProductOrderInfoMapValue('address')})
								 			   [/#if]
								 			</p>
								 		</div>
								 		<div class="attrs">
								 			<span>数量:${orderItem.getVirtualProductOrderInfoMapValue('numberOfRooms')}间</span>
								 			<span>入住时间:</span>
								 			<span>${orderItem.getVirtualProductOrderInfoMapValue('arrivalDate')}</span>
								 			
								 		</div>
					 				</a>
					 				
					 				<footer>
					 					<span class="total_price">总计:￥${orderItem.getVirtualProductOrderInfoMapValue('totalPrice')}</span>
					 					<div class="btns">
					 						<input type="button" onclick="javascript:location.href='/vp-web-buyer-wechat/hotel/orderDetail.jhtml?orderId=${order.id}'" value="订单详情" class="btn_theme">
					 						
					 						[#if order.orderStatus != "cancelled"]
					 						<input type="button" onclick="cancelOrder(${order.id},${order.toatlAmount})" value="取消订单" class="btn_theme">
								            [/#if]
					 					</div>
					 				</footer>
					 			</li>
					 		[/#list]
					[#else]
						<div class="public_empty_section [#if comPersonConf && com_person_EmptyBg != null && com_person_EmptyBg != ""] ${com_person_EmptyBg!}[#else] Public_Bg[/#if]">
                            <p class="tip">这里空空如也，快去预订吧~</p>
			 			 	<a href="${base}/hotel/index.jhtml" class="pub_link_theme">去预订吧</a>
			 			 </div>
					[/#if]
