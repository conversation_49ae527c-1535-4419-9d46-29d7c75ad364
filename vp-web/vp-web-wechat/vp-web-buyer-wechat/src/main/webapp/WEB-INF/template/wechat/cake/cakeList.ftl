<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta
            name="viewport"
            content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no"
    />
    <title>蛋糕首页</title>
    <script type="text/javascript">
        var hostname=window.location.protocol+"//"+document.location.hostname;
        window._AMapSecurityConfig = {
            serviceHost:hostname+'/_AMapService'
        }
    </script>
    <script type="text/javascript" src='https://webapi.amap.com/maps?v=1.4.2&key=af778b7f852561b0c1028ba36eaf6c68'></script>
    <!-- UI组件库 1.0 -->
    <script src="https://webapi.amap.com/ui/1.0/main.js?v=1.0.11"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
    <link rel="stylesheet" href="${base}/resources/wechat/css/vant/index.css">
    <link  type="text/css" rel="stylesheet" href="${base}/resources/wechat/css/common.css">
    <link  type="text/css" rel="stylesheet" href="${base}/resources/wechat/css/cake.css">
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/search_filter_box.css" />
    <script src="${base}/resources/wechat/plugins/vue/vue.min.js"></script>

    <script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/shopCommon.js"></script>
    <script src="${base}/resources/wechat/js/vant.min.js"></script>
</head>
<body class="bocPage indexPage">
<div id="app">
    <header >
        <van-sticky>
            <van-row class="headerTop" type="flex"  align="center">
                <van-col span="10">
                    <div class="address flexbox align-items-c ">
                        <img class="adressIcon" src="${base}/resources/wechat/img/cake/index/dingwei_icon.png">
                        <div class="addressDetail">深圳市数字技术园</div>
                        <img class="selectIcon" src="${base}/resources/wechat/img/cake/index/jiantou_icon.png"/>
                    </div>
                </van-col>
                <van-col span="10">
                    <van-search
                            v-model="searchValue"
                            placeholder="搜索"
                            background="inherit"
                            shape="round"
                    ></van-search>
                </van-col>
                <van-col span="4">
                [#if !(purchaseMemberFlag??&&purchaseMemberFlag)]
                    <a href="${base}/cart/list.jhtml" class="cart_num" id="enterShoppingCart">
                        <div class="icon_cart"></div>
                        <label class="num_circle" id="cart_quantity"></label>
                    </a>
                [#else ]
                     <a href="${base}/wechat/purchase/index.jhtml" class="cart_num" id="enterShoppingCart">
                         <div class="icon_purchase"></div>
                     </a>
                [/#if]
                </van-col>
            </van-row>
            <ul id="sort" class="sortSection flexbox justify-content-space-between">
                <li @click="changeSelect('')"  :class="orderType == ''?'active':''"  >
                    <a href="javascript:;">默认</a>
                </li>
                <li @click="changeSelect('price')"  :class="orderType == 'priceAsc'?'active top':orderType == 'priceDesc'?'active down':''"  >
                    <a href="javascript:;" type="price" >价格<i></i></a>
                </li>
                <li  id="id_filter">
                    <a href="javascript:;">筛选<i></i></a>
                </li>
            </ul>
        </van-sticky>

    </header>
    <section class="hotList ">
        <ul class="clearfix">
            <li class=" flexbox flex-column align-items-c justify-content-c"
                v-for="(item,index) in  productList"
                v-if="item!=null"
                :key="'brand_list_'+index">
                <div class="imgBox flexbox align-items-c justify-content-c">
                    <img :src="item.src"/>
                </div>
                <div class="des">
                    <p v-cloak>{{item.des}}</p>
                    <p v-cloak>{{item.brand}}</p>
                    <p class="flexbox justify-content-space-between align-items-c">
                            <span>
                                ￥<span v-cloak>{{item.discount}}</span>
                            </span>
                        <span class="sale" v-cloak>
                                ￥{{item.original}}
                            </span>
                    </p>
                </div>
            </li>
        </ul>
    </section>
    <div class="searchPage fixed_tab_page">
        <div class="popbg" id="search_condition">
            <div class="close"></div>
            <div class="search_condition_filter_section">
                <header>筛选<a href="javascript:;" onclick="closeCondition()">取消</a></header>
                <div class="scroll_section">
                    <div class="price_range clearfix">
                        <label>价格区间</label>
                        <div class="content">
                            <input type="number"  id="startPrice" :value="startPrice" placeholder="最低价"/>
                            <span class="split_line"></span>
                            <input type="number"  id="endPrice" :value="endPrice" placeholder="最高价"/>
                        </div>
                    </div>
                    <div class="band_select clearfix brand_name" v-for="(item,index) in  screenList" :key="'screen'+index" >
                        <label v-cloak>{{item.name}}</label>

                        <div class="content">
                                <div class="next_cate_section brandNames">
                                    <span
                                            @click="changeSelectItem(ite,item.type)"
                                            v-for="(ite,idx) in  item.list"
                                            v-if="ite!=null"
                                            :key="'ite_list_'+idx+item.name"
                                            :class="classObject(ite,item.type)"
                                            class="btn_selectable"
                                            v-cloak
                                    >
                                    {{ite.name}}
                                    </span>
                                </div>
                        </div>

                    </div>
                </div>
                <footer class="clearfix">
                    <input type="reset" name="" @click="reSet" id="res" value="重置" />
                    <input type="submit" name="" @click="submit" id="btn" value="确定" form="productSearchForm"/>
                </footer>
            </div>
        </div>
    </div>
<script>
    var right_filter_w = $(".search_condition_filter_section").width();
    let vm = new Vue({
        el: "#app",
        data() {
            return {
                searchValue:"",
                orderType:"",//排序方式
                startPrice:"",//最低价
                endPrice:"",///最高价
                brandId:"xf",//选中品牌
                scaleId:"1xf",//选中尺寸
                screenList:[
                    {
                        name:"品牌",
                        type:"brandId",
                        list:[
                            {
                                name:"幸福西饼",
                                id:"xf"
                            },
                            {
                                name:"幸福西饼1",
                                id:"xf1"
                            }
                        ]
                    },
                    {
                        name:"蛋糕尺寸",
                        type:"scaleId",
                        list:[
                            {
                                name:"1磅",
                                id:"1xf"
                            },
                            {
                                name:"1.5磅",
                                id:"xf1"
                            }
                        ]
                    },
                ],
                productList:[
                    {
                        src:"${base}/resources/wechat/img/cake/index/cakeImage.png" ,
                        des:"元祖萌虎鲜奶蛋糕动森元祖萌虎鲜奶蛋糕动森元祖萌虎鲜奶蛋糕动森元祖萌虎鲜奶蛋糕动森",
                        brand:"幸福西饼",
                        discount:"9.9",
                        original:"999"
                    },
                    {
                        src:"${base}/resources/wechat/img/cake/index/cakeImage.png" ,
                        des:"元祖萌虎鲜奶蛋糕动森元祖萌虎鲜奶蛋糕动森元祖萌虎鲜奶蛋糕动森元祖萌虎鲜奶蛋糕动森",
                        brand:"幸福西饼",
                        discount:"9.9",
                        original:"999"
                    },
                    {
                        src:"${base}/resources/wechat/img/cake/index/cakeImage.png" ,
                        des:"元祖萌虎鲜奶蛋糕动森元祖萌虎鲜奶蛋糕动森元祖萌虎鲜奶蛋糕动森元祖萌虎鲜奶蛋糕动森",
                        brand:"幸福西饼",
                        discount:"9.9",
                        original:"999"
                    }
                ]


            };
        },
        computed:{

        },
        mounted(){
            window.addEventListener('scroll',this.handleScroll,true)
        },

        methods: {
            //筛选选中的换颜色
            classObject(data,type) {
                let cname='';
                if(data.id===this[type]){
                    cname = "active";
                }
                return cname;

            },
            changeSelectItem(data,type){
                this[type] = data.id;
            },
            inint(){
                this.startPrice="";//最低价
                this.endPrice="";///最高价
                this.brandId="";//选中品牌
                this.scaleId="";//尺寸
            },
            //重置筛选条件
            reSet(){
                this.inint()
            },
            //确定筛选条件
            submit(){
                // debugger
            },
            handleScroll(e){
                var scrollTop = e.target.documentElement.scrollTop || e.target.body.scrollTop;
                if(scrollTop>$(".headerTop").height()){
                    $(".headerTop").css("background","#fff")
                }else {
                    $(".headerTop").css("background","inherit")
                }
            },
            changeSelect(data){
                switch (data){
                    case "":
                        this.orderType='';
                        break;
                    case "price":
                        if(this.orderType===""){
                            this.orderType="priceAsc";
                        }else {
                            if(this.orderType==="priceAsc"){
                                this.orderType="priceDesc";
                            }else {
                                this.orderType="priceAsc";
                            }
                        }
                        break;
                    case "screen":

                        break;
                    default:
                        this.orderType='';
                        break;

                }
            }
        },
    });
    $(function() {
        refreshCartConut();
        $("#id_filter").click(function(){
            $("#startPrice1").val("");
            $("#endPrice1").val("");
            $("#search_condition").show().children(".search_condition_filter_section").animate({right:0});
            $(".goods_list_section").addClass("hidden");
        });
        $(".popbg .close").click(function(){
            closeCondition();
        });
    })
    //刷新购物车数量
    // function refreshCartConut() {
      //  jQuery.post("${base}/cart/quantity.jhtml", "", function(data) {
    //         if (data != null) {
    //             if (data.totalQuantity > 99) {
    //                 $("#cart_quantity").append('99');
    //             } else {
    //                 $("#cart_quantity").html(data.totalQuantity);
    //             }
    //
    //         }
    //     }, "json");
    // }
    //关闭筛选
    function closeCondition(){

        $(".search_condition_filter_section").animate({
            "right":-right_filter_w
        },function(){
            setTimeout('$("#search_condition").hide()',500);
            $(".goods_list_section").removeClass("hidden");
        });
    }
</script>

</body>
</html>
