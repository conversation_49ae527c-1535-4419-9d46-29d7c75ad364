<!DOCTYPE html>
<html style="background: #fff;">
	<head>
		<meta charset="utf-8" />
		<title>选择白条</title>
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="black">
        <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js" ></script>
        <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
        <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/plusWap.css" /> 
		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/jquery.validate.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
		
		<script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js" ></script>
		<script>
			$(function(){
				//输入密码事件
				$("#password").keyup(function(){
					var l_pwd = this.value.length;
					if(l_pwd>=1 && l_pwd <= 6 && event.keyCode!=8){
						var _input = document.getElementById("number"+l_pwd);
						_input.value =  this.value.charAt(l_pwd-1);
						if(l_pwd == 6){//输入完成动作
							var _pwd=this.value;
							$("#paymentPop").hide();
							this.blur();
							
							var $checkedIds = $("#rechargeRecords input[name='coinIds']:enabled:checked");
							$.ajax({
								url: "check_paypassword.jhtml",
								type: "POST",
								data: $checkedIds.serialize()+"&paypassword="+_pwd,
								dataType: "json",
								cache: false,
								success: function(message) {
									$(".password_section input").val("");
									if (message.type == "success") {
										$("#orderRefreshForm").submit();
									} else {
										 layer.open({
											    content: message.content
											    ,skin: 'msg'
											    ,time: 2 //2秒后自动关闭
											  });
									}
								}
							});
							
						}
					}else if(event.keyCode==8){//退格键删除
						var _input = document.getElementById("number"+(l_pwd+1));
						_input.value = '';
					}
				})
				//点击输入框密码框获取焦点
				$(".submit_section .btn_submit_long").click(function(){
					/*var $checkedIds = $("#rechargeRecords input[name='coinIds']:enabled:checked");
					if($checkedIds.filter(":checked").size()>0){
						$("#paymentPop").show().find("#password").focus();
					}
					else{
						$("#orderRefreshForm").submit();
					}*/
					$("#orderRefreshForm").submit();
				})
				//点击输入框获取焦点
				$(".password_section .input_box").click(function(){
					$("#password").focus();
				})
				
				$(".popbg .icon_close").click(function(){
					$(this).parents(".popbg").hide();
					$(".password_section input").val("");
				})
				
				$(".btn_concel").click(function(){
				   $("#paymentset").hide();
			    });
				
				//新增penglong20161223 积分显示合并
				$("#selectAll").click( function() {
					var $this = $(this);
					var $enabledIds = $("#rechargeRecords input[name='whiteBarIds']:enabled");
					if ($this.prop("checked")) {
						$enabledIds.prop("checked", true);
					} else {
						$enabledIds.prop("checked", false);
					}
				});
				//新增结束
				
				var $submitBtn = $(":submit");
				
				// 表单验证
				$("#inputForm").validate({
					rules: {
						payPassword: {
							required:true,
							digits:true,
							minlength: 6,
							maxlength: 6
						},
						rePayPassword: {
							required:true,
							digits:true,
							minlength: 6,
							maxlength: 6,
							equalTo:"#payPassword"
						}
					},
					messages: {
						payPassword: {
							pattern: "格式错误,请输入六位数字密码",
							digits: "格式错误,请输入六位数字密码"
						},
						rePayPassword: {
							pattern: "格式错误,请输入六位数字密码",
							digits: "格式错误,请输入六位数字密码"
						}
					},
					submitHandler:function(form){
						$.ajax({
							url:'${base}/member/payPasswordSet.jhtml',
							type:'POST',
							data:$("#inputForm").serialize(),
							cache:false,
							async:false,
							beforeSend:function(){
								$submitBtn.prop("disabled", true);
							},
							success:function(flag){
								if(flag == 1){
									layer.open({
									    content: '设置成功'
									    ,skin: 'msg'
									    ,time: 2 //2秒后自动关闭
									  });
									 $("#paymentset").hide();
								
								}else if(flag == 2){
									layer.open({
									    content: '两次输入的支付密码不一致'
									    ,skin: 'msg'
									    ,time: 2 //2秒后自动关闭
									  });
									$submitBtn.prop("disabled", false);
								}else{
									layer.open({
									    content: '设置失败'
									    ,skin: 'msg'
									    ,time: 2 //2秒后自动关闭
									  });
									$submitBtn.prop("disabled", false);
								}
							}
						});
					}
					
				});
				
			})
			
			function ticketTab(flag,obj){
				$(".coin").removeClass("active");
				obj.addClass("active");
				if(flag){
					$("#unRechargeRecords").css('display', 'none');
					$("#rechargeRecords").css("display","block");
					$(".public_page_top_tips").css('display', 'none');
				}
				else{
					$("#rechargeRecords").css('display', 'none');
					$("#unRechargeRecords").css("display","block");
					$(".public_page_top_tips").css('display', 'block');
				}
			}

			function payPassword(){
				$("#paymentPop").hide()
				$("#paymentset").show();
	          }
			
			
		</script>
		
	</head>
	<body class="coupon_list_new_page order_select_coin_common" style="background: #fff;" >
		<div class="paymentPage">
			<div class="public_top_header">
                <span class="return_back" onclick="javascript:history.go(-1);"></span>
				白条支付
			</div>
			<div style="display:none" class="public_page_top_tips" [#if member.companyId.minimumWhiteBar==null || member.companyId.minimumWhiteBar<=0]style="display:none"[/#if]>
				提示：白条支付额单次需大于等于${coinConvert(member.companyId.minimumWhiteBar, coinShowRate!1)}
			</div>
			
			<div class="make_split">
				<div class="tab_theme_section">
			 		<div class="item coin active" onclick="ticketTab(true,$(this))"><a href="javascript:;">可用</a></div>
			 		<div class="item coin" onclick="ticketTab(false,$(this))"><a href="javascript:;">不可用</a></div>
			 	</div>
			</div>
		 	
		 	<form id="orderRefreshForm" action="[#if train??&&train.trainNumber??]info_virtual.jhtml[#else]groupInfo.jhtml[/#if]" method="post">
		 	     <input type="hidden" name="ids" value="${ids}" />
		 	     <input type="hidden" name="coinIds"  value="${coinIds}" />
	             <input type="hidden" name="receiverId"  value="${receiverId}" />  
	             <input type="hidden" name="couponCodeId" id="couponCodeId" value="${couponCodeId}" />  
	             <input type="hidden" name="memo" value="${memo!}" />
	             <input type="hidden" name="groupId" value="${groupId}" />
	             <input type="hidden" name="pId" value="${productId}" />
	             <input type="hidden" id="quantity"  name="quantity" value="${quantity}"/>
	             <input type="hidden" id="orderJson"  name="orderJson" value="${orderJson}"/>
	             <input type="hidden" name="mobile" value="${mobile}" />
	             <input type="hidden" name="type" value="${type}" />
	             <input type="hidden" id="gasType" name="gasType" value="${gasType}" />
				<input type="hidden" id="gasCard" name="gasCard" value="${gasCard}" />
				<input type="hidden" id="userName" name="userName" value="${userName}" />
				<input type="hidden" id="phone" name="phone" value="${phone}" />
				<input type="hidden" id="bookingResult" name="bookingResult" value="${bookingResult}" />
				<input type="hidden" name="from" value="${train.from}"/>
				<input type="hidden" name="to" value="${train.to}"/>
				<input type="hidden" name="date" value="${train.date}"/>
				<input type="hidden" name="trainNumber" value="${train.trainNumber}"/>
				<input type="hidden" name="passagers" id="passagers" value="${train.passagers}"/>
				<input type="hidden" name="itemIdInsur" id="itemIdInsur"  value="${train.itemIdInsur}"/>
				<input type="hidden" name="startTime" value="${train.startTime}" />
				<input type="hidden" name="runTime" value="${train.runTime}" />
				<input type="hidden" name="endWeek" value="${train.endWeek}" />
				<input type="hidden" name="endDate" value="${train.endDate}" />
				<input type="hidden" name="week" value="${train.week}" />
				<input type="hidden" name="endTime" value="${train.endTime}" />
				<input type="hidden" name="contactName" value="${train.contactName}" />
				<input type="hidden" name="contactTel" value="${train.contactTel}" />
				<input type="hidden" name="trainInsurePrice" value="${train.trainInsurePrice}" />
				<input type="hidden" name="userSave" value="${train.userSave}" />
				<input type="hidden" name="serviceCharge" value="${train.serviceCharge}" />
				<input type="hidden" name="passagers1"  id="passagers1" value="${train.passagers1}"/>
				<input type="hidden" name="agentMemberId" value="${agentMemberId}"/>
				<input type="hidden" name="isPublic" value="${isPublic}"/>
				<input type="hidden" name="thirdCoinAmount" value="${thirdCoinAmount}" />
				<input type="hidden" id="coinTypeIds"  name="coinTypeIds" value="${coinTypeIds}" />
				[#if tripSnapshot?? ]
					<input type="hidden" name="applyId" value="${tripSnapshot.applyId}"/>
					<input type="hidden" name="tripType" value="${tripSnapshot.tripType}"/>
					<input type="hidden" name="serviceInfo" value="${tripSnapshot.serviceInfo}"/>
					<input type="hidden" name="miniPriceFlight" value="${tripSnapshot.miniPriceFlight}"/>
					<input type="hidden" name="tripReasonCode" value="${tripSnapshot.tripReasonCode}"/>
					<input type="hidden" name="tripRuleBroken" value="${tripSnapshot.tripRuleBroken}"/>
					<input type="hidden" name="tripReason" value="${tripSnapshot.tripReason}"/>
					<input type="hidden" name="tripMemo" value="${tripSnapshot.tripMemo}"/>
					<input type="hidden" name="isBreak" id="isBreak" value="${tripSnapshot.isBreak}"/>
				[/#if]	
		 	<div class="coin_pay_section" id="rechargeRecords"> 
		 	[#if (whiteBarRecords?size > 0)]
		 	    <div class="item">
		 			<a href="javascript:;">
		 				  白条余额
		 				<div class="right">${coinConvert(whiteBarYAmount, coinShowRate!1)}
		 					<div class="radio_section">
					 			<input type="checkbox"   id="selectAll" [#if whiteBarIds1?size > 0]checked="checked"[/#if] >
					 			<label for="selectAll"></label>
					 		</div>
		 				</div>
		 			</a>
		 		</div>
		 		
		 		<p class="white_bar_intro">
		 			提示：白条为企业授予个人的信用额度，白条消费金额将由企业月度核算，并从个人待遇中直接扣减。
		 		</p>
		 		
		 		[#list whiteBarRecordsY as rechargeRecord]
		 		<div class="item" style="display: none;">
		 			<a href="javascript:;">
		 				${rechargeRecord.coinTypeId.name}<span>(有效期：[#if rechargeRecord.isLimitExpired]${rechargeRecord.beginTime} — ${rechargeRecord.endTime}[#else]长期有效 [/#if])</span>
		 				<div class="right">${coinConvert(rechargeRecord.balance, coinShowRate!1)}
		 					<div class="radio_section">
					 			<input type="checkbox"  name="whiteBarIds"  id="coin${rechargeRecord_index}" value="${rechargeRecord.id}"  [#if whiteBarIds1?seq_contains(rechargeRecord.id)]checked="checked"[/#if]>
					 			<label for="coin${rechargeRecord_index}"></label>
					 		</div>
		 				</div>
		 			</a>
		 		</div>
		 		[/#list]
		 		[#else]
					<div class="public_empty_section [#if comPersonConf && com_person_EmptyBg != null && com_person_EmptyBg != ""] ${com_person_EmptyBg!}[#else] Public_Bg[/#if]"> <p class="tip">这里空空如也</p></div>
				[/#if]	
		 	</div>
		   </form>
		 	
		 	<div class="coin_pay_section" id="unRechargeRecords" style="display: none;"> 
		 		[#if (whiteBarRecordsN?size > 0)]
		 		 <div class="item">
		 			<a href="javascript:;">
		 				  白条余额
		 				<div class="right">${coinConvert(whiteBarNAmount, coinShowRate!1)}
		 					<div class="radio_section">
					 			<input type="checkbox"  name="coinIds" id="coinnotall"  [#if whiteBarIds1?size > 0]checked="checked"[/#if] >
					 			<label for="coinnotall"></label>
					 		</div>
		 				</div>
		 			</a>
		 		</div>
		 		[#list whiteBarRecordsN as rechargeRecord]
		 		<div class="item" style="display: none;">
		 			<a href="javascript:;">
		 				${rechargeRecord.coinTypeId.name}<span>(有效期：[#if rechargeRecord.isLimitExpired]${rechargeRecord.beginTime} — ${rechargeRecord.endTime}[#else]长期有效 [/#if])</span>
		 				<div class="right">${coinConvert(rechargeRecord.balance, coinShowRate!1)}
		 					<div class="radio_section">
					 			<input type="checkbox" id="coin${rechargeRecord_index}" value="${rechargeRecord.id}" name="coin">
					 			<label for="coin${rechargeRecord_index}"></label>
					 		</div>
		 				</div>
		 			</a>
		 		</div>
		 		[/#list]
		 		[#else]
					<div class="public_empty_section [#if comPersonConf && com_person_EmptyBg != null && com_person_EmptyBg != ""] ${com_person_EmptyBg!}[#else] Public_Bg[/#if]"> <p class="tip">这里空空如也</p></div>
				[/#if]	
		 	</div>
		</div> 
		[#--<div class="submit_section">--]
	 		[#--<input type="button" name="" id="" value="确定" class="btn_submit_long"/>--]
	 	[#--</div>--]
        <div class="btn-option submit_section flexbox align-items-c justify-content-space-between">
            <input type="button" name="" id="" value="确定" class="btn_submit_long btn-option-right"/>
        </div>
		<div class="popbg" id="paymentPop">
			<div class="close"></div>
			
		 	<div class="pop">
		 		<header>输入支付密码<span class="icon_close"></span></header>
			 	<div class="password_section">
			 		<div class="input_box clearfix" id="inputs_box">
				 		<input type="number" id="number1" readonly="readonly"/>
				 		<input type="number" id="number2" readonly="readonly"/>
				 		<input type="number" id="number3" readonly="readonly"/>
				 		<input type="number" id="number4" readonly="readonly"/>
				 		<input type="number" id="number5" readonly="readonly"/>
				 		<input type="number" id="number6" readonly="readonly"/>
			 		</div>
			 		<input type="tel" name="" id="password" value="" maxlength="6" class="pwd"/>
			 		
			 		[#if member.payPassword]
						<a href="${base}/payPassword/find.jhtml?type=2" class="forgotton">忘记密码？</a>
					[#else]
					    <a href="javascript:void(0)" onclick="payPassword()" class="forgotton">设置密码</a>
					[/#if]
					
			 		
			 	</div> 
			 	<!-- <div class="footer">
			 		<input type="button" name="" id="" value="确定" class="btn_submit"/>
			 		<input type="button" name="" id="" value="取消" class="btn_concel"/>
			 	</div> -->
		 	</div>
		</div>
		<div class="popbg" id="paymentset">
			<div class="close"></div>
			<div class="pop">
				<form id="inputForm" action="payPasswordSet.jhtml" method="post">
					<header>设置支付密码<span class="icon_close"></span></header>
					<div class="fli_pub_pop_form">
						<div class="item">
							<input type="tel" name="payPassword" id="payPassword" class="input pwd" placeholder="请输入新密码"/>
						</div>
	
						<div class="item">
							<input type="tel" name="rePayPassword" id="rePayPassword" class="input pwd"  placeholder="请确认新密码"/>
						</div>
					</div>
					<div class="footer">
						<input type="button" name="" id="" value="取消" class="btn_concel" />
						<input type="submit" name="" id="payPasswordSetBtn" value="确定" class="btn_submit" />
					</div>
				</form>
			</div>
		</div>
		
	</body>  
</html>
