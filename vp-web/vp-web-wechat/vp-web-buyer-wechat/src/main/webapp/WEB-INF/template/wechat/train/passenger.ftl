<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<title>交通差旅</title>
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">
		<script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/lifeService.css" />
		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
		<script src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js"></script>
	</head>
	<body class="has_fixed_footer" style="position:relative">
	      <div class="fixedRight">
	            <div class="active">A</div>
	            <div>B</div>
	            <div>C</div>
	            <div>D</div>
	            <div>E</div>
	            <div>F</div>
	            <div>G</div>
	            <div>H</div>
	            <div>I</div>
	            <div>J</div>
	            <div>K</div>
	            <div>L</div>
	            <div>M</div>
	            <div>N</div>
	            <div>O</div>
	            <div>P</div>
	            <div>Q</div>
	            <div>R</div>
	            <div>S</div>
	            <div>T</div>
	            <div>U</div>
	            <div>V</div>
	            <div>W</div>
	            <div>X</div>
	            <div>Y</div>
	            <div>Z</div>
	            <div>#</div>
           </div>
	     
		<div class="public_top_header">
			<a href="javascript:;" class="return_back"></a> 
			乘客列表
		</div>
		
		<div class="memberListPages">
			<a href="javascript:;" class="btn_add"><span class="icon_plus"></span>新增乘客</a>
			<ul>
			[#assign preNamePy=""]
			[#if passengers??&&passengers?size>0]
				[#list passengers as passenger]		
				 <div class="word-section">
				 	[#if preNamePy!=passenger.transientNamePy]
					 	[#assign preNamePy=passenger.transientNamePy]
					    <header>${passenger.transientNamePy}</header>
					[/#if]
					<li>
						<div class="fli_checkbox_blue">
							<input type="checkbox" name="ids" id="id${passenger.id}" value="${passenger.id}" [#if ids?index_of(","+passenger.id+",")!=-1] class="default" checked="checked" [/#if]
							passengerCardType="${passenger.cardType}" passengerIdcardNo="${passenger.idcardNoDec}"   passengerName="${passenger.name}">
							<label for="id${passenger.id}"></label>
						</div>
						<div class="info">
							<p>${passenger.name}</p>
							<p>身份证 ${passenger.idcardNoDec}</p>
						</div>
						<div class="fixed_oparation">
							<div class="icon_minus" passengerId="${passenger.id}"></div>
						</div>
					</li>
					</div>
				[/#list]
			[/#if]	
			</ul>
			
			<div class="fixed_bottom_settlement">
	 			<button class="btn_submit">完成</button>
	 			<div class="payment">
	 				<p>选择人数：<span id="count">0人</span></p>
	 				<p class="text_gray">最多选择5名乘客</p>
	 			</div>
	 			<label class="FreightBox">			
		 		         <img src="${base}/resources/wechat/img/angle-down.png" >
		 		         <span class="txt">已选乘客</span>		   
		 		     </label>
	 		</div>
	 		
	 		<div class="FreightMask">
		        <div class="content">
		           
		            <ul class="lists">
		                
		            </ul>
		        </div>
           </div>
	 		
	 		
		</div>
		<script>
		$(function(){
			
			$(".return_back").click(function(){
				var ids=""
					$(".default").each(function(){
						ids+="&ids="+$(this).val();
					});
					[#if applyId?? ]
						location.href="purchase.jhtml?applyId=${applyId}&to=${to}&from=${from}&date=${date}&trainNumber=${trainNumber}&seatId=${seatId}&isInsurance=${isInsurance}"+ids;
					[#else]
						location.href="purchase.jhtml?to=${to}&from=${from}&date=${date}&trainNumber=${trainNumber}&seatId=${seatId}&isInsurance=${isInsurance}"+ids;
					[/#if]
			});
			$(".btn_add").click(function(){
				var ids=","
					$("input[name='ids']:checked").each(function(){
						ids+=+$(this).val()+",";
					});
					[#if applyId?? ]
						location.href="addPassenger.jhtml?applyId=${applyId}&to=${to}&from=${from}&date=${date}&trainNumber=${trainNumber}&seatId=${seatId}&isInsurance=${isInsurance}&ids="+ids;
					[#else]
						location.href="addPassenger.jhtml?to=${to}&from=${from}&date=${date}&trainNumber=${trainNumber}&seatId=${seatId}&isInsurance=${isInsurance}&ids="+ids;
					[/#if]
			});
			$(".btn_submit").click(function(){
				var ids=""
				$("input[name='ids']:checked").each(function(){
					ids+="&ids="+$(this).val();
				});
				[#if applyId?? ]
					location.href="purchase.jhtml?applyId=${applyId}&to=${to}&from=${from}&date=${date}&trainNumber=${trainNumber}&seatId=${seatId}&isInsurance=${isInsurance}"+ids;
				[#else]
					location.href="purchase.jhtml?to=${to}&from=${from}&date=${date}&trainNumber=${trainNumber}&seatId=${seatId}&isInsurance=${isInsurance}"+ids;
				[/#if]
			});
			$("#count").html($(":checkbox:checked").length+"人");
			$(":checkbox").click(function(){
				var length=$(":checkbox:checked").length;
				if(length>5){
					$(this).prop("checked",false);
					layer.open({
					    content: '最多添加5条乘客信息'
					    ,skin: 'msg'
					    ,time: 2 //2秒后自动关闭
					});
					return false;
				}
				$("#count").html(length+"人");
				var type="";
				if($(this).prop("checked")){
					type="add";
				}
				addSelectPassenger(type,$(this).attr("passengername"),$(this).attr("passengercardtype"),$(this).attr("passengeridcardno"))
			
			});
			$(":checkbox:checked").each(function (){
				var type="";
				if($(this).prop("checked")){
					type="add";
				}
				addSelectPassenger(type,$(this).attr("passengername"),$(this).attr("passengercardtype"),$(this).attr("passengeridcardno"))
			
			})
			$(".memberListPages .icon_minus").on("click",function(){
				var $this=$(this);
			 layer.open({
				    content: '确认要删除选中的联系人吗？'
				    ,btn: ['取消', '确定']
				    ,skin: 'footer'
				    ,no: function(index){
				    
				    	$.post("${base}/member/passenger/delete.jhtml",{id:$this.attr("passengerId")},function(data){
							if(data){
								if(data.type=="success"){
									$this.parent("li").remove()
								}else{
									layer.open({
									    content: data.content
									    ,skin: 'msg'
									    ,time: 2 //2秒后自动关闭
									  });
								}
							}else{
								layer.open({
								    content: "请重试"
								    ,skin: 'msg'
								    ,time: 2 //2秒后自动关闭
								  });
							}
						},"json");
						
					layer.close(index); 
				    }
			});
				
				
			})
			
			
			
			 $(".fixedRight div").on("click", function() {
		            var _text = $(this).html();
		            $(".has_fixed_footer").append("<div class='currentText'>" + _text + "</div>")
		            setTimeout("$('.currentText').remove()", 1000);
		            $(".word-section").each(function(i, v) {
		                $(v).children("header").html() == _text ? $(window).scrollTop(v.offsetTop) : false;
		            });
		        })
			  //运费明细弹窗
				 $('.fixed_bottom_settlement .FreightBox').click(function(e) {
                   $('.FreightMask').slideToggle(200);
                   $(this).find('img').hasClass('up') == true ? $(this).find('img').removeClass('up').addClass("down") : $(this).find('img').removeClass("down").addClass('up');
                })
				
                 $(".FreightMask").click(function(e){
                   if(e.target.className=='FreightMask'){
                	   $(".fixed_bottom_settlement .FreightBox").trigger("click")
                   } 
                 })
			
		})
		function addSelectPassenger(type,name,cardType,cardNo){
			var $li=$(".FreightMask .lists li[cardNo='"+cardNo+"']");
			if("add"==type){
				if($li.length<=0){
					var html=' <li class="item" cardNo="'+cardNo+'">';
						html+='<label class="name">'+name;
		                html+='</label>';
		                if("0"==cardType){
			                html+='<label class="cardType">身份证';
		                }else if("1"==cardType){
		                	  html+='<label class="cardType">护照';
		                }
		                html+='</label>';
		                html+='<span class="number">'+cardNo;
						html+='</span>';
						html+='</li>';
					$(".FreightMask .lists").append(html);
				}
			}else{
				$li.remove();
			}
			
		}
		</script>
	</body>
</html>
