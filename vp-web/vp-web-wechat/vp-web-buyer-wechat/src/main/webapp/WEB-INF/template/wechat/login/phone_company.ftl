<!DOCTYPE html>
<html style="background: #fff;">
	<head>
		<meta charset="utf-8" />
		<title>验证码登录</title>
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="black">
        <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js" ></script>
        <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
        <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/login.css" />
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/plugins/font-awesome-4.7.0/css/font-awesome.min.css" />
        <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/slide.css" />
		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/jquery.validate.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js" ></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/jsbn.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/prng4.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/base64.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/jquery.lgyslide.js"></script>
        <script type="text/javascript" src="${base}/resources/wechat/js/jigsawPuzzle.js"></script>
	</head>
	<script type="text/javascript">
		$().ready(function() {
			[@flash_message /]
		});
	</script>
	<body style="background: #fff;">

   <!-- <div class="public_top_header">
        <span class="return_back" onclick="javascript:window.history.go(-1);"></span>
    </div>-->

		<div class="loginPage"> 
			<div class="logo_section">
				<img src="[#if comPersonConf && com_person_wechatLoginTopLogo != null&& com_person_wechatLoginTopLogo != ""]${com_person_wechatLoginTopLogo }[#else]${base}/resources/wechat/img/fuli_logo.png[/#if]"/>
			</div>
			
			<div class="tab_section">
				验证码登录
			</div>
			
			<div class="login_form_section textLeft"> 
				<form id="inputForm" action="${base}/login/phoneLogin.jhtml" method="post" >
					<input type="hidden" name="companyId" value="${company.id}" id="companyId">
					<div class="item standard_input">
						<input type="tel" name="phone" id="phone" value="${phone}" placeholder="请输入手机号" class="input" onkeyup="javascript:checkWordPhone(this);" onmousedown="javascript:checkWordPhone(this);"/>
						<span class="icon_clear"></span>
						<div class="text_error2" id="closeTips4">
							该手机号码已达到当天次数上限
						</div>
					</div>
					<!--<div class="item hasbutton standard_input">
						<input type="text" name="captcha" id="captcha" value="" placeholder="请输入图形验证码" class="input" onkeyup="javascript:checkWord(this);" onmousedown="javascript:checkWord(this);"/>
						<span class="icon_clear"></span>
						<img id="captchaImage" class="captchaImage" src="${base}/common/captcha.jhtml?captchaId=${captchaId}" title="">
						<div class="text_error2" id="closeTips6">
							图形验证码错误
						</div>
					</div> -->
					<div class="item hasbutton standard_input long">
						<input type="tel" name="code" id="code" value="" placeholder="请输入短信验证码" class="input" onkeyup="javascript:checkWord(this);" onmousedown="javascript:checkWord(this);"/>
						<span class="icon_clear"></span>
						<input type="button" name="" id="emailCodeBtn" value="获取短信验证码" class="code"/>
						<!--禁用状态 disabled="disabled"-->
					</div>
					[#if errorType == "code"]
						<div class="text_error" style="display: block;">${errorMsg}</div>
					[/#if]
					<input type="submit" id="submit" value="登录" class="btn_cornor_long"/>
					<div  class="fli_checkbox_blue corner" >
						<input type="checkbox" id="agreement" name="agreement" />
						<label for="agreement"></label>
						已阅读并同意<a href="${base}/member/secret_agreement.jhtml">《福利平台隐私权政策》</a>
					</div>
				</form>

		<!--		<div class="otherLoginWaySimple">
					<a href="javascript:location.href='${base}/login.jhtml?redirectUrl='+encodeURIComponent('${redirectUrl}')">
						账号密码登录
					</a>
				</div>-->

				<div class="otherLoginWay" style="display: none;">
					<header><span>其他登录方式</span></header>
					<div class="list">
						<a href="#" id="wechatLogin">
							<i class="fa fa-wechat"></i>
							<p>微信登录</p>
						</a>
						<a href="javascript:location.href='${base}/login.jhtml?isDirectFlag=1&redirectUrl='+encodeURIComponent('${redirectUrl}')">
							<i class="fa fa-lock"></i>
							<p>账号密码登录</p>
						</a>
					</div>
				</div>
			</div>
		</div>

	<script type="text/html" id="slider_code">
		<div id="imgscode"></div>
	</script>

		<script type="text/javascript">
			var interval;
			$(function(){
			// var $captcha = $("#captcha");
			//以下两个方法是为了去掉前后空格
			$("input[name='phone']").blur(function(){
				$(this).val($(this).val().trim());
			});
			jQuery.validator.addMethod("phoneFormat",function(value,element,param){
					value = value.trim();
					var regex = commonRule.phone;
				  	if (regex.test(value)){
				  		return true;
				  	}else{
				        return false;
				  	}
		        },$.validator.format("手机号码格式错误")  
		    );
				// 表单验证
				$("#inputForm").validate({
					rules: {
						phone: {
							required:true,
							phoneFormat: true,
							maxlength: 11
						},
						code: {
							required:true,
							digits:true
						}
					},
					messages: {
						phone: {
							pattern: "手机号码不存在"
						}
					},
					submitHandler: function(form) {
						if(!$("#agreement").prop("checked")){
	                        layer.open({
	                            content:'请阅读并同意《福利平台隐私权政策》'
	                            ,skin: 'msg'
	                            ,time: 2
	                        })
	                        return false;
	                    }
                        //清除本地cookie 和 本地存储
                        deleteCookie();
                        window.localStorage.clear();
						$("#submit").prop("disabled", true);
						$.ajax({
							url:$("#inputForm").attr("action"),
							type: "POST",
							data: {
								phone: $("#phone").val(),
								phoneCode: $("#code").val()
							},
							dataType: "json",
							cache: false,
							success: function(message) {
								$("#submit").prop("disabled", false);
								if (message.type == "success") {
								    var device=openDevice();
								    //登陆成功，判断是否是android app。是调用android原生方法传入用户名用于android端将用户名和设备绑定
								    try{
								        if(device=="android") { //android打开
									       vpshop_android.userLogin($username.val());
									    }
                                        if(device=="ios"){ //ios打开
										   var url = 'ios://userLogin?username='+$username.val();
										   loadURL(url);
                                            function loadURL(url) {
                                                var iFrame;
                                                iFrame = document.createElement("iframe");
                                                iFrame.setAttribute("src", url);
                                                iFrame.setAttribute("style", "display:none;");
                                                iFrame.setAttribute("height", "0px");
                                                iFrame.setAttribute("width", "0px");
                                                iFrame.setAttribute("frameborder", "0");
                                                document.body.appendChild(iFrame);
                                                // 发起请求后这个 iFrame 就没用了，所以把它从 dom 上移除掉
                                                iFrame.parentNode.removeChild(iFrame);
                                                iFrame = null;
                                            }

									   }
									}catch(e){

									}

                                    var categoryAll=getCookie("categoryAll");
								    if(categoryAll!=null && categoryAll=='0'){
								        removeCookie("categoryAll");
                                        location.href = "${base}/index.jhtml";
									}else{
								        [#if redirectUrl??]
										location.href = "${redirectUrl?replace('[\"|;|<|>|(|)]','','ri')}";
									[#else]
										location.href = "${base}/index.jhtml";
									[/#if]
									}
								} else {
                                    if ("shop.login.unSmsCodeError" == message.content || "shop.login.smsCodeError"== message.content) {
                                        window.location.href="${base}/login/toVerifyFlag.jhtml?username="+$("#username").val()+"&password="+encodeURIComponent(enPassword);
                                    }else{
                                        layer.open({
                                            content: message.content
                                            ,skin: 'msg'
                                            ,time: 2 //2秒后自动关闭
                                        });
									}
								}
							}
						});
					}
				});
				
				$("#emailCodeBtn").click(function(){
					var phone = $("#phone").val();
					if(phone == ""){
						layer.open({
						    content: '请输入手机号码'
						    ,skin: 'msg'
						    ,time: 2 //2秒后自动关闭
						  });
						return false; 
					}
				    if(!(commonRule.phone.test(phone))){
				    	layer.open({
						    content: '请输入正确的手机号码'
						    ,skin: 'msg'
						    ,time: 2 //2秒后自动关闭
						  });
				        return false; 
				    }

					// if($captcha.val() == null || $captcha.val() == ""){
				    // 	layer.open({
					// 	    content: "请输入图形验证码"
					// 	    ,skin: 'msg'
					// 	    ,time: 2 //2秒后自动关闭
					// 	  });
				    //     return false;
					// }
					if($(this).val() == "获取短信验证码"){
						layer.open({
							type: 1,
							title: false,
							closeBtn: 0,
							resize: false,
							scrollbar: false,
							area: ['auto', '420px'], //宽高
							skin:"layer-code",
							content: $("#slider_code").html(),
							success: function () {
								createcode(sendPhoneCode);
							}
						});
					}
				});

				// 更换验证码
				//$("#captchaImage").click(function() {
				//	$("#captchaImage").attr("src", "${base}/common/captcha.jhtml?captchaId=${captchaId}&timestamp=" + (new Date()).valueOf());
				//});
			});
            //发送短信验证码
            function sendPhoneCode() {
                var phone = $("#phone").val();
				var companyId = $("#companyId").val();
                $.ajax({
                    url: shop.base+'/login/sendPhoneCode.jhtml',
                    type:'POST',
                    data:{
                        phone: phone,
						companyId: companyId
                    },
                    cache:false,
                    async:false,
                    success:function(flag){
                        $("#closeTips4").hide();
                        // $("#closeTips6").hide();
                        if(flag == 0){
                            //验证码发送成功
                            emailCodeBtnVal();
                            interval = setInterval("emailCodeBtnVal()", 1000);
                        }else if(flag == 1){
                            layer.open({
                                content: '60秒内只允许发送一次'
                                ,skin: 'msg'
                                ,time: 2 //2秒后自动关闭
                            });
                        }else if(flag == 6){
                            $("#closeTips4").show();
                        }
                        // else if(flag == 7){//图形验证码错误
                        // 	$("#closeTips6").show();
                        // }
                        else if (flag == 9){
                            layer.open({
                                content: "手机号不存在，请联系企业管理员！",
                                skin: 'msg',
                                time: 2
                            });
                        } else{
                            //验证码发送失败
                            layer.open({
                                content: '短信验证码发送失败'
                                ,skin: 'msg'
                                ,time: 2 //2秒后自动关闭
                            });
                        }
                    }
                });
            }
            // 清除所有的cookie
            function deleteCookie() {
                var cookies = document.cookie.split(";");
                for(var i=0; i < cookies.length; i++) {
                    var equals = cookies[i].indexOf("=");
                    var name = equals > -1 ? cookies[i].substr(0, equals) : cookies[i];
                    document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT";
                }
            }
			//发送验证码按钮显示秒数递减
			function emailCodeBtnVal(){
				if($("#emailCodeBtn").val() == "获取短信验证码"){
					$("#emailCodeBtn").removeClass("active");
					$("#emailCodeBtn").val("59");
				}else if($("#emailCodeBtn").val() == "1"){
					clearInterval(interval);
					$("#emailCodeBtn").addClass("active");
					$("#emailCodeBtn").val("获取短信验证码");
				}else{
					$("#emailCodeBtn").val(Number($("#emailCodeBtn").val()) - 1);
				}
			}
			
			//手机号码隐藏错误信息
			function checkWordPhone(){
				$("#closeTips").hide();
				$("#closeTips1").hide();
				$("#closeTips2").hide();
				$("#closeTips4").hide();
				$("#closeTips3").hide();
			}
			
			//验证码隐藏错误信息
			function checkWord(){
				$(".text_error").hide();
			}


		</script>
	</body>  
</html>
