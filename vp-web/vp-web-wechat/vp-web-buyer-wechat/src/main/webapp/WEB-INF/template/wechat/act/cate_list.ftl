<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>商品分类</title>
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/plusWap.css" />
    <script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

    <script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
</head>
<body class="has_fixed_footer cate-list-page-new cate-list-page-new-zj">
	<div class="public_top_header" id="menuheader_obj">
        <a href="javascript:;" class="return_back"></a>
		<span class="titleName">商品分类</span>
        	[#include "./wechat/include/head_nav.ftl" /]
	</div>
	<div class="classify-page">
		<div class="displayBox productListPage" id="CscrollSection">
			<!--将右侧内容代码添加到此处-->
		 	<div class="cateList cateItemList">
			[#if list?size>0]
		 	[#list list as category]

		 		<div class="cateItem" >
					<a class="title flexbox align-items-c justify-content-space-between" href="javascript:;">
						<span class="category-name">${category.productCategoryName}</span>
                        <span class="changeChild" data-type="0">
                           <img   src="${base}/resources/wechat/img/icon_arrow_down.png"/>
                        </span>
					</a>
		 			<div class="list">
		 			[#list category.productCategoryList as productCategory1]
                            <a class="item" data-id="${productCategory1.productCategoryId}"  data-name="${productCategory1.productCategoryName}" href="javascript:;">
                                ${productCategory1.productCategoryName}
                            </a>
					[/#list]
		 			</div>
		 		</div>
				[/#list]
			  [/#if]
	    	</div>
            <footer class="clearfix">
                <input type="reset" name="" id="res" value="重置" /> <input type="button" name="" id="btn" value="确定" form="searchActProduct" />
            </footer>
		</div>
        <div class="search-section-list" style="display:none">
            <form id="searchActProduct" action="${base}/act/search.jhtml" method="post">
                <input type="hidden" name="actId" value="${actId}">
            </form>
        </div>
	</div>

	<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
	<script type="text/javascript" src="${base}/resources/wechat/js/dropload.min.js"></script>
	<!-- 列表页面缓存数据 -->
	<script type="text/javascript" src="${base}/resources/wechat/js/listPageSessionStorage.js"></script>
	<script type="text/javascript">
		var baseUrl = "${base}";
		var srcUrl = "${setting.siteUrlAdmin}";
        var arrIds=[];
        var arrNames =[];
		$(function() {

		    $(".return_back").on("click",function () {
          		    var backUrl = localStorage.getItem('zjActPage');
					try {
						window.location.href = backUrl;
					}catch (e) {
						history.go(-1);
					}
            });
            $("body").on('click', "#res", function() {//重置
                $(".cateList  .item").removeClass("active");
            });
			//点击确认
            $("body").on('click', "#btn", function() {
                var select = $(".cateList ").find(".item.active");
                arrIds= [];
                arrNames = [];
                if(select.length > 0){ //有选中的分类
                    select.map(index=>{
                        arrIds.push($(select[index]).attr("data-id"));
                        arrNames.push($(select[index]).attr("data-name"));
                    })
                }
                try {
                    var screenList = {
                        "orderType":"",
                        "productCategoryIds":arrIds.join(","),
                        "productCategoryNames":arrNames.join(","),
                        'keyword': "",
                        'brand': "",
                        'minPrice':"",
                        'maxPrice':"",
                    };
                    screenListStr = JSON.stringify(screenList);
                    //存储筛选条件
                    localStorage.setItem('screenList',screenListStr);
                   $("#searchActProduct").submit();
                }catch (e) {

                }
            });
            //选择分类
            $(".cateList ").on("click", ".item", function() {
                if($(this).hasClass("active")) {//取消选中
                    $(this).removeClass("active")
                }else {//选中
                    $(this).addClass("active");
                }
            })
            // 分类点击二级分类
            $(".classify-page").on("click", ".changeChild", function() {
                var dataType = $(this).attr("data-type"); //0是已展开 1是已收起
                var childs = $(this).parent().parent().find(".list");
                if(dataType==0){
                    $(this).attr("data-type",1);
                    $(this).addClass("imgUp");
                    //需要把下面的内容收起

                }else {
                    $(this).attr("data-type",0);
                    $(this).removeClass("imgUp")
                }
                childs.slideToggle('slow')
            })


		})

		//设置左边分类名位置
		function setCatScrollTop() {
			var _element = $(".classify-list-section .active");
			var h_element = _element.height();
			var left_scroll_top = _element.offset().top;
			$(".classify-list-section").scrollTop(
					left_scroll_top - 2 * h_element);
		}
		
	
	</script>

</body>
</html>
