<!DOCTYPE html>
<html lang="en" data-dpr="1" style="font-size: 16px;">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>搜索</title>
    <link href="https://cdn.bootcss.com/font-awesome/4.7.0/css/font-awesome.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css">
    <link rel="stylesheet" href="${base}/resources/wechat/css/qunaer-wechat.css">
    <script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

    <script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>

</head>

<body class="no_fixed_top">
    <div class="qunaerpage">
        <div class="searchPage">
            <header>
                <i class="return_back" onclick="javascript:history.back(-1);"></i>
                <div class="inputBox left">
                    <input type="text"  id="keywords"  placeholder="请输入城市名或者景点名" class="seacherinput">
                </div>
                <input type="button" value="搜索" onclick="search()" class="btn right">
            </header>
            <article>
                <div class="title">
                    热门搜索
                </div>
                <div class="hotsights listsContainer">
                    <div class="icon left">
                        <img src="${base}/resources/wechat/img/qunaer/red_icon.png" alt="">
                    </div>
                    <ul class="left sights lists">
                    	[#list hotList as sight]
                       		 <li class="item" onclick="toDetail(${sight.sightId})">${sight.name}</li>
                        [/#list]
                    </ul>
                </div>
                <div class="hotcitys listsContainer">
                    <div class="icon left">
                        <img src="${base}/resources/wechat/img/qunaer/yellow_icon.png" alt="">
                    </div>
                    <ul class="left citys lists">
                        <li class="item">北京</li>
                        <li class="item">上海</li>
                        <li class="item">广州</li>
                        <li class="item">深圳</li>
                        <li class="item">长沙</li>
						<li class="item">南京</li>
						<li class="item">杭州</li>
						<li class="item">成都</li>
						<li class="item">重庆</li>
						<li class="item">武汉</li>
						<li class="item">西安</li>
						<li class="item">哈尔滨</li>
                    </ul>
                </div>
            </article>

        </div>

    </div>
</body>

</html>
<script>
    //   设置搜索列表的省略的样式
    function fixWidth(selector) {
        var width = 0;
        selector.find(".lists .item").each(function(index, ele) {
            width += $(this).width();
            var length = $(this).siblings().length + 1;
            var average = width / length;
            index >= (length - 1) ? sizewidth(average, $(this)) : false;
        })

        function sizewidth(average, $this) {
            $this.parent().children().each(function(index, ele) {
                $(this).width() > (average * 1.1) ? $(this).width(average * 1.25) : false;
            })
        }
    }
    //fixWidth($('.listsContainer').eq(0));
    //点击选择城市
    $('.citys .item').on('click',
        function(){
        	window.location.href='sightsfromindex.jhtml?homecity_name='+$(this).text();
    });
    
    //查询
    function search(){
    	var keywords = $("#keywords").val();
    	if(keywords){
    		window.location.href='sights.jhtml?keywords='+keywords+'&homecity_name=${homecity_name}';
    	}else{
    		window.location.href='sights.jhtml?homecity_name=${homecity_name}';
    	}
    }
    
    //跳转详情
  	function toDetail(sightId){
  		location.href="${base}/tickets/detail.jhtml?sightid="+sightId;
  	}
    
</script>