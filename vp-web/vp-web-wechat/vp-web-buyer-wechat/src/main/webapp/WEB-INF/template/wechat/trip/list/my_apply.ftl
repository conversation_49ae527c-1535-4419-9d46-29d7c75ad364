<input type="hidden" id="totalPages" value="${(page.totalPages)!}"/>
[#if page.content?? && page.content?size > 0]
	[#list page.content as tripApply]
		<li class="item">
           <div class="title">
               <div class="left">
                   ${tripApply.createDate?string("yyyy-MM-dd HH:mm") }
               </div>
               		[#assign showClass = "" /]
               		[#assign status = "其他" /]
               		[#if tripApply.applyStatus == "wait_submit"]
               			[#assign showClass = "waitpost" /]
               			[#assign status = "待提交" /]
					[#elseif tripApply.applyStatus == "wait_review"]
						[#assign showClass = "waitcheck" /]
               			[#assign status = "待审核" /]
					[#elseif tripApply.applyStatus == "pass"]
						[#assign showClass = "success" /]
               			[#assign status = "已通过" /]
					[#elseif tripApply.applyStatus == "reject"]
						[#assign showClass = "fail" /]
               			[#assign status = "未通过" /]
					[#elseif tripApply.applyStatus == "revoke"]
						[#assign showClass = "cancel" /]
               			[#assign status = "已撤销" /]
					[#elseif tripApply.applyStatus == "finish_reject"]
               			[#assign showClass = "cancel" /]
               			[#assign status = "已撤销" /]
               		[#elseif tripApply.applyStatus == "finish"]
						[#assign showClass = "success" /]
               			[#assign status = "已完成" /]
					[#else]
						[#assign showClass = "" /]
               			[#assign status = tripApply.applyStatus! /]
					[/#if]
               
               <div class="right ${showClass! }" id="status_${tripApply.id }">
                   ${status! }
               </div>
           </div>
           <div class="info">
               <div href="${base}/trip/view.jhtml?id=${tripApply.id }" class="left">
                   <h4>
                   	[#if tripApply.applyType == "0"]
							拜访客户
					[#elseif tripApply.applyType == "1"]
							项目出差
					[#elseif tripApply.applyType == "2"]
							外出培训
					[#elseif tripApply.applyType == "3"]
							参加会议
					[#elseif tripApply.applyType == "5"]
								加班用车                 
					[#elseif tripApply.applyType == "4"]
							其他
					[/#if]
                   </h4>
                   <p>
                   	<label>出差人员:</label>
                   	[#list tripApply.members as member]
					${member.name}[#if member_has_next]、[/#if]
					[/#list]
                   </p>
                   <p><label>出差行程:</label>${tripApply.tripCity! }</p>
                   <p><label>出差时间:</label>${tripApply.tripDate! }</p>
                   <p><label>预算:</label>${currency(tripApply.estimatedCost)! }</p>
                   <p><label>成本:</label>
                   		[#if tripApply.applyStatus == "finish" && tripApply.tripOrders??]
                   		<span [#if tripApply.estimatedCost != null && tripApply.tripOrders.cost != null && tripApply.estimatedCost lt tripApply.tripOrders.cost] style="color: red"[/#if]>${currency(tripApply.tripOrders.cost) }</span>
                   		[#else]
	                   <span [#if tripApply.estimatedCost != null && tripApply.cost != null && tripApply.estimatedCost lt tripApply.cost] style="color: red"[/#if]>${currency(tripApply.cost) }</span>
                   		[/#if]
	                </p>
                   [#if tripApply.applyStatus == "pass" || tripApply.applyStatus == "finish"]
                   <p><label>订单:</label>
                       <a class="airplane icon" href="${base}/plane/queryList.jhtml?applyId=${tripApply.id}&source=tripList">
                       		 [#if tripApply.applyType != "5" && tripApply.transport?? && tripApply.transport?split(",")?seq_contains("1")]<img src="${base}/resources/wechat/img/travelapply/airplane.png" alt="">[#else] <img src="${base}/resources/wechat/img/travelapply/airplane_gray.png" alt=""> [/#if]
                       </a><span>[#if tripApply.applyStatus == "finish" && tripApply.tripOrders??]${tripApply.tripOrders.planeOrders!0 }[#else]${tripApply.planeOrdersCount!0 }[/#if]</span>
                       <a class="hotel icon " href="${base}/ctrip_hotel/orderList.jhtml?applyId=${tripApply.id}&source=tripList">[#if tripApply.applyType != "5" && tripApply.transport?? && tripApply.transport?split(",")?seq_contains("4")]<img src="${base}/resources/wechat/img/travelapply/hotel.png" alt="">[#else] <img src="${base}/resources/wechat/img/travelapply/hotel_gray.png" alt=""> [/#if]</a> <span>[#if tripApply.applyStatus == "finish" && tripApply.tripOrders??]${tripApply.tripOrders.hotelOrders!0 }[#else]${tripApply.hotelOrdersCount!0 }[/#if]</span>
                       <a class="train icon " href="${base}/ly/train/orderList.jhtml?applyId=${tripApply.id}&source=tripList">[#if tripApply.applyType != "5" && tripApply.transport?? && tripApply.transport?split(",")?seq_contains("2")]<img src="${base}/resources/wechat/img/travelapply/train.png" alt="">[#else] <img src="${base}/resources/wechat/img/travelapply/train_gray.png" alt=""> [/#if]</a><span>[#if tripApply.applyStatus == "finish" && tripApply.tripOrders??]${tripApply.tripOrders.trainOrders!0 }[#else]${tripApply.trainOrdersCount!0 }[/#if]</span>
                       <a class="DiDi icon " href="${base}/taxi/getMemberOrder.jhtml?applyId=${tripApply.id}&source=tripList">[#if tripApply.transport?? && tripApply.transport?split(",")?seq_contains("3")]<img src="${base}/resources/wechat/img/travelapply/DiDi.png" alt="">[#else] <img src="${base}/resources/wechat/img/travelapply/DiDi_gray.png" alt=""> [/#if]</a> <span>[#if tripApply.applyStatus == "finish" && tripApply.tripOrders??]${tripApply.tripOrders.didiOrders!0 }[#else]${tripApply.didiOrdersCount!0 }[/#if]</span>
				   </p>
                   [/#if]
               </div>
               <div class="right travel-on-business">
                   <a href="${base}/trip/view.jhtml?id=${tripApply.id }" class="tosee">查看明细</a>

                   <div class="operate_btn flexbox  flex_wrap" id="operate_${tripApply.id }">
                         [#if tripApply.applyStatus != "finish"]
                            [#if !(isDingTalkSync!=null&&isDingTalkSync)]
                                [#if tripApply.applyStatus != "wait_review" && tripApply.applyStatus != "revoke"]
                                <a href="${base}/trip/edit.jhtml?id=${tripApply.id }&type=[#if tripApply.applyStatus == "wait_submit"]edit[#else]change[/#if]" class="change item">[#if tripApply.applyStatus == "wait_submit"]编辑[#else]变更[/#if]</a>
                                [/#if]
                                [#if tripApply.applyStatus == "wait_review" && !tripApply.isPassed]
                                <button type="button" onclick="cancelTrip(${tripApply.id }, this)" class="remove item" >撤销</button>
                                [/#if]
                            [/#if]
                            [#if tripApply.applyStatus == "wait_submit" || tripApply.applyStatus == "revoke"]
                            <button type="button" onclick="deleteTrip(${tripApply.id }, this)" class="cancel item" >删除</button>
                            [/#if]
                            [#if tripApply.applyStatus == "pass" && tripApply.transport?? && tripApply.transport != '5' ]
                            <button type="button" transports="${tripApply.transport }" onclick="toorder(${tripApply.id }, '${tripApply.applyType }', this)" class="toorder item">预订</button>
                            [/#if]
                         [/#if]
                       [#if tripApply.clockInCount >0]
                       <a href="${base}/clockIn/view.jhtml?tripId=${tripApply.id }" class="toorder item">打卡记录</a>
                       [/#if]
                       [#if tripApply.applyStatus == "pass"]
                             <a href="${base}/clockIn/index.jhtml?id=${tripApply.id }" class="toorder item">打卡</a>
                       [/#if]
                   </div>




               </div>
           </div>
       </li>
    [/#list]
[/#if]
