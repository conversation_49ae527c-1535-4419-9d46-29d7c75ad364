<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta
            name="viewport"
            content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no"
    />
    <title>加班餐补贴</title>
    <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
    <script src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js"></script>
    <link rel="stylesheet" href="${base}/resources/wechat/css/vant/index.css">
    <link type="text/css" rel="stylesheet" href="${base}/resources/wechat/css/common.css">
    <link type="text/css" rel="stylesheet" href="${base}/resources/wechat/css/overTimeMeals.css">
    <script src="${base}/resources/wechat/plugins/vue/vue.min.js"></script>

    <script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
    <script src="${base}/resources/wechat/js/vant.min.js"></script>
    <script type="text/javascript">
        var hostname=window.location.protocol+"//"+document.location.hostname;
        window._AMapSecurityConfig = {
            serviceHost:hostname+'/_AMapService'
        }
    </script>
    <script type="text/javascript" src='https://webapi.amap.com/maps?v=1.4.2&key=af778b7f852561b0c1028ba36eaf6c68'></script>
</head>
<body class="over-time-meals-page">
<div id="app" @click.stop="hideHomeList">
    <van-sticky>
        <van-nav-bar
                title="加班餐补贴"
                left-text=""
                left-arrow
                @click-left.stop="onClickLeft"
                @click-right.stop="onClickRight"
        >
            <template #right>
                <van-icon name="ellipsis" />
                <ul class="showHomeList modify_public_right_nav " v-if="showHomeList">
                    <li>
                        <a href="${base}/index.jhtml">
                            <span class="icon_home"></span>
                            <p>首页</p>
                        </a>
                    </li>
                    <li>
                        <a href="${base}/cart/list.jhtml">
                            <span class="icon_shoppingCart"></span>
                            <p class="nav_top_cartNum">
                                购物车
                                <span class="num_circle" id="cart_quantity"  v-cloak>
                                   {{ cart_quantity}}
                                </span>
                            </p>
                        </a>
                    </li>

                    <li>
                        <a href="[#if my_home_link?? && my_home_link != ""]${my_home_link}[#else]${base}/member/index.jhtml[/#if]">
                            <span class="icon_user"></span>
                            <p>个人中心</p>
                        </a>
                    </li>
                </ul>
            </template>
        </van-nav-bar>
    </van-sticky>
    <div class="header-content">
        <img class="header-bg" src="${base}/resources/wechat/img/airway/overtimeMeals.png"/>
    </div>
    <div class="module-content">
        <p class="content-top flexbox align-items-c justify-content-space-between">
            <span class="content-top-left">加班餐补积分</span>
            <span  class="content-top-right">
                余额:
                <span class="amount">￥<span  v-cloak>{{balance}}</span></span>
            </span>
        </p>
        <p class="content-middle">
                可消费应用
        </p>
        <ul class="application clearfix">

            <li v-for="(item,index) in moduleList" :key="'modleList'+index" class="flexbox flex-column application-item align-items-c justify-content-c">
               <a :href="item.links">
                   <div class="img-box flexbox align-items-c" :class="{old_icon_box:!(item.img&&item.img!==null&&item.img!==undefined&&item.img!=='')}" >
                       <div class="flexbox align-items-c" v-if="!(item.img&&item.img!==null&&item.img!==undefined&&item.img!=='')" :style="{ backgroundColor: (!(item.img&&item.img!='')?item.bgColor:'transparent')}">
                           <img :src="item.img?item.img:item.icon">
                       </div>
                       <img v-else  :src="item.img?item.img:item.icon">
                   </div>
                   <span class="module-name" v-cloak>{{item.title}}</span>
               </a>
            </li>
        </ul>
    </div>

    <div class="content ticket_list">
        <div class="tab_theme_section">
            <div @click="changeNav(item.type)" class="item" :class="item.type==activeType?'active':''"  v-for="(item,index) in navList" :key="'navList'+index"><a href="javascript:void(0);">{{item.label}}</a></div>
        </div>
        <ul class="available">
            [#--待领取--]
                <template  v-if="activeType==1">
                    <li @click="godetail(item,index)" v-for="(item,index) in ticketList" :class="!(item.isReceive=='true')?'cantReceive':''">
                        <div class="ticket_info">
                            <div class="text">
                                <div  class="flexbox align-items-c">
                                    <p class="num">
                                        <i>￥</i><span  v-cloak>{{item.amount}}</span>
                                    </p>
                                    <div class="content-right">
                                        <p  v-cloak>{{item.name}}</p>
                                        <p  v-cloak>有效期至  -  {{validityPeriod}}</p>
                                    </div>
                                </div>
                            </div>
                            <p class="category-of-use"  v-cloak>
                                {{item.memo}}
                            </p>
                            <div class="content-right-fix">
                                <p class="btn"  v-cloak>{{getBtnText(item.isReceive)}}</p>
                                <p class="timeText"  v-cloak>{{item.receivePeriod}}</p>
                            </div>

                        </div>
                    </li>
                </template>
                [#--已领取--]
                <template  v-else>
                    <van-list @load="getConsumeRecordList" v-model="loading" :finished="finished" finished-text="没有更多了"  >
                        <li @click="seeDetail(item)" v-for="(item,index) in recodList"  v-if="item!=null" :key="'record_list_'+index" class="receivedItem" :class="{expired:(item.expiredStatus=='expired')||(item.expiredStatus!='expired'&&item.consumAmount==0)}">
                            <div class="ticket_info">
                                <div class="text">
                                    <div  class="flexbox align-items-c">
                                        <div>
                                            <p class="num">
                                                <i>￥</i><span  v-cloak>{{item.amount}}</span>
                                            </p>
                                            <p v-if="item.consumAmount>0" class="consumAmount-tip" v-cloak>({{item.consumAmount}}元可用)</p>
                                        </div>
                                        <div class="content-right">
                                            <p  v-cloak>{{item.mealName}}</p>
                                            <p class="category-of-use"  v-cloak>
                                                {{item.memo}}
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <p class="time-label"  v-cloak>{{item.receiveDate}}  -  {{item.endDate}}</p>
                                <div class="content-right-fix">
                                    <p class="btn" v-if="item.expiredStatus==='expired'">已过期</p>
                                    <p class="btn"  v-if="item.expiredStatus!='expired'&&item.consumAmount==0">已用完</p>
                                </div>
                            </div>
                        </li>
                    </van-list>
                </template>
        </ul>
    </div>
    <div class="tip module-content" v-if="activeType==1">
        <p class="tip-header content-middle">使用规则</p>
        <div class="tip-content">
            1.领取限时有效期内每个员工限领一次。<br/>
            2.补贴时段： [#list config.realTripMeals as item] ${item.receivePeriodStart}-${item.receivePeriodEnd}  [#if item_index+1<config.realTripMeals?size],[/#if] [/#list]。<br/>
            3.加班餐补贴积分可叠加使用。<br/>
            4.支付金额超过补贴积分时，支持混合其他自费付款<br/>
        </div>
    </div>
    <van-dialog class="PhoneCodeDialog modify" v-model:show="show" title="消费记录"  close-on-click-overlay="true" confirm-button-text="知道了">
        <ul class="consumptionRecordsList">
            <li class="item flexbox align-items-c justify-content-space-between" v-for="(item,index) in consumptionRecords" :key = "'consumptionRecords'+index">
                <div class="flexbox flex-column justify-content-space-between">
                    <p>
                        订单：{{item.sn}}
                    </p>
                    <p>
                        {{item.createDate}}
                    </p>
                </div>
                <p :class="item.consumAmount>0?'positive':'negative'">{{item.consumAmount}}</p>
            </li>
        </ul>
    </van-dialog>
</div>
<script>
    let vm = new Vue({
        el: "#app",
        data() {
            return {
                show:false,
                activeType:1,
                navList:[
                    {
                        type:1,
                        label:'待领取'
                    },
                    {
                        type:2,
                        label:'已领取'
                    }
                ],
                cart_quantity:0,//购物车数量
                balance:'${availableBalance?string("0.00")}',
                validityPeriod:"[#if validityPeriod??]${validityPeriod?string("yyyy.MM.dd")}[#else ]长期[/#if]",
                pageNumber: 0,
                pageSize: 10,
                showHomeList:false,
                loading: false,
                finished: false,
                consumptionRecords:[],//消费记录
                ticketList:[

                    [#list mealConfigs as meal]
                        {
                            "id":"${meal.id}",
                            "amount":"${meal.receiveAmount}",
                            "name":"${meal.mealName}",
                            "memo":"${meal.memo!'仅限加班餐补贴应用消费'}",
                            "receivePeriod":"${meal.receivePeriodStart}~${meal.receivePeriodEnd}",
                            "isReceive": "${meal.isReceive}"
                        }
                        [#if meal_index+1<mealConfigs?size],[/#if]
                    [/#list]
                ],
                moduleList:[ //可用模块
                          [#list availableModules as memberModule]
                              [#assign mobileImg='']
                              [#if memberModule.phoneIcon??]
                                  [#if memberModule.phoneIcon?index_of(",")!=-1]
                                      [#assign urlArr = memberModule.phoneIcon?split(",")]
                                      [#assign mobileImg=urlArr[1]]
                                  [#else ]
                                      [#assign mobileImg=memberModule.phoneIcon]
                                  [/#if]
                              [/#if]
                        {
                            "id":"${memberModule.id}",
                            "title":"${memberModule.name?html}",
                            "bgColor": "${memberModule.colour}",
                            "icon": "[#if memberModule.icon??&& memberModule.icon?index_of("/")!=-1]${setting.siteUrlAdmin}${ memberModule.icon}[#else]${base}/resources/wechat/img/icons/${ memberModule.icon}[/#if]",
                            "model": "[#if memberModule.code??&&(memberModule.code =='ExchangeEvent'||memberModule.code =='GiftEvent' )]3[/#if][#if memberModule.code??&&memberModule.code =='UnionActivities']4[/#if]",
                            "img": "[#if mobileImg??&&mobileImg?index_of("/")!=-1]${setting.siteUrlAdmin}${mobileImg}[#else][/#if]",
                            [#if memberModule.name=="微信红包" ]
                                  "links": "javascript:sendRedpack('${memberModule.url}')"
                            [#elseif memberModule.url?lower_case?index_of("meituan")!=-1||memberModule.url?lower_case?index_of("lupay")!=-1||memberModule.url?lower_case?index_of("qipiao")!=-1]
                                  "links": "javascript:mtOperate('${memberModule.url}',${memberModule.id})"
                            [#else]
                                  "links": "[#if memberModule.url??][#if memberModule.url?index_of('jqk.jd')!=-1]${memberModule.url}&channel_token={secretMemberId}[#else][#if memberModule.url?index_of('http') != -1||memberModule.url?index_of('weixin') != -1]${memberModule.url}[#else][#if memberModule.url?index_of('?') == -1]${base}${memberModule.url}?sysModuleId=${memberModule.id}[#else]${base}${memberModule.url}&sysModuleId=${memberModule.id}[/#if][/#if][/#if][/#if]"
                            [/#if]
                        }
                              [#if memberModule_index+1<availableModules?size],[/#if]
                          [/#list]
                ],
                recodList:[  ],//消费记录数据
                positionObj:{ //当前定位信息
                    lat:'',
                    lng:''
                }
            };
        },
        computed:{

        },
        mounted(){
            this.refreshCartConut();
            this.mapService();
            this.initData();
        },
        methods: {
            //查看详情
            seeDetail(data){
                const {consumAmount,expiredStatus,amount} = data;
                let param = {
                    "id":data.id
                };
                if(amount>consumAmount){ //有使用
                    let $that = this;
                    $.ajax({
                        type: "get",
                        url: "tripMealConsumRecord.jhtml",
                        contentType: "application/x-www-form-urlencoded",
                        data: param,
                        success: function(data){
                            if(data.length > 0){
                                $that.show = true;
                                $that.consumptionRecords = data;
                            }
                            if(data.type==='success') {
                                $that.consumptionRecords = data.data;
                                if(data.data.length > 0){
                                    $that.show = true;
                                }
                            }
                        }
                    });
                }
            },
            initData(){
                if(this.ticketList.length===0){ //没有待领取的
                    this.activeType = 2;
                    this.navList.splice(0, 1);
                }
            },
             mapService(){
                    let map, geolocation;
                    let $that = this;
                    //加载地图，调用浏览器定位服务
                    map = new AMap.Map('container', {
                        resizeEnable: true
                    });
                    map.plugin('AMap.Geolocation', function() {
                        geolocation = new AMap.Geolocation({
                            enableHighAccuracy: true,//是否使用高精度定位，默认:true
                            timeout: 10000,          //超过10秒后停止定位，默认：无穷大
                            buttonOffset: new AMap.Pixel(10, 20),//定位按钮与设置的停靠位置的偏移量，默认：Pixel(10, 20)
                            zoomToAccuracy: true,      //定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：false
                            buttonPosition:'RB'
                        });
                        map.addControl(geolocation);
                        geolocation.getCurrentPosition(function (result,data) {
                            if(result == "complete"){
                                $that.positionObj.lat = data.position.lat;
                                $that.positionObj.lng = data.position.lng;
                            }
                        });

                    });
            },
            getBtnText(flag){
              let btnText = '限时领取';
              if(flag=='true')  {
                  btnText = '立即领取';
              }
              return btnText
            },
            changeNav(type){
                if(!(this.activeType==type) ){
                    this.activeType = type;
                    this.pageNumber =0;
                    this.loading = false;
                    this.finished = false;
                    this.recodList =[];
                }

            },
            refreshCartConut(){
                var that = this;
                jQuery.post("${base}/cart/quantity.jhtml","",function(data){
                    if(data!=null && data.totalQuantity>0){
                        $("#nav_top_cartNum").find("label").remove();
                        if(data.totalQuantity>99){
                            that.cart_quantity = 99;
                        }else{
                            that.cart_quantity = data.totalQuantity;

                        }
                    }
                },"json");
            },
            godetail(item,index){
                 if(!(item.isReceive=='true')){ //未到领取时间 不能领取
                     layer.open({
                         content: "未到领取时间,不能领取"
                         ,skin: 'msg'
                         ,time: 2 //2秒后自动关闭
                     });
                     return false
                 }
                let param = {
                    mealConfigId:item.id,
                    currentLon:this.positionObj.lng,
                    currentLat:this.positionObj.lat
                };
                let $that = this;
                $.ajax({
                    type: "POST",
                    url: "receivedCoin.jhtml",
                    contentType: "application/x-www-form-urlencoded",
                    data: param,
                    success: function(data){
                        if(data.type==='success'){
                            layer.open({
                                content: "领取成功"
                                ,skin: 'msg'
                                ,time: 2 //2秒后自动关闭
                            });
                            $that.balance =(parseFloat($that.balance) + parseFloat(item.amount)).toFixed(2);//余额加上当前的金额
                            //删除这条记录
                            if($that.ticketList.length > 1){ //待领取的还有券，留在此页
                                $that.ticketList.splice(index, 1)
                            }else { //没有可以领取的券了 切换tab
                                $that.activeType = 2;
                                $that.navList.splice(0, 1);
                                $that.changeNav($that.activeType);
                            }
                        }else {
                            layer.open({
                                content: data.content
                                ,skin: 'msg'
                                ,time: 2 //2秒后自动关闭
                            });
                        }
                    }
                });
            },
            hideHomeList(){
                this.showHomeList=false;
            },
            onClickLeft() {
                history.back();
            },
            onClickRight() {
                this.showHomeList=!this.showHomeList;
            },
            formatYaMaD(DT){
                 if(DT==null||DT==undefined||DT==''){
                     return '长期'
                 }
                var T =new Date(DT);
                var obj={
                    Y:T.getFullYear(),
                    M:parseInt(T.getMonth())<8?"0"+(T.getMonth() + 1):T.getMonth() + 1,
                    D:T.getDate()<10?"0"+T.getDate():T.getDate(),
                    H:T.getHours()<10?"0"+T.getHours():T.getHours(),
                    Ms:T.getMinutes()<10?"0"+T.getMinutes():T.getMinutes(),

                };
                return obj.Y+"-"+obj.M+"-"+obj.D+" "+obj.H+":"+obj.Ms;
            },
            getConsumeRecordList(){//获得订单列表分页数据
                let param = {};
                let $that = this;
                param.pageNumber = $that.pageNumber + 1;
                param.pageSize = $that.pageSize;
                param.type=this.activeType;
                $.ajax({
                    type: "POST",
                    url: "receivedRecords.jhtml",
                    contentType: "application/x-www-form-urlencoded",
                    data: param,
                    success: function(data){
                        $that.loading = false;//加载结束
                        if(data.content == null || data.content == undefined||data.content.length==0) {
                            $that.finished = true;//数据全部加载完成
                            return ;
                        }
                        //请求成功，显示数据
                        $that.pageNumber ++;//修改当前页数
                        data.content.map(item=>{
                            item.memo =  item.memo? item.memo:'仅限加班餐补贴应用消费',
                            item.endDate = $that.formatYaMaD(item.endDate),
                            item.receiveDate = $that.formatYaMaD(item.receiveDate)
                        });
                        $that.recodList = $that.recodList.concat(data.content);
                        if($that.pageNumber  >= data.totalPages) {
                            $that.finished = true;//数据全部加载完成
                        }
                    }
                });
            }
        },
    });
    //如果是美团模块，判断是否公众号打开
    function mtOperate(url,id) {
        var sxo = getCookie('small_wx_openid');  //小程序
        var app = getCookie('memberAppOauthLogin');  //app
        if(sxo!=null ){
            layer.open({
                title:"温馨提示",
                content : '请关注并到“福利关怀平台”微信公众号进行操作！',
                btn : '我知道了'
            });
        }else{
            if(id==455){ //生鲜水果
                location.href = url;
            }else if(id==389){ //美团电影票
                // 判断是否是app
                var device=openDevice();

                if(device!="ios" && device!="android"){
                    layer.open({
                        title: "温馨提示",
                        content: '美团电影票公众号暂不支持积分支付,请前往app！',
                        btn: ['确定','取消'],
                        yes:function(index){
                            location.href = "${base}"+url;
                        },
                        no:function(index){
                            layer.close(index);
                        }
                    });
                }else{
                    location.href = "${base}"+url;
                }


            }else{
                location.href = "${base}"+url;
            }
        }
    }
</script>
</body>
</html>
