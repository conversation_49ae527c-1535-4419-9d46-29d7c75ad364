<input type="hidden" id="totalPages" value="${page.totalPages}"/>
[#if page.total > 0]
	[#list page.content as returns]
		<li>
			<header>
				<span class="no">订单号码：${returns.order.sn}</span>
				<!--<span class="icon_del"></span>-->
				<span class="status complete">
					[#if returns.returnStatus == "unapprove"]
						待审核
					[#elseif returns.returnStatus == "unreturned"]
						待退货
					[#elseif returns.returnStatus == "returning"]
						退货中
					[#elseif returns.returnStatus == "returned"]
						已完成
					[#elseif returns.returnStatus == "notpass"]
						审核不通过
					[#elseif returns.returnStatus == "cancelled"]
						已取消
					[#elseif returns.returnStatus == "repairing"]
						维修中
					[#elseif returns.returnStatus == "repaired"]
						已维修
					[#elseif returns.returnStatus == "received"]
						已收货
					[#elseif returns.returnStatus == "unrefund"]
						待退款
					[/#if]
				</span>
			</header>
			<a href="#" class="item">
				<div class="img">
		 			[#--<img src="${setting.siteUrlImage}[#if returns.orderItem.thumbnail??]${returns.orderItem.thumbnail}[#elseif com_person_product_default_img != null]${com_person_product_default_img}[#else]${setting.defaultThumbnailProductImage}[/#if]" />--]
	
						[#if returns.orderItem.product.supplierId.id==68 || returns.orderItem.product.supplierId.id==181|| returns.orderItem.product.supplierId.id==695]
							[#if returns.orderItem.product.attributeValue8!=null]
	                            <img title="${returns.orderItem.product.name}" src="${returns.orderItem.product.attributeValue8}" />
							[#elseif  returns.orderItem.product.image!=null]
	                            <img title="${returns.orderItem.product.name}" src="${setting.siteUrlImg}${returns.orderItem.product.image}" />
							[#else]
	                            <img title="${returns.orderItem.product.name}" src="[#if com_person_product_default_img != null]${com_person_product_default_img}[#else]${setting.siteUrlImg}${setting.defaultThumbnailProductImage}[/#if]" />
							[/#if]
						[#else]
							[#if returns.orderItem.product.image!=null]
	                            <img title="${returns.orderItem.product.name}" src="${setting.siteUrlImg}${returns.orderItem.product.image}" />
							[#else]
	                            <img title="${returns.orderItem.product.name}" src="[#if com_person_product_default_img != null]${com_person_product_default_img}[#else]${setting.siteUrlImg}${setting.defaultThumbnailProductImage}[/#if]" />
							[/#if]
						[/#if]
	
				</div>
		 		<div class="title">
		 			<p>${returns.orderItem.name}</p>
		 		</div>
		 		<div class="attrs">
		 			[#if returns.order.orderType!=4]<span>                           x${returns.qty}</span>[/#if]
		 			[#if returns.orderItem.product.specificationValues]
						[#list returns.orderItem.product.specificationValues as specificationValue]
							<span>
								${specificationValue.specification.name}:${specificationValue.name}
							</span>
						[/#list]
					[/#if]
		 		</div>
			</a>
			<footer>
				[#if returns.order.orderType!=4]<span class="total_price">[#if returns.refundAmount !=null]退款金额: [#if productShowRate??]${coinConvert(returns.refundAmount, productShowRate, false, false)}[#else]${currency(returns.refundAmount)}[/#if]</span>[/#if][/#if]
				<div class="btns">
	
					<input type="button"  onclick="location.href='${base}/member/order/returnsDetail.jhtml?id=${returns.id}[#if pageSource??&&pageSource=="weixincoupon"]&pageSource=weixincoupon[/#if]'"  value="售后详情" class="btn_theme">
	
					[#if returns.returnStatus == "unapprove"]
						<input type="button" onclick="cancelApply('${returns.id}','${returns.sn}')" value="取消申请" class="btn_gray"/>
					[#elseif returns.returnStatus == "unreturned"]
						<input type="button" onclick="cancelApply('${returns.id}','${returns.sn}')" value="取消申请" class="btn_gray"/>
						<input type="button" onclick="confirmReturns('${returns.id}', true,'${returns.supplierId.address}','${returns.afsServiceId}','${returns.supplierId.isOweOrder}')"
							   id="confirmReturnsBtn" afsServiceId="${returns.afsServiceId}"
							   returnwareAddress="${returns.returnwareAddress}"
							   returnwareConsignee="${returns.returnwareConsignee}"
							   returnwarePhone="${returns.returnwarePhone}"
							   value="确认退货" class="btn_gray"/>
					[/#if]
				</div>
			</footer>
		</li>
	[/#list]
[#else]
	<div class="public_empty_section [#if comPersonConf && com_person_EmptyBg != null && com_person_EmptyBg != ""] ${com_person_EmptyBg!}[#else] Public_Bg[/#if]">
        <p class="tip">这里空空如也，先去逛逛吧~</p>
	[#if pageSource??&&pageSource=='weixincoupon']
        <a href="${base}/weixinCoupon/index.jhtml" class="pub_link_theme">去逛逛</a>
	[#else ]
		<a href="${base}/index.jhtml" class="pub_link_theme">去逛逛</a>
	[/#if]
	 </div>
[/#if]
