<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<title>火车票</title>
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">
		<script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/lifeService.css" />
		<script src="${base}/resources/wechat/plugins/jquery/jquery-1.9.1.min.js"></script>
		<script src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
	</head>
	<body class="has_fixed_footer">
	[#include "./wechat/include/footer.ftl" /]
		<div class="public_top_header">
			[#if applyId?? ]
				<a href="${base}/trip/index.jhtml" class="return_back"></a>
			[#else]
			 	<a href="[#if yphFlag??]${base}/index.jhtml[#else]${base}/travel_service/index.jhtml?sysModuleId=162[/#if]" class="return_back"></a>
			[/#if]
			火车票[#if "${orderNo}"!=null && "${orderNo}"!="" && "${passengerId}"!=null && "${passengerId}"!=""]改签[/#if]
			[#include "./wechat/include/head_nav.ftl" /]
		</div>
		
		<div class="fly_index_page">
			<div class="img_banner">
				<img src="${base}/resources/wechat/img/recharge/pic_train.png"/>
			</div>
			<form action="ticketQuery.jhtml" id="searchForm" method="get">
                <div class="lifePubTabSection">
                    <div class="header">
                        <div class="item active">公务出行</div>
                        <div class="item">私人旅行</div>
                    </div>
                    <div class="content">
                        <div class="boxItem">
                            <div class="item select chailvItem">
                                <label>差旅申请单</label>
                                <div class="value text-ellipsis">
                                    差旅申请单差旅申请单差旅申请单差旅申请单差旅申请单差旅申请单
                                </div>
                            </div>
                            
                            <div class="place_form_to">
                                <span class="from" >
                                    ${from!"出发地"}
                                </span>
                                <span class="icon_change"></span>
                                <span class="to">${to!"目的地"}</span>
                            </div>
                        
                            <div class="select_time">
                                [#if applyId?? ]
                                    <input type="text" onfocus="this.blur()" readonly="readonly" name="date"  id="date" value="${date}" placeholder="出发日期" />
                                [#else]
                                    <input type="text" onfocus="this.blur()" readonly="readonly" name="date"  id="date" value="${date}" placeholder="出发日期" />
                                [/#if]
                            </div>

                            <div class="item standard chailvItem">
                                <label>差旅标准<span class="icon_tips"></span></label>
                            </div>
                        </div>
                    </div>
                    <div class="btn_box">
                        <div class="agreement_line">点击查看<a href="javascript:;">《购票须知》</a></div>
                        <input type="button" name="" id="search" onclick='' value="搜索" class="fli_btn_long fli_btn_primary"/>
                        <input type="button" name="" id="" value="我的订单" class="fli_btn_long fli_btn_yellow" onclick='location.href="orderList.jhtml?applyId=${applyId}"'/>
                    </div>
                </div>
			</form>
		</div> 

        <script type="text/html" id="train_agreement_box">
            <span class="layer_close"></span>
            <div class="train_agreement_box">
				<div class="scrollbox">
					<div class="item">
						<p class="title">服务时间：<span>6:00-23:00</span></p>
					</div>
					<div class="item">
						<p class="title">购票说明：</p>
						<p>我司通过铁路客运机构官方网站或授权代售点为客户提供火车票预订和增值服务。</p>
						<p>因通过铁路客运机构出票，我司无法承诺百分之百出票成功，我司预先收取您的票款，如果出票失败，退款会1~3个工作日原渠道返回您支付的账户。</p>
					</div>
					<div class="item">
						<p class="title">身份核验</p>
						<p>2014年新规：从未在网络购票的用户，请先携身份证去火车站专门窗口核验身份证信息，核验通过后才能网上购票。</p>
					</div>
					<div class="item">
						<p class="title">如何取票</p>
						<p>发车前凭预订时使用的证件原件和电子取票号，可在全国任意火车站窗口、自动售取票机或客票代售点取票。</p>
						<p>部分高铁动车可持二代居民身份证直接检票进站，如果遇到无法出站的情况请及时与车站工作人员联系。</p>
						<p>在车站售票窗口、自动售（取）机换取车票时，不收取任何服务费。在铁路代售点窗口换取车票时，每张车票将收取5元客票销售服务费。</p>
					</div>
					<div class="item">
						<p class="title">退改签及变更到站说明</p>
						<p>在线退票时间：6:00-23：00。</p>
						<p>变更到站：未取纸质票，只能在距开车时间48小时以外办理；</p>
						<p>改签条件：未取纸质票，且离发车时间大于35分钟，其他不能在线改签的情况需在发车前至火车站窗口办理。</p>
						<p>退票条件：未取纸质票，且离发车时间大于35分钟。</p>
						<p>进/出藏列车等其他不能在线退票的情况需在发车前至火车站窗口办理。</p>
						<p>其他不能在线退票的情况需在发车前至火车站窗口办理。</p>
					</div>
					<div class="item">
						<p class="title">其他说明</p>
						<p>1、短信通知您，因运营商网关延迟可能导致您无法及时接收短信，请到我的订单中跟踪出票情况。</p>
						<p>2、如遇列车停运，请最晚在发车前35分钟进行线上退票，我司将退回全款。如果已经取出车票或者列车已过发车时间，请您务必在5日内（含出发日）前往车站退票，全款将按支付渠道退回。</p>
					</div>
				</div>
			</div>
        </script>

        [#include "./wechat/include/chailvTemplate.ftl" /]
		<script>
		try{
			var trainSearchInfo=getCookie("trainSearchInfo");
			if(trainSearchInfo){
				var trainSearchInfoJson=$.parseJSON( trainSearchInfo);
				if(trainSearchInfoJson.from&&$(".from").html().trim()=="出发地"){
					$(".from").html(trainSearchInfoJson.from);
				}
				if(trainSearchInfoJson.to&&$(".to").html().trim()=="目的地"){
					$(".to").html(trainSearchInfoJson.to);
				}
				if(trainSearchInfoJson.date&&!$("#date").val()){
					 var old=Date.parse(trainSearchInfoJson.date);
					 if(old>new Date()){
						$("#date").val(trainSearchInfoJson.date);
					 }else{
						 $("#date").val('${.now?string("yyyy-MM-dd")}');
					 }
				}
			}
		}catch(err){

		}
			
			
        $(".agreement_line a").on("click",function(){
            layer.open({
                title: "购买须知"
                , content: $("#train_agreement_box").html()
                , success: function () {
                    $(".layer_close").on("click", function () {
                        layer.closeAll();
                    })
                }
            });
        }) 

			$("#search").click(function(){
				var from=$(".from").html();
				var to=$(".to").html();
				var date=$("#date").val(); 
				if(!from||from.trim()=="出发地"){
					layer.open({
					    content: '请选择出发地'
					    ,skin: 'msg'
					    ,time: 2 //2秒后自动关闭
					});
					return false;
				}
				if(!to||to.trim()=="目的地"){
					layer.open({
					    content: '请选择目的地'
					    ,skin: 'msg'
					    ,time: 2 //2秒后自动关闭
					});
					return false;
				}
				if(!date){
					layer.open({  
					    content: '请选择出发日期'
					    ,skin: 'msg'
					    ,time: 2 //2秒后自动关闭
					});
					return false;
				}
				addCookie("trainSearchInfo", '{"to":"${to}","from":"${from}","date":"'+date+'"}', {expires: 30*24 * 60 * 60});
				[#if applyId?? ]
				    [#if orderNo?? && passengerId??] <!-- 改签-->
                        location.href="ticketQuery.jhtml?applyId=${applyId}&to="+to+"&from="+from+"&date="+date+"&orderNo=${orderNo}"+"&passengerId=${passengerId}"+"&sn=${sn}";
                    [#else]
                        location.href="ticketQuery.jhtml?applyId=${applyId}&to="+to+"&from="+from+"&date="+date;
				    [/#if]
				[#else]
				    [#if orderNo?? && passengerId??] <!-- 改签-->
                       location.href="ticketQuery.jhtml?to="+to+"&from="+from+"&date="+date+"&orderNo=${orderNo}"+"&passengerId=${passengerId}"+"&sn=${sn}";
                    [#else]
                       location.href="ticketQuery.jhtml?to="+to+"&from="+from+"&date="+date;
				    [/#if]
				[/#if]
			});


		   $(".place_form_to .from,.place_form_to .to").on("click", function () {
                   var from = $(".from").html();
                   var to = $(".to").html();
                   var date = $("#date").val();
                   var type = $(this).prop("class");
			   [#if applyId?? ]
                   location.href = "selectCity.jhtml?applyId=${applyId}&from=" + from + "&to=" + to + "&date=" + date + "&type=" + type+"&orderNo=${orderNo}&passengerId=${passengerId}&sn=${sn}";
			   [#else]
                   location.href = "selectCity.jhtml?from=" + from + "&to=" + to + "&date=" + date + "&type=" + type+"&orderNo=${orderNo}&passengerId=${passengerId}&sn=${sn}";
			   [/#if]
               })


		   if("${orderNo}"!=null && "${orderNo}"!="" && "${passengerId}"!=null && "${passengerId}"!="" ) {  // 改签判断是否支持变更到站
               var canChangeStation = 1; // 默认支持变更到站
               $.ajax({
                   type: "GET",//方法类型
                   dataType: "json",//预期服务器返回的数据类型
                   url: "${base}/ly/train/verifyApplyChangeStation.jhtml?orderNo=${orderNo}&passengerId=${passengerId}&sn=${sn}" ,//url
                   success: function (result) {
                        var changeStatus=result["changeStatus"];
                       if (changeStatus != 1) {
                           canChangeStation = 0; // 不支持变更到站
                       }
                   }
               })
               if (canChangeStation != 1) { // 不支持变更到站，禁用目的地的点击事件
                   $(".place_form_to .to").off("click");
               }
               // 出发地禁用
			   $(".place_form_to .from").off("click");
           }



			$(".icon_change").on("click",function(){
					var _from = $(".from").html();
					var _to = $(".to").html();
					$(".from").html(_to);
					$(".to").html(_from);
				})

			if("${orderNo}"!=null && "${orderNo}"!="" && "${passengerId}"!=null && "${passengerId}"!="") { // 改签
                  $(".icon_change").off("click");
			}

			$("#date").on("click",function(){

				var href = "calendar.jhtml?orderNo=${orderNo}&passengerId=${passengerId}&sn=${sn}&date="+$(this).val();
				var _from = $(".from").html();
				var _to = $(".to").html();
				href = href + "&to=" + _to + "&from=" + _from;
				
				[#if applyId?? ]
				href = href + "&applyId=${applyId}";
				[/#if]

				location.href=href;
			});
		</script>
	</body>
</html>
