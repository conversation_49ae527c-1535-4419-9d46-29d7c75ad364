<!DOCTYPE html>
<html>

	<head>
		<meta charset="UTF-8">
		<title>火车票</title>
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">
		<script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/lifeService.css" />
		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js" ></script>
	</head>

	<body class="has_fixed_top has_fixed_footer">
		<div class="public_top_header fixed trainsearchlist">
			[#if applyId?? ]
				<a href="javascript:;" onclick='location.href="index.jhtml?applyId=${applyId}&to=${to}&from=${from}&date=${date}&orderNo=${orderNo}&passengerId=${passengerId}&agentMemberId=${agentMemberId}&isPublic=${isPublic}"' class="return_back"></a>
			[#else]
				<a href="javascript:;" onclick='location.href="index.jhtml?to=${to}&from=${from}&date=${date}&orderNo=${orderNo}&passengerId=${passengerId}"' class="return_back"></a>
			[/#if]
			${from}-${to} [#if passengerId??]改签[/#if]
			[#if com_person_hide_menu?? && com_person_hide_menu == '1']
				<!-- 隐藏右上角菜单目录 -->
			[#else ]
				[#include "./wechat/include/head_nav.ftl" /]
			[/#if]
		</div>
		<form action="ticketQuery.jhtml" id="searchForm" method="get">
		<div class="fly_search_detail_page">
			<header>
				<a href="javascript:;" class="prev">前一天</a>
				<a href="javascript:;" class="next">后一天</a>
				<input type="hidden" name="date" value="${date}"/>
				<input type="text"  id="search" value="${date}" class="input_text" readonly="readonly" onfocus="this.blur()" onclick='location.href="calendar.jhtml?to=${to}&from=${from}&type=query&date=${date}&orderNo=${orderNo}&passengerId=${passengerId}&sn=${sn}&agentMemberId=${agentMemberId}&isPublic=${isPublic}[#if applyId?? ]&applyId=${applyId!}[/#if]"'/>
			</header>
			[#if trainlines??&&trainlines?size>0]
			<div class="train_list_table">
				[#list trainlines as trainline]

				[#if  trainline.getTicketsList()??]
					<div class="Option" fromStation="${trainline.fromStation}" toStation="${trainline.toStation }" trainSeatNone="[#if !(trainline.getTicketsList()??&&trainline.getTicketsList()?size>0)]none[#else]have[/#if]" trainTypeName="${trainline.trainClass}" trainNumber="${trainline.trainNo}"  >
						<div class="section">
							<div class="section_1 sec">
								<p class="from_time">${trainline.fromTime }</p>
								<p class="station"><span class="iconPassType [#if trainline.fromPassType??&& trainline.fromPassType=='途经']icon_road[#else ]icon_start[/#if]">[#if trainline.fromPassType??&&trainline.fromPassType=='途经']过[#else ]始[/#if]</span>${trainline.fromStation}</p>
							</div>
							<div class="section_2 sec">
							    <p class="taken runTime">${trainline.runTimeSpan}</p>
								<p class="train_no">${trainline.trainNo }</p>
							</div>
							<div class="section_3 sec">
								<p class="to_time">${trainline.toTime }</p>
								<p class="station"><span class="iconPassType [#if trainline.toPassType??&&trainline.toPassType=='途经']icon_road[#else ]icon_end[/#if]">[#if trainline.toPassType??&&trainline.toPassType=='途经']过[#else ]终[/#if]</span>${trainline.toStation }</p>
							</div>
							<div class="section_4 sec">
								[#if trainline.getTicketsList()??&&trainline.getTicketsList()?size>0]
									[#assign minPrice=train.getMinPrice(trainline.getTicketsList())]
									[#if minPrice??]
									<p class="price">￥${minPrice}<span></span></p>
									[#else]
										<p class="type">无票</p>
									[/#if]
								[#else]
									<p class="type">无票</p>
								[/#if]
							</div>
						</div>
						[#assign isNoneCount=0]
						[#if trainline.getTicketsList()??&&trainline.getTicketsList()?size>0]
						<ul class="seatslevel">
								[#list trainline.getTicketsList() as trainSeat ]
									[#if trainSeat.seats==0]  <!--  余票数量为0-->
										[#assign isNoneCount=isNoneCount+1] <!-- 无票数量-->
									[/#if]

									[#assign tripLevelMsg=""]
									[#assign trainShow=-1]
									[#if tripLevelConfig??]
										[#if tripLevelConfig.trainFlag]
											[#if tripLevelConfig.trainSeatType??]
												[#if !tripLevelConfig.trainSeatNames?split(",")?seq_contains(trainSeat.seatName)]
													[#assign tripLevelMsg="未选择差旅标准里的火车票座席、"]
													[#if tripLevelConfig.trainExceedConctrol == "unLimited"]
														[#assign trainShow=0]
													[#elseif tripLevelConfig.trainExceedConctrol == "orderLimit"]
														[#assign trainShow=1]
													[#elseif tripLevelConfig.trainExceedConctrol == "displayLimit"]
														[#assign trainShow=2]
													[/#if]
												[/#if]
											[/#if]
										[/#if]
									[/#if]
									
								[#if trainShow != 2 && trainSeat_index<4]
							    <li class="item [#if trainSeat.seats==0 || trainShow == 1]dis[/#if]" >
								    <lable data-seatName="${trainSeat.seatName}">
								   ${trainSeat.seatName}:
									    <span>
									    [#if trainShow == 1]
									    	超标
									    [#else]
										    [#if trainSeat.seats>20]
										    	有票
										    [#else]
										    	[#if trainSeat.seats>0]
										    		${trainSeat.seats}张
										    	[#else]
										    			无
										    	[/#if]
										    [/#if]
									    [/#if]
								    	</span>
								    </lable>
							    </li>
								[/#if]
							    [/#list]
						</ul>
						[#if isNoneCount==trainline.getTicketsList()?size]
							<input type="hidden" class="trainSeatNone">
						[/#if]
						[/#if]
					</div>
					[/#if]
				[/#list]
			</div>
			[/#if]
			<div class="public_empty_section2" style="display:[#if trainlines??&&trainlines?size>0] none[#else ] block[/#if]">
				<img src="${base}/resources/wechat/img/recharge/img_search_train.png"/>
				<p>${errMsg!"抱歉，没有符合您查询条件的车次，请重新查询"}!</p>
			</div>



			<footer>
                <a href="javascript:;"  data-index="1" [#if sort=="from_time"]class="active"[/#if]>
                    <span class="icon_filter_G"></span>
                    <p>[#if sortType=="desc"]最晚出发[#else ]最早出发[/#if]</p>
                </a>
                <a href="javascript:;"  data-index="3"  [#if sort=="price"]class="active"[/#if]>
                    <span class="icon_filter_price"></span>
                    <p>[#if sortType=="desc"]价格最高[#else ]价格最低[/#if]</p>
                </a>
				<a href="javascript:;"  data-index="0"  [#if sort=="run_time"]class="active"[/#if]>
					<span class="icon_filter_time"></span>
                    <p>[#if sortType=="desc"]耗时最长[#else ]耗时最短[/#if]</p>
				</a>
                <a href="javascript:;"  data-index="-1" id="icon_filter_a">
                    <span class="icon_filter"></span>
                    <p>筛选</p>
                </a>
			</footer>
		</div>
			<input type="hidden" name="fromStation" id="fromStation" value="${fromStation}">
			<input type="hidden" name="toStation" id="toStation" value="${toStation}">
			<input type="hidden" name="from" id="from" value="${from}">
			<input type="hidden" name="to" id="to" value="${to}">
			<input type="hidden" name="sort" id="sort" value="${sort}">
			<input type="hidden" name="sortType" id="sortType" value="${sortType}">
			<input type="hidden" name="orderNo"  value="${orderNo}">
			<input type="hidden" name="passengerId"  value="${passengerId}">
			<input type="hidden" name="sn"  value="${sn}">
			[#if applyId??]
			<input type="hidden" name="applyId"  value="${applyId}">
			[/#if]
			<input type="hidden" name="agentMemberId"  value="${agentMemberId}">
			<input type="hidden" name="isPublic"  value="${isPublic}">
		</form>
        <!-- 筛选 -->
        <div class="popbg filter-pop" id="pop_cut">
            <div class="close"></div>
            <div  class="select-reason-lists">
					<p class="til">车次类型</p>
					<div class="content" id="trainTypeName-content">
                        <span data-type="G">高铁/动车</span><span data-type="Z">普通车</span><span data-type="P">只看有票</span>
					</div>
					<p class="til">出发车站</p>
					<div class="content" id="fromStation-content">
					</div>
                    <p class="til">到达车站</p>
                    <div class="content" id="toStation-content">
                    </div>
					<p class="til">席别类型</p>
					<div class="content" id="seatName-content">
				    </div>
					<p class="til">出发时间</p>
					<div class="content seatTime-content" id="form-seatTime-content">
                        <span data-type="0">00:00-08:00</span>
                        <span data-type="1">08:00-14:00</span>
                        <span data-type="2">14:00-19:00</span>
                        <span data-type="3">19:00-24:00</span>
					</div>
                <p class="til">到达时间</p>
                <div class="content seatTime-content" id="to-seatTime-content">
                    <span data-type="0">00:00-08:00</span>
                    <span data-type="1">08:00-14:00</span>
                    <span data-type="2">14:00-19:00</span>
                    <span data-type="3">19:00-24:00</span>
                </div>
					<div class="btn-option">
							<input type="button" class="cancel" value="清空" />
							<input type="button" class="makerSure" value="确认" />
					</div>
            </div>
        </div>
		<script>
			var filterNum=0;
            var trainObj =[];
            var fromStationList= [];
            var toStationList =[];
            var seatNameList = [];
			[#if trainlines??&&trainlines?size>0]
			 trainObj =[
			    	[#list trainlines as trainline]
						[#if  trainline.getTicketsList()??]
							[#assign ctrainSeatNone  ='none']
							[#if trainline.getTicketsList()??&&trainline.getTicketsList()?size>0]
								[#list trainline.getTicketsList() as trainSeat ]
									[#if trainSeat.seats>0]
										[#assign ctrainSeatNone  ='have']
									[/#if]
								[/#list]
							[/#if]
							[#if trainline.getTicketsList()??&&trainline.getTicketsList()?size>0]
								[#assign minPrice=train.getMinPrice(trainline.getTicketsList())]
								[#if minPrice??]
										[#assign price=minPrice]
								[#else]
									[#assign price=0]
								[/#if]
							[#else]
								[#assign price=0]
							[/#if]
							{
							    "fromPassType":"${trainline.fromPassType!''}",
								"toPassType":"${trainline.toPassType!''}",
							    "price":"${price}",
							    "runTime":"${trainline.runTimeSpan}",
								"fromTime":"${trainline.fromTime}",
								"toTime":"${trainline.toTime}",
                                "fromStation":"${trainline.fromStation}",
                                "toStation":"${trainline.toStation}",
                                "trainSeatNone":"${ctrainSeatNone}",
								"trainTypeName":"${trainline.trainClass}",
								"trainNumber":"${trainline.trainNo}",
                                "seatName":[
									[#if trainline.getTicketsList()??&&trainline.getTicketsList()?size>0]
										[#list trainline.getTicketsList() as trainSeat ]
											[#assign tripLevelMsg=""]
											[#assign trainShow=-1]
											[#if tripLevelConfig??]
												[#if tripLevelConfig.trainFlag]
													[#if tripLevelConfig.trainSeatType??]
														[#if !tripLevelConfig.trainSeatNames?split(",")?seq_contains(trainSeat.seatName)]
															[#assign tripLevelMsg="未选择差旅标准里的火车票座席、"]
															[#if tripLevelConfig.trainExceedConctrol == "unLimited"]
																[#assign trainShow=0]
															[#elseif tripLevelConfig.trainExceedConctrol == "orderLimit"]
																[#assign trainShow=1]
															[#elseif tripLevelConfig.trainExceedConctrol == "displayLimit"]
																[#assign trainShow=2]
															[/#if]
														[/#if]
													[/#if]
												[/#if]
											[/#if]
										[#assign textLabel=""]
											[#if trainShow == 1]
												[#assign textLabel="超标"]
											[#else]
												[#if trainSeat.seats>20]
													[#assign textLabel="有票"]
												[#else]
													[#if trainSeat.seats>0]
														[#assign textLabel="${trainSeat.seats}张"]
													[#else]
														[#assign textLabel="无"]
													[/#if]
												[/#if]
											[/#if]
											{
											    "seats":"${trainSeat.seats}",
											    "trainShow":"${trainShow}",
											    "name":"${trainSeat.seatName}",
												"label":"${textLabel}"
											} [#if trainSeat_index+1<trainline.getTicketsList()?size],[/#if]

										[/#list]
									[/#if]
								]
							}   [#if trainline_index+1<trainlines?size],[/#if]
						[/#if]
					[/#list]
			  ]
			fromStationList =searchKey("fromStation");
             toStationList =searchKey("toStation");
             seatNameList = searchKey("seatName");
			[/#if ]

		Date.prototype.Format = function(fmt){ //author: meizz
	  		var o = {
	    		"M+" : this.getMonth()+1,                 //月份
	    		"d+" : this.getDate(),                    //日
	    		"h+" : this.getHours(),                   //小时
	    		"m+" : this.getMinutes(),                 //分
	    		"s+" : this.getSeconds(),                 //秒
	    		"q+" : Math.floor((this.getMonth()+3)/3), //季度
	    		"S"  : this.getMilliseconds()             //毫秒
	  		};

	  		if(/(y+)/.test(fmt)){
	  			fmt=fmt.replace(RegExp.$1, (this.getFullYear()+"").substr(4 - RegExp.$1.length));
	  		}

	  		for(var k in o){
	  			if(new RegExp("("+ k +")").test(fmt)){
	  				fmt = fmt.replace(RegExp.$1, (RegExp.$1.length==1) ? (o[k]) : (("00"+ o[k]).substr((""+ o[k]).length)));
	  			}
	  		}
	  		return fmt;
		}
			$(function(){
				[#if trainlines??&&trainlines?size>0]
                	filterPop();
                [/#if]
				$(".trainSeatNone").each(function(){
					$(this).parent().attr("trainSeatNone","none");
				});
				$(".filter-pop").on("click","span",function () {
					if($(this).hasClass("active")){
                        $(this).removeClass("active");
					}else {
                        $(this).addClass("active");
					}
                });
                $(".filter-pop").on("click",".makerSure",function () {
                    filterNum=0;
                    var trainTypeNameListArr=[]; //车次类型
                    var fromStationListArr = [];//出发车站
					var toStationListArr = [];//到达车站
                    var seatNameListArr = [];//席别类型
                    var formSeatTimeListArr = [];//出发时间
                    var toSeatTimeListArr = [];//出发时间
                    var fromStationList = $("#fromStation-content span");
                    var toStationList = $("#toStation-content span");
                    var  trainTypeNameList = $("#trainTypeName-content span");
                    var  seatNameList= $("#seatName-content span");
                    var  formSeatTimeList= $("#form-seatTime-content span");
                    var  toSeatTimeList= $("#to-seatTime-content span");
                    for(var i=0;i<fromStationList.length;i++){
                        if($(fromStationList[i]).hasClass("active")){
                            fromStationListArr.push($(fromStationList[i]).html());
                            filterNum++
                        }
                    }
                    for(var j=0;j<toStationList.length;j++){
                        if($(toStationList[j]).hasClass("active")){
                            toStationListArr.push($(toStationList[j]).html());
                            filterNum++
                        }
                    }
                    for(var k=0;k<trainTypeNameList.length;k++){
                        if($(trainTypeNameList[k]).hasClass("active")){
                            trainTypeNameListArr.push($(trainTypeNameList[k]).attr("data-type"));
                            filterNum++
                        }
                    }
                    for(var m=0;m<seatNameList.length;m++){
                        if($(seatNameList[m]).hasClass("active")){
                            seatNameListArr.push($(seatNameList[m]).html());
                            filterNum++
                        }
                    }
                    for(var n=0;n<formSeatTimeList.length;n++){
                        if($(formSeatTimeList[n]).hasClass("active")){
                            formSeatTimeListArr.push($(formSeatTimeList[n]).attr("data-type"));
                            filterNum++
                        }
                    }
                    for(var l=0;l<toSeatTimeList.length;l++){
                        if($(toSeatTimeList[l]).hasClass("active")){
                            toSeatTimeListArr.push($(toSeatTimeList[l]).attr("data-type"));
                            filterNum++
                        }
                    }
                    if(filterNum>0){
                        $("#icon_filter_a").addClass("active");
					}else {
                        $("#icon_filter_a").removeClass("active");
					}
                    filterContent(searchFilter());
                    $("#pop_cut .select-reason-lists").animate({"bottom":"-100%"},function(){
                        $("#pop_cut").hide();
                    })
				})
                $(".filter-pop").on("click",".cancel",function () {
					var spanList = $(".filter-pop span");
					for(var i=0;i<spanList.length;i++){
                        if($(spanList[i]).hasClass("active")){
                            $(spanList[i]).removeClass("active");
						}
					}
                })
                //点击时间关闭
                $("#pop_cut .close").on("click",function () {
                    $("#pop_cut .select-reason-lists").animate({"bottom":"-100%"},function(){
                        $("#pop_cut").hide();
                    })
                });
				//控制静态页面跳转  请删除
				//yejinlong
				$(".fly_search_detail_page").on("click",".Option",function () {
					if($(this).hasClass("none")){
						layer.open({
						    content: "此车次无票！"
						    ,skin: 'msg'
						    ,time: 2 //2秒后自动关闭
						  });
						return false;
					}
					var trainNumber=$(this).attr("trainNumber");
					var fromStation=$(this).attr("fromStation");
					var toStation=$(this).attr("toStation");
					var url = "purchase.jhtml?fromStation=" + fromStation + "&toStation=" + toStation + "&to=${to}&from=${from}&date=${date}&trainNumber=" + trainNumber;
					[#if passengerId??] <!-- 改签-->
						url = url + "&orderNo=${orderNo}"+"&passengerId=${passengerId}&sn=${sn}";
					[/#if]
					[#if applyId??]//出差申请单
						url = url + "&applyId=${applyId}";
					[/#if]
					//代订
					[#if agentMemberId?? && agentMemberId!=""]
						url = url + "&agentMemberId=${agentMemberId}";
					[/#if]
					//从新差旅官网过来
					[#if isPublic?? && isPublic!=""]
						url = url + "&isPublic=${isPublic}";
					[/#if]
					location.href = url;
				});

				//底部切换
				$(".prev").click(function(){
					var date="${date}";
					 var d=new Date(date);
				     d.setDate(d.getDate()-1);
				     if(d.getTime()<new Date("${.now}").getTime()){
				    	 return false;
				     }
				     $("#search").val(d.Format("yyyy-MM-dd"));
				     $("input[name='date']").val(d.Format("yyyy-MM-dd"));
				     $("#sort").val(null);
				     $("#searchForm").submit();
				});

				$(".next").click(function(){
					var date="${date}";
					 var d=new Date(date);
				     d.setDate(d.getDate()+1);
				     var end=new Date();
				     end.setDate(end.getDate()+14);
				     if(d.getTime()>end.getTime()){
				    	 return false;
				     }
				     $("#search").val(d.Format("yyyy-MM-dd"));
				     $("input[name='date']").val(d.Format("yyyy-MM-dd"));
				     $("#sort").val(null);
				     $("#searchForm").submit();
				});


                // 底部按钮点击事件
				$(".fly_search_detail_page footer a").on("click",function(){
					// $(this).toggleClass("active");
					$(".train_list_tabl .Option").show();
					var sort;
					var dataIndex = parseInt($(this).attr("data-index"));
					var sortType=$("#sortType").val();
					if(!(sortType&&sortType.length>0)){
                        sortType='desc';
					}
                    var chtml = $($(this).find("p")).html();
				    if(dataIndex === 0){ //耗时短长
						sort="runTime";
                        if($(this).hasClass("active")){
                            if(chtml==="耗时最短"){
                                $($(this).find("p")).html("耗时最长")
                            }else {
                                $($(this).find("p")).html("耗时最短")
                            }
						}

                        $($($(".fly_search_detail_page footer a")[0]).find("p")).html('最早发车')
                        $($($(".fly_search_detail_page footer a")[1]).find("p")).html('价格最低')
					}else if(dataIndex === 1){ //最早出发
						sort="fromTime";
                        if($(this).hasClass("active")){
                            if(chtml==="最早出发"){
                                $($(this).find("p")).html("最晚出发")
                            }else {
                                $($(this).find("p")).html("最早出发")
                            }
						}
                        $($($(".fly_search_detail_page footer a")[2]).find("p")).html('耗时最短')
                        $($($(".fly_search_detail_page footer a")[1]).find("p")).html('价格最低')
					}else if(dataIndex=== 2){ //只看有票
						
					}else if(dataIndex === 3){ //价格 低-高
						sort="price";
                        if($(this).hasClass("active")){
                            if(chtml==="价格最低"){
                                $($(this).find("p")).html("价格最高")
                            }else {
                                $($(this).find("p")).html("价格最低")
                            }
						}
                        $($($(".fly_search_detail_page footer a")[0]).find("p")).html('最早发车')
                        $($($(".fly_search_detail_page footer a")[2]).find("p")).html('耗时最短')
					}else if(dataIndex === -1){ //点击的是筛选
                        $("#pop_cut").show().children(".select-reason-lists").animate({"bottom":"0"});
                    }
					if(dataIndex !== -1){// 排序
						if($(this).hasClass("active")){
							if(sortType==='desc'){
								sortType="asc";
							}else {
								sortType="desc";
							}
						}else {
                            sortType="asc";
						}
                        $($(".fly_search_detail_page footer a")[0]).removeClass("active");
                        $($(".fly_search_detail_page footer a")[1]).removeClass("active");
                        $($(".fly_search_detail_page footer a")[2]).removeClass("active");
						$(this).addClass("active");
						$("#sort").val(sort);
						$("#sortType").val(sortType);
                        filterContent(searchFilter());
						// $("#searchForm").submit();
					}
				});

				//计算运行时间
				$(".runTime").each(function(){
					var runTime=Number( $(this).html());  //分钟
                    runTime=runTime*60;  //转为秒
					$(this).html(formatDuring(runTime));
				});

				var a = new Array("日", "一", "二", "三", "四", "五", "六");  
				var week = new Date("${date}").getDay();  
				var str = "星期"+ a[week];    
				$("#search").val("${date}  " + str);
			})
			function searchFilter() {
                filterNum=0;
                var trainTypeNameListArr=[]; //车次类型
                var fromStationListArr = [];//出发车站
                var toStationListArr = [];//到达车站
                var seatNameListArr = [];//席别类型
                var formSeatTimeListArr = [];//出发时间
                var toSeatTimeListArr = [];//出发时间
                var fromStationList = $("#fromStation-content span");
                var toStationList = $("#toStation-content span");
                var  trainTypeNameList = $("#trainTypeName-content span");
                var  seatNameList= $("#seatName-content span");
                var  formSeatTimeList= $("#form-seatTime-content span");
                var  toSeatTimeList= $("#to-seatTime-content span");
                for(var i=0;i<fromStationList.length;i++){
                    if($(fromStationList[i]).hasClass("active")){
                        fromStationListArr.push($(fromStationList[i]).html());
                        filterNum++
                    }
                }
                for(var j=0;j<toStationList.length;j++){
                    if($(toStationList[j]).hasClass("active")){
                        toStationListArr.push($(toStationList[j]).html());
                        filterNum++
                    }
                }
                for(var k=0;k<trainTypeNameList.length;k++){
                    if($(trainTypeNameList[k]).hasClass("active")){
                        trainTypeNameListArr.push($(trainTypeNameList[k]).attr("data-type"));
                        filterNum++
                    }
                }
                for(var m=0;m<seatNameList.length;m++){
                    if($(seatNameList[m]).hasClass("active")){
                        seatNameListArr.push($(seatNameList[m]).html());
                        filterNum++
                    }
                }
                for(var n=0;n<formSeatTimeList.length;n++){
                    if($(formSeatTimeList[n]).hasClass("active")){
                        formSeatTimeListArr.push($(formSeatTimeList[n]).attr("data-type"));
                        filterNum++
                    }
                }
                for(var l=0;l<toSeatTimeList.length;l++){
                    if($(toSeatTimeList[l]).hasClass("active")){
                        toSeatTimeListArr.push($(toSeatTimeList[l]).attr("data-type"));
                        filterNum++
                    }
                }
                if(filterNum>0){
                    $("#icon_filter_a").addClass("active");
                }else {
                    $("#icon_filter_a").removeClass("active");
                }
               var  objData={};
                objData.trainTypeName=trainTypeNameListArr;
                objData.fromStation=fromStationListArr;
                objData.toStation=toStationListArr;
                objData.seatName=seatNameListArr;
                objData.formSeatTime=formSeatTimeListArr;
                objData.toSeatTime=toSeatTimeListArr;
                objData.sort=$("#sort").val();
                objData.sortType=$("#sortType").val();
                return objData
            }
			function formatDuring(mss) {
			    var days = parseInt(mss / ( 60 * 60 * 24));
			    var hours = parseInt((mss % ( 60 * 60 * 24)) / ( 60 * 60));
			    var minutes = parseInt((mss % ( 60 * 60)) / ( 60));
			    hours=hours+days*24;
			    return  hours + "小时 " + minutes + "分 " ;
			    
			}
			function searchKey(key) {
			    var arr=[];
                var seatNameArr=[]
				for(var i=0;i<trainObj.length;i++){
				    if(key==='seatName'){
						 seatNameArr =trainObj[i][key];
						 if(seatNameArr.length>0){
						     for(var j=0;j<seatNameArr.length;j++){
                                 if(!(arr.indexOf(seatNameArr[j].name)>-1)){
                                         arr.push(seatNameArr[j].name);
                                 }
							 }
						 }
					}else {
                        if(!(arr.indexOf(trainObj[i][key])>-1)){
                            arr.push(trainObj[i][key]);
                        }
					}

				}
				return arr;
            }
            function filterPop() {
		       var fromStationHtml = '';
				for(var i=0;i<fromStationList.length;i++){
                    fromStationHtml+='<span>'+fromStationList[i]+'</span>'
				}
				$("#fromStation-content").html(fromStationHtml);
                var toStationHtml = '';
                for(var j=0;j<toStationList.length;j++){
                    toStationHtml+='<span>'+toStationList[j]+'</span>'
                }
                $("#toStation-content").html(toStationHtml);
                var trainTypeNameHtml = '';
                for(var k=0;k<seatNameList.length;k++){
                    trainTypeNameHtml+='<span>'+seatNameList[k]+'</span>'
                }
                $("#seatName-content").html(trainTypeNameHtml);
            }

            function checkSeatTime2(formSeatTime,toSeatTime,cformSeatTime,ctoSeatTime) {
		    var flag=true;
                //判断是否选择了出发时间和到达时间
                if(formSeatTime&&formSeatTime.length>0&&toSeatTime&&toSeatTime.length>0){ //选择了出发时间和到达时间
                    if(formSeatTime.length===4&&toSeatTime.length>4){ //选择全天时间
                    }else {
                        if(formSeatTime.length===4){ //出发时间全选
                            if(checkTime(toSeatTime,ctoSeatTime)){

                            }else {
                                flag = false;
                            }
                        }else if(toSeatTime.length===4){//到达时间全选
                            if(checkTime(formSeatTime,cformSeatTime)){

                            }else {
                                flag = false;
                            }
                        }else { //都没有全选
                            if(checkTime(toSeatTime,ctoSeatTime)&&checkTime(formSeatTime,cformSeatTime)){

                            }else {
                                flag = false;
                            }
                        }
                    }
                }else if(formSeatTime&&formSeatTime.length>0){ //只选择了出发时间
                    if(formSeatTime.length===4) { //出发时间全选

                    }else {
                        if(checkTime(formSeatTime,cformSeatTime)){

                        }else {
                            flag = false;
                        }
                    }
                }else if(toSeatTime&&toSeatTime.length>0){ //只选择了到达时间
                    if(toSeatTime.length===4) { //出发时间全选

                    }else {
                        if(checkTime(toSeatTime,ctoSeatTime)){

                        }else {
                            flag = false;
                        }
                    }
                }else { //没选择

                }
                return flag
            }
            function checkSeatTime(formSeatTime,toSeatTime,cformSeatTime,ctoSeatTime,obj) {
                //判断是否选择了出发时间和到达时间
                if(formSeatTime&&formSeatTime.length>0&&toSeatTime&&toSeatTime.length>0){ //选择了出发时间和到达时间
                    if(formSeatTime.length===4&&toSeatTime.length>4){ //选择全天时间
                        obj.show();
                    }else {
                        if(formSeatTime.length===4){ //出发时间全选
                            if(checkTime(toSeatTime,ctoSeatTime)){
                                obj.show();
                            }else {
                                obj.hide();
                            }
                        }else if(toSeatTime.length===4){//到达时间全选
                            if(checkTime(formSeatTime,cformSeatTime)){
                                obj.show();
                            }else {
                                obj.hide();
                            }
                        }else { //都没有全选
                            if(checkTime(toSeatTime,ctoSeatTime)&&checkTime(formSeatTime,cformSeatTime)){
                                obj.show();
                            }else {
                                obj.hide();
                            }
                        }
                    }
                }else if(formSeatTime&&formSeatTime.length>0){ //只选择了出发时间
                    if(formSeatTime.length===4) { //出发时间全选
                        obj.show();
                    }else {
                        if(checkTime(formSeatTime,cformSeatTime)){
                            obj.show();
                        }else {
                            obj.hide();
                        }
                    }
                }else if(toSeatTime&&toSeatTime.length>0){ //只选择了到达时间
                    if(toSeatTime.length===4) { //出发时间全选
                        obj.show();
                    }else {
                        if(checkTime(toSeatTime,ctoSeatTime)){
                            obj.show();
                        }else {
                            obj.hide();
                        }
                    }
                }else { //没选择
                    obj.show();
                }
            }
            //判断某个时间是否在区间范围内
             function timeRange(beginTime, endTime, nowTime) {

                var strb = beginTime.split (":");
                if (strb.length != 2) {
                    return false;
                }

                var stre = endTime.split (":");
                if (stre.length != 2) {
                    return false;
                }

                var strn = nowTime.split (":");
                if (stre.length != 2) {
                    return false;
                }
                var b = new Date ();
                var e = new Date ();
                var n = new Date ();

                b.setHours (strb[0]);
                b.setMinutes (strb[1]);
                e.setHours (stre[0]);
                e.setMinutes (stre[1]);
                n.setHours (strn[0]);
                n.setMinutes (strn[1]);

                if (n.getTime () - b.getTime () > 0 && n.getTime () - e.getTime () < 0) {
                    return true;
                } else {
                    return false;
                }
            }
            //判断车次是否在区间内
			function checkTime(arr,ctime) {

                var flag = false;
                arr.map(item=>{
                    switch (item) {
                        case "0": //选择了0-8:00
                            if(timeRange("00:00","08:00",ctime)){
                                flag = true;
                            }
                            break;
                        case "1"://选择了8-14:00
                            if(timeRange("08:00","14:00",ctime)){
                                flag = true;
                            }
                            break;
                        case "2"://选择了14-19:00
                            if(timeRange("14:00","19:00",ctime)){
                                flag = true;
                            }
                            break;
                        case "3"://选择了19-2400
                            if(timeRange("19:00","24:00",ctime)){
                                flag = true;
                            }
                            break;
                    }
                })
				return flag
            }
            //判断两个数组是否有相同的值
            function haveDuplicateValues(arr1, arr2) {
                for (let i = 0; i < arr1.length; i++) {
                    if (arr2.includes(arr1[i])) {
                        return true; // 找到重复值，返回true
                    }
                }
                return false; // 遍历完第一个数组后未找到重复值，返回false
            }
            function sortData(sort,sortType) {
                var dataList = trainObj;
				if(!(sort&&sortType)){
                    return dataList
				}
                dataList.sort(function(a, b) {
                    var star=a[sort];
                    var end = b[sort];
                    if(sort==="fromTime"){
                        var strb = star.split (":");
                        var stre = end.split (":");
                        var bTime = new Date ();
                        var eTime = new Date ();
                        bTime.setHours (strb[0]);
                        bTime.setMinutes (strb[1]);
                        eTime.setHours (stre[0]);
                        eTime.setMinutes (stre[1]);
                        star=bTime.getTime ();
                        end =eTime.getTime ();
					}
                    if(sortType==='desc'){
                        return end - star;
					}else {
                        return star - end;
					}
                });
                return dataList
            }
            //判断除席座类别其他的条件
			function checkFilterOther(seatName,cseatName,formSeatTime,toSeatTime,cformSeatTime,ctoSeatTime) {
		        var flag = true;
                if(seatName&&seatName.length>0) { //选择了席位
                    if(haveDuplicateValues(seatName,cseatName)){ //是否有相同的席位
                        //判断是否选择了出发时间和到达时间
                        flag =   checkSeatTime2(formSeatTime,toSeatTime,cformSeatTime,ctoSeatTime);
                    }else {
                        flag= false;
                    }
                }else {
                    //判断是否选择了出发时间和到达时间
                    flag =   checkSeatTime2(formSeatTime,toSeatTime,cformSeatTime,ctoSeatTime);
                }
                return flag;
            }
            //判断除了起始车站的其他条件是否满足
            function checkFilter2(formSeatTime,toSeatTime,cformSeatTime,ctoSeatTime,trainTypeName,ctrainseatnone,seatName,cseatName, ctrainTypeName) {
				 var flag = true;
                if(trainTypeName&&trainTypeName.length>0){ //选择了车次类型
                    if(trainTypeName.indexOf("G")>-1&&trainTypeName.indexOf("Z")>-1){ //选择了高铁和普通车
                        if(trainTypeName.indexOf("P")>-1){ //选择了只有票
                            if(ctrainseatnone==='have'){
                                //判断是否选择了席位
                                flag=  checkFilterOther(seatName,cseatName,formSeatTime,toSeatTime,cformSeatTime,ctoSeatTime);
                            }else {
                                flag= false;
                            }
                        }else {
                            //判断是否选择了席位
                            flag=  checkFilterOther(seatName,cseatName,formSeatTime,toSeatTime,cformSeatTime,ctoSeatTime)
                        }
                    }else if(trainTypeName.indexOf("G")>-1) { //只选择了高铁
                        if(ctrainTypeName==='C'||ctrainTypeName==="G"||ctrainTypeName==="D"){
                            if(trainTypeName.indexOf("P")>-1){ //只选择了有票
                                if(ctrainseatnone==='have'){
                                    //判断是否选择了席位
                                    flag=  checkFilterOther(seatName,cseatName,formSeatTime,toSeatTime,cformSeatTime,ctoSeatTime)
                                }else {
                                    flag= false;
                                }
                            }else {
                                //判断是否选择了席位
                                flag=  checkFilterOther(seatName,cseatName,formSeatTime,toSeatTime,cformSeatTime,ctoSeatTime)
                            }
                        }else{
                            flag= false;
                        }

                    }else if(trainTypeName.indexOf("Z")>-1) { //只选择了普通车
                        if(!(ctrainTypeName==='C'||ctrainTypeName==="G"||ctrainTypeName==="D")){
                            if(trainTypeName.indexOf("P")>-1){ //只选择了有票
                                if(ctrainseatnone==='have'){
                                    //判断是否选择了席位
                                    flag=  checkFilterOther(seatName,cseatName,formSeatTime,toSeatTime,cformSeatTime,ctoSeatTime)
                                }else {
                                    flag= false;
                                }
                            }else {
                                //判断是否选择了席位
                                flag=  checkFilterOther(seatName,cseatName,formSeatTime,toSeatTime,cformSeatTime,ctoSeatTime)
                            }
                        }else{
                            flag= false;
                        }
                    }else { //只选择了有票
                        if(ctrainseatnone==='have'){
                            //判断是否选择了席位
                            flag=  checkFilterOther(seatName,cseatName,formSeatTime,toSeatTime,cformSeatTime,ctoSeatTime)
                        }else {
                            flag= false;
                        }

                    }
                }else { //没有选择了车次类型
                    //判断是否选择了席位
                    flag=  checkFilterOther(seatName,cseatName,formSeatTime,toSeatTime,cformSeatTime,ctoSeatTime)
                }
				return flag;
            }
            function filterContent(objData) {
		  	    var  trainTypeName = objData.trainTypeName;
		  	    var fromStation= objData.fromStation;
                var  toStation = objData.toStation;
                var seatName= objData.seatName;
                var  formSeatTime = objData.formSeatTime;
                var toSeatTime= objData.toSeatTime;
                var  sort = objData.sort;
                var sortType= objData.sortType;
				var html ="";
                var list = sortData(sort,sortType);
               for(var i=0;i<list.length;i++){
                  var item = list[i];
                  var flag = true;
                  var  ctrainTypeName =item.trainTypeName;
                  var  cfromStation = item.fromStation;
                  var  ctoStation = item.toStation;
                  var  cseatName = [];
			      var  cformSeatTime =  item.fromTime;
			      var  ctoSeatTime = item.toTime;
			      var ctrainseatnone = item.trainSeatNone;
			      var cseatNamecontent =item.seatName;
			      var crunTime =item.runTime;
			      var ctrainNumber = item.trainNumber;
			      var cprice = item.price;
			      var cfromPassType = item.fromPassType;
			      var ctoPassType=item.toPassType;
			      var seatHtml = '';

                 var  crunTimes=crunTime*60;  //转为秒
				   var crunTimeHtml =formatDuring(crunTimes);
					for(var j=0;j<cseatNamecontent.length;j++){
                        cseatName.push(cseatNamecontent[j].name)
						var obj = cseatNamecontent[j];
                        var ctrainShow =obj.trainShow;
                        var cseats = obj.seats;
                        var cseatNameObj = obj.name;
                        var clabel =obj.label;
                        if(ctrainShow!==2&&j<4){ //拼接座位信息
                            if(ctrainShow.toString()==='1'||cseats.toString()==='0'){
                                seatHtml+='<li class="item dis" >'
							}else {
                                seatHtml+='<li class="item" >'
							}
                            seatHtml+='<lable data-seatName="'+cseatNameObj+'">'+cseatNameObj+'\n'+
													'<span>'+clabel+'</span>\n'+
												' </lable>'
							if(ctrainseatnone==='none'){
                                seatHtml+='<input type="hidden" class="trainSeatNone">'
							}
                            seatHtml+='</li>'
						}
					}
                   if(fromStation&&fromStation.length>0&&toStation&&toStation.length>0){ //选择了起始车站
                       if(fromStation.indexOf(cfromStation)>-1&&toStation.indexOf(ctoStation)>-1){ //符合条件
                           flag =   checkFilter2(formSeatTime,toSeatTime,cformSeatTime,ctoSeatTime,trainTypeName,ctrainseatnone,seatName,cseatName, ctrainTypeName)
					   }else {
                           flag = false;
					   }
                   }else {
                       if(fromStation&&fromStation.length>0){//只选择了始发站
                           if(fromStation.indexOf(cfromStation)>-1){ //符合条件
                               flag =   checkFilter2(formSeatTime,toSeatTime,cformSeatTime,ctoSeatTime,trainTypeName,ctrainseatnone,seatName,cseatName, ctrainTypeName)
                           }else {
                               flag = false;
						   }
					   }else if(toStation&&toStation.length>0){ //只选择了到达车站
                           if(toStation.indexOf(ctoStation)>-1){ //符合条件
                               flag =  checkFilter2(formSeatTime,toSeatTime,cformSeatTime,ctoSeatTime,trainTypeName,ctrainseatnone,seatName,cseatName, ctrainTypeName)
                           }else {
                               flag = false;
						   }
					   }else {//符合条件
                           flag =    checkFilter2(formSeatTime,toSeatTime,cformSeatTime,ctoSeatTime,trainTypeName,ctrainseatnone,seatName,cseatName, ctrainTypeName)
					   }
				   }
				   if(flag){ //符合条件则渲染
                       html +='<div class="Option" fromStation="'+cfromStation+'" toStation="'+ctoStation+'" trainSeatNone="'+ctrainseatnone+'" trainTypeName="'+ctrainTypeName+'" trainNumber="'+ctrainNumber+'"  >\n'+
							   '<div class="section">\n'+
									   '<div class="section_1 sec">\n' +
                              				 '<p class="from_time">'+cformSeatTime+'</p>\n';
					   if(cfromPassType==="途经"){
						   html +=   '<p class="station"><span class="iconPassType icon_road">过</span>'+cfromStation+'</p>\n'
					   }else {
						   html +=   '<p class="station"><span class="iconPassType icon_start">始</span>'+cfromStation+'</p>\n'
					   }

                       html +=	 '</div>\n' +
                              			 '<div class="section_2 sec">\n' +
                             				  '<p class="taken runTime">'+crunTimeHtml+'</p>\n' +
                             				  '<p class="train_no">'+ctrainNumber+'</p>\n' +
                             			  '</div>\n' +
                             			  '<div class="section_3 sec">\n' +
                             				  '<p class="to_time">'+ctoSeatTime+'</p>\n'
                       if(ctoPassType==="途经"){
                           html +=   '<p class="station"><span class="iconPassType icon_road">过</span>'+ctoStation+'</p>\n'
                       }else {
                           html +=   '<p class="station"><span class="iconPassType icon_start">终</span>'+ctoStation+'</p>\n'
                       }
				      html +=	  '</div>\n'+
							            '<div class="section_4 sec">\n'
                       if(cprice===0){
                           html +=     '<p class="price">无票<span></span></p>\n'
                       }else {
                           html +=      '<p class="price">￥'+cprice+'<span></span></p>\n'
                       }
                       html +=  '</div>\n'+
                               '</div>\n'+
							   '<ul class="seatslevel">\n'+
									seatHtml+
							   '</ul>'+
					   '</div>'
				   }

			   }
                $(".train_list_table").html(html);
                if(html.length===0){
                    $(".public_empty_section2").show()
                }else {
                    $(".public_empty_section2").hide()
                }
            }
		</script>
	</body>

</html>