<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<title>火车票</title>
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">
		<script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/lifeService.css" />
    	<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js"></script>
	</head>
	<body class="has_fixed_footer">
	[#assign oi=order.orderItems[0]]
	[#assign ticketOrder=oi.getVirtualProductOrderInfoMapValue('ticketOrders')[0]]
	[#assign amount=oi.getVirtualProductOrderInfoMapValue('amount')]
	
		<div class="public_top_header">
			<a href="javascript:history.go(-1);" class="return_back"></a> 
			退票详情
			[#include "./wechat/include/head_nav.ftl" /]
		</div>
		[#if oi.getVirtualProductOrderInfoMapValue('refundDate')??]
		<div class="refund_detail_page">
			<header>退票信息</header>
			<div class="box">
				<div class="item">
					<label>乘客信息：</label>
					<div class="value">
						${ticketOrder.passengerName}&nbsp;&nbsp;${ticketOrder.idcardNo?substring(0,1)}***************${ticketOrder.idcardNo?substring((ticketOrder.idcardNo?length-1),(ticketOrder.idcardNo?length))}
					</div>
				</div>
				<div class="item">
					<label>申请时间：</label>
					<div class="value">
						${oi.getVirtualProductOrderInfoMapValue('refundDate')}
					</div>
				</div>
				<div class="item">
					<label>退款状态：</label>
					<div class="value">
						[#if ticketOrder.state==11]
										审核通过
								[#elseif ticketOrder.state==7]
									审核拒绝
								[#else]
									审核中
								[/#if]	
					</div>
				</div>
				<div class="item">
					<label>退款金额：</label>
					<div class="value">
						￥${order.toatlAmount?number-ticketOrder.refundFee?number-(ticketOrder.purPrice?number-ticketOrder.facePrice?number)-oi.getVirtualProductOrderInfoMapValue("serviceCharge")?number}
					</div>
				</div>
				<div class="item">
					<label>审核原因：</label>
					<div class="value">
						-
					</div>
				</div>
			</div>
		</div>
		[/#if]	
		[#if oi.returns??&&oi.returns?size>0]
		[#list oi.returns as obj]
		 <div class="refund_detail_page">
			<header>线下退票/改签信息</header>
			<div class="box">
			<div class="item">
					<label>乘客信息：</label>
					<div class="value">
						${ticketOrder.passengerName}&nbsp;&nbsp;${ticketOrder.idcardNo?substring(0,1)}***************${ticketOrder.idcardNo?substring((ticketOrder.idcardNo?length-1),(ticketOrder.idcardNo?length))}
					</div>
				</div>
				<div class="item">
					<label>申请时间：</label>
					<div class="value">
						${obj.createDate}
					</div>
				</div>
				<div class="item">
					<label>退款状态：</label>
					<div class="value">
						[#if obj.returnStatus='unapprove']
									审核中
								[#elseif obj.returnStatus='notpass']
									审核拒绝
								[#elseif obj.returnStatus='returned']
									审核通过
								[/#if]	
					</div>
				</div>
				<div class="item">
					<label>退款金额：</label>
					<div class="value">
						[#if obj.refundAmount??]
							￥${obj.refundAmount}
						[#else]
							-
						[/#if]
					</div>
				</div>
				<div class="item">
					<label>审核原因：</label>
					<div class="value">
						${obj.auditReason!"-"}
					</div>
				</div>
			</div>
		</div>
		[/#list]
		[/#if]
		<script>
			$(function(){
				
			})
		</script>
	</body>
</html>
