<!DOCTYPE html>
<html style="background: #fff;">
	<head>
		<meta charset="utf-8" />
		<title>选择积分</title>
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="black">
        <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js" ></script>
        <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
        <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/plusWap.css" /> 
		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

		<script type="text/javascript" src="${base}/resources/wechat/js/jquery.validate.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
		
		<script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js" ></script>
		<script>
			$(function(){
				//输入密码事件
				$("#password").keyup(function(){
					var l_pwd = this.value.length;
					if(l_pwd>=1 && l_pwd <= 6 && event.keyCode!=8){
						var _input = document.getElementById("number"+l_pwd);
						_input.value =  this.value.charAt(l_pwd-1);
						if(l_pwd == 6){//输入完成动作
							var _pwd=this.value;
							$("#paymentPop").hide();
							this.blur();
							
							var $checkedIds = $("#rechargeRecords input[name='coinIds']:enabled:checked");
							$.ajax({
								url: "check_paypassword.jhtml",
								type: "POST",
								data: $checkedIds.serialize()+"&paypassword="+_pwd,
								dataType: "json",
								cache: false,
								success: function(message) {
									$(".password_section input").val("");
									if (message.type == "success") {
										$("#orderRefreshForm").submit();
									} else {
										 layer.open({
											    content: message.content
											    ,skin: 'msg'
											    ,time: 2 //2秒后自动关闭
											  });
									}
								}
							});
							
						}
					}else if(event.keyCode==8){//退格键删除
						var _input = document.getElementById("number"+(l_pwd+1));
						_input.value = '';
					}
				})
				//点击输入框密码框获取焦点
				$(".submit_section .btn_submit_long").click(function(){
					/*var $checkedIds = $("#rechargeRecords input[name='coinIds']:enabled:checked");
					if($checkedIds.filter(":checked").size()>0){
						$("#paymentPop").show().find("#password").focus();
					}
					else{
						$("#orderRefreshForm").submit();
					}*/
					$("#orderRefreshForm").submit();
				})
				//点击输入框获取焦点
				$(".password_section .input_box").click(function(){
					$("#password").focus();
				})
				
				$(".popbg .icon_close").click(function(){
					$(this).parents(".popbg").hide();
					$(".password_section input").val("");
				})
				
				$(".btn_concel").click(function(){
				   $("#paymentset").hide();
			    });
				
				//新增penglong20161223 积分显示合并
				$("#selectAll").click( function() {
					var $this = $(this);
					var $enabledIds = $("#rechargeRecords input[name='coinIds']:enabled");
					if ($this.prop("checked")) {
						$enabledIds.prop("checked", true);
					} else {
						$enabledIds.prop("checked", false);
					}
				});
				//新增结束
				
				var $submitBtn = $(":submit");
				
				// 表单验证
				$("#inputForm").validate({
					rules: {
						payPassword: {
							required:true,
							digits:true,
							minlength: 6,
							maxlength: 6
						},
						rePayPassword: {
							required:true,
							digits:true,
							minlength: 6,
							maxlength: 6,
							equalTo:"#payPassword"
						}
					},
					messages: {
						payPassword: {
							pattern: "格式错误,请输入六位数字密码",
							digits: "格式错误,请输入六位数字密码"
						},
						rePayPassword: {
							pattern: "格式错误,请输入六位数字密码",
							digits: "格式错误,请输入六位数字密码"
						}
					},
					submitHandler:function(form){
						$.ajax({
							url:'${base}/member/payPasswordSet.jhtml',
							type:'POST',
							data:$("#inputForm").serialize(),
							cache:false,
							async:false,
							beforeSend:function(){
								$submitBtn.prop("disabled", true);
							},
							success:function(flag){
								if(flag == 1){
									layer.open({
									    content: '设置成功'
									    ,skin: 'msg'
									    ,time: 2 //2秒后自动关闭
									  });
									 $("#paymentset").hide();
								
								}else if(flag == 2){
									layer.open({
									    content: '两次输入的支付密码不一致'
									    ,skin: 'msg'
									    ,time: 2 //2秒后自动关闭
									  });
									$submitBtn.prop("disabled", false);
								}else{
									layer.open({
									    content: '设置失败'
									    ,skin: 'msg'
									    ,time: 2 //2秒后自动关闭
									  });
									$submitBtn.prop("disabled", false);
								}
							}
						});
					}
					
				});
				
			})
			
			function ticketTab(flag,obj){
				$(".coin").removeClass("active");
				obj.addClass("active");
				if(flag){
					$("#unRechargeRecords").css('display', 'none');
					$("#rechargeRecords").css("display","block")
					
				}
				else{
					$("#rechargeRecords").css('display', 'none');
					$("#unRechargeRecords").css("display","block")
				}
			}

			function payPassword(){
				$("#paymentPop").hide()
				$("#paymentset").show();
	          }
			
			
		</script>
		
	</head>
	<body style="background: #fff;" class="coupon_list_new_page order_select_coin_common">
		<div class="paymentPage">
			<div class="public_top_header">
                <span class="return_back" onclick="javascript:history.back();"></span>
				积分支付
			</div>
			<div class="make_split">
				<div class="tab_theme_section">
			 		<div class="item coin active" onclick="ticketTab(true,$(this))"><a href="javascript:;">可用</a></div>
			 		<div class="item coin" id="recordN" onclick="ticketTab(false,$(this))"><a href="javascript:;">不可用</a></div>
			 	</div>
			</div>
		 	
		 	
		 	<form id="orderRefreshForm" action="info_virtual.jhtml" method="post">
		 	     <input type="hidden" name="ids" value="${ids}" />
	             <input type="hidden" name="receiverId"  value="${receiverId}" />  
	             <input type="hidden" name="whiteBarIds"  value="${whiteBarIds}" />
	             <input type="hidden" name="couponCodeId" id="couponCodeId" value="${couponCodeId}" />  
	             <input type="hidden" name="memo" value="${memo!}" />
	             <input type="hidden" name="groupId" value="${groupId}" />
	             <input type="hidden" name="pId" value="${productId}" />
	             <input type="hidden" id="quantity"  name="quantity" value="${quantity}"/>	   
	             <input type="hidden" name="type" value="${type}" />
	             
	             
	            <input type="hidden" name="hotelId" id="hotelId" value="${hotelInfoVo.hotelId}"/>
			    <input type="hidden" name="departureDate" id="departureDate" value="${hotelInfoVo.departureDate}"/>
			    <input type="hidden" name="roomTypeId" id="roomTypeId" value="${hotelInfoVo.roomTypeId}"/>
			    <input type="hidden" name="ratePlanId" id="ratePlanId" value="${hotelInfoVo.ratePlanId}"/>
				<input type="hidden" name="arrivalDate" id="arrivalDate" value="${hotelInfoVo.arrivalDate}"/>
				<input type="hidden" name="numberOfRooms" id="numberOfRooms" value="${hotelInfoVo.numberOfRooms}"/>
				<input type="hidden" name="latestArrivalTime" id="latestArrivalTime" value="${hotelInfoVo.latestArrivalTime}"/>
				<input type="hidden" name="currencyCode" id="currencyCode"  value="RMB"/>
				<input type="hidden" name="bookingResult"   value="${hotelInfoVo.customNames}"/>
			    <input type="hidden" name="mobile" id="mobile"  value="${hotelInfoVo.mobile}"/>
			    <input type="hidden" name="paymentType" id="paymentType"  value="Prepay"/>
				<input type="hidden" name="totalCost" id="totalCost"  value="${hotelInfoVo.totalCost}"/>
				<input type="hidden" name="totalPrice" id="totalPrice"  value="${hotelInfoVo.totalPrice}"/>
				<input type="hidden" name="bedType" id="bedType"  value="${hotelInfoVo.bedType}"/>
				<input type="hidden" name="url" id="url"  value="${hotelInfoVo.url}"/>
				<input type="hidden" name="hotelName" id="hotelName"  value="${hotelInfoVo.hotelName}"/>
				<input type="hidden" name="ratePlanName" id="ratePlanName"  value="${hotelInfoVo.ratePlanName}"/>
				<input type="hidden" name="address" id="address"  value="${hotelInfoVo.address}"/>
				<input type="hidden" name="maxCustomers"  value="${hotelInfoVo.maxCustomers}"/>
			    <input type="hidden" name="wifi"  value="${hotelInfoVo.wifi}"/>
			    <input type="hidden" name="window"  value="${hotelInfoVo.window}"/>
			    <input type="hidden" name="customNames" id="customNames"  value="${hotelInfoVo.customNames}"/>
	           
				
						
		 	<div class="coin_pay_section" id="rechargeRecords"> 
		 	[#if (rechargeRecords?size > 0)]
		 	    <div class="item">
		 			<a href="javascript:;">
		 				  积分余额
		 				<div class="right">${coinConvert(rechargeYAmount, coinShowRate!1)}
		 					<div class="radio_section">
					 			<input type="checkbox"   id="selectAll"  [#if coinIds1?size > 0]checked="checked"[/#if]>
					 			<label for="selectAll"></label>
					 		</div>
		 				</div>
		 			</a>
		 		</div>
		 		[#list rechargeRecords as rechargeRecord]
		 		<div class="item" style="display: none;">
		 			<a href="javascript:;">
[#--		 				${rechargeRecord.coinTypeId.name}<span>(有效期：[#if rechargeRecord.isLimitExpired]${rechargeRecord.beginTime} — ${rechargeRecord.endTime}[#else]长期有效 [/#if])</span>--]
						${aliasCoinName(rechargeRecord.coinTypeId.id, member.companyId.id, rechargeRecord.coinTypeId.name)}<span>(有效期：[#if rechargeRecord.isLimitExpired]${rechargeRecord.beginTime} — ${rechargeRecord.endTime}[#else]长期有效 [/#if])</span>
		 				<div class="right">${coinConvert(rechargeRecord.balance, coinShowRate!1)}
		 					<div class="radio_section">
					 			<input type="checkbox"  name="coinIds"  id="coin${rechargeRecord_index}" value="${rechargeRecord.id}"  [#if coinIds1?seq_contains(rechargeRecord.id)]checked="checked"[/#if]>
					 			<label for="coin${rechargeRecord_index}"></label>
					 		</div>
		 				</div>
		 			</a>
		 		</div>
		 		[/#list]
		 		[#if (rechargeRecordsun?size > 0)]
		 		<!-- 不可用积分 -->
		 		<div class="noUserInfo">
	 				<p>温馨提示：您有不支持订单商品的积分，<span style="color:#f68e13" onclick="ticketTab(false,$('#recordN'))">前往查看</span></p>
	 			</div>
		 		[/#if]
		 		[#else]
					<div class="public_empty_section [#if comPersonConf && com_person_EmptyBg != null && com_person_EmptyBg != ""] ${com_person_EmptyBg!}[#else] Public_Bg[/#if]"> <p class="tip">这里空空如也</p></div>
				[/#if]	
		 	</div>
		   </form>
		 	
		 	<div class="coin_pay_section" id="unRechargeRecords" style="display: none;"> 
		 		[#if (rechargeRecordsun?size > 0)]
		 		 <div class="item">
		 			<a href="javascript:;">
		 				  积分余额
		 				<div class="right">${coinConvert(rechargeNAmount, coinShowRate!1)}
		 					<div class="radio_section">
					 			<input type="checkbox"  name="coinIds" id="coinnotall"   >
					 			<label for="coinnotall"></label>
					 		</div>
		 				</div>
		 			</a>
		 		</div>
		 		[#list rechargeRecordsun as rechargeRecord]
		 		<div class="item" style="display: none;">
		 			<a href="javascript:;">
[#--		 				${rechargeRecord.coinTypeId.name}<span>(有效期：[#if rechargeRecord.isLimitExpired]${rechargeRecord.beginTime} — ${rechargeRecord.endTime}[#else]长期有效 [/#if])</span>--]
						${aliasCoinName(rechargeRecord.coinTypeId.id, member.companyId.id, rechargeRecord.coinTypeId.name)}<span>(有效期：[#if rechargeRecord.isLimitExpired]${rechargeRecord.beginTime} — ${rechargeRecord.endTime}[#else]长期有效 [/#if])</span>
		 				<div class="right">${coinConvert(rechargeRecord.balance, coinShowRate!1)}
		 					<div class="radio_section">
					 			<input type="checkbox" id="coin${rechargeRecord_index}" value="${rechargeRecord.id}" name="coin">
					 			<label for="coin${rechargeRecord_index}"></label>
					 		</div>
		 				</div>
		 			</a>
		 		</div>
		 		[/#list]
		 		[#else]
					<div class="public_empty_section [#if comPersonConf && com_person_EmptyBg != null && com_person_EmptyBg != ""] ${com_person_EmptyBg!}[#else] Public_Bg[/#if]"> <p class="tip">这里空空如也</p></div>
				[/#if]	
		 	</div>
		</div> 
		[#--<div class="submit_section">--]
	 		[#--<input type="button" name="" id="" value="确定" class="btn_submit_long"/>--]
	 	[#--</div>--]
        <div class="btn-option submit_section flexbox align-items-c justify-content-c">
            <input type="button" name="" id="" value="确定" class="btn_submit_long btn-option-right"/>
        </div>
		<div class="popbg" id="paymentPop">
			<div class="close"></div>
			
		 	<div class="pop">
		 		<header>输入支付密码<span class="icon_close"></span></header>
			 	<div class="password_section">
			 		<div class="input_box clearfix" id="inputs_box">
				 		<input type="number" id="number1" readonly="readonly"/>
				 		<input type="number" id="number2" readonly="readonly"/>
				 		<input type="number" id="number3" readonly="readonly"/>
				 		<input type="number" id="number4" readonly="readonly"/>
				 		<input type="number" id="number5" readonly="readonly"/>
				 		<input type="number" id="number6" readonly="readonly"/>
			 		</div>
			 		<input type="tel" name="" id="password" value="" maxlength="6" class="pwd"/>
			 		
			 		[#if member.payPassword]
						<a href="${base}/payPassword/find.jhtml?type=2" class="forgotton">忘记密码？</a>
					[#else]
					    <a href="javascript:void(0)" onclick="payPassword()" class="forgotton">设置密码</a>
					[/#if]
					
			 		
			 	</div> 
			 	<!-- <div class="footer">
			 		<input type="button" name="" id="" value="确定" class="btn_submit"/>
			 		<input type="button" name="" id="" value="取消" class="btn_concel"/>
			 	</div> -->
		 	</div>
		</div>
		<div class="popbg" id="paymentset">
			<div class="close"></div>
			<div class="pop">
				<form id="inputForm" action="payPasswordSet.jhtml" method="post">
					<header>设置支付密码<span class="icon_close"></span></header>
					<div class="fli_pub_pop_form">
						<div class="item">
							<input type="tel" name="payPassword" id="payPassword" class="input pwd" placeholder="请输入新密码"/>
						</div>
	
						<div class="item">
							<input type="tel" name="rePayPassword" id="rePayPassword" class="input pwd"  placeholder="请确认新密码"/>
						</div>
					</div>
					<div class="footer">
						<input type="button" name="" id="" value="取消" class="btn_concel" />
						<input type="submit" name="" id="payPasswordSetBtn" value="确定" class="btn_submit" />
					</div>
				</form>
			</div>
		</div>
		
	</body>  
</html>
