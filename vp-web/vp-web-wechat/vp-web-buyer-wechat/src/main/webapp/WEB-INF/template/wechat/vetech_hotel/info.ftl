<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>确认订单</title>
		<meta name="apple-mobile-web-app-capable" content="yes" />
		<meta name="apple-mobile-web-app-status-bar-style" content="black" />
		<meta http-equiv="Expires" content="0" />
		<meta http-equiv="Cache-Control" content="no-cache" />
		<meta http-equiv="Pragma" content="no-cache" />
		<script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/plusWap.css" />
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/lifeService.css" />
		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/card.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/jquery.validate.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>

		<script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js"></script>

		<script type="text/javascript">
			var $orderRefreshForm;
			var $memo;
			var $memoMsg;
			$().ready(function() {
				$orderRefreshForm = $("#orderRefreshForm");
				$memo = $("#memo");
				$memoMsg = $("#memoMsg");
				var $submit = $("#submit");
				var $orderForm = $("#orderForm");
				var $receiverIdCard = $("#receiverIdCard");
				var $saveIdcardNo = $("#saveIdcardNo");
				var $receiverId = $("#receiverId");
				function submitOrder() {
				     if (repeatFlag) {
                         return false;
                     }
                     repeatFlag = true;
					var url = "${base}/member/order/createVirtual.jhtml";
					$.ajax({
						url: url,
						type: "POST",
						data: $orderForm.serialize(),
						dataType: "json",
						cache: false,
						beforeSend: function() {
							$submit.prop("disabled", true);
							repeatFlag = true;
							$submit.val("提交中，请稍候");
						},
						success: function(message) {
							if(message.type == "success") {
							    repeatFlag = true;
							    location.href = "payment.jhtml?sn=" + message.content + "&type=0";
							}
							if(message.type == "warn") {
							    repeatFlag = false;
                                $submit.val("确认支付");
								$submit.prop("disabled", false);
								layer.open({
									content: message.content,
									skin: 'msg',
									time: 2 //2秒后自动关闭
								});
							} else if(message.type == "error") {
							    repeatFlag = false;
                                $submit.val("确认支付");
								var msg="下单失败，请稍后重试";
								if(message.content!=null && message.content!=""){
								    msg=message.content;
								}
								layer.open({
									content: msg,
									skin: 'msg',
									time: 2 //2秒后自动关闭
								});

							}
						}
					});
				}

				//输入密码事件
				$("#password").keyup(function() {
					var l_pwd = this.value.length;
					if(l_pwd >= 1 && l_pwd <= 6 && event.keyCode != 8) {
						var _input = document.getElementById("number" + l_pwd);
						_input.value = this.value.charAt(l_pwd - 1);
						if(l_pwd == 6) { //输入完成动作
							var _pwd = this.value;
							$("#paymentPop").hide();
							this.blur();
							var device = openDevice();
							if(device == "android") {
								vpshop_android.callHideSoftInput();
							}

							var $checkedIds = $("#rechargeRecords input[name='coinIds']:enabled:checked");
							$.ajax({
								url: "check_paypassword.jhtml",
								type: "POST",
								data: {
									sn: $("#orderSn").val(),
									coinIds: $("#coinIds").val(),
                                    whiteBarIds: $("#whiteBarIds").val(),
									paypassword: _pwd
								},
								dataType: "json",
								cache: false,
								success: function(message) {
									$(".password_section input").val("");
									if(message.type == "success") {
										submitOrder();
									} else {
										layer.open({
											content: message.content,
											skin: 'msg',
											time: 2 //2秒后自动关闭
										});
									}
								}
							});

						}
					} else if(event.keyCode == 8) { //退格键删除
						var _input = document.getElementById("number" + (l_pwd + 1));
						_input.value = '';
					}
				})





				var $payPasswordSetBtn = $("#payPasswordSetBtn");



				//保存身份证号
				$saveIdcardNo.click(function() {
					var receiverId = $receiverId.val();
					var idcardNo = $receiverIdCard.val();
					if(!idCardNoUtil.checkIdCardNo(idcardNo)) {
						layer.open({
							content: '请正确填写身份证号！',
							skin: 'msg',
							time: 2 //2秒后自动关闭
						});
						return false;
					}
					$.ajax({
						url: "saveReviceIdcard.jhtml",
						type: "POST",
						data: {
							receiverId: receiverId,
							idcardNo: idcardNo
						},
						dataType: "json",
						cache: false,
						success: function(msg) {
							if(msg == true) {
								layer.open({
									content: '身份证号码保存成功!',
									skin: 'msg',
									time: 2 //2秒后自动关闭
								});
							} else {
								layer.open({
									content: '身份证号码保存失败',
									skin: 'msg',
									time: 2 //2秒后自动关闭
								});
							}
						}
					});

				});

				// 订单提交
                //提交订单(防止重复提交)
			   var repeatFlag = false;
				$submit.click(function() {
				    var rechargeRecordAmount = "${rechargeRecordAmount}"; //勾选了积分支付
                        var whiteBarYAmount = "${whiteBarYAmount}"; //勾选了白条支付

						[#if member.companyId.isFreePay ]
							submitOrder();
						[#else]
						var cashPayFlag="${member.companyId.cashPayFlag}";
					    var amountPayable ="${order.amountPayable}";//还需支付金额
					    if(cashPayFlag!=null && cashPayFlag!="" && cashPayFlag==0){ //不允许使用现金支付
							   if(amountPayable > 0) {
					             layer.open({
					                 content: '积分余额不足！',
					                 skin: 'msg',
					                 time: 2 //2秒后自动关闭
					             });
					             return false;
					         }
						   }
                        if (rechargeRecordAmount > 0) { //可用积分大于0，弹出密码框
                            $("#paymentPop").show().find("#password").focus();
                        } else if (whiteBarYAmount > 0) {
                            $("#paymentPop").show().find("#password").focus();
                        } else {
                            submitOrder();
                        }
						[/#if]
				});
			});



			//选择积分页面跳转
			function selectCoin() {
                var url = "groupSelectCoin.jhtml";
				$("#orderRefreshForm").attr("action", url);
				$("#orderRefreshForm").submit();
			}

			//选择积分页面跳转
			function selectWhiteBar() {
                var url = "groupSelectWhiteBar.jhtml";
				$("#orderRefreshForm").attr("action", url);
				$("#orderRefreshForm").submit();
			}

			function selectCreditCard(){
                var sn=$("#orderSn").val();
                location.href="${base}/ctrip_hotel/creditCardList.jhtml?sn="+sn;
			}

			// 验证手机号
			jQuery.validator.addMethod("isTel", function(value,element) {
			    var length = value.length;
			    var mobile = /^(((13[0-9]{1})|14[57]{1}|(15[0-9]{1})|17[378]{1}|(18[0-9]{1}))+\d{8})$/;
			    if(this.optional(element) || (length==11 && mobile.test(value))){
			    	$("#midMobile").val(value);
			    }
			    return this.optional(element) || (length==11 && mobile.test(value));
			   }, "请正确填写您的联系方式");

			$("#orderForm").validate({
			    rules : {
			        mobile : {
			        	isTel:"#mobile",
			            required : true,
			            minlength : 11

			        }
			    },
			    messages : {
			    	mobile : {
			            required : "请输入手机号",
			            minlength : "确认手机不能小于11个字符",
			            isMobile : "请正确填写您的手机号码"
			        }
			    },
				errorPlacement: function(error, element) {
					error.appendTo(element.parent());
				}
			});

		</script>
	</head>

	<body id="myOrderPage">
		<div class="myOrderPage hotel_order_page common-order-info-page" style="margin-top:0;">
			<div class="public_top_header bg_theme">
				<a href="javascript:history.back();" class="return_back"></a>
				确认订单
				[#include "./wechat/include/head_nav.ftl" /]
			</div>

			<div class="page_header_bg hotel">
                <header>
                    <div class="base">
                        <div class="img">
                            [#if homeHotelInfoVO.picUrl!=null]
                                <img src="${homeHotelInfoVO.picUrl}"/>
                            [#else]
                                <img src="${base}/resources/wechat/img/hotel/hotel_default.jpg"/>
                            [/#if]
                        </div>
                        <div class="info">
                            <p class="title">${homeHotelInfoVO.hotelName}</p>
                            <p class="time">${homeHotelInfoVO.arrDate}至${homeHotelInfoVO.depDate}<span>共${homeHotelInfoVO.diffDays}晚</span></p>
                            <p class="bed_type">${homeHotelInfoVO.roomTypeName}</p>
                            <div class="attr">
                                <span>
                                    ${roomBedInfo}
                                </span>

                              [#--  [#if hotelInfoVo.breakfast!=null]
                                    <span>${hotelInfoVo.breakfast}</span>
                                [/#if]--]

								[#-- [#if hotelInfoVo.floor!=null]
                                    <span>${hotelInfoVo.floor}楼</span>
                                [/#if]--]
                            </div>
                        </div>
                    </div>
                    <div class="tips_section">
                        <span class="icon_hotel"></span>
						[#if homeHotelInfoVO.cancelType=='22']
							限时取消 ${cancelCheckRule}
						[#elseif homeHotelInfoVO.cancelType=='23']
							免费取消
						[#else]
						    不可取消
						[/#if]
                    </div>
                </header>
			</div>

			<section class="favourable_section card-rd margin-side occupant_section">
				<div class="lines_Section_all">
					<a href="javascript:void(0)">
						<label>房客姓名</label>
						<div class="right_text">
							[#assign gustName=homeHotelInfoVO.contactName]
							[#if homeHotelInfoVO.contactName?contains("-")]
								[#assign gustName=""]
								[#list homeHotelInfoVO.contactName?split(",") as names]
									[#if gustName!=""]
										[#assign gustName=gustName+","]
									[/#if]
									[#assign gustName=gustName+names?split("-")[0]]
								[/#list]
							[/#if]
							<span>${gustName}</span>
						</div>
					</a>
				</div>
				<div class="lines_Section_all">
					<a href="javascript:void(0)">
						<label>联系电话</label>
						<div class="right_text">
							${homeHotelInfoVO.mobileTel}
						</div>
					</a>
				</div>
			</section>



 				<form id="orderRefreshForm" action="" method="post">
				[#list cartItems as cartItem]
				<input type="hidden" name="ids" value="${cartItem.id}" /> [/#list]
				<input type="hidden" name="coinIds" value="${coinIds}" id="coinIds" />
				<input type="hidden" name="whiteBarIds" value="${whiteBarIds}" id="whiteBarIds" />
				<input type="hidden" id="receiverId" name="receiverId" [#if receiver??] value="${receiver.id}" [/#if] />
				<input type="hidden" id="productId" name="productId" value="${productId}" />
				<input type="hidden" name="couponCodeId" value="${couponCodeId}" />
				<input type="hidden" name="pId" value="${productId}" />
				<input type="hidden" name="quantity" value="${quantity}" />
				<input type="hidden" name="toatlAmount" value="[#if order??]${order.toatlAmount}[#else]000[/#if]" />
				<input type="hidden" name="coinAmount" value="${order.coinAmount}" />
				<input type="hidden" name="amountPaid" value="${order.amountPaid}" />
				<input type="hidden" id="" name="type" value="${type}" />

              	<input type="hidden" name="HotelCd" id="hotelId" value="${homeHotelInfoVO.hotelCd}"/>
				<input type="hidden" name="ArrDate"  value="${homeHotelInfoVO.arrDate}"/>
				<input type="hidden" name="DepDate"  value="${homeHotelInfoVO.depDate}"/>
				<input type="hidden" name="GustName" id="contactName" value="${homeHotelInfoVO.gustName}"/>
				<input type="hidden" name="MobileTel" id="mobile"  value="${homeHotelInfoVO.mobileTel}"/>
				<input type="hidden" name="CRSRmType" value="${homeHotelInfoVO.CRSRmType}"/>
				 <input type="hidden" name="RmNum" id="numberOfRooms" value="${homeHotelInfoVO.rmNum}"/>
				<input type="hidden" name="GuestNum" id="guestCount" value="${homeHotelInfoVO.guestNum}"/>
				 <input type="hidden" name="MustPayMoney" id="totalPrice1"  value="${homeHotelInfoVO.mustPayMoney}"/>
				 <input type="hidden" name="apiPrice" value="${homeHotelInfoVO.apiPrice}"/>
                <input type="hidden" name="customNames" id="customNames"  value="${homeHotelInfoVO.gustName}"/>
                <input type="hidden" id="dailyPriceInfo"  name="dailyPriceInfo" value="${homeHotelInfoVO.dailyPriceInfo}"/>
				 <input type="hidden"  name="picUrl" value="${homeHotelInfoVO.picUrl}"/>
				 <input type="hidden"  name="hotelName" value="${homeHotelInfoVO.hotelName}"/>
				 <input type="hidden"  name="address" value="${homeHotelInfoVO.address}"/>
                 <input type="hidden"  name="roomTypeName" value="${homeHotelInfoVO.roomTypeName}"/>
                 <input type="hidden"  name="bedType" value=" ${homeHotelInfoVO.bedType}"/>
					<input type="hidden"  name="breakfastType" value=" ${homeHotelInfoVO.breakfastType}"/>
				<input type="hidden"  name="diffDays" value=" ${homeHotelInfoVO.diffDays}"/>
				<input type="hidden"  name="contactName" value=" ${homeHotelInfoVO.contactName}"/>
                <input type ="hidden" id="orderAmountHi" name="totalPrice" value="${homeHotelInfoVO.mustPayMoney}"/><!--订单总额-->
				<input type="hidden"  name="pricePlanId" value="${homeHotelInfoVO.pricePlanId}"/>
				<input type="hidden"  name="msj" value="${homeHotelInfoVO.msj}"/>
				<input type="hidden"  name="payCd" value="${homeHotelInfoVO.payCd}"/>
                 <input type ="hidden" id="freightAmountHi" value="0"/>
                <input type ="hidden" id="actSaveAmount" value="0"/>
                <input type="hidden" id="paymentMethodId" name="paymentMethodId" maxlength="200" value="1"/>  <!--支付方式-->
                <input type="hidden" id="shippingMethodId" name="shippingMethodId" maxlength="200" value="1"/>  <!--支付方式-->
				<input type="hidden" name="cancelCheckRule"  value="${homeHotelInfoVO.cancelCheckRule}" />
				<input type="hidden" name="cancelType" value="${homeHotelInfoVO.cancelType}"/>
				<input type="hidden" name="lastTime"  value="${homeHotelInfoVO.lastTime}"/>
				<input type="hidden" name="invoiceMode" id="invoiceMode" value="${homeHotelInfoVO.invoiceMode}"/>
				<input type="hidden" name="thirdCoinAmount" value="${thirdCoinAmount}" />
				<input type="hidden" name="coinBalance" value="${coinBalance}"/>

				<input type="hidden" name="coinTypeIdsEcho" value="${coinTypeIdsEcho}" />
			    <input type="hidden" name="coinTypeIds" value="${coinTypeIds}" />
				<!-- 下面参数为了计算金额 -->
				<input type="hidden" id="price"  name="price" value="${order.price}"/>
				<input type="hidden" id="freight"  name="freight" value="${order.freight}"/>
				<input type="hidden" id="couponDiscount"  name="couponDiscount" value="${order.couponDiscount}"/>
				<input type="hidden" id="promotionDiscount"  name="promotionDiscount" value="${order.promotionDiscount}"/>
				[#if tripSnapshot?? ]
					<input type="hidden" name="applyId" value="${tripSnapshot.applyId}"/>
					<input type="hidden" name="tripType" value="${tripSnapshot.tripType}"/>
					<input type="hidden" name="serviceInfo" value="${tripSnapshot.serviceInfo}"/>
					<input type="hidden" name="tripReasonCode" value="${tripSnapshot.tripReasonCode}"/>
					<input type="hidden" name="tripRuleBroken" value="${tripSnapshot.tripRuleBroken}"/>
					<input type="hidden" name="tripReason" value="${tripSnapshot.tripReason}"/>
					<input type="hidden" name="tripMemo" value="${tripSnapshot.tripMemo}"/>
					<input type="hidden" name="isBreak" value="${tripSnapshot.isBreak}"/>
				[/#if]
					<input type="hidden" name="whetherExceedStandard"  value="${homeHotelInfoVO.whetherExceedStandard}"/>
					<input type="hidden" id="agentMemberId" name="agentMemberId" value="${agentMemberId}">
			</form>


 			<form id="orderForm" action="createVirtual.jhtml" method="post">
				<input type="hidden" name="ids" value="${coinIds}" />
				<input type="hidden" name="whiteBarIds" value="${whiteBarIds}" />
				<input type="hidden" name="HotelCd" value="${homeHotelInfoVO.hotelCd}"/>
				<input type="hidden" name="ArrDate"  value="${homeHotelInfoVO.arrDate}"/>
				<input type="hidden" name="DepDate"  value="${homeHotelInfoVO.depDate}"/>
				<input type="hidden" name="GustName" value="${homeHotelInfoVO.gustName}"/>
				<input type="hidden" name="MobileTel"  value="${homeHotelInfoVO.mobileTel}"/>
				<input type="hidden" name="CRSRmType" value="${homeHotelInfoVO.CRSRmType}"/>
				 <input type="hidden" name="RmNum" value="${homeHotelInfoVO.rmNum}"/>
				<input type="hidden" name="GuestNum" value="${homeHotelInfoVO.guestNum}"/>
				 <input type="hidden" name="MustPayMoney" value="${homeHotelInfoVO.mustPayMoney}"/>
				 <input type="hidden" name="apiPrice" value="${homeHotelInfoVO.apiPrice}"/>
                <input type="hidden" name="customNames" value="${homeHotelInfoVO.gustName}"/>
				<input type="hidden" name="productId"  value="${productId}"/>
                  <input type="hidden" name="quantity" value="1"/>
                <input type="hidden" name="dailyPriceInfo" value="${homeHotelInfoVO.dailyPriceInfo}"/>
				 <input type="hidden"  name="picUrl" value="${homeHotelInfoVO.picUrl}"/>
				 <input type="hidden"  name="hotelName" value="${homeHotelInfoVO.hotelName}"/>
				 <input type="hidden"  name="address" value="${homeHotelInfoVO.address}"/>
				 <input type="hidden"  name="pricePlanId" value="${homeHotelInfoVO.pricePlanId}"/>
				<input type="hidden"  name="msj" value="${homeHotelInfoVO.msj}"/>
				<input type="hidden"  name="PayCd" value="${homeHotelInfoVO.payCd}"/>
                 <input type="hidden"  name="roomTypeName" value="${homeHotelInfoVO.roomTypeName}"/>
                 <input type="hidden"  name="bedType" value=" ${homeHotelInfoVO.bedType}"/>
				<input type="hidden"  name="breakfastType" value=" ${homeHotelInfoVO.breakfastType}"/>
				<input type="hidden"  name="diffDays" value=" ${homeHotelInfoVO.diffDays}"/>
				<input type="hidden"  name="contactName" value=" ${homeHotelInfoVO.contactName}"/>
                <input type ="hidden" name="totalPrice" value="${homeHotelInfoVO.mustPayMoney}"/><!--订单总额-->
                <input type="hidden" name="thirdCoinAmount" value="${thirdCoinAmount}" />
                <!-- 
                <input type ="hidden" id="freightAmountHi" value="0"/>
                <input type ="hidden" id="actSaveAmount" value="0"/>
                 -->
                <input type="hidden" name="paymentMethodId" maxlength="200" value="1"/>  <!--支付方式-->
                <input type="hidden" name="shippingMethodId" maxlength="200" value="1"/>  <!--支付方式-->
				<input type="hidden" name="lastTime"  value="${homeHotelInfoVO.lastTime}" id="lastTime"/>
				<input type="hidden" name="cancelCheckRule"  value="${homeHotelInfoVO.cancelCheckRule}" id="cancelCheckRule"/>
				<input type="hidden" name="invoiceMode" id="invoiceMode" value="${homeHotelInfoVO.invoiceMode}"/>
                <input type="hidden" name="cancelType" value="${homeHotelInfoVO.cancelType}"/>
                [#if tripSnapshot?? ]
					<input type="hidden" name="applyId" value="${tripSnapshot.applyId}"/>
					<input type="hidden" name="tripType" value="${tripSnapshot.tripType}"/>
					<input type="hidden" name="serviceInfo" value="${tripSnapshot.serviceInfo}"/>
					<input type="hidden" name="tripReasonCode" value="${tripSnapshot.tripReasonCode}"/>
					<input type="hidden" name="tripRuleBroken" value="${tripSnapshot.tripRuleBroken}"/>
					<input type="hidden" name="tripReason" value="${tripSnapshot.tripReason}"/>
					<input type="hidden" name="tripMemo" value="${tripSnapshot.tripMemo}"/>
					<input type="hidden" name="isBreak" value="${tripSnapshot.isBreak}"/>
				[/#if]
				<input type="hidden" name="whetherExceedStandard"  value="${homeHotelInfoVO.whetherExceedStandard}"/>
				<input type="hidden" name="agentMemberId" value="${agentMemberId}">
			</form>



          [#--[#if (member.companyId?? && (member.companyId.sourceFlag==null || member.companyId.sourceFlag==""))]--]
			[#--<section class="favourable_section">--]
				[#--<div class="lines_Section_all">--]
					[#--<a href="javascript:void(0)" onclick="selectCoin()" class="fli_link_line">--]
						 [#--积分支付--]

						[#--<div class="like_btn_box">--]
							[#--[#list coins as coin] [#if coin.coinTypeId.isCredit]--]
							[#--<span class="like_btn">	${coin.coinTypeId.name?substring(0,1)}${coinConvert(coin.balance, coinShowRate!1)}[#if isUnifiedDeduct](企)[/#if]</span> [/#if] [/#list]--]
							[#----]
							[#--[#if thirdCoin??]--]
							[#--<span class="like_btn" thirdCoinAmount="${thirdCoinAmount }">${thirdCoin.coinName?substring(0,1)}${thirdCoin.amount}</span>--]
							[#--[/#if]--]
							[#----]
						[#--</div>--]
						[#--[#if order.coinAmount > 0]--]
						[#--<div class="right_text red">-${coinConvert(order.coinAmount, coinShowRate!1)}</div>--]
						[#--[#else]--]
						[#--<div class="right_text gray">未使用</div>--]
						[#--[/#if]--]
					[#--</a>--]
				[#--</div>--]

				[#--<div class="lines_Section_all" [#if (member.companyId?? && member.companyId.hasWhitebar?? && member.companyId.hasWhitebar)] [#else]style="display:none;" [/#if]>--]
					[#--<a href="javascript:void(0)" onclick="selectWhiteBar()" class="fli_link_line">--]

						[#--白条支付--]
						[#--[#list whiteBarCoins as coin] [#if coin.coinTypeId.isCredit && coin.enabledFlag && coin.isFreeze]--]
						[#--<span class="like_btn">	${coin.coinTypeId.name?substring(0,1)}${coinConvert(coin.balance, coinShowRate!1)}[#if isUnifiedDeduct](企)[/#if]</span> [/#if] [/#list] [#if order.whiteBarAmount gte 0]--]
						[#--<div class="right_text red">-${coinConvert(order.whiteBarAmount, coinShowRate!1)}</div>--]
						[#--[#else]--]
						[#--<div class="right_text gray">未使用</div>--]
						[#--[/#if]--]
					[#--</a>--]
				[#--</div>--]
			[#--</section>--]
           [#--[/#if]--]



			<section class="amount_section card-rd margin-side">

				 <p class="item flexbox align-items-c justify-content-space-between">
					     <label>订单总额</label>
					    <span>[#if productShowRate??]${coinConvert(order.toatlAmount, productShowRate, true, false)}[#else]${currency(order.toatlAmount, true, false)}[/#if]</span>
				    </p>
					[#if serviceCharge!=null && serviceCharge!=""]
                        <p class="item flexbox align-items-c justify-content-space-between">
                            <label>服务费</label>
                            <span>[#if productShowRate??]${coinConvert(serviceCharge, productShowRate, true, false)}[#else]${currency(serviceCharge, true, false)}[/#if]</span>
                        </p>
					[/#if]

					<div class="lines_Section_all fli_link_line" onclick="selectCoin()">
						<!--<a href="javascript:void(0)" onclick="selectCoin()" class="fli_link_line">-->
							<label>积分支付</label>
							<div class="right_text red">
									[#if order.coinAmount > 0]
									-${coinConvert(order.coinAmount, coinShowRate!1)}
									[#else]
										[#list coins as coin] [#if coin.coinTypeId.isCredit]
										<span class="like_btn">	${coin.coinTypeId.name?substring(0,1)}${coinConvert(coin.balance, coinShowRate!1)}[#if isUnifiedDeduct](企)[/#if]</span> [/#if] [/#list]

										[#if thirdCoin??]
										<span class="like_btn" thirdCoinAmount="${thirdCoinAmount }">${thirdCoin.coinName?substring(0,1)}${thirdCoin.amount}</span>
										[/#if]
									[/#if]
							</div>
						[#--</a>--]
					</div>
					[#if order.whiteBarAmount > 0 || whiteBarCoins??&&whiteBarCoins?size>0]
						<div class="lines_Section_all fli_link_line" onclick="selectWhiteBar()"  [#if (member.companyId?? && member.companyId.hasWhitebar?? && member.companyId.hasWhitebar)] [#else]style="display:none;" [/#if]>
							[#--<a href="javascript:void(0)" onclick="selectWhiteBar()" class="fli_link_line">--]

								<label>白条支付</label>
								<div class="right_text red">
									[#if order.whiteBarAmount > 0]
										-${coinConvert(order.whiteBarAmount, coinShowRate!1)}
									[#else]
										[#list whiteBarCoins as coin] [#if coin.coinTypeId.isCredit && coin.enabledFlag && coin.isFreeze]
										<span class="like_btn">	${coin.coinTypeId.name?substring(0,1)}${coinConvert(coin.balance, coinShowRate!1)}[#if isUnifiedDeduct](企)[/#if]</span> [/#if] [/#list]
									[/#if]
								</div>
							[#--</a>--]
						</div>
					[/#if]
				    [#--<p [#if member.companyId?? && member.companyId.cashPayFlag?? && member.companyId.cashPayFlag == 0] style="display: none;" [/#if]>--]
						[#--实付款--]
                        [#--<span>${currency(order.amount, true, false)}</span>--]
					    [#--<input type="hidden" id="_amountPayable" value="${order.amount}">--]
				    [#--</p>--]
				      <p class="item flexbox align-items-c justify-content-space-between">
					    <label>下单立减</label>
					  <span>-${currency(discountAmount, true, false)}</span>
				    </p>
			</section>


            <div class="bottom info-footer flexbox align-items-c justify-content-space-between">
                <!-- [#if member.companyId?? && member.companyId.cashPayFlag?? && member.companyId.cashPayFlag == 0] style="display: none;" [/#if] -->
                <div class="total" >
                    <input type="hidden" id="_amountPayable" value="${order.amount}">
                [#--[#assign floatValue = order.amountPayable?number]--]
				 [#if productShowRate??]
					 [#assign floatValue =coinConvert(order.amountPayable,productShowRate)?number]
				 [#else]
					 [#assign floatValue = order.amountPayable?number]
				 [/#if]
                [#assign integerPart = floatValue?floor]
                [#assign decimalPart = (floatValue * 100)?round % 100 / 100]
                    <span class="money-total" id="money-total">
						[#if productShowRate??]
                			<i class="iconfontAli icon-ali-jifen"></i>
						[#else]
                			￥
						[/#if]
						<span>${integerPart}</span>${decimalPart?string(".00")}</span>
                    <p>实付款</p>
                </div>
                <input type="button" name="" id="submit" value="确认支付" class="btn_submit_long" />
            </div>
			[#--<div class="bottom">--]
				 [#--<input type="button" name="" id="submit"  value="确认支付" class="btn_submit_long" />--]
			[#--</div>--]
		</div>

		[#include "./wechat/include/set_payPasswords.ftl" /]

        <script>
        	
        	[#include "./wechat/include/categoryCheck.ftl" /]
        
            $(function(){
                $(".header .right_text_gray").on("click",function(){
                    $(this).next("p").toggle()
                })

            })

        </script>
	</body>

</html>