		 			
		 		<input type="hidden" id="totalPages" value="${page.totalPages}"/>
		 		[#if page.total > 0]
		 		
		 		 [#list page.content as coupon]
					 [#if !(coupon.isAllRecived&&!coupon.isRecived)]
				<div class="item flexbox align-items-c [#if coupon.isRecived]have[/#if] [#if coupon.isExpired?? && coupon.isExpired]expired[/#if]" couponId="${coupon.id}" onclick="receive(this)">
					<div class="text">
						[#if coupon.couponSn=="conversion"]
							<div class="flexbox align-items-c ">
								<div class="numLeft  flexbox align-items-c justify-content-c flex-column">
								<p class="num exchange-num">
									<span>兑换券</span>
								</p>
                                    <div class="btn flexbox justify-content-c align-items-c 	">
					<span id="receiveSpan${coupon.id}"
						[#if coupon?? && coupon.uri??]
							[#if coupon.uri?index_of("http") == 0]
								data-url="${coupon.uri}"
							[#else ]
							 	data-url="${base}${coupon.uri}"
							[/#if]
						[#else]
							[#if coupon.couponSn=="marketing"]
							data-url="${base}/coupon_center_wechat/coupon_product_list.jhtml?couponId=${coupon.id}"
							[#elseif coupon.couponSn=="ActCoupon"]
								[#if coupon.actId??]
								data-url="${base}/act/list/${coupon.actId}.jhtml"
								[#else]
								data-url="${base}/product/index.jhtml"
								[/#if]
							[#elseif coupon.couponSn=="product"]
								[#if coupon.products?size==1]
									[#list coupon.products as product]
									data-url="${base}${product.path}"
									[/#list]
								[#else]
								data-url="${base}/coupon_center_wechat/coupon_product_list.jhtml?couponId=${coupon.id}"
								[/#if]
							[/#if]
						[/#if]
						>
						[#if coupon.isRecived]
							[#if coupon.couponBeginDate??&&coupon.couponBeginDate>.now]
								未生效
							[#elseif coupon.isConvert?? && coupon.isConvert]
								已使用
							[#elseif coupon.isExpired?? && coupon.isExpired]
								已过期
							[#else]
								去使用
							[/#if]
						[#else]
							[#if coupon.isAllRecived]
								已领完
							[#elseif coupon.isAllRecived==false && coupon.isRecived==false]
								立即领取
							[/#if]
						[/#if]
                    </span>
                                    </div>
								</div>
								<div class="content-right flex1">
									<p>
										  兑换指定商品
									</p>
									<p>
										[#if coupon.couponInvalidDays??]
											领券当天起${coupon.couponInvalidDays}天内可用
										[#else]
											[#if coupon.couponBeginDate!=null]${coupon.couponBeginDate?string('yyyy.MM.dd')}[/#if][#if coupon.couponEndDate!=null]-${coupon.couponEndDate?string('yyyy.MM.dd')}
										[/#if]
										[/#if]
									</p>
								</div>
							</div>
						[#else]
							<div class="flexbox align-items-c ">
								<div class="numLeft  flexbox align-items-c justify-content-c flex-column">
								<p class="num">
									[#assign floatValue = coupon.couponAmount?number]
									[#assign integerPart = floatValue?floor]
									[#assign decimalPart = (floatValue * 100)?round % 100 / 100]
									[#if coupon.type??&&coupon.type==2]
												<span class="discountNum"> ${coupon.couponAmount}</span>折
									[#else ]
										<i>￥</i><span>${integerPart}</span>[#if decimalPart>0]${decimalPart?string(".00")}[/#if]
									[/#if]
                                </p>
                                    <div class="btn flexbox justify-content-c align-items-c 	">
					<span id="receiveSpan${coupon.id}"
						[#if coupon?? && coupon.uri??]
							[#if coupon.uri?index_of("http") == 0]
								data-url="${coupon.uri}"
							[#else ]
							 	data-url="${base}${coupon.uri}"
							[/#if]
						[#else]
							[#if coupon.couponSn=="marketing"]
							data-url="${base}/coupon_center_wechat/coupon_product_list.jhtml?couponId=${coupon.id}"
							[#elseif coupon.couponSn=="ActCoupon"]
								[#if coupon.actId??]
								data-url="${base}/act/list/${coupon.actId}.jhtml"
								[#else]
								data-url="${base}/product/index.jhtml"
								[/#if]
							[#elseif coupon.couponSn=="product"]
								[#if coupon.products?size==1]
									[#list coupon.products as product]
									data-url="${base}${product.path}"
									[/#list]
								[#else]
								data-url="${base}/coupon_center_wechat/coupon_product_list.jhtml?couponId=${coupon.id}"
								[/#if]
							[/#if]
						[/#if]
						>
						[#if coupon.isRecived]
							[#if coupon.couponBeginDate??&&coupon.couponBeginDate>.now]
								未生效
							[#elseif coupon.isConvert?? && coupon.isConvert]
								已使用
							[#elseif coupon.isExpired?? && coupon.isExpired]
								已过期
							[#else]
								去使用
							[/#if]
						[#else]
							[#if coupon.isAllRecived]
								已领完
							[#elseif coupon.isAllRecived==false && coupon.isRecived==false]
								立即领取
							[/#if]
						[/#if]
                    </span>
                                    </div>
								</div>
								<div class="content-right flex1">
									<p>
								          [#if coupon.minimumPrice!=null && coupon.minimumPrice!=0&& coupon.maxPrice!=null && coupon.maxPrice!=0]
											  ${coupon.minimumPrice}到${coupon.maxPrice}以内可用
										  [#elseif coupon.minimumPrice!=null &&coupon.minimumPrice!=0]
											  满${coupon.minimumPrice}可用
										  [#elseif coupon.maxPrice!=null && coupon.maxPrice!=0]
											  ${coupon.maxPrice}以内可用
										  [#else ]
											  不限金额
										  [/#if]

									</p>
									<p>
										[#if coupon.couponInvalidDays??]
											领券当天起${coupon.couponInvalidDays}天内可用
										[#else]
											[#if coupon.couponBeginDate!=null]${coupon.couponBeginDate?string('yyyy.MM.dd')}[/#if][#if coupon.couponEndDate!=null]-${coupon.couponEndDate?string('yyyy.MM.dd')}
										[/#if]
										[/#if]
									</p>
                                    <p class="category-of-use">
											[#if coupon.scope??]
												${coupon.scope}
											[#else]
												[#if coupon.isAllSupplier]全品牌.[#else][#list coupon.brands as brand]${brand.name}.[/#list][/#if][#if coupon.isAllCategory]全分类[#else]${coupon.categoryName}[/#if]
											[/#if]
														(不含邮费，特价商品除外)
                                    </p>
								</div>
							</div>
						[/#if]
					</div>

					[#if coupon.isAllRecived || coupon.isRecived]
					   <span class="icon_obtained"></span>
					[/#if]

				</div>
					 [/#if]
				[/#list]
			
			[#else]
						<div class="public_empty_section [#if comPersonConf && com_person_EmptyBg != null && com_person_EmptyBg != ""] ${com_person_EmptyBg!}[#else] Public_Bg[/#if]">
                            <p class="tip">这里空空如也，先去逛逛吧~</p>
			 			 	<a href="${base}/product/index.jhtml" class="pub_link_theme">去逛逛</a>
			 			 </div>
		 	[/#if]


             