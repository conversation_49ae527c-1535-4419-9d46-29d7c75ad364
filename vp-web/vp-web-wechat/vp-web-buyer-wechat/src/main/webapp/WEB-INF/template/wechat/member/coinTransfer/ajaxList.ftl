		 	[#if type??&&type=="creditCardList"]
				
					[#if page??&&page.content?size>0]
						[#list page.content as obj]
					 		<li>
					 			<p class="title">
					 				<span class="name">${obj.bankName}</span>
					 				[#if obj.bankAccountNoDec?length>4]
										********${obj.bankAccountNoDec?substring(obj.bankAccountNoDec?length-4,obj.bankAccountNoDec?length)} 
									[#else]
										${obj.bankAccountNoDec} 
									[/#if]
					 			</p>
					 			<p>发卡城市：${obj.bankCity}</p>
					 			
					 			<div class="right">
					 				<a href="repayment.jhtml?id=${obj.id}" class="btn_main btn_primary sm">还款</a>
									<a href="javascript:;" val="${obj.id}" class="btn_main btn_red delete sm">解绑</a>
					 			</div>
					 		</li>
					 	[/#list]
					 [/#if]
		 	[#else]
			 
			 		[#if page??&&page.content?size>0]
						[#list page.content as oi]
					 		<li>
					 			<p class="title">
					 				<span class="name">
					 					${oi.getVirtualProductOrderInfoMapValue('bankName')}
					 				</span>
					 				[#if oi.getVirtualProductOrderInfoMapValue('bankAccountNo')?length>4]
										********${oi.getVirtualProductOrderInfoMapValue('bankAccountNo')?substring(oi.getVirtualProductOrderInfoMapValue('bankAccountNo')?length-4,oi.getVirtualProductOrderInfoMapValue('bankAccountNo')?length)} 
									[#else]
										${oi.getVirtualProductOrderInfoMapValue('bankAccountNo')} 
									[/#if]
								</p>
					 			<p>申请日期：${oi.createDate?string("yyyy-MM-dd HH:mm:ss")}</p>
					 			
					 			<div class="right">
					 				<p class="coin">${oi.getVirtualProductOrderInfoMapValue('payAmount')}</p>
					 				<p class="type">
					 						[#if oi.order.orderStatus == "unpaid"]
												交易失败
										[#elseif oi.order.orderStatus == "unconfirmed"]
												交易中
										[#elseif oi.order.orderStatus == "confirmed"]
												交易中
										[#elseif oi.order.orderStatus == "shipped"]
												交易中
										[#elseif oi.order.orderStatus == "completed"]
												交易成功
										[#elseif oi.order.orderStatus == "cancelled"]
												交易失败
										[/#if]
					 				</p>
					 			</div>
					 		</li>
				 		[/#list]
				 	[/#if]
		 	[/#if]
			
