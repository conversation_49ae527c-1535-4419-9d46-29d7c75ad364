<!DOCTYPE html>
<html style="background: #fff;">
	<head>
		<meta charset="utf-8" />
		<title>系统头像</title>
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="black">
        <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js" ></script>
        <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
        <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/local-img.css" />
		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>

        <script type="text/javascript" src="${base}/resources/wechat/plugins/mobilelayer/layer/layer.js" ></script>
	</head>
	<style type="text/css">
        .layui-layer-msg.layui-layer-dialog{
			width: auto;
			min-width: 3.6rem;
		}
		.layui-layer-msg.layui-layer-dialog .layui-layer-content{
			font-size: 0.32rem;
			padding: 0.68rem;
			/*padding-left: 1.18rem;*/
			padding-right: 0;
			padding-left: 0;
            white-space: nowrap;
			width: 100%;
			min-width: 2.6rem;
			background: #ffffff;
			color: #444;
		}
        .layui-layer-msg.layui-layer-dialog .layui-layer-content	.dongup{
            margin-left: 1.15rem;
		}
        .layui-layer-dialog .layui-layer-content .layui-layer-ico{
            top: 50%;
            transform: translateY(-50%);
			left: 0.32rem;
            width: 0.68rem;
            background-size: cover;
            height: 0.68rem;
		}
	</style>
	<body class="local-img-page" style="background: #fff;" >
         <input type="hidden" name="imgId" id="imgId" value="" />
		<div class="loginPage edit_info">
			<div class="public_top_header">
                <span class="return_back" onclick="javascript:history.back();"></span>
				系统头像
			</div>
			<form id="tf" enctype="multipart/form-data">
				<div class="select_user_head">
					<div class="select_head_box">
						[#assign imgObj=[
							{
							'type':'male',
							'typeName':'男',
							'length':8
							},
							{
							'type':'female',
							'typeName':'女',
							'length':8
							}
						]]
						[#list imgObj as item]
							<div class="head_box clearfix">
								[#list 1..(item.length) as i]
									<div class="item flexbox align-items-c justify-content-c" id="${item.type}_${i}">
										<div class="state" ></div>
										<img src="${base}/imghead/user/${item.type}_${i}.png"/>
                                	</div>
								[/#list]
							</div>
						[/#list]
						<input type="button" id="submit" value="确定" class="btn_cornor_long"/>
					</div>
				</div>
			</form>
		</div>

		<script>
            $(function () {
                $(".item").on("click",function () {
                    let str = "/imghead/user/" + $(this).attr("id") +".png"
					$("#imgId").val(str)
                    $(".item").removeClass("active")
                    $(this).addClass("active")
                });
                //点击确认按钮
                $("#submit").click(function () {
                    var $sex = $("#imgId").val();
                    if ($sex == null || $sex == "") {

                        layer.msg('请选择头像!',{time: 1000, shade: [0.3,'#000',true]});
                        return;
                    }

                    var form = new FormData(document.getElementById("tf"));
                    form.append("albumId", $sex);
                    $.ajax({
                        url: "${base}/member/uploadFile.jhtml",
                        type: "post",
                        data: form,
                        processData: false,
                        contentType: false,
						async:false,
                        success: function (data) {
                            if (data.type == "success") {
                                layer.msg("<span class='dongup'>上传中...</span>", {time: 2000, icon: 16, shade: [0.3,'#000',true]}, function () {
                                    // 跳转个人资料页 or 个人中心页
									window.location.href = "${base}/member/information.jhtml";
                                });
                            } else {
                                layer.msg(data.content, {time: 1000, shade: [0.3,'#000',true]}, function () {
                                    layer.closeAll();
                                    // 上传失败
                                });
                            }
                        }
                    });
                })
			})

		</script>
	</body>  
</html>
