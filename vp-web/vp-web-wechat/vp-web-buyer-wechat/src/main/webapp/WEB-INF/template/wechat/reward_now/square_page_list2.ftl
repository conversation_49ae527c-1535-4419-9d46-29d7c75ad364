<input type="hidden" id="totalPages" value="${page.totalPages}"/>
[#if page.total > 0]
[#if member??&&member.sourceUIVersion==0]
    [#assign versionType="old"]
    [#assign imgSrc='gift/icon_good_gray']
[#else ]
    [#assign versionType="new"]
    [#assign imgSrc='icons/dz']
[/#if]
    [#list page.content as rewardRecord]
        [#if rewardRecord.type==1 ]
        <div class="item square_item " rewardRecordId="${rewardRecord.id}" type="1" id="item${rewardRecord.id}_1">
            <div class="top">
            <div class="start">
            <div class="portrait">
                <div class="picture">
                    [#if rewardRecord.image??]
                        [#if rewardRecord.image?contains("dingtalk")||rewardRecord.image?contains("http")]
                            <img class="header" src="${rewardRecord.image}" alt="">
                        [#else]
                            <img class="header" src="${base}/${rewardRecord.image}" alt="">
                        [/#if]

                    [#else]
                        [#if rewardRecord.gender == 'female']
                            <img class="header" src="${base}/resources/wechat/img/girl.png" alt="">
                        [#else]
                            <img class="header" src="${base}/resources/wechat/img/boy.png" alt="">
                        [/#if]
                    [/#if]

                    [#if rewardRecord.gender == 'female']
                        <img class="sex" src="${base}/resources/wechat/img/gift/icon_female.png" alt="">
                    [#else]
                        <img class="sex" src="${base}/resources/wechat/img/gift/icon_male.png" alt="">
                    [/#if]
                </div>
            </div>
            [#if rewardRecord.redType == 0]
                <div class="info  [#if  versionType=='new']flexbox flex-column justify-content-c [/#if]" [#if member.id!=rewardRecord.memberId && coinShowRate == null]onclick="jumpUrl('memberHome/${rewardRecord.memberId}.jhtml')"[/#if] >
                    <div>
                        <strong class="name word-nowarp-noellipsis">${rewardRecord.memberName}</strong>
                        [#if  versionType=='old'] <span class="orange word-nowarp-noellipsis">积分打赏</span>[/#if]
                    </div>
                    <div class="number">${rewardRecord.memberUserName}</div>
                </div>
                </div>
                [#if  versionType=='old']
                    <div class="end">
                        [#if rewardRecord.photo == 1]
                            <span>打赏${rewardRecord.receiveName}<span class="integral">${coinConvert(rewardRecord.redAmount, coinShowRate!1)}</span>积分</span>
                            <img src="${base}/resources/wechat/img/gift/icon_team.png" alt="">
                        [#else]
                            <span>每人打赏<span class="integral">${coinConvert(rewardRecord.redAmount, coinShowRate!1)}</span>积分</span>
                            <img src="${base}/resources/wechat/img/gift/icon_team.png" alt="">
                        [/#if]
                    </div>
                [/#if]
                </div>
                [#if  versionType=='old']
                    <div class="message">
                        <div class="msg">
                            <span><a [#if member.id!=rewardRecord.memberId && coinShowRate == null]href="memberHome/${rewardRecord.memberId}.jhtml"[/#if]>${rewardRecord.memberName}</a></span>
                            对
                            <span><a
                                    href="${base}/square/redEnvelopeResult/${rewardRecord.id}.jhtml">${rewardRecord.receiveName}</a></span>[#if rewardRecord.photo > 1] 等${rewardRecord.photo}人[/#if]
                            说到:
                            <div>${rewardRecord.message}</div>
                        </div>
                    </div>
                [#else]
                    <div class="content-box flexbox">
                        <img onclick="jumpUrl('${base}/square/sendPacket.jhtml?type=0')" class="img-type" src="${base}/resources/wechat/img/img_2019/jf.png"/>
                        <div class="content-box-right">
                            <div class="message-top">
                               <span onclick="jumpUrl('${base}/square/sendPacket.jhtml?type=0')" class="reward">#[#if rewardRecord.photo == 1]打赏[#else]每人打赏[/#if]</span> <span class="reward-person" onclick="jumpUrl('memberHome/${rewardRecord.receiveIds}.jhtml')"  > @${rewardRecord.receiveName}[#if rewardRecord.photo != 1]等${rewardRecord.photo}人[/#if]</span>  <span class="tab"><span >${coinConvert(rewardRecord.redAmount, coinShowRate!1)}</span><i class="jf-tab">积分</i></span>
                            </div>
                            <div class="message">
                                ${rewardRecord.message}
                            </div>
                        </div>
                    </div>
                [/#if]
                <div class="state">
                <div class="privacy">
                    <span class="time">${rewardRecord.timeDiff}</span>
                    [#if rewardRecord.isPrivate!=null && rewardRecord.isPrivate]
                        <span class="private">私</span>
                        <span class="visible">仅收发人可见</span>
                    [#else]
                        [#if member.id == rewardRecord.memberId]
                            <span class="changePublicOrPrivate" data-id="${rewardRecord.id}">
                                <img src="${base}/resources/wechat/img/redEnvelope/simi_icon.png">
                                设为私密
                            </span>
                        [/#if]
                    [/#if]
                </div>
            [#else]
                <div class="info" [#if  versionType=='new'][#if member.id!=rewardRecord.memberId && coinShowRate == null]onclick="jumpUrl('memberHome/${rewardRecord.memberId}.jhtml')"[/#if][/#if]>
                    <div>
                        <strong class="name word-nowarp-noellipsis">${rewardRecord.memberName}</strong>
                        [#if  versionType=='old']<span class="orange word-nowarp-noellipsis">红包打赏</span>[/#if]
                    </div>
                    <div class="number">${rewardRecord.memberUserName}</div>
                </div>
                </div>
                [#if  versionType=='old']
                    <div class="end">
                        <span>发了个拼手气红包</span>
                        <img src="${base}/resources/wechat/img/redEnvelope/hongbaoLogo.png" alt="">
                    </div>
                [/#if]
                </div>
                [#if  versionType=='new']
               <div class="content-box flexbox">
                   <img onclick="jumpUrl('${base}/square/sendPacket.jhtml?type=1')" class="img-type" src="${base}/resources/wechat/img/img_2019/hb.png"/>
                [/#if]
                [#if rewardRecord.isPrivate]

                    [#if .now?datetime gt (rewardRecord.createDate?long+86400000)?number_to_datetime]
                        <div id="redEnvelopeReward" rewardRecordId="${rewardRecord.id}" data-id="expired" data-name="${rewardRecord.memberName}"
                             class=" redEnvelopeReward compeletRedEnvelopeReward">
                            <img class="bg1" src="${base}/resources/wechat/img/redEnvelope/hongbao-logo.png"/>
                            <img class="bg2" src="${base}/resources/wechat/img/redEnvelope/hongbao-inbg.png"/>
                            <div class="message flexbox align-items-c">
                                <p>${rewardRecord.message}</p>
                            </div>
                            <p class="claimStatus">已过期</p>
                        </div>
                    [#else]
                        [#if rewardRecord.redGetStatus == 0]
                            <div id="redEnvelopeReward" data-id="uncomplete"
                                 data-name="${rewardRecord.memberName}" rewardRecordId="${rewardRecord.id}" class=" redEnvelopeReward">
                                <img class="bg1" src="${base}/resources/wechat/img/redEnvelope/hongbao-logo.png"/>
                                <img class="bg2"
                                     src="${base}/resources/wechat/img/redEnvelope/hongbao-inbg.png"/>
                                <div class="message flexbox align-items-c" id="memberMessage">
                                   <p>${rewardRecord.message}</p>
                                </div>
                                <p class="claimStatus">立即领取</p>
                            </div>
                        [#else]
                            <div id="redEnvelopeReward" rewardRecordId="${rewardRecord.id}" data-name="${rewardRecord.memberName}"
                                 data-id="complete"
                                 class=" redEnvelopeReward compeletRedEnvelopeReward">
                                <img class="bg1" src="${base}/resources/wechat/img/redEnvelope/hongbao-logo.png"/>
                                <img class="bg2"
                                     src="${base}/resources/wechat/img/redEnvelope/hongbao-inbg.png"/>
                                <div class="message flexbox align-items-c">
                                    <p>${rewardRecord.message}</p>
                                </div>
                                <p class="claimStatus">已经领取</p>
                            </div>
                        [/#if]
                    [/#if]

                [#else]
                    [#if .now?datetime gt (rewardRecord.createDate?long+86400000)?number_to_datetime]
                        <div id="redEnvelopeReward" rewardRecordId="${rewardRecord.id}" data-id="expired" data-name="${rewardRecord.memberName}"
                             class=" redEnvelopeReward compeletRedEnvelopeReward">
                            <img class="bg1" src="${base}/resources/wechat/img/redEnvelope/hongbao-logo.png"/>
                            <img class="bg2" src="${base}/resources/wechat/img/redEnvelope/hongbao-inbg.png"/>

                            <div class="message flexbox align-items-c">
                                <p>${rewardRecord.message}</p>
                            </div>
                            <p class="claimStatus">已过期</p>
                        </div>
                    [#else]
                        [#if rewardRecord.receiveIds == null || rewardRecord.receiveIds!=member.id]
                            <div id="redEnvelopeReward" rewardRecordId="${rewardRecord.id}" data-name="${rewardRecord.memberName}" data-id="uncomplete"
                                 class=" redEnvelopeReward">
                                <img class="bg1" src="${base}/resources/wechat/img/redEnvelope/hongbao-logo.png"/>
                                <img class="bg2" src="${base}/resources/wechat/img/redEnvelope/hongbao-inbg.png"/>
                                <div class="message flexbox align-items-c" id="memberMessage">
                                   <p>${rewardRecord.message}</p>
                                </div>
                                <p class="claimStatus">立即领取</p>
                            </div>
                        [#else]
                            <div id="redEnvelopeReward" data-name="${rewardRecord.memberName}" data-id="complete"
                                 class=" redEnvelopeReward  compeletRedEnvelopeReward" rewardRecordId="${rewardRecord.id}">
                                <img class="bg1" src="${base}/resources/wechat/img/redEnvelope/hongbao-logo.png"/>
                                <img class="bg2" src="${base}/resources/wechat/img/redEnvelope/hongbao-inbg.png"/>
                                <div class="message flexbox align-items-c">
                                    <p>${rewardRecord.message}</p>
                                </div>
                                <p class="claimStatus">已经领取</p>
                            </div>
                        [/#if]
                    [/#if]
                [/#if]
                [#if  versionType=='new'] </div>[/#if]
                <div class="state">
                <div class="privacy">
                    <span class="time">${rewardRecord.timeDiff}</span>
                </div>
            [/#if]

            <div class="include">
                <div class="good">
                    [#if rewardRecord.isMyClickGood]
                        <img class="like selected" onclick="clickGood(${rewardRecord.id},this,'1')"
                             src="${base}/resources/wechat/img/gift/icon_like.png" alt=""
                             rewardRecordId="${rewardRecord.id}">

                    [#else]
                        <img class="like" onclick="clickGood(${rewardRecord.id},this,'1')"
                             src="${base}/resources/wechat/img/gift/icon_good.png" alt=""
                             rewardRecordId="${rewardRecord.id}">
                    [/#if]
                    <span class="span_good_${rewardRecord.id}_1">
                        [#if rewardRecord.squClickGoods?size>0]
                            ${rewardRecord.squClickGoods?size}
                        [#else ]
                            点赞
                        [/#if]
                    </span>
                </div>
                <div class="info">
                    <img class="comment" onclick="clickcomment(${rewardRecord.id},'null','null',this,'1','null')"
                         src="${base}/resources/wechat/img/gift/icon_message.png" alt=""
                         rewardRecordId="${rewardRecord.id}">
                    <span class="span_comment_${rewardRecord.id}_1">
                    [#if rewardRecord.squReviews?size>0]
                        ${rewardRecord.squReviews?size}
                    [#else ]
                        评论
                    [/#if]
                    </span>
                </div>
            </div>
            </div>
            [#if rewardRecord.squReviews?size>0||rewardRecord.squClickGoods?size>0]
                <div class="reply" id="replyMessage${rewardRecord.id}_1" >
                    <div class="mreply [#if rewardRecord.squClickGoods?size>0&& rewardRecord.squReviews?size>0]have_border_mreply[/#if]"  id="goodMessage${rewardRecord.id}_1">
                        [#if rewardRecord.squClickGoods?size>0]
                            <img src="${base}/resources/wechat/img/${imgSrc}.png" alt="">
                             [#list rewardRecord.squClickGoods as squClickGood]
                                 <span class="${squClickGood.memberId}" onclick="jumpUrl('memberHome/${squClickGood.memberId}.jhtml')">
                                    ${squClickGood.memberName}
                                </span>
                             [/#list]
                        [/#if]
                    </div>
                    [#if rewardRecord.squReviews?size>0]
                        [#list rewardRecord.squReviews as squReview]
                            [#if rewardRecord.squReviews?size>6]
                                [#if squReview_index>rewardRecord.squReviews?size-7]
                                    <div class="msg msg-item"  id="${squReview.id}_1"
                                         onclick="clickcomment(${rewardRecord.id},${squReview.memberId},'${squReview.memberName}',this,'1',${squReview.id})">
                                        <span class="blue">
                                            ${squReview.memberName}
                                             [#if squReview.toMemberId??]
                                                 <span>回复</span>
                                                 ${squReview.toMemberName}
                                             [/#if]
                                        </span>:
                                        <span>${squReview.content}</span>
                                    </div>
                                [#else ]
                                  <div class="msg msg-item" style="display: none" id="${squReview.id}_1"
                                       onclick="clickcomment(${rewardRecord.id},${squReview.memberId},'${squReview.memberName}',this,'1',${squReview.id})">
                                        <span class="blue">
                                            ${squReview.memberName}
                                             [#if squReview.toMemberId??]
                                                 <span>回复</span>
                                                 <i onclick="jumpUrl('memberHome/${squReview.toMemberId}.jhtml')">${squReview.toMemberName}</i>
                                             [/#if]
                                        </span>:
                                      <span>${squReview.content}</span>
                                  </div>
                                [/#if]
                            [#else]
                                <div class="msg msg-item"
                                     id="${squReview.id}_1"
                                     onclick="clickcomment(${rewardRecord.id},${squReview.memberId},'${squReview.memberName}',this,'1',${squReview.id})">
                                    <span class="blue">
                                        ${squReview.memberName}
                                         [#if squReview.toMemberId??]
                                             <span>回复</span>
                                             <i onclick="jumpUrl('memberHome/${squReview.toMemberId}.jhtml')">${squReview.toMemberName}</i>
                                         [/#if]
                                    </span>:
                                    <span>${squReview.content}</span>
                                </div>
                            [/#if]
                        [/#list]
                        [#if rewardRecord.squReviews?size>6]
                            <div class="msg msg-more">
                                    <span class="blue"><a href="reviewDetail.jhtml?publishId=${rewardRecord.id}&type=1&receiveName=${rewardRecord.receiveName}&receiveNum=${rewardRecord.photo}"
                                                          target="_self">查看更多</a></span>
                            </div>
                        [/#if]
                    [/#if]
                </div>
            [/#if]
            </div>
        [/#if]

        [#if rewardRecord.type==2]
            <div class="item square_item" rewardRecordId="${rewardRecord.id}" type="1" id="item${rewardRecord.id}_2">
                [#--<a href="${base}/buyer/birthdayreminder/showbirthdayGreetings.jhtml?mid=${rewardRecord.receiveId}">--]
                <div class="top">
                    <div class="start">
                        <div class="portrait">
                            <div class="picture">
                                [#if rewardRecord.image??]
                                    [#if rewardRecord.image?contains("dingtalk")||rewardRecord.image?contains("http")]
                                        <img class="header" src="${rewardRecord.image}" alt="">
                                    [#else]
                                        <img class="header" src="${base}/${rewardRecord.image}" alt="">
                                    [/#if]

                                [#else]
                                    [#if rewardRecord.gender == 'female']
                                        <img class="header" src="${base}/resources/wechat/img/girl.png" alt="">
                                    [#else]
                                        <img class="header" src="${base}/resources/wechat/img/boy.png" alt="">
                                    [/#if]
                                [/#if]
                                [#if rewardRecord.gender == 'female']
                                    <img class="sex" src="${base}/resources/wechat/img/gift/icon_female.png" alt="">
                                [#else]
                                    <img class="sex" src="${base}/resources/wechat/img/gift/icon_male.png" alt="">
                                [/#if]
                            </div>
                        </div>
                        <div class="info [#if  versionType=='new']flexbox flex-column justify-content-c[/#if]" [#if member.id!=rewardRecord.memberId && coinShowRate == null]onclick="jumpUrl('memberHome/${rewardRecord.memberId}.jhtml')"[/#if]>
                            <div>
                                <strong class="name word-nowarp-noellipsis">${rewardRecord.memberName}</strong>
                                [#if  versionType=='old']<span class="orange word-nowarp-noellipsis">最近生日</span>[/#if]
                            </div>
                            <div class="number">${rewardRecord.memberUserName}</div>
                        </div>
                    </div>
                    [#if  versionType=='old']
                    <div class="end">
                        <span class="orange">在生日墙给${rewardRecord.receiveName}祝福留言</span>
                        <img src="${base}/resources/wechat/img/gift/icon_jifen.png" alt="">
                    </div>
                    [/#if]
                </div>
            [#if  versionType=='new']
                <div class="content-box flexbox">
                    <img onclick="jumpUrl('${base}/buyer/birthdayreminder/index.jhtml')" class="img-type" src="${base}/resources/wechat/img/img_2019/birthday.png"/>
                    <div class="content-box-right">
                        <div class="message-top">
                            <span onclick="jumpUrl('${base}/buyer/birthdayreminder/index.jhtml')" class="reward">#生日祝福</span> <span class="reward-person" [#if member.id!=receiveId && coinShowRate == null]onclick="jumpUrl('memberHome/${rewardRecord.receiveIds}.jhtml')"[/#if] > @${rewardRecord.receiveName}</span>
                        </div>
                        <div class="message">
                            ${rewardRecord.message}
                        </div>
                    </div>
                </div>
            [#else ]
                <div class="message">
                    <div class="msg">
                        <span><a [#if member.id!=rewardRecord.memberId && coinShowRate == null]href="memberHome/${rewardRecord.memberId}.jhtml"[/#if]>${rewardRecord.memberName}</a></span>
                        对
                        <span><a
                                    [#if member.id!=receiveId && coinShowRate == null]href="memberHome/${rewardRecord.receiveIds}.jhtml"[/#if]>${rewardRecord.receiveName}</a></span>说到:
                        <div>${rewardRecord.message}</div>
                    </div>
                </div>
            [/#if]
                <div class="state">
                    <div class="privacy">
                        <span class="time">${rewardRecord.timeDiff}</span>
                    </div>

                    <div class="include">
                        <div class="good">
                            [#if rewardRecord.isMyClickGood]
                                <img class="like selected" onclick="clickGood(${rewardRecord.id},this,'2')"
                                     src="${base}/resources/wechat/img/gift/icon_like.png" alt=""
                                     rewardRecordId="${rewardRecord.id}">

                            [#else]
                                <img class="like" onclick="clickGood(${rewardRecord.id},this,'2')"
                                     src="${base}/resources/wechat/img/gift/icon_good.png" alt=""
                                     rewardRecordId="${rewardRecord.id}">
                            [/#if]
                            <span class="span_good_${rewardRecord.id}_2">
                                [#if rewardRecord.squClickGoods?size>0]
                                    ${rewardRecord.squClickGoods?size}
                                [#else ]
                                    点赞
                                [/#if]
                            </span>
                        </div>
                        <div class="info">
                            <img class="comment" onclick="clickcomment(${rewardRecord.id},'null','null',this,'2','null')"
                                 src="${base}/resources/wechat/img/gift/icon_message.png" alt=""
                                 rewardRecordId="${rewardRecord.id}">
                            <span class="span_comment_${rewardRecord.id}_2">
                                [#if rewardRecord.squReviews?size>0]
                                ${rewardRecord.squReviews?size}
                                [#else ]
                                    评论
                                [/#if]
                            </span>
                        </div>
                    </div>
                </div>
                [#if rewardRecord.squReviews?size>0||rewardRecord.squClickGoods?size>0]
                    <div class="reply" id="replyMessage${rewardRecord.id}_2">
                        <div class="mreply [#if rewardRecord.squClickGoods?size>0&&rewardRecord.squReviews?size>0]have_border_mreply[/#if]" id="goodMessage${rewardRecord.id}_2">
                            [#if rewardRecord.squClickGoods?size>0]
                                <img src="${base}/resources/wechat/img/${imgSrc}.png" alt="">
                                [#list rewardRecord.squClickGoods as squClickGood]
                                    <span class="${squClickGood.memberId}" onclick="jumpUrl('memberHome/${squClickGood.memberId}.jhtml')">
                                            ${squClickGood.memberName}
                                    </span>
                                [/#list]
                            [/#if]
                        </div>
                        [#if rewardRecord.squReviews?size>0]
                            [#list rewardRecord.squReviews as squReview]
                                [#if rewardRecord.squReviews?size>6]
                                    [#if squReview_index>rewardRecord.squReviews?size-7]
                                        <div class="msg msg-item"
                                             id="${squReview.id}_2"
                                             onclick="clickcomment(${rewardRecord.id},${squReview.memberId},'${squReview.memberName}',this,'2',${squReview.id})">
                                            <span class="blue">
                                                 ${squReview.memberName}
                                                 [#if squReview.toMemberId??]
                                                   <span>回复</span>
                                                      <i onclick="jumpUrl('memberHome/${squReview.toMemberId}.jhtml')">${squReview.toMemberName}</i>
                                                 [/#if]
                                            </span>:
                                            <span>${squReview.content}</span>
                                        </div>
                                    [#else ]
                                            <div class="msg msg-item"
                                                 style="display: none"
                                                 id="${squReview.id}_2"
                                                 onclick="clickcomment(${rewardRecord.id},${squReview.memberId},'${squReview.memberName}',this,'2',${squReview.id})">
                                            <span class="blue">
                                                 ${squReview.memberName}
                                                 [#if squReview.toMemberId??]
                                                   <span>回复</span>
                                                      <i onclick="jumpUrl('memberHome/${squReview.toMemberId}.jhtml')">${squReview.toMemberName}</i>
                                                 [/#if]
                                            </span>:
                                                <span>${squReview.content}</span>
                                            </div>
                                    [/#if]
                                [#else]
                                    <div class="msg msg-item"
                                         id="${squReview.id}_2"
                                         onclick="clickcomment(${rewardRecord.id},${squReview.memberId},'${squReview.memberName}',this,'2',${squReview.id})">
                                        <span class="blue">
                                            ${squReview.memberName}
                                             [#if squReview.toMemberId??]
                                                 <span>回复</span>
                                                  <i onclick="jumpUrl('memberHome/${squReview.toMemberId}.jhtml')">${squReview.toMemberName}</i>
                                             [/#if]
                                        </span>:
                                        <span>${squReview.content}</span>
                                    </div>
                                [/#if]
                            [/#list]
                            [#if rewardRecord.squReviews?size>6]
                                <div class="msg msg-more">
                                    <span class="blue"><a href="reviewDetail.jhtml?publishId=${rewardRecord.id}&&type=2"
                                                          target="_self">查看更多</a></span>
                                </div>
                            [/#if]
                        [/#if]
                    </div>
                [/#if]
                [#--</a>--]
            </div>
        [/#if]

        [#if rewardRecord.type==3]
            <div class="item square_item" id="item${rewardRecord.id}_3">
                [#--<a href="${base}/buyer/birthdayreminder/showbirthdayGreetings.jhtml?mid=${rewardRecord.receiveId}">--]
                <div class="top">
                    <div class="start">
                        <div class="portrait">
                            <div class="picture">
                                [#if rewardRecord.image??]
                                    [#if rewardRecord.image?contains("dingtalk")||rewardRecord.image?contains("http")]
                                        <img class="header" src="${rewardRecord.image}" alt="">
                                    [#else]
                                        <img class="header" src="${base}/${rewardRecord.image}" alt="">
                                    [/#if]

                                [#else]
                                    [#if rewardRecord.gender == 'female']
                                        <img class="header" src="${base}/resources/wechat/img/girl.png" alt="">
                                    [#else]
                                        <img class="header" src="${base}/resources/wechat/img/boy.png" alt="">
                                    [/#if]
                                [/#if]

                                [#if rewardRecord.gender == 'female']
                                    <img class="sex" src="${base}/resources/wechat/img/gift/icon_female.png" alt="">
                                [#else]
                                    <img class="sex" src="${base}/resources/wechat/img/gift/icon_male.png" alt="">
                                [/#if]
                            </div>
                        </div>
                        <div class="info [#if  versionType=='new']flexbox flex-column justify-content-c[/#if]" [#if member.id!=rewardRecord.memberId && coinShowRate == null]onclick="jumpUrl('memberHome/${rewardRecord.memberId}.jhtml')"[/#if]>
                            <div>
                                <strong class="name word-nowarp-noellipsis">${rewardRecord.memberName}</strong>
                                [#if  versionType=='old'] <span class="blue word-nowarp-noellipsis">生日墙集福</span>[/#if]
                            </div>
                            <div class="number">${rewardRecord.memberUserName}</div>
                        </div>
                    </div>
            [#if  versionType=='old']
                    <div class="end">
                        <span class="orange">在生日墙给${rewardRecord.receiveName}集福</span>
                        <img src="${base}/resources/wechat/img/gift/icon_cake.png" alt="">
                    </div>
            [/#if]
                </div>
            [#if  versionType=='new']
                    <div class="content-box flexbox">
                        <img class="img-type" onclick="jumpUrl('${base}/buyer/birthdayreminder/showbirthdayGreetings.jhtml?mid=${rewardRecord.receiveIds}')" src="${base}/resources/wechat/img/img_2019/birthday.png"/>
                        <div class="content-box-right">
                            <div class="message-top">
                                <span class="reward" onclick="jumpUrl('${base}/buyer/birthdayreminder/showbirthdayGreetings.jhtml?mid=${rewardRecord.receiveIds}')">#集福</span> <span class="reward-person" [#if member.id!=receiveId && coinShowRate == null]onclick="jumpUrl('memberHome/${rewardRecord.receiveIds}.jhtml')"[/#if]> @${rewardRecord.receiveName}</span>  <span class="tab jf">给你集个福<img src="${base}/resources/wechat/img/img_2019/jf-h.png"/></span>
                            </div>
                            <div class="message">
                                生日快乐，给你集个福!
                            </div>
                        </div>
                    </div>
            [#else ]
                <div class="message">
                    <img class="blessing" src="${base}/resources/wechat/img/gift/icon_bless.png" alt=""
                         onclick="javascript:location.href='${base}/buyer/birthdayreminder/showbirthdayGreetings.jhtml?mid=${rewardRecord.receiveIds}'">
                    <div class="msg">
                        <span><a [#if member.id!=rewardRecord.memberId && coinShowRate == null]href="memberHome/${rewardRecord.memberId}.jhtml"[/#if]>${rewardRecord.memberName}</a></span>
                        对
                        <span><a [#if member.id!=rewardRecord.receiveIds && coinShowRate == null]href="memberHome/${rewardRecord.receiveIds}.jhtml"[/#if]>${rewardRecord.receiveName}</a></span>:
                        <div>生日快乐，给你集个福!</div>
                    </div>
                </div>
            [/#if]
                <div class="state">
                    <div class="privacy">
                        <span class="time">${rewardRecord.timeDiff}</span>
                    </div>

                    <div class="include">
                        <div class="good">

                            [#if rewardRecord.isMyClickGood]
                                <img class="like selected" onclick="clickGood(${rewardRecord.id},this,'3')"
                                     src="${base}/resources/wechat/img/gift/icon_like.png" alt=""
                                     rewardRecordId="${rewardRecord.id}">

                            [#else]
                                <img class="like" onclick="clickGood(${rewardRecord.id},this,'3')"
                                     src="${base}/resources/wechat/img/gift/icon_good.png" alt=""
                                     rewardRecordId="${rewardRecord.id}">
                            [/#if]
                            <span class="span_good_${rewardRecord.id}_3">
                                [#if rewardRecord.squClickGoods?size>0]
                                ${rewardRecord.squClickGoods?size}
                                [#else ]
                                点赞
                                [/#if]
                                </span>
                        </div>
                        <div class="info">

                            <img class="comment" onclick="clickcomment(${rewardRecord.id},'null','null',this,'3','null')"
                                 src="${base}/resources/wechat/img/gift/icon_message.png" alt=""
                                 rewardRecordId="${rewardRecord.id}">
                            <span class="span_comment_${rewardRecord.id}_3">
                                [#if rewardRecord.squReviews?size>0]
                                ${rewardRecord.squReviews?size}
                                [#else ]
                                    评论
                                [/#if]
                            </span>
                        </div>
                    </div>
                </div>
                [#if rewardRecord.squReviews?size>0||rewardRecord.squClickGoods?size>0]
                    <div class="reply" id="replyMessage${rewardRecord.id}_3">
                        <div class="mreply [#if rewardRecord.squClickGoods?size>0&&rewardRecord.squReviews?size>0]have_border_mreply[/#if]" id="goodMessage${rewardRecord.id}_3">
                            [#if rewardRecord.squClickGoods?size>0]
                                <img src="${base}/resources/wechat/img/${imgSrc}.png" alt="">
                                 [#list rewardRecord.squClickGoods as squClickGood]
                                    <span class="${squClickGood.memberId}" onclick="jumpUrl('memberHome/${squClickGood.memberId}.jhtml')">
                                            ${squClickGood.memberName}
                                    </span>
                                 [/#list]
                            [/#if]
                        </div>
                        [#if rewardRecord.squReviews?size>0]
                            [#list rewardRecord.squReviews as squReview]
                                [#if rewardRecord.squReviews?size>6]
                                    [#if squReview_index>rewardRecord.squReviews?size-7]
                                        <div class="msg msg-item"
                                             id="${squReview.id}_3"
                                             onclick="clickcomment(${rewardRecord.id},${squReview.memberId},'${squReview.memberName}',this,'3',${squReview.id})">
                                            <span class="blue">
                                                ${squReview.memberName}
                                                 [#if squReview.toMemberId??]
                                                     <span>回复</span>
                                                      <i onclick="jumpUrl('memberHome/${squReview.toMemberId}.jhtml')">${squReview.toMemberName}</i>
                                                 [/#if]
                                            </span>:
                                            <span>${squReview.content}</span>
                                        </div>
                                    [#else ]
                                          <div class="msg msg-item"
                                               style="display: none"
                                               id="${squReview.id}_3"
                                               onclick="clickcomment(${rewardRecord.id},${squReview.memberId},'${squReview.memberName}',this,'3',${squReview.id})">
                                            <span class="blue">
                                                ${squReview.memberName}
                                                 [#if squReview.toMemberId??]
                                                     <span>回复</span>
                                                      <i onclick="jumpUrl('memberHome/${squReview.toMemberId}.jhtml')">${squReview.toMemberName}</i>
                                                 [/#if]
                                            </span>:
                                              <span>${squReview.content}</span>
                                          </div>
                                    [/#if]
                                [#else]
                                    <div class="msg msg-item"
                                         id="${squReview.id}_3"
                                         onclick="clickcomment(${rewardRecord.id},${squReview.memberId},'${squReview.memberName}',this,'3',${squReview.id})">
                                        <span class="blue">
                                             ${squReview.memberName}
                                             [#if squReview.toMemberId??]
                                                 <span>回复</span>
                                                 <i onclick="jumpUrl('memberHome/${squReview.toMemberId}.jhtml')">${squReview.toMemberName}</i>
                                             [/#if]
                                        </span>:
                                        <span>${squReview.content}</span>
                                    </div>
                                [/#if]
                            [/#list]
                            [#if rewardRecord.squReviews?size>6]
                                <div class="msg msg-more">
                                    <span class="blue"><a href="reviewDetail.jhtml?publishId=${rewardRecord.id}&&type=3"
                                                          target="_self">查看更多</a></span>
                                </div>
                            [/#if]
                        [/#if]
                    </div>
                [/#if]
                [#--</a>--]
            </div>
        [/#if]

        [#if rewardRecord.type==4 ]
            <div class="item square_item" rewardRecordId="${rewardRecord.id}" type="4" id="item${rewardRecord.id}_4">
                <div class="top">
                    <div class="start">
                        <div class="portrait">
                            <div class="picture">
                                [#if rewardRecord.image??]
                                    [#if rewardRecord.image?contains("dingtalk")||rewardRecord.image?contains("http")]
                                        <img class="header" src="${rewardRecord.image}" alt="">
                                    [#else]
                                        <img class="header" src="${base}/${rewardRecord.image}" alt="">
                                    [/#if]
                                [#else]
                                    [#if rewardRecord.gender == 'female']
                                        <img class="header" src="${base}/resources/wechat/img/girl.png" alt="">
                                    [#else]
                                        <img class="header" src="${base}/resources/wechat/img/boy.png" alt="">
                                    [/#if]
                                [/#if]

                                [#if rewardRecord.gender == 'female']
                                    <img class="sex" src="${base}/resources/wechat/img/gift/icon_female.png" alt="">
                                [#else]
                                    <img class="sex" src="${base}/resources/wechat/img/gift/icon_male.png" alt="">
                                [/#if]
                            </div>
                        </div>
                        <div class="info   [#if  versionType=='new']flexbox flex-column justify-content-c[/#if]" [#if member.id!=rewardRecord.memberId && coinShowRate == null]onclick="jumpUrl('memberHome/${rewardRecord.memberId}.jhtml')"[/#if]>
                            <div>
                                <strong class="name word-nowarp-noellipsis">${rewardRecord.memberName}</strong>
                                [#if  versionType=='old']<span class="blue word-nowarp-noellipsis">礼物赠送</span>[/#if]
                            </div>
                            <div class="number">${rewardRecord.memberUserName}</div>
                        </div>
                    </div>
            [#if  versionType=='old']
                    <div class="end">
                        <span class="orange">送给${rewardRecord.receiveName}一个礼物</span>
                        <img src="${base}/resources/wechat/img/gift/icon_box.png" alt="">
                    </div>
            [/#if]
                </div>
            [#if  versionType=='new']
                <div class="content-box flexbox">
                    <img class="img-type" onclick="jumpUrl('${base}/giftGiving/myGiftView.jhtml?id=${rewardRecord.id}')" src="${base}${rewardRecord.photo}" />
                    <div class="content-box-right">
                        <div class="message-top">
                            <span class="reward" onclick="jumpUrl('${base}/giftGiving/myGiftView.jhtml?id=${rewardRecord.id}')">#礼物</span> <span class="reward-person" [#if member.id!=receiveId && coinShowRate == null]onclick="jumpUrl('memberHome/${rewardRecord.receiveIds}.jhtml')"[/#if]> @${rewardRecord.receiveName}</span>
                            <span class="tab gift-view">
                                [#if rewardRecord.photo?contains("18")]
                                    小红花
                                [#elseif rewardRecord.photo?contains("17")]
                                    快乐肥宅水
                                [#elseif rewardRecord.photo?contains("16")]
                                    养生枸杞
                                [#elseif rewardRecord.photo?contains("15")]
                                    快乐星球
                                [#elseif rewardRecord.photo?contains("14")]
                                    心想事成
                                [#elseif rewardRecord.photo?contains("13")]
                                    好柿发生
                                [#elseif rewardRecord.photo?contains("12")]
                                    橘子蛋糕
                                [#elseif rewardRecord.photo?contains("11")]
                                    花束
                                [#elseif rewardRecord.photo?contains("10")]
                                    珍珠奶茶蛋糕
                                [#elseif rewardRecord.photo?contains("9")]
                                    星星蛋糕
                                [#elseif rewardRecord.photo?contains("8")]
                                    好运蛋糕
                                [#elseif rewardRecord.photo?contains("7")]
                                    水蜜桃蛋糕
                                [#elseif rewardRecord.photo?contains("6")]
                                    荣誉之星
                                [#elseif rewardRecord.photo?contains("5")]
                                    奋斗之星
                                [#elseif rewardRecord.photo?contains("4")]
                                    能量之星
                                [#elseif rewardRecord.photo?contains("3")]
                                    最佳拍档
                                [#elseif rewardRecord.photo?contains("2")]
                                    快速响应
                                [#elseif rewardRecord.photo?contains("1")]
                                    阳光勋章
                                [/#if]
                                <img src="${base}/resources/wechat/img/img_2019/heart-z.png"/>
                            </span>
                        </div>
                        <div class="message">
                            ${rewardRecord.message}
                        </div>
                    </div>
                </div>
            [#else ]
                <div class="message">
                    <a href="${base}/giftGiving/myGiftView.jhtml?id=${rewardRecord.id}">
                        <img class="medal" src="${base}${rewardRecord.photo}" alt="">
                    </a>
                    <div class="msg">
                        <span><a [#if member.id!=rewardRecord.memberId && coinShowRate == null]href="memberHome/${rewardRecord.memberId}.jhtml"[/#if]>${rewardRecord.memberName}</a></span>
                        对
                        <span><a
                                    [#if member.id!=rewardRecord.receiveIds]href="memberHome/${rewardRecord.receiveIds}.jhtml"[/#if]>${rewardRecord.receiveName}</a></span>说到:
                        <div>${rewardRecord.message}</div>
                    </div>
                </div>
            [/#if]
                <div class="state">
                    <div class="privacy">
                        <span class="time">${rewardRecord.timeDiff}</span>
                    </div>

                    <div class="include">
                        <div class="good">

                            [#if rewardRecord.isMyClickGood]
                                <img class="like selected" onclick="clickGood(${rewardRecord.id},this,'4')"
                                     src="${base}/resources/wechat/img/gift/icon_like.png" alt=""
                                     rewardRecordId="${rewardRecord.id}">

                            [#else]
                                <img class="like" onclick="clickGood(${rewardRecord.id},this,'4')"
                                     src="${base}/resources/wechat/img/gift/icon_good.png" alt=""
                                     rewardRecordId="${rewardRecord.id}">
                            [/#if]
                            <span class="span_good_${rewardRecord.id}_4">
                                [#if rewardRecord.squClickGoods?size>0]
                                    ${rewardRecord.squClickGoods?size}
                                [#else ]
                                    点赞
                                [/#if]
                            </span>
                        </div>
                        <div class="info">

                            <img class="comment" onclick="clickcomment(${rewardRecord.id},'null','null',this,'4','null')"
                                 src="${base}/resources/wechat/img/gift/icon_message.png" alt=""
                                 rewardRecordId="${rewardRecord.id}">
                            <span class="span_comment_${rewardRecord.id}_4">
                                [#if rewardRecord.squReviews?size>0]
                                ${rewardRecord.squReviews?size}
                                [#else ]
                                    评论
                                [/#if]
                            </span>
                        </div>
                    </div>
                </div>
                [#if rewardRecord.squReviews?size>0||rewardRecord.squClickGoods?size>0]
                    <div class="reply" id="replyMessage${rewardRecord.id}_4">
                        <div class="mreply [#if rewardRecord.squClickGoods?size>0&&rewardRecord.squReviews?size>0]have_border_mreply[/#if]" id="goodMessage${rewardRecord.id}_4">
                            [#if rewardRecord.squClickGoods?size>0]
                                <img src="${base}/resources/wechat/img/${imgSrc}.png" alt="">
                                [#list rewardRecord.squClickGoods as squClickGood]
                                    <span class="${squClickGood.memberId}" onclick="jumpUrl('memberHome/${squClickGood.memberId}.jhtml')">
                                            ${squClickGood.memberName}
                                    </span>
                                 [/#list]
                            [/#if]
                        </div>
                        [#if rewardRecord.squReviews?size>0]
                            [#list rewardRecord.squReviews as squReview]
                                [#if rewardRecord.squReviews?size>6]
                                    [#if squReview_index>rewardRecord.squReviews?size-7]
                                        <div class="msg msg-item"
                                             id="${squReview.id}_4"
                                             onclick="clickcomment(${rewardRecord.id},${squReview.memberId},'${squReview.memberName}',this,'4',${squReview.id})">
                                            <span class="blue">
                                                ${squReview.memberName}
                                                [#if squReview.toMemberId??]
                                                    <span>回复</span>
                                                    <i onclick="jumpUrl('memberHome/${squReview.toMemberId}.jhtml')">${squReview.toMemberName}</i>
                                                [/#if]
                                            </span>:
                                            <span>${squReview.content}</span>
                                        </div>
                                    [#else ]
                                     <div class="msg msg-item"
                                          style="display: none"
                                          id="${squReview.id}_4"
                                          onclick="clickcomment(${rewardRecord.id},${squReview.memberId},'${squReview.memberName}',this,'4',${squReview.id})">
                                            <span class="blue">
                                                ${squReview.memberName}
                                                [#if squReview.toMemberId??]
                                                    <span>回复</span>
                                                     <i onclick="jumpUrl('memberHome/${squReview.toMemberId}.jhtml')">${squReview.toMemberName}</i>
                                                [/#if]
                                            </span>:
                                         <span>${squReview.content}</span>
                                     </div>
                                    [/#if]
                                [#else]
                                    <div class="msg msg-item" id="${squReview.id}_4"
                                         onclick="clickcomment(${rewardRecord.id},${squReview.memberId},'${squReview.memberName}',this,'4',${squReview.id})">
                                        <span class="blue">
                                             ${squReview.memberName}
                                             [#if squReview.toMemberId??]
                                                 <span>回复</span>
                                                 <i onclick="jumpUrl('memberHome/${squReview.toMemberId}.jhtml')">${squReview.toMemberName}</i>
                                             [/#if]
                                        </span>:
                                        <span>${squReview.content}</span>
                                    </div>
                                [/#if]
                            [/#list]
                            [#if rewardRecord.squReviews?size>6]
                                <div class="msg msg-more">
                                    <span class="blue"><a href="reviewDetail.jhtml?publishId=${rewardRecord.id}&&type=4"
                                                          target="_self">查看更多</a></span>
                                </div>
                            [/#if]
                        [/#if]
                    </div>
                [/#if]
            </div>
        [/#if]
        <script>
            $(function () {
                /*南浔IOS兼容问题--a标签绑定点击事件*/
                [#if thirdLoginFlag??&&thirdLoginFlag=='rmt' ]
                hrefToClick()
                [/#if]
                [#if com_person_moreAtts_birthblessing??&&com_person_moreAtts_birthblessing=='close']
                    $('.info').find(".number").attr("style","display: none");
                [/#if]
                //企业个性化配置关闭广场评论
                [#if (com_person_square_comment_switch?? && com_person_square_comment_switch == "0")]
                    $('.include .info').hide();
                    $('.reply .msg-item.msg').hide();
                    $(".mreply").removeClass('have_border_mreply');
                [/#if]
            });
        </script>
    [/#list]
[#else ]
    <div class="public_empty_section [#if comPersonConf && com_person_EmptyBg != null && com_person_EmptyBg != ""] ${com_person_EmptyBg!}[#else] Public_Bg[/#if]">
        <p class="tip">这里空空如也，快去说点什么~</p>
    </div>
[/#if]
