	                         <!--发红包场景-->
	            	     <div class="section_scene_2" >
						  <img src="${base}/resources/greetingCard/img/coins.png" class="twinkle_gifts"/>
						   <div class="redSection">
							 <div class="red_body">
							   <img src="${base}/resources/greetingCard/img/red_body_front.png" class="img_body"/>
							   <header class="red_header">
									<div class="header_face">
										<img src="${base}/resources/greetingCard/img/red_header.png" class="img_header"/>
										<img src="${base}/resources/greetingCard/img/red_header_back.png" class="img_header back"/>
										<img src="${com_person_loginTopLogo!(base + "/resources/greetingCard/img/logo.png") }" class="img_text"/>
									</div> 
								</header>
								<div class="ticketSection">
									<img src="${base}/resources/greetingCard/img/quan.png"/>
									<div class="num">${coinConvert(carePlanRecord.giftName, coinShowRate!1)}积分</div>
								</div>
								<article>
									<div class="before_section">
										<img src="${base}/resources/greetingCard/img/text_hb_before.png" class="before_text"/>
									</div>
									
									<div class="afterSection" style="display: none;">
										<div class="available"><span>${coinConvert(carePlanRecord.giftName, coinShowRate!1)}</span></div>
										<img src="${base}/resources/greetingCard/img/text_notice.png"/>
									</div>
									
									<div class="button_area">
										<input type="button" name="" id="" value="" class="btn"/>
									</div> 
								</article>
							</div>
						</div>  
		
<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>		
 <script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js"></script>
<script>



//红包控制
$(".section_scene_2 .button_area .btn").one("touchstart",function(){
	$(".showBoxPage .img_1").remove();
	$(this).css("animation-play-state","running");
	$(".red_header").css("animation-play-state","running");
	$(".red_header .back").css("animation-play-state","running");
	
	$.ajax({
	    url:"receiveRedpacket.jhtml",
	    data:{"carePlanId":'${carePlanRecord.carePlanId.id}'},
        type: "POST",
        success:function(data){
        	if(!data){
        		layer.open({
    			    content: '红包已经被领取!'
    			    ,skin: 'msg'
    			    ,time: 2 //2秒后自动关闭
    			});
        	}
        }
 
	});
	
	setTimeout('showTicket()',2000);
	
	
})

</script>