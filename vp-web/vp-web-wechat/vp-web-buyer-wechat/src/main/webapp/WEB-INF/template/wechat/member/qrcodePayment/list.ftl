<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8">
    <title>二维码积分支付</title>
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="format-detection" content="telephone=no, email=no" />
    <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/fli_2019.css" />
    <script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/dropload.min.js" ></script>
    <script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js"></script>
    <!--日期选择-->
    <link rel="stylesheet" href="${base}/resources/wechat/plugins/LCalendar/css/LCalendar.css">
    <script src="${base}/resources/wechat/plugins/LCalendar/js/LCalendar.min.js"></script>
</head>

<body>
    <div class="qrCodeService">
        <div class="public_top_header">
            <a href="${base}/qrcode/payment/index.jhtml" class="return_back"></a>
            二维码积分支付
        </div>
        <div class="time_header">
            <div class="time">${.now?string("yyyy-MM-dd") }（今天）</div>
            <div class="toggle_btn">跨日期查询</div>
        </div>
        <div class="time_select_section">
            <div class="item">
                <label for="date_start">开始时间</label>
                <div class="inputs">
                    <input type="text" value="${startDate!(.now?string("yyyy-MM-dd")) }" id="date_start" readonly placeholder="选择开始时间">
                </div>
            </div>
            <div class="item">
                <label for="date_end">结束时间</label>
                <div class="inputs">
                    <input type="text" value="${endDate!(.now?string("yyyy-MM-dd")) }" id="date_end" readonly placeholder="选择结束时间">
                </div>
            </div>
            <input type="button" value="查询" class="search_text" />
        </div>
        <div class="header">
            <div class="order_amount">
                收款笔数<span id="totalSize">0</span>
            </div>
            <div class="coin_amount">
                收款总计<span id="totalCoin">0.00</span>
            </div>
        </div>

        <ul class="list">
        </ul>
    </div>
    <script>
    var calendar = new LCalendar();
    calendar.init({
        'trigger': '#date_start', //标签id
        'type': 'date', //date 调出日期选择 datetime 调出日期时间选择 time 调出时间选择 ym 调出年月选择,
        'maxDate':'${.now?string("yyyy-MM-dd") }'//最大日期 注意：该值会覆盖标签内定义的日期范围
    });
    var calendar2 = new LCalendar();
    calendar2.init({
        'trigger': '#date_end', //标签id
        'type': 'date', //date 调出日期选择 datetime 调出日期时间选择 time 调出时间选择 ym 调出年月选择,
        'maxDate': '${.now?string("yyyy-MM-dd") }'//最小日期 注意：该值会覆盖标签内定义的日期范围
    });

    $(".toggle_btn").on("click",function(){
        if($(this).text() == "只看今天" ){
            $(this).text("跨日期查询");
            $(this).prev().html('${.now?string("yyyy-MM-dd") }（今天）');
            $("#date_start").val('${.now?string("yyyy-MM-dd") }');
            $("#date_end").val('${.now?string("yyyy-MM-dd") }');
            reloadPage();
        }else{
            $(this).text("只看今天");
            $(this).prev().html("目前只支持开始时间起31天内的查询");
        }
        $(".time_select_section").toggle();
    });
    
    //两日期时间间隔
    function daysBetween(sDate1,sDate2){
        //Date.parse() 解析一个日期时间字符串，并返回1970/1/1 午夜距离该日期时间的毫秒数
        var time1 = Date.parse(new Date(sDate1));
        var time2 = Date.parse(new Date(sDate2));
        var nDays = parseInt((time2 - time1)/1000/3600/24);
        return  nDays;
    }
    
    $(function () {
    	//加载数据
    	loadData('subList.jhtml', {"id": "${paymentQrcode.id}"}, 'qrCodeService', 'list', 'POST', 'totalPages', null, function(){
			$("#totalPages").remove();
		});

    	$(".search_text").click(function(){
        	reloadPage();
		});
    });
    
    function reloadPage(){
        var startDate = $("#date_start").val();
        var endDate = $("#date_end").val();
        var days = daysBetween(startDate, endDate);
        console.log("相隔天数:" + days);
        if(days > 31){
        	layer.open({
                content: "目前只支持开始时间起31天内的查询!"
                ,skin: 'msg'
                ,time: 2 //2秒后自动关闭
            });
            return false;
        } else if(days < 0){
        	layer.open({
                content: "开始时间晚于结束时间!"
                ,skin: 'msg'
                ,time: 2 //2秒后自动关闭
            });
            return false;
        }
        $(".list").html("");
      //加载数据
    	loadData('subList.jhtml', {"id": "${paymentQrcode.id}", "startDate": startDate, "endDate": endDate}, 'listDiv', 'list', 'POST', 'totalPages', null, function(){
			$("#totalPages").remove();
		});
    }

    </script>
</body>

</html>