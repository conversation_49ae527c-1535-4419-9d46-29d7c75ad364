<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>申请退票</title>
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="black">
        <meta name="format-detection" content="telephone=no, email=no"/>
        <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js" ></script>
        <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/plusWap.css" />
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/lifeService.css" />
		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js"></script>
		<style>
			.myOrderPage{
				background:transparent;
				margin-top:0;
			}
		</style>
	</head>
	<body>
	[#assign oi=order.orderItems[0]]
	[#assign ticketOrder=oi.getVirtualProductOrderInfoMapValue('ticketOrders')[0]]
	[#assign textParams=oi.getVirtualProductOrderInfoMapValue('textParams')]
	
		<div class="myOrderPage">
			<div class="public_top_header bg_theme">
				<a href="javascript:history.go(-1);" class="return_back"></a>
				申请退票
				[#include "./wechat/include/head_nav.ftl" /]
			</div>
			
			<section class="orderCreate">
				<p class="no_order">订单号：${order.sn}</p>
			</section>
			
			<div class="fly_order_info_section train">
				<header>车票信息</header>
				  <div class="page_header_bg fly train">
				<header>
					<div class="line2">
						<div class="from">
							<p>${ oi.getVirtualProductOrderInfoMapValue('from')}</p>
							<p>${oi.getVirtualProductOrderInfoMapValue('startTime') }</p>
							<p>${ oi.getVirtualProductOrderInfoMapValue('date')}<span>${oi.getVirtualProductOrderInfoMapValue('week')}</span></p>
						</div>
	
						<div class="arrow">
							<p>${oi.getVirtualProductOrderInfoMapValue('trainNumber') }</p>
							<p>${oi.getVirtualProductOrderInfoMapValue('runTime') }</p>
						</div>
	
						<div class="to">
							<p>${oi.getVirtualProductOrderInfoMapValue('to') }</p>
							<p>${oi.getVirtualProductOrderInfoMapValue('endTime')}</p>
							<p>${oi.getVirtualProductOrderInfoMapValue('endDate') }<span>${oi.getVirtualProductOrderInfoMapValue('endWeek') }</span></p>
						</div>
					</div>
				</header>
				</div>
				<div class="preson_info">
					<div class="item">
						<label>乘车人</label>
						<div class="values">
							<span>${ticketOrder.passengerName}</span>
							<span>身份证${ticketOrder.idcardNo?substring(0,1)}***************${ticketOrder.idcardNo?substring((ticketOrder.idcardNo?length-1),(ticketOrder.idcardNo?length))}</span>
							<span>[#if ticketOrder.trainInsurePrice?? ]
									保险X1
								[/#if]</span>
						</div>
					</div>
					<div class="item">
						<label>联系人</label>
						<div class="values">
							<span>${textParams.contactName }</span>
							<span>${textParams.contactTel }</span>
						</div>
					</div>
				</div>
				
			<!-- 	<header>
					<div class="fli_checkbox_blue">
						<input type="checkbox" name="ids" id="id2" value="">
						<label for="id2"></label>
					</div>全选
				</header>
				<div class="preson_list">
					<div class="item">
						<label>
							<div class="fli_checkbox_blue">
								<input type="checkbox" name="ids" id="id2" value="">
								<label for="id2"></label>
							</div>熊大
						</label>
						<div class="values">
							身份证******************
						</div>
					</div>
					<div class="item">
						<label>
							<div class="fli_checkbox_blue">
								<input type="checkbox" name="ids" id="id2" value="">
								<label for="id2"></label>
							</div>熊大大
						</label>
						<div class="values">
							身份证******************
						</div>
					</div>
				</div> -->
			</div>
			
			<div class="bottom">
				<input type="button" name="" id="" value="提交" class="btn_submit_long" />
			</div>
		</div> 
		
		<div class="public_info_bottom">
			<div class="title">退票规则：</div>
			<div class="content">
				<p>1. 车票预订成功后，未取纸质票且发车4小时前，可以申请在线退票，如遇特殊情况，以12306实际规定为准。</p>
				<p>3. 非在线退票时间，或已取出纸质票，或离发车时间4小时内，请携带购票证件原件到车站售票窗口退票。</p>
				<p>4. 退票手续费：开车前15天（不含）以上退票的，不收取退票费；票面乘车站开车时间前48小时以上的按票价5%计，24小时以上、不足48小时的按票价10%计，不足24小时的按票价20%计。 开车前48小时～15天期间内，改签或变更到站至距开车15天以上的其他列车，又在距开车15天前退票的，仍核收5%的退票费。最终退款以铁路总局是退为准。</p>
				<p>5.上述计算的尾数以5角为单位，尾数小于2.5角的舍去、2.5角以上且小于7.5角的计为5角、7.5角以上的进为1元。退票费最低按2元计收</p>
				<p>6. 车站窗口退票或改签，退款7个工作日左右退回原支付账号。</p>
				<p>注：款项到账部分银行没有短信通知，建议您查询账户明细；</p>
			</div>
		</div>
		
		<div class="popbg" id="pop1">
			<div class="pop">
				<header>退票费用信息</header>
				<div class="content">
					退票将执行《购票退改签规则》，如需收取退票费用，退票成功后将在扣除手续费后，将剩余款项原路退回，预计7个工作日退回原支付账户。
				</div>
				<div class="footer">
					<input type="button" name="" id="" value="确认" class="btn_submit"/>
					<input type="button" name="" id="" value="取消" class="btn_concel"/>
				</div>
			</div>
		</div>
		
		<div class="popbg" id="pop2">
			<div class="pop">
				<header>申请已提交</header>
				<div class="content">
					您的退票申请已提交，请耐心等待
				</div>
				<div class="footer">
					<input type="button" name="" id="" value="我知道了" class="btn_submit"/>
				</div>
			</div>
		</div>
		
		<script type="text/javascript">
		 	$(function(){
		 		$(".btn_submit_long").on("click",function(){
					$("#pop1").show();
				})
		 		
				$("#pop1 .btn_concel").on("click",function(){
					$("#pop1").hide();
				})
				
				$("#pop1 .btn_submit").on("click",function(){
					$("#pop1").hide();
					applyRefundSave();
					
				})
				
				$("#pop2 .btn_submit").on("click",function(){
					var backUrl = "orderList.jhtml";
					[#if applyId??]
						backUrl="${base}/trip/view.jhtml?id=${applyId}";
					[/#if]
					setTimeout("location.href='" + backUrl + "';",300);
					$("#pop2").hide();
				})
			});
		 	 function applyRefundSave() {
					$(".btn_submit_long").prop("disabled",true);
					$(".btn_submit_long").val("提交中...");
					$.post("applyRefundSave.jhtml",{"id":"${order.id}"},function(data){
						if(data){
							if(data.type=="success"){
								 $("#pop2").show();
							}else{
								error(data.content); 
							}
						}else{
							error();
						}
					},"json");
				};
				
			function error(msg){
				$(".btn_submit_long").prop("disabled",false);
				$(".btn_submit_long").val("提交");
				if(!msg){
					msg="申请提交失败，请重试！";
				}
				layer.open({
		              content: msg
		              ,skin: 'msg'
		              ,time: 2 //2秒后自动关闭
		           });
			}
		
		 </script>
	</body>  
</html>
