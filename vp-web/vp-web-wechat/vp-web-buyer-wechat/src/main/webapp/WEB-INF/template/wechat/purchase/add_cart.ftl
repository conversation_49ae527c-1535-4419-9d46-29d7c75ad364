<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta
            name="viewport"
            content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no"
    />
    <title>选择集采清单</title>
    <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
    <link rel="stylesheet" href="${base}/resources/wechat/css/vant/index.css">
    <link  type="text/css" rel="stylesheet" href="${base}/resources/wechat/css/common.css">
    <link  type="text/css" rel="stylesheet" href="${base}/resources/wechat/css/purchaseMobile.css">
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/slide.css" />
    <script src="${base}/resources/wechat/plugins/vue/vue.min.js"></script>

    <script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
    <script src="${base}/resources/wechat/js/vant.min.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/jquery.lgyslide.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/jigsawPuzzle.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js" ></script>
</head>
<body class="purchaseListPage">
<div id="app"  >
        <p class="title">
            选择集采清单
        </p>
        <div class="purchaseListContent">
                <div @click="change(item.id)" class="item"  :class="activeID==item.id?'active':''" v-for="(item,index) in dataList" :key ="'purchase'+index">
                        <p v-cloak>
                            {{item.purchaseName}}
                        </p>
                        <p v-cloak>
                            {{item.crateDate}}
                        </p>
                </div>

        </div>
        <div class="option flexbox justify-content-c align-items-c">
            <van-button color="#666666"  class="addPurchase" size="small" @click="createProcurement" plain hairline round>新建采购清单</van-button>
            <van-button color="#1875f9"   size="small" @click="addProduct" round>加入清单</van-button>
        </div>
</div>
<script type="text/html" id="slider_code">
    <div id="imgscode"></div>
</script>
<script>
    let vm = new Vue({
        el: "#app",
        data() {
            return {
                activeID:'${defaultPurchaseId}',
                dataList:[
                [#list page as purchase]
                    {
                    purchaseName:'${purchase.purchaseName}',
                    crateDate:" ${purchase.createDate?string('yyyy-MM-dd HH:mm:ss')}",
                    id:'${purchase.id}'
                }[#if page_index<page?size-1],[/#if]
                    [/#list]
                    ]

            };
        },
        computed:{

        },
        mounted(){

        },
        methods: {
            change(id){
                this.activeID = id;
            },
            createProcurement(){
                var index = parent.layer.getFrameIndex(window.name);
                //调用父页面的方法，重新打开新建清单的页面
                window.parent.addCreateProcurement();
                //关闭当前的layer子页面
                parent.layer.close(index);

            },
            addProduct(){
                window.parent.$("#purchaseId").val(this.activeID);
                window.parent.addJc();
                //关闭现有的弹窗
                window.parent.layer.closeAll();
            }

        },
    });
    $(function () {

    })
</script>
</body>
</html>
