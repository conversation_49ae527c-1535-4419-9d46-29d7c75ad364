<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <title>福豆记录</title>
    <script src="${base}/resources/wechat/plugins/flexible/flexible.js"></script>
    <link rel="stylesheet" href="${base}/resources/wechat/css/common.css" />
    <link rel="stylesheet" href="${base}/resources/wechat/css/fudou.css" />
    <link
      rel="stylesheet"
      href="${base}/resources/wechat/css/vant/index.css"
    />
  </head>

  <body class="fuDouRecordPage">
    <div id="fuDouRecordPage">
      <div class="bg_top_transparent colorFul">
        <div class="public_top_header transparent">
          <a href="javascript:history.go(-1);" class="return_back"></a>
          福豆记录
        </div>
      </div>

      <div class="filterSection">
        <div class="filter" v-cloak @click="show=true">{{filter}}</div>
        <div class="num"><span v-cloak>{{beans}}</span></div>
      </div>

      <van-list
        class="recordSection"
        v-model="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="init"
        :error.sync="error"
        error-text="请求失败，点击重新加载"
      >
        <div class="box" v-for="(item, index) in recordList" :key="index">
          <header v-cloak>
            {{item.month}}
            <div class="total" v-cloak>
              获取<span v-cloak>{{item.obtain}}</span> 支出 {{item.expenses}}
            </div>
          </header>
          <div class="list">
            <div class="item" v-for="detail in item.detail" :key="detail.id">

              <div class="info">
                <p v-cloak>{{detail.memo}}</p>
                <p v-cloak>{{detail.createDate}}</p>
              </div>
              <div class="num" v-cloak :class="{down:!detail.isIncrease}">
                <span v-if="detail.isIncrease">+</span>
                <span v-else>-</span>
                {{detail.bean}}
              </div>
            </div>
          </div>
        </div>
      </van-list>

      <van-action-sheet v-model="show" :actions="actions" @select="onSelect" />
    </div>
    <script type="text/javascript">
      [#if beans??]
      var beans = ${beans};
      [/#if]
    </script>

    <script src="${base}/resources/wechat/plugins/vue/vue.min.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

    <script src="${base}/resources/wechat/js/common.js"></script>
    <script src="${base}/resources/wechat/js/vant.min.js"></script>
    <script src="${base}/resources/wechat/js/moment.js"></script>
    <script src="${base}/resources/wechat/es6/fuDouRecord.js"></script>

  </body>
</html>
