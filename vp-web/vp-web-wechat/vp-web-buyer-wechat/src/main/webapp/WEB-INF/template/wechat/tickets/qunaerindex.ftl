<!DOCTYPE html>
<html lang="en" data-dpr="1" style="font-size: 16px;">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>门票首页</title>
    <link href="https://cdn.bootcss.com/font-awesome/4.7.0/css/font-awesome.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css">
    <link rel="stylesheet" href="${base}/resources/wechat/css/swiper.css">
    <link rel="stylesheet" href="${base}/resources/wechat/css/qunaer-wechat.css">
    <script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

    <script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
    
    <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/swiper.jquery.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/plugins/raty-2.5.2/jquery.raty.min.js"></script>
</head>

<body class="no_fixed_top">
    <div class="qunaerpage">
        <div class="index">
            <header>
              <!--<i class="fa fa-angle-left left" onclick="javascript:location.href='${base}/travel_service/index.jhtml?sysModuleId=92';"></i>  --> 
               <i class="return_back" onclick="javascript:location.href='[#if yphFlag??]${base}/index.jhtml[#else]${base}/travel_service/index.jhtml?sysModuleId=204[/#if]';"></i>
                <div class="inputBox left">
                    <label onclick="selectCity()">${homecity_name}</label><i class="fa fa-angle-down"></i>
                    <input onclick="toSearch()" type="text" placeholder="请输入景点名称">
                </div>
                <i class="fa fa-file-text-o right" onclick="javascript:location.href='${base}/tickets/orderList.jhtml';"></i>
            </header>
            <!-- 轮播图 图片替换-->
            <div class="banner">
                <div class="swiper-container swiper-container-horizontal swiper-container-autoheight swiper-container-android">
                    <div class="swiper-wrapper">
                    	[@ad_position id = 192 companyId = companyId/]
                       <!--   <a href="/act/list/200.jhtml" class="swiper-slide " style="width: 400px;">
                            <img src="http://admin.fliplus.com/uploads/admin/upload/image/201803/658ee318-6828-4030-863f-341e09cccd8c.jpg">
                        </a>
                        <a href="/act/list/224.jhtml" class="swiper-slide" style="width: 400px;">
                            <img src="http://admin.fliplus.com/uploads/admin/upload/image/201803/bbb5876d-5df1-4149-b50b-50ad36cf7270.jpg">
                        </a> --> 
                    </div>
                    <!-- Add Pagination -->
                    <div class="swiper-pagination swiper-pagination-clickable">

                    </div>

                </div>
            </div>
            <article>
                <div class="title">
                    <img src="${base}/resources/wechat/img/qunaer/hot.png" alt=""><span>本周热搜榜</span>
                </div>
                <div class="swiper-container swiper-container-horizontal ">
                    <div class="swiper-wrapper">
                    	[#list sightsz as sight]
	                        <div class="swiper-slide " style="width: 105px;" onclick="toDetail(${sight.sightId})">
	                            <div class="img">
	                                <img src="${sight.sightImages}" alt="">
	                            </div>
	                            <div class="textBox">
	                                <p class="text">${sight.name}</p>
	                                <p class="money red">
	                                	[#if productShowRate??]
	                                	<span class="cny">${coinConvert(sight.lowestPrice, productShowRate, true, false, 0)}</span>起
	                                	[#else]
	                                    <i class="fa fa-cny"></i>
	                                    <span class="cny">${sight.lowestPrice}</span>起
	                                    [/#if]
	                                </p>
	                            </div>
	                        </div>
                        [/#list]
                    </div>

                </div>
            </article>
            <aside>
                <div class="title">
                    <img src="${base}/resources/wechat/img/qunaer/recommend.png" alt=""><span class="rec-text">猜你喜欢</span>
                    <!-- <i class="fa fa-angle-right right"></i>
                    <span class="more right">查看所有</span> -->
                </div>
                <div class="lists">
                    <ul class="list">
                    [#list hot as sight]
                        <li class="item" onclick="toDetail(${sight.sightId})">
                            <div class="section1 left">
                                <img src="${sight.sightImages}" alt="">
                            </div>
                            <div class="section2 right">
                                <p><span class="name">${sight.name}</span><span class="level">(${sight.star}级)</span></p>
                                <div class="score">
                                    <span class="stars" data-score="${sight.score}"></span>
                                </div>
                                <p class="price">
                                	[#if productShowRate??]
                                	<span class="number red">${coinConvert(sight.lowestPrice, productShowRate, true, false, 0)}</span>起
                                	[#else]
                                    <i class="fa fa-cny red"></i>
                                    <span class="number red">${sight.lowestPrice}</span>起
                                    [/#if]
                                    <span class="district right">
                                               ${sight.areaNamePath}
                                    </span>
                                </p>
                            </div>
                        </li>
                    [/#list]   
                    </ul>
                </div>
            </aside>
        </div>


    </div>
    <a class="fli_to_top_arrow backTotop"></a>
</body>
<script type="text/javascript">
    var toTop = $('.fli_to_top_arrow')
    $(window).scroll(function() {
            // 这里一定要兼容各浏览器，不然得到的结果都是0   
            var top = document.documentElement.scrollTop || window.pageYOffset || document.body.scrollTop;
            // 控制底部按钮显示与切换
            top >= ($(window).height() * 1.5) ? toTop.show() : toTop.hide();
            // 控制顶部搜索栏的样式切换,实现那渐变的效果
            //var opci = top / $('.banner').height();
           // top >= $('.banner').height() ? $('header').css("backgroundColor", "#FFF") : $('header').css("backgroundColor", "rgba(255, 255, 255," + opci + ")")
        })
        //点击回到顶部事件
    $('.backTotop').click(function(e) {
        $('body').animate({
            scrollTop: 0
        }, 500)
    })
	var city = "${homecity_name}";

	// 选择出发城市
    function selectCity(){    	 
    	 location.href="${base}/tickets/selectCity.jhtml?homecity_name="+city;    	  	
  	} 
  	
  	//跳转搜索页
  	function toSearch(){
  		location.href="${base}/tickets/toSearch.jhtml?homecity_name="+city;
  	}
  	
  	//跳转详情
  	function toDetail(sightId){
  		location.href="${base}/tickets/detail.jhtml?sightid="+sightId;
  	}
  	
    //   简单的带分页器的轮播图
    var swiper1 = new Swiper('.banner .swiper-container', {
        pagination: '.swiper-pagination',
        paginationClickable: true,
        centeredSlides: true,
        //autoplay: 2500,
        loop: true,
     //   autoHeight: true,
       // autoWidth: true,
        autoplayDisableOnInteraction: false,

    });

    // 多个slide的轮播图
    var swiper2 = new Swiper('article .swiper-container', {
        slidesPerView: 3,
        spaceBetween: 5,
        freeMode: true,
        // autoplay: 2500,
        // pagination: {
        //     el: '.swiper-pagination',
        //     clickable: true,
        // },
    });
    // 评星级
    $.fn.raty.defaults.path = '${base}/resources/wechat/plugins/raty-2.5.2/img';
    $('.stars').raty({
        score: function() {
            return $(this).attr('data-score');
        },
        readOnly: true,
    });
</script>
</html>

