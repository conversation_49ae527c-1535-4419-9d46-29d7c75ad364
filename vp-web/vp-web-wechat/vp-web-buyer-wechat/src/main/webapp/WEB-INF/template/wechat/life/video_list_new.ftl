<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		[#--[#if brandSn??&&brandSn=='supermarket']
			[#assign titleName='商超会员' /]
		[#elseif brandSn??&&brandSn=='food']
			[#assign titleName='美食卡券' /]
		[#elseif brandSn??&&brandSn=='knowledge']
			[#assign titleName='知识会员' /]
		[#else]
			[#assign titleName='音视频卡充值' /]
		[/#if]--]
		<title>${moduleName}</title>
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">
		<script type="text/javascript" src="${base}/resources/wechat/js/flexible.js" ></script>
        <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/lifeService.css" />
		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/card.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/jquery.validate.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
	</head>
	[#assign couponMode = (couponCountVo?? && couponCountVo.couponMode) ]
	<body class="has_fixed_footer new-video-list-page">
		<div class="public_top_header">
            <span class="return_back" onclick="javascript:history.back();"></span>
			${moduleName}
		</div>
        <div class="header-top">
            <img src="${setting.siteUrlImg}${brand.logo}"/>
            <div class="text flexbox flex-column justify-content-space-between">
                <p class="title">${brand.name}</p>
                <p class="desc">${brand.introduction}</p>
            </div>
        </div>
		<div class="list_tv clearfix">
			[#if products?size>0]
				[#list products as product]
					[#if product_index==0 && product.introduction??]
						[#assign isShow = true ]
					[/#if]
				<div data-videoSn = "${product.jdSku}" data-videoId="${product.id}"
					 [#if (product.supplierId.isOweOrder==58||product.supplierId.isOweOrder==65)&&product.introduction??]data-introducrion="1"[#else ]data-introducrion="0"[/#if]
						class="items flexbox flex-column justify-content-c align-items-c [#if product_index == 0] active [/#if]">
					<p class="fullName">
						${product.name}
					</p>
					<p class="tip">
						${product.productCategory.name}
					</p>
					<p class="price">
					[#assign productPrice = product.price]
					
					[#if couponMode]
						[#assign couponNum=((product.marketPrice/couponCountVo.minAmount)?round)]
						<span>[#if couponNum==0]1[#else]${couponNum}[/#if]</span>张
					[#else]
						￥<span >
						[#if productShowRate??]
							${coinConvert(productPrice, productShowRate, false, false)}
						[#else]
							${currency(productPrice, false, false)}
						[/#if]
						</span>
					[/#if]
					</p>
					[#if couponMode]
					<p class="quan">
                        兑换券
					</p>
					[#else]
						<p class="marketPrice">
							￥${product.marketPrice}
						</p>
					[/#if]
					[#if product.memo??]
					<p class="p-bable">${product.memo} </p>
					[/#if]
				</div>
				[/#list]
				<input type="hidden" id="videoSn" value="${products[0].jdSku}">
				<input type="hidden" id="videoId" value="${products[0].id}">
				[#else ]
				<div class="public_empty_section [#if comPersonConf && com_person_EmptyBg != null && com_person_EmptyBg != ""] ${com_person_EmptyBg!}[#else] Public_Bg[/#if]">
                    <p class="tip">这里空空如也，先去逛逛吧~</p>
                    <a href="${base}/index.jhtml" class="pub_link_theme">去逛逛</a>
                </div>
			[/#if]
		</div>
		<div class="fixed_footer">
			[#if products?size>0]
				<p class="tip" [#if !products[0].memo??]style="display: none"[/#if]>
					[#if products[0].memo??]
					${products[0].memo}
					[/#if]
				</p>
				<div class="footer-option flexbox align-items-c justify-content-space-between">
					<button id="submit-notice" class="submit-notice  [#if !(isShow??&&isShow)]hide-notice[/#if]"  type="button">使用说明</button>
					<button id="submit-recharge"  [#if products?size==0]disabled="disabled"[/#if] type="button">[#if couponMode]立即兑换[#else]立即充值[/#if]</button>
				</div>
			[/#if]
		</div>

	</body>
<script>
	$(function () {
        $(".items").on("click",function () {
            $(this).addClass("active").siblings().removeClass("active");
            $("#videoSn").val($(this).attr("data-videoSn")) ;
            $("#videoId").val($(this).attr("data-videoId")) ;
			var showIntroducrion = $(this).attr("data-introducrion");
			if(showIntroducrion==="0"){
                $("#submit-notice").addClass('hide-notice');
			}else {
                $("#submit-notice").removeClass('hide-notice');
			}
            var tip = $(this).find('.p-bable');
            if(tip.length>0){
                $(".fixed_footer .tip").show();
                $(".fixed_footer .tip").html($(tip).html())
			}else {
                $(".fixed_footer .tip").hide()
			}

        })
		$("#submit-recharge").on("click",function () {
            $(this).attr("disabled","disabled");
		    const videoId = $("#videoId").val();
            const  yanjingFlag = '${yanjingFlag}';
            if(yanjingFlag!=null && yanjingFlag ==1){
                location.href = "${base}/yanjingOrder/info_virtual.jhtml?isFirst=1&type=3&videoId=" + videoId;
                $(this).attr('disabled',false);
            }else {
                location.href = "${base}/member/order/info_virtual.jhtml?isFirst=1&type=3&videoId=" +  videoId;
                $(this).attr('disabled',false);
            }
        })
		//购买须知
		$("#submit-notice").on("click",function () {
            const videoId = $("#videoId").val();
            location.href = "${base}/seecom/purchaseNotice.jhtml?id=" + videoId;
		})
    })

</script>
</html>
