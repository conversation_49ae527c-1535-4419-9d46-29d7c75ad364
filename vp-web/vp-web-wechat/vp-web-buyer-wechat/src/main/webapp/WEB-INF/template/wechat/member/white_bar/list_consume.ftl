<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>我的积分</title>
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="black">
        <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js" ></script>
        <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
        <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/activity.css" />
		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
		
		<script type="text/javascript" src="${base}/resources/wechat/js/dropload.min.js" ></script>
	</head>
	[#assign current = "my" /]
	<body class="has_fixed_footer">
	[#include "./wechat/include/footer.ftl" /]
		 <div class="myCoinsPage">
		 	<div class="public_top_header">
				<a href="javascript:history.go(-1);" class="return_back"></a>
				我的白条
			</div> 
		 	<section class="coin_section">
				<div>
					<label>白条积分</label>
					<div class="lists">
				[#if whiteBarCoins!=null && whiteBarCoins?size>0]
				  [#list whiteBarCoins as coin]
					<span>授信额度<b>${coin.whiteBarLimitAmount}</b></span>
					<span>余额<b>${coin.balance}[#if !coin.isFreeze](冻结)[/#if]</b></span>
				  [/#list]
				[#else]
				    <span>授信额度<b>0</b></span>
					<span>积分余额<b>0</b></span>
				[/#if]
					</div>
				</div>
			</section> 
			
			
		 	<div class="tab_theme_section">
		 		<div class="item"><a href="${base}/member/white_bar.jhtml">已出账</a></div>
		 		<div class="item active"><a href="${base}/member/whiteBarConsume.jhtml">未出账</a></div> 
		 	</div>
		 	
		 	<div class="tab_content">
		 		<ul class="consume_list">
		 		</ul>
		 	</div>
		 </div>
		 
		 <script type="text/javascript">
			$(function(){
				//加载数据
			    loadData('${base}/member/whiteBarConsumePage.jhtml', null, 'myCoinsPage', 'consume_list', 'POST', 'totalPages', null, null);
			})
		</script>
		 
	</body>
</html>
