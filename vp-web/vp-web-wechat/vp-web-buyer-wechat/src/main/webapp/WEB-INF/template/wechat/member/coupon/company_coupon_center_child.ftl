		 			
		 		<input type="hidden" id="totalPages" value="${page.totalPages}"/>
		 		[#if page.total > 0]
		 		
		 		 [#list page.content as coupon]
			     <div [#if coupon.isAllRecived]class="item end" [#else] class="item"[/#if] couponId="${coupon.id}" onclick="receive(this)">
					<div class="text">
					[#if coupon.couponSn=="conversion"]
					<p class="num"><span>礼品兑换券</span></p>
						<p>
	                                                                          兑换指定商品
							</p>
					[#else]
						<p class="num">
						[#if coupon.type??&&coupon.type==2]
											<span class="discountNum"> ${coupon.couponAmount}<span>折</span></span>
						[#else ]
											￥<span>${coupon.couponAmount}</span>
						[/#if]
						</p>
							<p>
								[#if coupon.minimumPrice!=null && coupon.minimumPrice!=0&& coupon.maxPrice!=null && coupon.maxPrice!=0]
									${coupon.minimumPrice}到${coupon.maxPrice}以内可用
								[#elseif coupon.minimumPrice!=null &&coupon.minimumPrice!=0]
								   满${coupon.minimumPrice}可用
								[#elseif coupon.maxPrice!=null && coupon.maxPrice!=0]
									${coupon.maxPrice}以内可用
								[#else ]
									不限金额
								[/#if]
							(不含邮费，特价商品除外)
							</p>
					[/#if]	
						<p>
						[#if coupon.scope??]
							${coupon.scope} 
						[#else]
							[#if coupon.isAllSupplier]全品牌.[#else][#list coupon.brands as brand]${brand.name}.[/#list][/#if][#if coupon.isAllCategory]全分类[#else]${coupon.categoryName}[/#if]
						[/#if]
						</p>
						<p>
					[#if coupon.couponInvalidDays??]
						有效期：领券当天起${coupon.couponInvalidDays}天内可用
			            [#else]
							有效期：[#if coupon.couponBeginDate!=null]${coupon.couponBeginDate}[/#if][#if coupon.couponEndDate!=null]至${coupon.couponEndDate} 
							[/#if]
						[/#if]
						</p> 
					</div>
					[#if coupon.isAllRecived || coupon.isRecived]
					   <span class="icon_obtained"></span>
					[/#if]
					<div class="btn">
					<span id="receiveSpan${coupon.id}" 
						[#if coupon?? && coupon.uri??]
							[#if coupon.uri?index_of("http") == 0]
								data-url="${coupon.uri}"
							[#else ]
							 	data-url="${base}${coupon.uri}"
							[/#if]
						[#else]
						[#if coupon.couponSn=="marketing"]
							data-url="${base}/coupon_center_wechat/coupon_product_list.jhtml?couponId=${coupon.id}"
						[#elseif coupon.couponSn=="ActCoupon"]
							[#if coupon.actId??]
								data-url="${base}/act/list/${coupon.actId}.jhtml"
							[#else]
								data-url="${base}/product/index.jhtml"
							[/#if]
						[#elseif coupon.couponSn=="product"]	
							[#if coupon.products?size==1]
								[#list coupon.products as product]
									data-url="${base}${product.path}"
								[/#list]
							[#else]	
								data-url="${base}/coupon_center_wechat/coupon_product_list.jhtml?couponId=${coupon.id}"
							[/#if]	
						[/#if]
						[/#if]
						>
						[#if coupon.isRecived]
							[#if coupon.couponBeginDate??&&coupon.couponBeginDate>.now]
								未生效
							[#else]
							去使用
							[/#if]
							
						[#else]
							[#if coupon.isAllRecived]
								去商城
							[#elseif coupon.isAllRecived==false && coupon.isRecived==false]
								立即领取
							[/#if]
						[/#if]	
						</span>	
					</div>
				</div>
				
				[/#list]
			
			[#else]
						<div class="public_empty_section [#if comPersonConf && com_person_EmptyBg != null && com_person_EmptyBg != ""] ${com_person_EmptyBg!}[#else] Public_Bg[/#if]">
                            <p class="tip">这里空空如也，先去逛逛吧~</p>
							<a href="${base}/product/index.jhtml" class="pub_link_theme">去逛逛</a>
			 			 </div>
		 	[/#if]