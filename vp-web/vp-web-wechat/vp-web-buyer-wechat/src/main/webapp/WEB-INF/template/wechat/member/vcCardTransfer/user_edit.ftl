<!DOCTYPE html>
<html style="background: #fff;">
<head>
    <meta charset="utf-8" />
    <title>更改账号绑定</title>
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js" ></script>
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/login.css" />
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/commonForm.css" />
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/slide.css" />
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/plugins/font-awesome-4.7.0/css/font-awesome.min.css" />
    <script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

    <script type="text/javascript" src="${base}/resources/wechat/js/jquery.validate.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js" ></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/jsbn.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/prng4.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/base64.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/jquery.lgyslide.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/jigsawPuzzle.js"></script>
</head>
<script type="text/javascript">
    $().ready(function() {
			[@flash_message /]
    });
</script>
<body class="transferUserInfoPage" style="background: #fff;">

<div class="public_top_header">
    <span class="return_back" onclick="javascript:window.history.go(-1);"></span>
    更改账号绑定
</div>

<div class="loginPage">
    <div class="login_form_section textLeft">
        <form class="commomFrom" id="inputForm" action="${base}/member/vcCardTransfer/card/accountCommit.jhtml" >
            <div class="item standard_input">
                <input type="tel" name="mobile" id="mobile" value="${mobile}" placeholder="请输入手机号" class="input" />
                <span class="icon_clear"></span>
            </div>
            <!--<div class="item hasbutton standard_input">
                <input type="text" name="captcha" id="captcha" value="" placeholder="请输入图形验证码" class="input" />
                <span class="icon_clear"></span>
                <img id="captchaImage" class="captchaImage" src="${base}/common/captcha.jhtml?captchaId=${captchaId}" title="">
            </div> -->
            <div class="item hasbutton standard_input long">
                <input type="tel" name="code" id="code" value="" placeholder="请输入短信验证码" class="input" />
                <span class="icon_clear"></span>
                <input type="button" name="" id="emailCodeBtn" value="获取短信验证码" class="code"/>
            </div>
            <div  class="fli_checkbox_blue corner" >
                <input type="checkbox" id="agreement" name="agreement" />
                <label for="agreement"></label>
                已阅读并同意<a href="${base}/member/vcCardTransfer/userAgreement.jhtml">《账号授权协议》</a>
            </div>
            <input type="submit" id="submit" value="确定" class="btn_cornor_long"/>

        </form>
    </div>
</div>

<script type="text/html" id="slider_code">
    <div id="imgscode"></div>
</script>
<script type="text/javascript">
    var interval;

    $(function(){
        // var $captcha = $("#captcha");

        //以下两个方法是为了去掉前后空格
        $("input[name='mobile']").blur(function(){
            $(this).val($(this).val().trim());
        });
        jQuery.validator.addMethod("phoneFormat",function(value,element,param){
                    value = value.trim();
                    var regex = commonRule.phone;
                    if (regex.test(value)){
                        return true;
                    }else{
                        return false;
                    }
                },$.validator.format("手机号码格式错误")
        );
        //清除session保存的值
        function clearSessionStorage(){
            var BankInfoArr =["bankName","bankNum","name","id"];//seession保存的银行卡信息
            BankInfoArr.map(item=>{
                sessionStorage.removeItem(item);
            })
        };
        // 表单验证
        $("#inputForm").validate({
            rules: {
                mobile: {
                    required:true,
                    phoneFormat: true,
                    digits:true,
                    maxlength: 11
                },
                code: {
                    required:true,
                    digits:true
                }
            },
            messages: {
                mobile: {
                    pattern: "手机号码不存在"
                }
            },
            submitHandler: function(form) {
                if(!$("#agreement").prop("checked")){
                    layer.open({
                        content:'请阅读并同意《账号授权协议》'
                        ,skin: 'msg'
                        ,time: 2
                    })
                    return false;
                }

                $("#submit").prop("disabled", true);
                $.ajax({
                    url:$("#inputForm").attr("action"),
                    type: "POST",
                    data: {
                        mobile: $("#mobile").val(),
                        code: $("#code").val()
                    },
                    dataType: "json",
                    cache: false,
                    success: function(flag) {
                        $("#submit").prop("disabled", false);
                        if (flag == 0) {
                            layer.open({
                                content: '短信验证码已过期！'
                                ,skin: 'msg'
                                ,time: 2 //2秒后自动关闭
                            });
                        } else if (flag == 1) {

                            sessionStorage.setItem("thirdMobile",$("#mobile").val());
                            clearSessionStorage();//清楚之前账号选中的银行卡信息
                            // 注册成功 延时跳转
                            setTimeout(function () {
                                location.href="${base}" + "/member/vcCardTransfer/card/index.jhtml";
                            },1000);
                        } else if (flag == 2) {
                            layer.open({
                                content: '短信验证码输入错误！'
                                ,skin: 'msg'
                                ,time: 2 //2秒后自动关闭
                            });
                        } else if (flag == 3) {
                            layer.open({
                                content: '验证手机号码不一致！'
                                ,skin: 'msg'
                                ,time: 2 //2秒后自动关闭
                            });
                        } else if (flag == 4) {
                            layer.open({
                                content: '此手机号码已被绑定！'
                                ,skin: 'msg'
                                ,time: 2 //2秒后自动关闭
                            });
                        } else if (flag == 5) {
                            layer.open({
                                content: '该账户已被锁定！'
                                ,skin: 'msg'
                                ,time: 2 //2秒后自动关闭
                            });
                        } else {
                            layer.open({
                                content: '用户信息修改失败！'
                                ,skin: 'msg'
                                ,time: 2 //2秒后自动关闭
                            });
                        }
                    }
                });
            }
        });

        $("#emailCodeBtn").click(function(){
            var mobile = $("#mobile").val();
            if(mobile == ""){
                layer.open({
                    content: '请输入手机号码'
                    ,skin: 'msg'
                    ,time: 2 //2秒后自动关闭
                });
                return false;
            }
            if(!(commonRule.phone.test(mobile))){
                layer.open({
                    content: '请输入正确的手机号码'
                    ,skin: 'msg'
                    ,time: 2 //2秒后自动关闭
                });
                return false;
            }

            // if($captcha.val() == null || $captcha.val() == ""){
            //     layer.open({
            //         content: "请输入图形验证码"
            //         ,skin: 'msg'
            //         ,time: 2 //2秒后自动关闭
            //     });
            //     return false;
            // }
            if($(this).val() == "获取短信验证码"){
                layer.open({
                    type: 1,
                    title: false,
                    closeBtn: 0,
                    resize: false,
                    scrollbar: false,
                    area: ['auto', '420px'], //宽高
                    skin:"layer-code",
                    content: $("#slider_code").html(),
                    success: function () {
                        createcode(sendPhoneCode);
                    }
                });
            }
        });

        // 更换验证码
        //$("#captchaImage").click(function() {
         //   $("#captchaImage").attr("src", "${base}/common/captcha.jhtml?captchaId=${captchaId}&timestamp=" + (new Date()).valueOf());
        //});
    });
    function sendPhoneCode() {
        var mobile = $("#mobile").val();
        $.ajax({
            url:'${base}/member/vcCardTransfer/sendPhoneCode.jhtml',
            type:'POST',
            data:{
                mobile: mobile
            },
            cache:false,
            async:false,
            success:function(flag){
                if(flag == 1){
                    //验证码发送成功
                    emailCodeBtnVal();
                    interval = setInterval("emailCodeBtnVal()", 1000);
                }else if(flag == 2){
                    layer.open({
                        content: '短信验证码达到当日上线'
                        ,skin: 'msg'
                        ,time: 2 //2秒后自动关闭
                    });
                }
                // else if(flag == 4){//图形验证码错误
                //     layer.open({
                //         content: '图形验证码错误，请重新输入'
                //         ,skin: 'msg'
                //         ,time: 2 //2秒后自动关闭
                //     });
                // }
                else if (flag == 5){
                    layer.open({
                        content: "距上次发送短信验证码不足60秒",
                        skin: 'msg',
                        time: 2
                    });
                } else{
                    //验证码发送失败
                    layer.open({
                        content: '短信验证码发送失败'
                        ,skin: 'msg'
                        ,time: 2 //2秒后自动关闭
                    });
                }

                // if(flag != 1){
                //     $("#captchaImage").click();//刷新图形验证码
                // }
            }
        });
    }
    //发送验证码按钮显示秒数递减
    function emailCodeBtnVal(){
        if($("#emailCodeBtn").val() == "获取短信验证码"){
            $("#emailCodeBtn").removeClass("active");
            $("#emailCodeBtn").val("59");
        }else if($("#emailCodeBtn").val() == "1"){
            clearInterval(interval);
            $("#emailCodeBtn").addClass("active");
            $("#emailCodeBtn").val("获取短信验证码");
        }else{
            $("#emailCodeBtn").val(Number($("#emailCodeBtn").val()) - 1);
        }
    }
</script>
</body>
</html>
