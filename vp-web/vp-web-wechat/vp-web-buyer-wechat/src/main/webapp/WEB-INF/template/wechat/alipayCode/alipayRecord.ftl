<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta
            name="viewport"
            content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no"
    />
    <title>消费明细</title>
    <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
    <link rel="stylesheet" href="${base}/resources/wechat/css/vant/index.css">
    <link type="text/css" rel="stylesheet" href="${base}/resources/wechat/css/common.css">
    <link type="text/css" rel="stylesheet" href="${base}/resources/wechat/css/wechatConsumeRecord.css">
    <script src="${base}/resources/wechat/plugins/vue/vue.min.js"></script>

    <script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
    <script src="${base}/resources/wechat/js/vant.min.js"></script>
</head>
<body class="consumeRecordPage wechatConsumePage alipay-code-consume-page">
<div id="app" @click.stop="hideHomeList">
    <van-sticky>
        <van-nav-bar
                title="消费记录"
                left-text=""
                left-arrow
                @click-left.stop="onClickLeft"
                @click-right.stop="onClickRight"
        >
            <template #right>
                <van-icon name="ellipsis" />
                <ul class="showHomeList  modify_public_right_nav " v-if="showHomeList">
                    <li>
                        <a href="${base}/index.jhtml">
                            <span class="icon_home"></span>
                            <p>首页</p>
                        </a>
                    </li>
                    <li>
                        <a href="${base}/alipayCode/index.jhtml">
                            <span class="wechaticon_home"></span>
                            <p>企业码首页</p>
                        </a>
                    </li>
                    <li>
                        <a href="[#if my_home_link?? && my_home_link != '']${my_home_link}[#else]${base}/member/index.jhtml[/#if]">
                            <span class="icon_user"></span>
                            <p>个人中心</p>
                        </a>
                    </li>
                </ul>
            </template>
        </van-nav-bar>
        <van-tabs
                v-if="type==0"
                @change="changeTab"
                color="#1c77f9"
                 title-inactive-color="rgb(153,153,153)"
                 title-active-color="rgb(51,51,51)"
                v-model="active">
            <van-tab title="支付宝消费"></van-tab>
            <van-tab title="购买记录"></van-tab>
        </van-tabs>
    </van-sticky>

    <div class="content">
        <ul class="recodList">
            <van-list @load="getConsumeRecordList" v-model="loading" :finished="finished" finished-text="没有更多了" >
                <li v-for="(item,index) in recodList" @click="godetail(item.sn)" v-if="item!=null" :key="'record_list_'+index">
                    <div class="contentItem">
                        <template v-if="active===0">
                            <van-row type="flex" justify="space-between" align="center">
                                <van-col span="20" >
                                    <p class="labelText flexbox align-items-c">
                                        <label class="des">
                                            <template v-if="type==='1'">
                                                订单号
                                            </template>
                                            <template v-else>
                                                交易流水号
                                            </template>
                                            <span></span>
                                        </label>
                                        :
                                        <span class="value" v-cloak>
                                         {{item.sn}}
                                    </span>
                                    </p>
                                </van-col>
                                <van-col span="4" >
                                    <p class=" textAlign_right transactionState" :class="item.orderStatus" v-cloak>{{getStateText(item.orderStatus)}}</p>

                                </van-col>
                            </van-row>
                            <van-row type="flex" justify="space-between" align="center">
                                <van-col span="16">
                                    <p class="labelText flexbox align-items-c">
                                        <label class="des">
                                            支付宝账户
                                            <span></span>
                                        </label>
                                        :
                                        <span class="value" v-cloak>
                                         {{hideAccount(item.account)}}
                                    </span>
                                    </p>
                                </van-col>
                                <van-col span="8">
                                    <p class=" textAlign_right transactionNum"><span v-cloak><template v-if="item.orderStatus==='deduct'">-</template>{{item.amount}}</span>元</p>
                                </van-col>
                            </van-row>
                            <van-row type="flex" justify="space-between" align="center">
                                <van-col span="18">
                                    <p class="labelText flexbox align-items-c">
                                        <label class="des">
                                            交易时间
                                            <span></span>
                                        </label>
                                        :
                                        <span class="value" v-cloak>
                                      {{item.createDate}}
                                    </span>
                                    </p>

                                </van-col>
                                <van-col span="6">
                                    <p class="textAlign_right" :class="type==='0'?'refundText':''" v-cloak>
                                        <template v-if="type==='0'">
                                            <template  v-if="type==='0'&&item.refundAmount&&item.refundAmount>0">
                                                {{getRefund(item)}}
                                            </template>
                                        </template>
                                        <template v-else>
                                            <span v-cloak>{{item.payAmount}}</span>{{item.coinTypeName}}</p>
                                        </template>
                                </van-col>
                            </van-row>
                        </template>
                        <template v-else>
                            <van-row type="flex" justify="space-between" align="center">
                                <van-col span="16">
                                    <p class="labelText flexbox align-items-c" v-cloak>
                                            {{item.name}}
                                    </span>
                                    </p>
                                </van-col>
                                <van-col span="8">
                                    <p class=" textAlign_right transactionNum"><span v-cloak>{{item.rechargeAmount}}</span>元</p>

                                </van-col>
                            </van-row>
                            <van-row type="flex" justify="space-between" align="center">
                                <van-col span="24">
                                    <p class="labelText flexbox align-items-c">
                                        <label class="des">
                                            支付宝账户
                                            <span></span>
                                        </label>
                                        :
                                        <span class="value" v-cloak>
                                         {{hideAccount(item.account)}}
                                    </span>
                                    </p>
                                </van-col>
                            </van-row>
                            <van-row type="flex" justify="space-between" align="center">
                                <van-col span="18">
                                    <p class="labelText flexbox align-items-c">
                                        <label class="des">
                                            交易时间
                                            <span></span>
                                        </label>
                                        :
                                        <span class="value" v-cloak>
                                      {{item.createDate}}
                                    </span>
                                    </p>

                                </van-col>
                                <van-col span="6">
                                    <p class="textAlign_right purchaseRecord" v-cloak><span v-cloak>{{item.payAmount}}</span>{{item.coinTypeName}}</p>
                                </van-col>
                            </van-row>
                        </template>

                    </div>
                </li>
            </van-list>
        </ul>
    </div>
</div>
<script>
    let vm = new Vue({
        el: "#app",
        data() {
            return {
                active:0,//0消费记录 1购买记录
                pageNumber: 0,
                pageSize: 10,
                showHomeList:false,
                loading: false,
                finished: false,
                type:"${type}",//1代表是直接使用 0代表要先买额度
                recodList:[],//消费记录数据
            };
        },
        computed:{

        },
        mounted(){
            if(this.type==='0'){ //是购买额度的 如果是从订单操作跳转过来，则显示购买记录
                var referrer = document.referrer;
                if(referrer.indexOf('member/order/list')>-1){
                    this.active =1;
                }
            }
        },
        methods: {
            getRefund(data){
                let {refundAmount,amount} = data;
                let str = "已退款("+refundAmount+"元)";
                if(refundAmount===amount){
                    str = "已全额退款"
                }
                return str;
            },
            inintListData(){
                this.recodList=[];
                this.finished=false;
                this.pageNumber=0;
            },
            //切换菜单
            changeTab(){
                this.inintListData();
                this.getConsumeRecordList();
            },
            godetail(sn){
                if(sn){
                    if(this.type==='0'){
                        if(this.active===1){
                            window.location.href='detail.jhtml?sn='+sn
                        }
                    }else {
                        window.location.href='detail.jhtml?sn='+sn
                    }
                }
            },
            getStateText(state){
                let text;
                if(this.type==='0'){
                    text=state==='deduct'?'支付':'退款';
                }else {
                    text=state==='completed'?'已完成':'已退款';
                }
                return text
            },
            hideAccount(account){ //转换支付宝账户
                if(account===null||account===undefined||account===''){
                    return
                }
                let val =  account.toString();
                let reg;
                let isphone =val.indexOf('@')>-1?false:true;
                if(isphone){ //支付宝账户是否是手机号
                    reg = /^(.{3}).*(.{2})$/;
                    return val.replace(reg, "$1******$2");
                }else {
                    let arr = account.split('@');
                    if(arr[1].length<10){
                        let param = arr[1].length+1;
                        reg = new RegExp("^(.{3}).*(.{"+param+"})$");
                        return val.replace(reg, "$1***$2")
                    }else {
                        let  reg0 = /^(.{3}).*$/;
                        reg = /^(.{5}).*(.{4})$/;
                        return arr[0].replace(reg0, "$1***")+"@"+arr[1].replace(reg, "$1***$2")
                    }

                }
            },
            //转换时间戳 为时间格式显示
            transferTime(date) {
                var date = new Date(date);
                var YY = date.getFullYear() + '-';
                var MM = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
                var DD = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate());
                var hh = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
                var mm = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
                var ss = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());
                return YY + MM + DD + " " + hh + mm + ss;
            },
            hideHomeList(){
                this.showHomeList=false;
            },
            onClickLeft() {
                history.back();
            },
            onClickRight() {
                this.showHomeList=!this.showHomeList;
            },
            getConsumeRecordList(){//获得订单列表分页数据
                this.loading = true;//加载结束
                let param = {};
                let $that = this;
                param.pageNumber = $that.pageNumber + 1;
                param.pageSize = $that.pageSize;
                if(this.active===0){//消费记录
                    $.ajax({
                        type: "POST",
                        url: "alipayRecordList.jhtml",
                        contentType: "application/x-www-form-urlencoded",
                        data: param,
                        success: function(data){
                            $that.loading = false;//加载结束
                            if(data.content == null || data.content == undefined||data.content.length==0) {
                                $that.finished = true;//数据全部加载完成
                                return ;
                            }
                            //请求成功，显示数据
                            $that.pageNumber ++;//修改当前页数
                            $that.recodList = $that.recodList.concat(data.content);
                            if($that.pageNumber  >= data.totalPages) {
                                $that.finished = true;//数据全部加载完成
                            }
                        }
                    });
                }else {
                    $.ajax({
                        type: "POST",
                        url: "alipayPrePay.jhtml",
                        contentType: "application/x-www-form-urlencoded",
                        data: param,
                        success: function(data){
                            $that.loading = false;//加载结束
                            if(data.content == null || data.content == undefined||data.content.length==0) {
                                $that.finished = true;//数据全部加载完成
                                return ;
                            }
                            //请求成功，显示数据
                            $that.pageNumber ++;//修改当前页数
                            $that.recodList = $that.recodList.concat(data.content);
                            if($that.pageNumber  >= data.totalPages) {
                                $that.finished = true;//数据全部加载完成
                            }
                        }
                    });
                }

            }
        },
    });
</script>
</body>
</html>
