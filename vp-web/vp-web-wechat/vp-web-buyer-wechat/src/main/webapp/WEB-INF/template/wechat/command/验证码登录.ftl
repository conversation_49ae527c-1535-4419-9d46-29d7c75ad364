<!DOCTYPE html>
<html style="background: #fff;">
	<head>
		<meta charset="utf-8" />
		<title>验证手机</title>
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="black">
        <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js" ></script>
        <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
        <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/login.css" />
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/plugins/font-awesome-4.7.0/css/font-awesome.min.css" />
		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.9.1.min.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/jquery.validate.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js" ></script>
	</head>
	<script type="text/javascript">
		$().ready(function() {
			[@flash_message /]
		});
	</script>
	<body style="background: #fff;">

    <div class="public_top_header">
        <span class="return_back" onclick="javascript:window.history.go(-1);"></span>
    </div>

		<div class="loginPage"> 
			<div class="logo_section">
				<img src="[#if comPersonConf && com_person_wechatLoginTopLogo != null&& com_person_wechatLoginTopLogo != ""]${com_person_wechatLoginTopLogo }[#else]${base}/resources/wechat/img/fuli_logo.png[/#if]"/>
			</div>
			
			<div class="tab_section">
				验证手机
			</div>
			
			<div class="login_form_section textLeft"> 
				<form id="inputForm" action="commandNext.jhtml" method="get" enctype="multipart/form-data">
					<input type="hidden" name="enterCode" value="${enterCode}" />
					<input type="hidden" name="companyId" id="companyId" value="${companyId}" />
					<div class="item standard_input">
						<input type="tel" name="phone" id="phone" value="${phone}" placeholder="请输入手机号" class="input" onkeyup="javascript:checkWordPhone(this);" onmousedown="javascript:checkWordPhone(this);"/>
						<span class="icon_clear"></span>
						<div class="text_error2" id="closeTips">
							您已经是该组织会员不需要重复加入。
						</div>
						<div class="text_error2" id="closeTips1">
							您已经是该组织会员不需要重复加入。
						</div>
						<div class="text_error2" id="closeTips2">
							您加入的组织数量已经超过了上限
						</div>
						<div class="text_error2" id="closeTips4">
							该手机号码已达到当天次数上限
						</div>
						[#if errorType == "phone"]
							<div class="text_error2" style="display: block;" id="closeTips3">${errorMsg}</div>
						[/#if]
					</div>
					<div class="item hasbutton standard_input">
						<input type="text" name="captcha" id="captcha" value="" placeholder="请输入图形验证码" class="input" onkeyup="javascript:checkWord(this);" onmousedown="javascript:checkWord(this);"/>
						<span class="icon_clear"></span>
						<img id="captchaImage" class="captchaImage" src="${base}/common/captcha.jhtml?captchaId=${captchaId}" title="">
						<div class="text_error2" id="closeTips6">
							图形验证码错误
						</div>
					</div>
					<div class="item hasbutton standard_input long">
						<input type="text" name="code" id="code" value="" placeholder="请输入短信验证码" class="input" onkeyup="javascript:checkWord(this);" onmousedown="javascript:checkWord(this);"/>
						<span class="icon_clear"></span>
						<input type="button" name="" id="emailCodeBtn" value="获取短信验证码" class="code"/>
						<!--禁用状态 disabled="disabled"-->
					</div>
					[#if errorType == "code"]
						<div class="text_error" style="display: block;">${errorMsg}</div>
					[/#if]
					<input type="submit" value="登录" class="btn_cornor_long"/>
				</form>

				<div class="otherLoginWay">
					<header><span>其他登录方式</span></header>
					<div class="list">
						<a href="#">
							<i class="fa fa-wechat"></i>
							<p>微信登录</p>
						</a>
						<a href="#">
							<i class="fa fa-lock"></i>
							<p>账号密码登录</p>
						</a>
					</div>
				</div>
			</div>
		</div>
		
		<script type="text/javascript">
			var interval;
			
			
			$(function(){

				var $captcha = $("#captcha");
			
			//以下两个方法是为了去掉前后空格
			$("input[name='phone']").blur(function(){
				$(this).val($(this).val().trim());
			});
			jQuery.validator.addMethod("phoneFormat",function(value,element,param){
					value = value.trim();
					var regex = /^1[34578]\d{9}$/g;
				  	if (regex.test(value)){
				  		return true;
				  	}else{
				        return false;
				  	}
		        },$.validator.format("手机号码不存在")  
		    );
				// 表单验证
				$("#inputForm").validate({
					rules: {
						phone: {
							required:true,
							phoneFormat: true,
							maxlength: 11
						},
						code: {
							required:true,
							digits:true
						}
					},
					messages: {
						phone: {
							pattern: "手机号码不存在"
						}
					}
				});
				
				$("#emailCodeBtn").click(function(){
					var phone = $("#phone").val();
					var companyId = $("#companyId").val();
					if(phone == ""){
						layer.open({
						    content: '请输入手机号码'
						    ,skin: 'msg'
						    ,time: 2 //2秒后自动关闭
						  });
						return false; 
					}
				    if(!(/^1[34578]\d{9}$/.test(phone))){ 
				    	layer.open({
						    content: '请输入正确的手机号码'
						    ,skin: 'msg'
						    ,time: 2 //2秒后自动关闭
						  });
				        return false; 
				    }

					if($captcha.val() == null || $captcha.val() == ""){
				    	layer.open({
						    content: "请输入图形验证码"
						    ,skin: 'msg'
						    ,time: 2 //2秒后自动关闭
						  });
				        return false; 
					}
					if($(this).val() == "获取短信验证码"){
						$.ajax({
							url:'${base}/command/sendPhoneCode.jhtml',
							type:'POST',
							data:{phone: phone,companyId: companyId, captchaId: "${captchaId}", captcha: $captcha.val()},
							cache:false,
							async:false,
							success:function(flag){
								$("#closeTips").hide();
								$("#closeTips1").hide();
								$("#closeTips2").hide();
								$("#closeTips4").hide();
								$("#closeTips3").hide();
								$("#closeTips6").hide();
								
								if(flag == 0){
									//验证码发送成功
									emailCodeBtnVal();
									interval = setInterval("emailCodeBtnVal()", 1000);
								}else if(flag == 1){
									layer.open({
									    content: '60秒内只允许发送一次'
									    ,skin: 'msg'
									    ,time: 2 //2秒后自动关闭
									  });
								}else if(flag == 2){
									$("#closeTips").show();
								}else if(flag==3){
									$("#closeTips1").show();
								}else if(flag == 4){
									$("#closeTips2").show();
								}else if(flag == 6){
									$("#closeTips4").show();
								}else if(flag == 7){//图形验证码错误
									$("#closeTips6").show();
								}else{
								   //验证码发送失败
									layer.open({
									    content: '短信验证码发送失败'
									    ,skin: 'msg'
									    ,time: 2 //2秒后自动关闭
									  });
								}

								if(flag != 7){//不是图形验证码错误
									$("#captchaImage").click();//刷新图形验证码
								}
							}
						});
					}
				});

				// 更换验证码
				$("#captchaImage").click(function() {
					$("#captchaImage").attr("src", "${base}/common/captcha.jhtml?captchaId=${captchaId}&timestamp=" + (new Date()).valueOf());
				});
			});
			
			//发送验证码按钮显示秒数递减
			function emailCodeBtnVal(){
				if($("#emailCodeBtn").val() == "获取短信验证码"){
					$("#emailCodeBtn").removeClass("active");
					$("#emailCodeBtn").val("59");
				}else if($("#emailCodeBtn").val() == "1"){
					clearInterval(interval);
					$("#emailCodeBtn").addClass("active");
					$("#emailCodeBtn").val("获取短信验证码");
				}else{
					$("#emailCodeBtn").val(Number($("#emailCodeBtn").val()) - 1);
				}
			}
			
			//手机号码隐藏错误信息
			function checkWordPhone(){
				$("#closeTips").hide();
				$("#closeTips1").hide();
				$("#closeTips2").hide();
				$("#closeTips4").hide();
				$("#closeTips3").hide();
			}
			
			//验证码隐藏错误信息
			function checkWord(){
				$(".text_error").hide();
			}
		</script>
	</body>  
</html>
