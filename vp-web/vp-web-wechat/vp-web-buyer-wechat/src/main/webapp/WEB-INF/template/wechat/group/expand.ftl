<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">
    <title>拼团抢购</title>
    <script src="${base}/resources/wechat/plugins/flexible/flexible.js"></script>
    <link rel="stylesheet" href="${base}/resources/wechat/css/common.css">
    <link rel="stylesheet" href="${base}/resources/wechat/css/cIndexStyle.css">
    <link rel="stylesheet" href="${base}/resources/wechat/css/vant/index.css">
</head>

<body class="tuanSavePage has_fixed_footer">
    <div id="tuanSavePage">
        <van-nav-bar fixed left-text="我要推广" left-arrow @click-left="javascript:history.go(-1);" :z-index="99999">
        </van-nav-bar>

        <a href="javascript:void(0)" class="goodsItem" id="saveAsPictrue">
            <div class="img">
                <van-image width="6.5333rem" height="6.5333rem" :src="item.img"></van-image>
            </div>
            <div class="intro">
                <div class="title" v-cloak>{{item.title}}</div>
                <div class="price_now" v-cloak>
                    <span>￥<span>{{item.price}}</span></span>
                </div>
                <van-tag type="danger">团购价</van-tag>
                <div class="price_shop" v-cloak>
                    市场价 {{item.oldPrice}}
                </div>
                <div class="tips" v-cloak>长按二维码立即购买</div>
                <div class="qrimg" id="qrcode">
                    <!-- <van-image width="2.5333rem" height="2.5333rem" :src="item.img"></van-image> -->
                </div>
            </div>
        </a>

        <div class="btns">
            <button @click="savePictrue">保存海报</button>
            <button>立即分享</button>
        </div>
    </div>

	<form action="${base}/group/savePoster.jhtml" id="savePosterForm" method="post">
		<input name="base64" id="base64" type="hidden">
	</form>

    <script src="${base}/resources/wechat/plugins/vue/vue.min.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

    <script src="${base}/resources/wechat/js/common.js"></script>
    <script src="${base}/resources/wechat/js/vant.min.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/plugins/qrcode/utf.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/plugins/qrcode/jquery.qrcode.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/plugins/html2canvas/html2canvas.min.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/plugins/html2canvas/canvas2image.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/plugins/html2canvas/base64.js"></script>
    
    <script>
        var dataItemId = ${groupProduct.id};
        var dataItemImg = '${setting.siteUrlImg}${groupProduct.productId.image}';
        var dataItemTitle = "${groupProduct.productId.fullName}";
        var dataItemPrice = "${groupProduct.groupPrice}";
        var dataItemOldPrice = "${groupProduct.productId.marketPrice}";
        $(function () {
            $('#qrcode').qrcode({
                render: "canvas",
                width: "192",
                height: "192",
                text: "${url}",
                src: "${base}/resources/wechat/img/fuli_logo.png"
            });
        })
    </script>
    <script type="text/javascript" src="${base}/resources/wechat/es6/group_expand.js"></script>
    <!-- 集成微信JSAPI的配置信息 -->
		[#if configStr??]
		<script src="http://res.wx.qq.com/open/js/jweixin-1.0.0.js"></script>
		<script>
		    wx.config(${configStr});
			wx.error(function(res){
			});
			wx.ready(function(){
				try{
					//分享到朋友圈
					wx.onMenuShareTimeline({
						title: '"福利plus"分享领红包', // 分享标题
    					desc: '我从福利plus领取了红包给你', // 分享描述
    					link: '${url}',
    					imgUrl: '${siteUrl}/resources/wechat/img/fuli_logo.png', // 分享图标
    					success: function () {

    						$.ajax({
    	            			url: "${currentUrl}/share/shareOrder.jhtml",
    	            			type: "POST",
    	            			data: {orderId: "${orderId}",shareType:'Timeline'},
    	            			dataType: "json",
    	            			cache: false,
    	            			success: function(msg){

    	            				if(msg.type=='success'){
    	            					shareOrder(true);
    	            					$('#gua_amount').text(msg.content);
    	            					alert("分享成功");
    						   		}else{
    						   			shareOrder(false);
    						   			alert(msg.content);
    						   		}
    	            			}
    	            		});
    					}
					});
					//分享给朋友
					wx.onMenuShareAppMessage({
						title: '"福利plus"分享领红包', // 分享标题
    					desc: '我从福利plus领取了红包给你', // 分享描述
    					link: '${currentUrl}/share/index.jhtml?orderId=${orderId}',
    					imgUrl: '${currentUrl}/resources/wechat/img/fuli_logo.png', // 分享图标
    					success: function () {
    						$.ajax({
    	            			url: "${currentUrl}/share/shareOrder.jhtml",
    	            			type: "POST",
    	            			data: {orderId: ${orderId},shareType:'AppMessage'},
    	            			dataType: "json",
    	            			cache: false,
    	            			success: function(msg){
    	            				if(msg.type=='success'){
    	            					shareOrder(true);
    	            					$('#gua_amount').text(msg.content);
    	            					alert("分享成功");
    						   		}else{
    						   			shareOrder(false);
    						   			alert(msg.content);
    						   		}
    	            			}
    	            		});
    					}
					});
				}catch(e){
			  };
			});
		</script>
	    [/#if]
</body>

</html>