<!DOCTYPE html>
<html lang="en" data-dpr="1" style="font-size: 16px;">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>景点详情</title>
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css">
    <link href="https://cdn.bootcss.com/font-awesome/4.7.0/css/font-awesome.css" rel="stylesheet">
    <link rel="stylesheet" href="${base}/resources/wechat/css/qunaer-wechat.css">
    <script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

    <script src="${base}/resources/wechat/js/flexible.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
</head>

<body class="no_fixed_top">
    <div class="qunaerpage">
        <div class="sightsdetailPage">
            <header>
                <i class="return_back" onclick="javascript:history.back(-1);"></i>
                <span class="name">
                  	  景点介绍
                </span>
            </header>
            <div class="content">
            	<div class="title">${sight.name}</div>
                <p class="text">${sight.introduction}</p>
            </div>
        </div>
    </div>

</body>

</html>