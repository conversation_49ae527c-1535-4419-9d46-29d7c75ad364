<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<title>打车出行</title>
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">
		<script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/didiTravel.css" />
		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

		
		<script type="text/javascript" src="${base}/resources/wechat/js/dropload.min.js" ></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
	</head>
	<body>
	<div class="myCoinsPage">
		<div class="public_top_header">
			<a href="javascript:history.go(-1);" class="return_back"></a> 
			我的行程
			 [#include "./wechat/include/head_nav.ftl" /]
		</div>
		<div class="consume_list">
		 		
		</div>

        <!-- <div class="order_list_end_tips">2019年及之前的订单可在我的订单中查询</div>-->
		<!-- <div class="didi_wait_page">
			<div class="info_section">
				<div class="order_header">
					快车<span class="right">行程中</span>
				</div>
				<div class="status">
					<span class="icon_time"></span>今天 15:29
				</div>
				<div class="location from">南山区.科技园.科技南路高新公寓-西门对面</div>
				<div class="location to">深圳湾人才公园</div>
			</div>
			
		</div> -->
		[#if applyId?? ][#else]
           <!-- <div class="order_list_end_tips">2019年及之前的订单可在我的订单中查询</div> -->
           [#include "./wechat/include/old_order_tips.ftl" /]
	 	[/#if]
	</div>
		
		 
		
		<script>
		$(function(){
			//加载数据
			
			loadData('${base}/taxi/getMemberOrderPage.jhtml', 
					{
						[#if applyId?? ]
							"applyId":${applyId}
						[/#if]
						[#if source?? ]
							,"source":'${source}'
						[/#if]
					}, 
					'myCoinsPage', 'consume_list', 'POST', 'totalPages', null, null);
		//    loadData('${base}/member/whiteBarConsumePage.jhtml', null, 'myCoinsPage', 'consume_list', 'POST', 'totalPages', null, null);
		})
		</script>
	</body>
</html>
