<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="viewport"
        content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">
    <title>意见反馈</title>
    <script src="${base}/resources/wechat/plugins/flexible/flexible.js"></script>
    <link rel="stylesheet" href="${base}/resources/wechat/css/common.css">
    <link rel="stylesheet" href="${base}/resources/wechat/css/kefu.css">
    <link rel="stylesheet" href="${base}/resources/wechat/css/vant/index.css">
</head>

<body class="has_fixed_footer">
    <div class="kefu_tab_page">
        <van-nav-bar title="意见反馈" fixed :border="false" left-arrow @click-left="javascript:location.href='index.jhtml[#if pageSource??&&pageSource=="weixincoupon"]?pageSource=weixincoupon[/#if]';">
        </van-nav-bar>
        <van-tabs type="card" v-model="active" sticky @change="handleTabChange">
            <van-tab title="意见反馈">
                <div class="vant-form-section">
                    <van-cell-group>
                        <header>问题或意见</header>
                        <van-field v-model="form.adviceContent" rows="5" autosize type="textarea" placeholder="请输入不少于10个字的描述（必填）">
                        </van-field>

                        <p>提供相关的截图和照片</p>
                        <van-uploader v-model="fileList" multiple :max-count="5" :max-size="maxSize" :after-read="afterRead" @oversize="overSize"></van-uploader>

                        <van-field type="tel" v-model="form.mobile" placeholder="手机号码（必填）"></van-field>
                        <van-field v-model="form.email" placeholder="邮箱（必填）"></van-field>

                        <div class="btnBox">
                            <van-button round type="warning" @click="handleConcel">取 消</van-button>
                            <van-button round type="info" @click="handleSubmit" :loading="loading" loading-text="提交中...">提 交</van-button>
                        </div>
                    </van-cell-group>
                </div>
            </van-tab>
            <van-tab title="历史记录">
				<div v-if="historyList.length==0" class="emptySection">
					<van-icon name="info" />
					<p>您还没有提交过反馈意见</p>
				</div>
                <van-list
				  v-model="recordLoading"
				  :finished="finished"
				  finished-text="没有更多了"
				  :error.sync="error"
  				  error-text="请求失败，点击重新加载"
				  @load="onLoad"
				>
				  <van-panel v-for="(item,index) in historyList" class="historyList" @click="detail(item.id)"
                    :class="{processed:item.status == '处理成功',undo:item.status == '未处理'}" :title="'添加时间:'+item.time" :status="item.status">
                    <div v-cloak>提交内容：{{item.content}}</div>
                  </van-panel>
				</van-list>
            </van-tab>
        </van-tabs>

    </div>


    <script src="${base}/resources/wechat/plugins/vue/vue.min.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

    <script src="${base}/resources/wechat/js/common.js"></script>
    <script src="${base}/resources/wechat/js/vant.min.js"></script>
    <script>
        var app = new Vue({
            el: '.kefu_tab_page',
            data: {
            	[#if active??]
            	active: ${active},
            	[#else]
            	active: 0,
            	[/#if]
                form:{
                    adviceContent: "",
                    [#if member??]
	            	mobile: "${member.mobileDec}",
                    email: "${member.emailDec}",
	            	[#else]
	            	mobile: "",
                    email: "",
	            	[/#if]
                    adviceImages:""
                },
                maxSize:${setting.uploadMaxSize*1024*1024!10485760},
                fileList: [],
                totalPage:1,
                currentPage:1,
                error:false,
                loading:false,
                recordLoading:false,
                finished: false,
                historyList: []
            },
            methods: {
            	detail:function(id){
            	    var jumpUrl = 'adviceDetail.jhtml?id='+id;
            	   [#if pageSource??&&pageSource=='weixincoupon']
				   jumpUrl+='&pageSource=weixincoupon';
				   [/#if]
            		location.href=jumpUrl;
            	},
            	overSize:function(){
            		vant.Notify({ type: 'danger', message: "图片最大10M" });
            	},
            	afterRead:function(file){
            	},
            	onLoad: function() {
            		var that = this;
			      // 异步更新数据
			      $.ajax({
					url: "${base}/member/customerService/getAdvicePage.jhtml",
					type: "POST",
					data: {pageNumber:this.currentPage,pageSize:10},
					success: function (data) {
						that.totalPage = data.totalPages;
						// 数据全部加载完成
						if(that.currentPage >= that.totalPage){
							that.finished = true;
						}else{
							that.currentPage = that.currentPage + 1;
						}
						//显示数据
						data.content.map(function(item, index){
							that.historyList.push({
							  id: item.id,
				              time: item.createDate,
		                      status: item.adviceStatus,
		                      content: item.adviceContent
				            })
						});
				        // 加载状态结束
				        that.recordLoading = false;
					},
					error: function (xhr, type) {
						//加载数据失败提示
						that.error = true;
					}
				  });
			    },
                handleConcel: function(){
                    this.form = {};
                    location.href='index.jhtml[#if pageSource??&&pageSource=="weixincoupon"]?pageSource=weixincoupon[/#if]';
                },
                handleSubmit:function(){
                    if(this.form.adviceContent.length < 10){
                        vant.Toast("请输入不少于10个字的描述");
                        return;
                    }

                    if(this.form.mobile.length == 0){
                        vant.Toast("请输入手机号码");
                        return;
                    }else if(this.form.mobile.length < 11){
                        vant.Toast("请输入正确的手机号码");
                        return;
                    }

                    if(this.form.email.length == 0){
                        vant.Toast("请输入邮箱");
                        return;
                    }else if(this.form.email.indexOf("@")==-1){
                        vant.Toast("请输入正确的邮箱");
                        return;
                    }
                    this.loading=true
                    var that = this;
                    if(this.fileList.length > 0){
	                    var fd = new FormData();
	                    this.fileList.map(function(file){fd.append('file', file.file);});
	            		$.ajax({
							url: "${base}/member/customerService/uploadBatch.jhtml",
							type: "POST",
							data: fd,
							processData: false,
							contentType:false,
							success: function (data) {
								if(data.type == 'success'){
									that.form.adviceImages = data.content;
									$.ajax({
										url: "${base}/member/customerService/saveSuggest.jhtml",
										type: "POST",
										data: that.form,
										success: function (msg) {
											if (msg.type=="success") {
						    				   vant.Notify({ type: 'success', message: '提交成功' });
						    				  var timer = setTimeout(function () {
                                                  var url1 = "advice.jhtml?active=1";
												  [#if pageSource??&&pageSource=='weixincoupon']
													url1+='&pageSource=weixincoupon';
												  [/#if]
                                                  location.href=url1;
                                                  clearTimeout(timer);
                                              }, 2000);
						    				} else {
						    					vant.Notify({ type: 'danger', message: msg.content });
						    				}
						    				that.loading = false;
										},
										error: function (xhr, type) {
											//加载数据失败提示
											vant.Notify({ type: 'danger', message: '提交失败' });
											that.loading = false;
										}
									  });
								}else{
									vant.Notify({ type: 'danger', message: data.content });
									that.loading = false;
								}
							},
							error: function (xhr, type) {
								//加载数据失败提示
								vant.Notify({ type: 'danger', message: '上传失败' });
								that.loading = false;
							}
						  });
					  }else{
					  	$.ajax({
							url: "${base}/member/customerService/saveSuggest.jhtml",
							type: "POST",
							data: that.form,
							success: function (msg) {
								if (msg.type=="success") {
			    				   vant.Notify({ type: 'success', message: '提交成功' });
			    				  var timer2 = setTimeout(function () {
                                       var url1 = "advice.jhtml?active=1";
										[#if pageSource??&&pageSource=='weixincoupon']
										  url1+='&pageSource=weixincoupon';
										[/#if]
                                        location.href=url1;
                                        clearTimeout(timer2);
                                   }, 2000);
			    				} else {
			    					vant.Notify({ type: 'danger', message: msg.content });
			    				}
			    				that.loading = false;
							},
							error: function (xhr, type) {
								//加载数据失败提示
								vant.Notify({ type: 'danger', message: '提交失败' });
								that.loading = false;
							}
						  });
					  }
                },
				handleTabChange: function(){
				}
            }
        })
    </script>
</body>

</html>