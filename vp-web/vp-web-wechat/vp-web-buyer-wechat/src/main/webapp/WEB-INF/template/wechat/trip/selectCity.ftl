<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<title>出差行程-选择城市</title>
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">
		<script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/didiTravel.css" />

		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
	</head>

	<body>
		<div class="public_top_header">
			<a href="javascript:;" class="return_back"></a>
			出差行程-选择城市
			[#include "./wechat/include/head_nav.ftl" /]
		</div>

		<div class="didi_select_addr_page">
			<div class="fixed_header">
				<div class="input_section select_city">
					<input type="text" name="" id="search" value="" placeholder="城市中文名或拼音" class="input_text fixedPositionInput" style="width: 80%;"/>
					<span class="text_concel">取消</span>
				</div>
			</div>

			<div class="city_list_simple">
				<div class="item_city">
					<header>热门城市</header>
					<ul id="hot"></ul>
				</div>
			</div>

		</div>
		<form id="tripInfo" method="post" action="${url!"add.jhtml" }" >
			[#if tripApply??]
				[#list tripApply.tripRecords as tripRecord]
					[#if tripRecord??]
					<input name="tripRecords[${tripRecord_index }].fromCity" value="${tripRecord.fromCity! }" type="hidden" />
					<input name="tripRecords[${tripRecord_index }].toCity" value="${tripRecord.toCity! }" type="hidden" />
					<input name="tripRecords[${tripRecord_index }].startDate" value="${tripRecord.startDate! }" type="hidden" />
					<input name="tripRecords[${tripRecord_index }].startPeriod" value="${tripRecord.startPeriod! }" type="hidden" />
					[/#if]
				[/#list]
				<input name="applyType" value="${tripApply.applyType! }" type="hidden" />
				<input name="memo" value="${tripApply.memo! }" type="hidden" />
				<input name="id" value="${tripApply.id! }" type="hidden" />
				<input name="reviewer" value="${tripApply.reviewer! }" type="hidden" />
			[/#if]
			<input name="myReviews" value="${myReviews! }" type="hidden" />
			<input name="type" value="${type! }" type="hidden" />
			<input name="memberIds" value="[#if memberIds??][#list memberIds as member]${member}[#if member_has_next],[/#if][/#list][/#if]" type="hidden" />
			<input name="endTime" value="${endTime! }" type="hidden" />
			<input name="endPeriod" value="${endPeriod! }" type="hidden" />
			<input name="memberDesc" value="${memberDesc! }" type="hidden" />
			<input name="projectId.id" value="[#if tripApply.projectId??]${tripApply.projectId.id! }[/#if]" type="hidden" />
			<input id="projectName" name="projectId.name" value="[#if tripApply.projectId??]${tripApply.projectId.name! }[/#if]" type="hidden" />
			<input name="estimatedCost" value="${currency(tripApply.estimatedCost) }" type="hidden" />
			<input name="transport" value="${tripApply.transport! }" type="hidden" />
		</form>
		<script>
			//城市数组
			var cityArray = [];
			//搜索数据
			function searchData(keywords){
				$(".search_suggest_list ul").empty();
				if(keywords != ""){
					for(var i = 0; i < cityArray.length; i++){
						if(cityArray[i].cityName.indexOf(keywords) != -1 || cityArray[i].parentCityCode.indexOf(keywords) != -1 || cityArray[i].cityCode.indexOf(keywords) != -1){
							$(".search_suggest_list ul").append('<li data-code="'+cityArray[i].cityCode+'" data-name="'+cityArray[i].cityName+'">'+cityArray[i].cityName+'</li>');
						}
						if($(".search_suggest_list ul li").length == 8){
							break;
						}
			        }
					//没有数据则显示无数据
					if($(".search_suggest_list ul li").length == 0){
						$(".search_suggest_list ul").append('<li>无数据</li>');
					}
				}
			}
			
			$(function() {
				//获取热门城市
				$.getJSON("${base}/resources/wechat/js/hotelCity.json",function(result){
			        cityArray = result.station;
			        //console.log(cityArray)
			        for(var i = 0; i < cityArray.length; i++){
			        	if(cityArray[i].hot){
			        		$("#hot").append('<li data-code="'+cityArray[i].cityCode+'" data-name="'+cityArray[i].cityName+'">'+cityArray[i].cityName+'</li>');
			        	}
			        }       
			    });
			    
			    //点击选择城市
			    $("body").on("click",".item_city li,.search_suggest_list li",function(){
			    	if($(this).attr("data-code")){
			    		$("#search").val($(this).html());
			    		$(".fixed_header .search_suggest_list").remove();
			    		var city=$(this).attr("data-name");
			    		$("input[name='${name}']").val(city);
			    		$("#tripInfo").submit();
			    	}
			    })
			    
			    //鼠标聚焦输入框添加遮罩层
			    $("#search").on("focus",function(){
					if($(".fixed_header .search_suggest_list").length == 0){
						$(".fixed_header").append('<div class="search_suggest_list"><ul></ul></div>');
					}
				})
			    
			    $(".return_back").click(function(){
			    	$("#tripInfo").submit();
			    });
			    
			    //取消按钮
			    $(".fixed_header .text_concel").on("click",function(){
			    	$("#search").val("");
			    	$(".fixed_header .search_suggest_list").remove();
			    })
				
				//输入事件
				var searchWords = document.querySelector('#search');
				var cpLock = false;
				searchWords.addEventListener('compositionstart', function(){
				    cpLock = true;
				})
				searchWords.addEventListener('compositionend', function(){
				    cpLock = false;
				    if(!cpLock){
						searchData($("#search").val())
				    }
				})
				searchWords.addEventListener('input', function(){
				    if(!cpLock){
						searchData($("#search").val())
				    }
				});
			})
		</script>
	</body>

</html>