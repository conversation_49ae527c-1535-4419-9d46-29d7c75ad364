<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>出错喽！</title>
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="black">
        <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js" ></script>
        <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />

		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
        <script type="text/javascript" src="${base}/resources/wechat/js/common.js" ></script>
	    <style>
	     .errorInfo{
			  color:#234c9b;
			  text-align:center;
			  font-size:0.500rem;
			  line-height:0.5rem;
			  margin-top:0.4rem
		  }
	    
	    </style>
	</head>
	<body> 
		<div class="img_error"></div> 
	    [#if message]
		<p class="errorInfo"><span message="${message.code }">${message.businessTips }</span></p>
		<p class="errorInfo">请<span id="time" time="${message.unlockDate! }">稍</span>后再回来尝试一下.</p>
		[/#if]
	</body> 
	<script>
	$(function () {
		var time = $("#time").attr("time");
		if(time != null && time != ""){
			var $Timeindex = "xrange";
	        var now = new Date('${.now?string("yyyy/MM/dd HH:mm:ss")}').getTime();
	        var expire = new Date(time).getTime();
	        var time = parseInt((expire-now)/1000);
	        //var time = parseInt($(v).attr("data-second") / 1000);
	        $Timeindex = window.setInterval(function() {
	            if (time > 0) {
	                var hours = Math.floor(time / 3600),
	                    minute = Math.floor(time % 3600 / 60),
	                    second = Math.floor(time % 3600 % 60);
	                if (minute <= 9) minute = '0' + minute;
	                if (hours <= 9 && hours > 0) hours = '0' + hours;
	                $("#time").html(hours + ":" + minute + ":" + second);
	                time--;
	            } else {
	                clearInterval($Timeindex);
	            }
	        }, 1000)
		}
	})
</script>
</html>
