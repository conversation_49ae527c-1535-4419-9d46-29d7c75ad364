<!DOCTYPE html>
<html style="background: #fff;">
	<head>
		<meta charset="utf-8" />
		<title>选择白条</title>
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="black">
        <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js" ></script>
        <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
        <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/plusWap.css" /> 
		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/jquery.validate.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
		
        <script>
            $(function(){

                //点击确定
                $(".submit_section .btn_submit_long").click(function(){
                    $("#orderRefreshForm").submit();
                })

                //新增penglong20161223 积分显示合并
                $("#selectAll").click( function() {
                    var $this = $(this);
                    var $enabledIds = $("#rechargeRecords input[name='whiteBarIds']:enabled");
                    if ($this.prop("checked")) {
                        $enabledIds.prop("checked", true);
                    } else {
                        $enabledIds.prop("checked", false);
                    }
                });
                //新增结束

            })


            $("#selectAll").click( function() {
                var $this = $(this);
                var $enabledIds = $("#rechargeRecords input[name='whiteBarIds']:enabled");
                if ($this.prop("checked")) {
                    $enabledIds.prop("checked", true);
                } else {
                    $enabledIds.prop("checked", false);
                }
            });


            function ticketTab(flag,obj){
                $(".coin").removeClass("active");
                obj.addClass("active");
                if(flag){
                    $("#unRechargeRecords").css('display', 'none');
                    $("#rechargeRecords").css("display","block");
                    $(".public_page_top_tips").css('display', 'none');
                }
                else{
                    $("#rechargeRecords").css('display', 'none');
                    $("#unRechargeRecords").css("display","block");
                    $(".public_page_top_tips").css('display', 'block');
                }
            }

        </script>
		
	</head>
	<body style="background: #fff;" class="coupon_list_new_page order_select_coin_common">
		<div class="paymentPage">
			<div class="public_top_header">
                <span class="return_back" onclick="javascript:history.back();"></span>
				白条支付
			</div>
			<div style="display:none" class="public_page_top_tips" [#if member.companyId.minimumWhiteBar==null || member.companyId.minimumWhiteBar<=0]style="display:none"[/#if]>
				提示：白条支付额单次需大于等于${member.companyId.minimumWhiteBar}
			</div>
			
			<div class="make_split">
				<div class="tab_theme_section">
			 		<div class="item coin active" onclick="ticketTab(true,$(this))"><a href="javascript:;">可用</a></div>
			 		<div class="item coin" onclick="ticketTab(false,$(this))"><a href="javascript:;">不可用</a></div>
			 	</div>
			</div>
		 	
		 	<form id="orderRefreshForm" action="[#if train??&&train.trainNumber??]info_virtual.jhtml[#else]groupInfo.jhtml[/#if]" method="post">
		 	     <input type="hidden" name="ids" value="${ids}" />
		 	     <input type="hidden" name="coinIds"  value="${coinIds}" />
	             <input type="hidden" name="receiverId"  value="${receiverId}" />  
	             <input type="hidden" name="couponCodeId" id="couponCodeId" value="${couponCodeId}" />  
	             <input type="hidden" name="memo" value="${memo!}" />
	             <input type="hidden" name="groupId" value="${groupId}" />
	             <input type="hidden" name="pId" value="${productId}" />
	             <input type="hidden" id="quantity"  name="quantity" value="${quantity}"/>
	             <input type="hidden" id="orderJson"  name="orderJson" value="${orderJson}"/>
	             <input type="hidden" name="mobile" value="${mobile}" />
	             <input type="hidden" name="type" value="${type}" />
	             <input type="hidden" id="gasType" name="gasType" value="${gasType}" />
				<input type="hidden" id="gasCard" name="gasCard" value="${gasCard}" />
				<input type="hidden" id="userName" name="userName" value="${userName}" />
				<input type="hidden" id="phone" name="phone" value="${phone}" />
				<input type="hidden" id="bookingResult" name="bookingResult" value="${bookingResult}" />
				
				<input type="hidden" name="from" value="${train.from}"/>
				<input type="hidden" name="to" value="${train.to}"/>
				<input type="hidden" name="date" value="${train.date}"/>
				<input type="hidden" name="trainNumber" value="${train.trainNumber}"/>
				<input type="hidden" name="passagers" id="passagers" value="${train.passagers}"/>
				<input type="hidden" name="itemIdInsur" id="itemIdInsur"  value="${train.itemIdInsur}"/>
				<input type="hidden" name="startTime" value="${train.startTime}" />
				<input type="hidden" name="runTime" value="${train.runTime}" />
				<input type="hidden" name="endWeek" value="${train.endWeek}" />
				<input type="hidden" name="endDate" value="${train.endDate}" />
				<input type="hidden" name="week" value="${train.week}" />
				<input type="hidden" name="endTime" value="${train.endTime}" />
				<input type="hidden" name="contactName" value="${train.contactName}" />
				<input type="hidden" name="contactTel" value="${train.contactTel}" />
				<input type="hidden" name="trainInsurePrice" value="${train.trainInsurePrice}" />
				<input type="hidden" name="userSave" value="${train.userSave}" />
				<input type="hidden" name="serviceCharge" value="${train.serviceCharge}" />
				<input type="hidden" name="passagers1"  id="passagers1" value="${train.passagers1}"/>
                <input type="hidden" id="coinBalance1"  name="coinBalance" value="${coinBalance}" />
                <input type="hidden" name="hasSelectCoin" value="${hasSelectCoin}" />
		 	<div class="coin_pay_section" id="rechargeRecords"> 
		 	[#if (whiteBarRecords?size > 0)]
		 	    <div class="item">
		 			<a href="javascript:;">
		 				  白条余额
		 				<div class="right">${whiteBarYAmount}
		 					<div class="radio_section">
					 			<input type="checkbox"   id="selectAll" [#if whiteBarIds1?size > 0]checked="checked"[/#if] >
					 			<label for="selectAll"></label>
					 		</div>
		 				</div>
		 			</a>
		 		</div>
		 		
		 		<p class="white_bar_intro">
		 			提示：白条为企业授予个人的信用额度，白条消费金额将由企业月度核算，并从个人待遇中直接扣减。
		 		</p>
		 		
		 		[#list whiteBarRecordsY as rechargeRecord]
		 		<div class="item" style="display: none;">
		 			<a href="javascript:;">
		 				${rechargeRecord.coinTypeId.name}<span>(有效期：[#if rechargeRecord.isLimitExpired]${rechargeRecord.beginTime} — ${rechargeRecord.endTime}[#else]长期有效 [/#if])</span>
		 				<div class="right">${rechargeRecord.balance}
		 					<div class="radio_section">
					 			<input type="checkbox"  name="whiteBarIds"  id="coin${rechargeRecord_index}" value="${rechargeRecord.id}"  [#if whiteBarIds1?seq_contains(rechargeRecord.id)]checked="checked"[/#if]>
					 			<label for="coin${rechargeRecord_index}"></label>
					 		</div>
		 				</div>
		 			</a>
		 		</div>
		 		[/#list]
		 		[#else]
					<div class="public_empty_section [#if comPersonConf && com_person_EmptyBg != null && com_person_EmptyBg != ""] ${com_person_EmptyBg!}[#else] Public_Bg[/#if]"> <p class="tip">这里空空如也</p></div>
				[/#if]	
		 	</div>
		   </form>
		 	
		 	<div class="coin_pay_section" id="unRechargeRecords" style="display: none;"> 
		 		[#if (whiteBarRecordsN?size > 0)]
		 		 <div class="item">
		 			<a href="javascript:;">
		 				  白条余额
		 				<div class="right">${whiteBarNAmount}
		 					<div class="radio_section">
					 			<input type="checkbox"  name="coinIds" id="coinnotall"  [#if whiteBarIds1?size > 0]checked="checked"[/#if] >
					 			<label for="coinnotall"></label>
					 		</div>
		 				</div>
		 			</a>
		 		</div>
		 		[#list whiteBarRecordsN as rechargeRecord]
		 		<div class="item" style="display: none;">
		 			<a href="javascript:;">
		 				${rechargeRecord.coinTypeId.name}<span>(有效期：[#if rechargeRecord.isLimitExpired]${rechargeRecord.beginTime} — ${rechargeRecord.endTime}[#else]长期有效 [/#if])</span>
		 				<div class="right">${rechargeRecord.balance}
		 					<div class="radio_section">
					 			<input type="checkbox" id="coin${rechargeRecord_index}" value="${rechargeRecord.id}" name="coin">
					 			<label for="coin${rechargeRecord_index}"></label>
					 		</div>
		 				</div>
		 			</a>
		 		</div>
		 		[/#list]
		 		[#else]
					<div class="public_empty_section [#if comPersonConf && com_person_EmptyBg != null && com_person_EmptyBg != ""] ${com_person_EmptyBg!}[#else] Public_Bg[/#if]"> <p class="tip">这里空空如也</p></div>
				[/#if]	
		 	</div>
		</div> 
		[#--<div class="submit_section">--]
	 		[#--<input type="button" name="" id="" value="确定" class="btn_submit_long"/>--]
	 	[#--</div>--]
        <div class="btn-option submit_section flexbox align-items-c justify-content-c">
            <input type="button" name="" id="" value="确定" class="btn_submit_long btn-option-right"/>
        </div>
	</body>  
</html>
