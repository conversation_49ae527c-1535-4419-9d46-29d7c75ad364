<!DOCTYPE html>
<html lang="en">

<head>
<meta charset="UTF-8">
<title>违反差旅标准</title>
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="black">
<script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/fli_2019.css" />
<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

<script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/jquery.validate.js"></script>
<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>


    <script type="text/javascript" src="${base}/resources/wechat/js/md5.js" ></script>


</head>

<body>
	<div class="public_top_header">
		<a href="javascript:history.go(-1);" class="return_back"></a> 违反差旅标准
	</div>
	<div class="chailvReasonPage">
		<section class="lable_reason">
			<span class="icon_tips"></span>
			[#if tripSnapshot.tripRuleBroken??]
				[#list tripSnapshot.tripRuleBroken?split("、") as re]
					[#if re != null && re != ""]
					<p>${re_index + 1 }、${re }</p>
					[/#if]
				[/#list]
			[/#if]
		</section>
		<section class="list_reason">
			<header>选择原因:</header>
			[#if tripReasons??]
				[#list tripReasons as taxiReason]
					[#if taxiReason != null && taxiReason.resultCode != "TAX00"]
					<div class="item">
						<div class="fli_checkbox_blue">
							<input type="radio" name="radioBtn" id="${taxiReason.resultCode}" desc="${taxiReason.resultMessage}" value="${taxiReason.resultCode}" /> <label for="${taxiReason.resultCode}"></label>
						</div>
						${taxiReason.resultMessage}
					</div>
					[/#if]
				[/#list]
			[/#if]
			<div class="item">
				<div class="fli_checkbox_blue">
					<input type="radio" name="radioBtn" id="TAX00" value="TAX00" />
					<label for="TAX00"></label>
				</div>
				其它
			</div>
		</section>
		<section class="textarea">
			<textarea placeholder="说明" id="reasonMemo" maxlength="60"></textarea>
			<div class="btns">
				<input type="button" class="btn_half btn_yellow" onclick="javascript:history.go(-1);" value="取消"/>
				<input type="button" class="btn_half btn_blue" onclick="submit()" id="submit" value="继续预订"/>
			</div>
		</section>

	</div>
[#include "./wechat/include/set_payPasswords.ftl" /]
	<form id = "orderForm" action="${base}/taxi/createOrder.jhtml">
		<input type="hidden" name="applyId" value="${tripSnapshot.applyId}"/>
		<input type="hidden" name="tripType" value="${tripSnapshot.tripType}"/>
		<input type="hidden" name="serviceInfo" value="${tripSnapshot.serviceInfo}"/>
		<input type="hidden" name="tripReasonCode" value="${tripSnapshot.tripReasonCode}"/>
		<input type="hidden" name="tripRuleBroken" value="${tripSnapshot.tripRuleBroken}"/>
		<input type="hidden" name="tripReason" value="${tripSnapshot.tripReason}"/>
		<input type="hidden" name="tripMemo" value="${tripSnapshot.tripMemo}"/>
		<input type="hidden" name="isBreak" value="${tripSnapshot.isBreak}"/>
		<input id="cityName" name="cityName" type="hidden" value="${addrView.cityName}">
		<input id="endCityName" name="endCityName" type="hidden" value="${endCityName}">
		<input id="nowAddress" name="nowAddress" type="hidden" value="${addrView.nowAddress}">
		<input type="hidden" id="type" name="type" value="${addrView.type}">
		<input type="hidden" id="cityCode" name="cityCode" value="${addrView.cityCode}">
		<input type="hidden" id="openDidi" name="openDidi" value="${addrView.openDidi}">
		<input type="hidden" id="openKuaiche" name="openKuaiche" value="${addrView.openKuaiche}">
		<input type="hidden" id="openZhuanche" name="openZhuanche" value="${addrView.openZhuanche}">
		<input type="hidden" id="flng" name="flng" value="${addrView.flng}">
		<input type="hidden" id="flat" name="flat" value="${addrView.flat}">
		<input type="hidden" id="faddrName" name="faddrName" value="${addrView.faddrName}">
		<input type="hidden" id="faddress" name="faddress" value="${addrView.faddress}">
		<input type="hidden" id="tlng" name="tlng" value="${addrView.tlng}">
		<input type="hidden" id="tlat" name="tlat" value="${addrView.tlat}">
		<input type="hidden" id="taddrName" name="taddrName" value="${addrView.taddrName}">
		<input type="hidden" id="taddress" name="taddress" value="${addrView.taddress}">
		<input type="hidden" id="rule" name="rule" value="${addrView.rule}">
		<input type="hidden" id="carType" name="carType" value="${addrView.carType}">
		<input type="hidden" id="estimatePrice" name="estimatePrice" value="${addrView.estimatePrice}">
		<input type="hidden" id="member" name="member" value="${addrView.member}">
		<input type="hidden" id="phone" name="phone" value="${addrView.phone}">
		<input type="hidden" id="source" name="source" value="${addrView.source}"/>
		<input type="hidden" id="sourceFull" name="sourceFull" value="${addrView.sourceFull}"/>
		<input id="cartglevel" name="cartglevel" type="hidden" value="${addrView.cartglevel}">
		<input id="price" name="price" type="hidden" value="${addrView.price}">
		<input id="estimatedId" name="estimatedId" type="hidden" value="${addrView.estimatedId}">
		<input id="channel" name="channel" type="hidden" value="${addrView.channel}">
		<input id="channelname" name="channelname" type="hidden" value="${addrView.channelname}">
		<input id="CTransportList"  name="CTransportListStr" type="hidden" value="${addrView.CTransportListStr}"/>

     </form>
	
	<script>
		function submit(){
			var $submit = $("#submit");
			
			var reasonCodeRadio = $(".chailvReasonPage").find("input[name='radioBtn']:checked");
        	var resonText = reasonCodeRadio.attr("desc");
        	var reasonCode = reasonCodeRadio.val();
        	var remark = $(".chailvReasonPage").find('#reasonMemo').val();
        	if(reasonCode == null){
        		//layer.msg("请选择原因!", {time: 2000});
        		layer.open({
                    content: '请选择原因!！'
                    ,skin: 'msg'
                    ,time: 2 //2秒后自动关闭
                });
        		return ;
            }
            if(remark.length > 60){
				remark = remark.substring(0, 60);
            }

			$("#orderForm").find("input[name='tripMemo']").val(remark);
            $("#orderForm").find("input[name='tripReasonCode']").val(reasonCode);
			$("#orderForm").find("input[name='tripReason']").val(resonText);

            repeatFlag = false;
           	[#if member.companyId.isFreePay ]
           		submitOrder();
			[#else]
				$("#paymentPop").show().find("#password").focus();
			[/#if]
		}

		function submitOrder() {
			  $("#submit").attr("disabled", "disabled");
	          $.ajax({
	                type: "POST",//方法类型
	                dataType: "json",//预期服务器返回的数据类型
	                url: "${base}/taxi/createOrder.jhtml" ,//url
	                data: $('#orderForm').serialize(),
	                success: function (result) {

	                    if(typeof(result)!="undefined" && result!=null && result != ""){
	                        if(result==0){
	                            layer.open({
	                                content: '您尚未登录，请先登陆后再试'
	                                ,skin: 'msg'
	                                ,time: 2 //2秒后自动关闭
	                            });
	                            $("#submit").removeAttr("disabled");
	                            return false;
	                        }else if(result==-1 || result==-2 || result==-3|| result==-5){
	                            layer.open({
	                                content: '呼叫失败，请稍后再试！'
	                                ,skin: 'msg'
	                                ,time: 2 //2秒后自动关闭
	                            });
	                            $("#submit").removeAttr("disabled");
	                            return false;
	                        }else if(result==-4){
	                            layer.open({
	                                content: '您的可用积分余额不足'
	                                ,skin: 'msg'
	                                ,time: 2 //2秒后自动关闭
	                            });
	                            $("#submit").removeAttr("disabled");
	                            return false;
	                        }else{
	                            window.location.href="${base}/taxi/getWaitAccept.jhtml?no="+result;
	                        }

	                    }else{
	                        layer.open({
	                            content: '呼叫失败，请稍后再试！'
	                            ,skin: 'msg'
	                            ,time: 2 //2秒后自动关闭
	                        });
	                        $("#submit").removeAttr("disabled");
	                    }

	                }
	            });
	    }
	    
		$("#password").keyup(function () {
	        var l_pwd = this.value.length;
	        if (l_pwd >= 1 && l_pwd <= 6 && event.keyCode != 8) {
	            var _input = document.getElementById("number" + l_pwd);
	            _input.value = this.value.charAt(l_pwd - 1);
	            if (l_pwd == 6) { //输入完成动作
	                var _pwd = this.value;
	                $("#paymentPop").hide();
	                this.blur();
	                var device = openDevice();
	                if (device == "android") {
	                    vpshop_android.callHideSoftInput();
	                }

	                var $checkedIds = $("#rechargeRecords input[name='coinIds']:enabled:checked");
	                $.ajax({
	                    url: "${base}/member/order/check_paypassword.jhtml",
	                    type: "POST",
	                    data: {
	                        coinIds: 1,
	                        paypassword: _pwd,
	                        whiteBarIds: 1
	                    },
	                    dataType: "json",
	                    cache: false,
	                    success: function (message) {
	                        $(".password_section input").val("");
	                        if (message.type == "success") {
	                            submitOrder();
	                        } else {
	                            layer.open({
	                                content: message.content,
	                                skin: 'msg',
	                                time: 2 //2秒后自动关闭
	                            });
	                            $("#submit").removeAttr("disabled");
	                        }
	                    }
	                });
	            }
	        } else if (event.keyCode == 8) { //退格键删除
	            var _input = document.getElementById("number" + (l_pwd + 1));
	            _input.value = '';
	        }
	    });
	</script>
</body>

</html>