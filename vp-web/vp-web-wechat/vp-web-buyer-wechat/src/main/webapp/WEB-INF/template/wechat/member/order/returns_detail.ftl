<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="format-detection" content="telephone=no" />
    <meta name="viewport"
        content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">
    <title>售后详情</title>
    <script src="${base}/resources/wechat/plugins/flexible/flexible.js"></script>
    <link rel="stylesheet" href="${base}/resources/wechat/css/common.css">
    <link rel="stylesheet" href="${base}/resources/wechat/plugins/swiper4/css/swiper.min.css">
    <link rel="stylesheet" href="${base}/resources/wechat/css/scorepurchase.css">
    <link rel="stylesheet" href="${base}/resources/wechat/css/plusWap.css">
	<link rel="stylesheet" href="${base}/resources/wechat/plugins/LCalendar/css/LCalendar.css">
    <script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

    <script type="text/javascript" src="${base}/resources/wechat/plugins/swiper4/js/swiper.min.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/plugins/clipboard/clipboard.min.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/plugins/scale/pinchzoom.js"></script>
	<script src="${base}/resources/wechat/plugins/LCalendar/js/LCalendar.min.js"></script>
</head>

<body class="order_list_returns_page">
    <div class="afterSaleDetail">
        <div class="public_top_header">
            <a href="javascript:history.go(-1);" class="return_back"></a>
            售后详情
			[#if !(pageSource??&&pageSource=='weixincoupon')]
				[#include "./wechat/include/head_nav.ftl" /]
			[/#if]
        </div>
        <input type="hidden" id="returnsId">
        <div class="top_section">
        	<span class="red">
	            [#if returns.returnStatus == "unapprove"]
					待审核
				[#elseif returns.returnStatus == "cancelled"]
					已取消
				[#elseif returns.returnStatus == "unreturned"]
					<!-- 待退货，商家同意后三天内处理 -->
					[#if endDate??&&.now>(endDate?datetime)]
						已取消
					[#else]
						待退货
					[/#if]
				[#elseif returns.returnStatus == "returning"]
					退货中
				[#elseif returns.returnStatus == "returned"]
					已完成
				[#elseif returns.returnStatus == "notpass"]
					审核不通过
				[#elseif returns.returnStatus == "repairing"]
					维修中
				[#elseif returns.returnStatus == "repaired"]
					已维修
				[#elseif returns.returnStatus == "received"]
					已收货
				[#elseif returns.returnStatus == "unrefund"]
					待退款
				[/#if]
            </span>
            <p>订单编号:${returns.order.sn}</p>
            <div class="btns">
	            <input type="button" onclick="location.href='${base}/member/customerService/index.jhtml[#if pageSource??&&pageSource=="weixincoupon"]?pageSource=weixincoupon[/#if]'" value="联系客服" class="btn_simple"/>
	            [#if returns.returnStatus == "unapprove"]
					<input type="button" onclick="cancelApply('${returns.id}','${returns.sn}')" value="取消申请" class="btn_simple"/>
				[#elseif returns.returnStatus == "unreturned"]
					<input type="button" onclick="cancelApply('${returns.id}','${returns.sn}')" value="取消申请" class="btn_simple"/>
					<input type="button"  onclick="confirmReturns('${returns.id}', true,'${returns.supplierId.address}','${returns.afsServiceId}','${returns.supplierId.isOweOrder}','${returns.returnwareAddress}','${returns.returnwareConsignee}','${returns.returnwarePhone}')" id="confirmReturnsBtn" afsServiceId="${returns.afsServiceId}" value="填写退货物流" class="btn_simple"/>
				[/#if]
            </div>
        </div>
        <!-- 京东待退货-上门取件-->
 		[#if returns.returnStatus == "unreturned" && returns.pickwareType==4
 		 && (returns.supplierId.isOweOrder==1 || returns.supplierId.isOweOrder==18)]
	        <div class="infoBox">
	            <p>京东快递员会上门取件，请耐心等待</p>
	            <p>请您确保退换货商品、配件、说明书及各种包装完整</p>
	        </div>
		[#else]
			<!-- 待退货，商家同意后三天内处理 -->
			[#if (returns.returnStatus == "unreturned")&&(returns.serviceType==10||returns.serviceType==2)&&(endDate??&&(endDate?datetime)>.now)]
				[#if returns.sellerConsignee??]
			        <div class="detailCompTable">
			            <table>
			                <tr>
			                    <td>
			                        <div class="line_status">
			                            <p>商家已同意退货申请，请尽早退货</p>
			                            <p id="timeLeft"   data-end="${endDate?string('yyyy-MM-dd HH:mm:ss')}"></p> 
			                        </div>
			                    </td>
			                </tr>
			                <tr>
			                    <td>
									<div class="line_status paddingL">
                                        <button class="btn_simple sm text_copy" data-clipboard-text="${(returns.sellerConsignee)!}  ${(returns.sellerAddr)!}">点击复制</button>
                                        收货人：${(returns.sellerConsignee)!}
									</div>
									<div class="line_status paddingL">
                                     手机号：${(returns.sellerPhone)!}
									</div>
			                        <div class="line_address modifypadding">
			                                ${(returns.sellerAddr)!}
			                        </div>
			                    </td>
			                </tr>
			
			                <tr>
			                    <td>
			                        <div class="line_info">
			                            	未与商家协商一致，请勿使用到付或平邮，以免商家拒签获取
			                        </div>
			                    </td>
			                </tr>
			                <tr>
			                    <td>
			                        <div class="line_info">
			                          	  请填写真实退货物流信息，逾期未填写，退货申请将关闭
			                        </div>
			                    </td>
			                </tr>
			            </table>
			        </div>
		         [#else]
		        	 <div class="infoBox">
			            <p>客服人员会在24小时内联系您，请保持电话畅通</p>
			            <p>请您确保退换货商品、配件、说明书及各种包装完整</p>
			        </div>
		         [/#if]
	         [/#if]
        [/#if]
		[#if returns.deliveryCorp??&&(returns.returnStatus == "returning"||returns.returnStatus == "returned"||returns.returnStatus == "received")
			&&returns.returnStatus !="unreturned"&&(returns.serviceType==10||returns.serviceType==2)]
	        <div class="detailTable">
	            <table>
	                <caption>退货物流信息</caption>
	                <tr>
	                    <th>快递公司</th>
	                    <td>${returns.deliveryCorp}</td>
	                </tr>
	                <tr>
	                    <th>快递单号</th>
	                    <td>
	                    	<a href="${base}/member/order/toLogistics.jhtml?deliveryCorp=${returns.deliveryCorp}&expressNo=${returns.trackingNo}&ordertype=${returns.supplierId.isOweOrder}&mobile=${returns.sellerPhone}" >
	                    	${returns.trackingNo}
	                    	</a>
	                    </td>
	                </tr>
	            </table>
	        </div>
		[/#if]
		[#if returns.changeLiveryCorp??]
			<div class="detailTable return_trans_info">
	            <table>
	                <caption>换货物流信息</caption>
	                <tr>
	                    <th>快递公司</th>
	                    <td>${returns.changeLiveryCorp}</td>
	                </tr>
	                <tr>
	                    <th>快递单号</th>
	                    <td>
		                    <a href="${base}/member/order/toLogistics.jhtml?deliveryCorp=${returns.changeLiveryCorp}&expressNo=${returns.changeTrackingNo}&ordertype=${returns.supplierId.isOweOrder}&mobile=${returns.returnwarePhone}" >
		                    	${returns.changeTrackingNo}
		                    </a>
	                    </td>
	                </tr>
	            </table>
	        </div>
        [/#if]
        <div class="detailTable">
            <table>
                <caption>售后信息</caption>
                <tr>
                    <td colspan="2">
                        <div class="product">

                            <div class="img">
                               [#if returns.orderItem.product.supplierId.id==68 || returns.orderItem.product.supplierId.id==181|| returns.orderItem.product.supplierId.id==695]
									[#if returns.orderItem.product.attributeValue8!=null]
	                                             <img title="${returns.orderItem.product.name}" src="${returns.orderItem.product.attributeValue8}" />
									[#elseif  returns.orderItem.product.image!=null]
	                                             <img title="${returns.orderItem.product.name}" src="${setting.siteUrlImg}${returns.orderItem.product.image}" />
									[#else]
	                                             <img title="${returns.orderItem.product.name}" src="[#if com_person_product_default_img != null]${com_person_product_default_img}[#else]${setting.siteUrlImg}${setting.defaultThumbnailProductImage}[/#if]" />
									[/#if]
								[#else]
									[#if returns.orderItem.product.image!=null]
	                                             <img title="${returns.orderItem.product.name}" src="${setting.siteUrlImg}${returns.orderItem.product.image}" />
									[#else]
	                                             <img title="${returns.orderItem.product.name}" src="[#if com_person_product_default_img != null]${com_person_product_default_img}[#else]${setting.siteUrlImg}${setting.defaultThumbnailProductImage}[/#if]" />
									[/#if]
								[/#if]
                            </div>
                            <p class="title">${returns.orderItem.name}</p>
                        </div>
                    </td>
                </tr>
                <tr>
                    <th>售后单号</th>
                    <td>${returns.sn}</td>
                </tr>
                <tr>
                    <th>申请数量</th>
                    <td>${returns.qty}</td>
                </tr>
                [#if returns.order.orderType!=4]
                <tr>
                    <th>商品金额</th>
                    <td>[#if productShowRate??]${coinConvert(returns.orderItem.subtotal, productShowRate, false, false)}[#else]${currency(returns.orderItem.subtotal, true)}[/#if]</td>
                </tr>
				<tr>
					<th>退款金额</th>
					<td>[#if productShowRate??]${coinConvert(returns.refundAmount, productShowRate, false, false)}[#else]${currency(returns.refundAmount, true)}[/#if]</td>
				</tr>
                [/#if]
                <tr>
                    <th>返回方式</th>
                    <td>
                    	[#if returns.pickwareType==4]
                    		上门取件
                    	[#elseif returns.pickwareType==40]
                    		客户发货
                    	[#elseif returns.pickwareType==7]
                    		客户送货
                    	[#elseif returns.pickwareType==1]
                    		客户发货
                    	[#elseif returns.pickwareType==0]
                    		无需物流
                    	[/#if]
                    </td>
                </tr>
                <tr>
                    <th>收货地址</th>
                    <td>${(returns.returnwareConsignee)!} ${(returns.returnwarePhone)!} ${(returnwareArea.fullName)!}${(returns.returnwareAddress)!}</td>
                </tr>
                <tr>
                    <th>申请时间</th>
                    <td>${returns.createDate?string("yyyy-MM-dd HH:mm:ss")}</td>
                </tr>
                <tr>
                    <th>售后理由</th>
                    <td>${(returns.returnReason)!}</td>
                </tr>
                <tr>
                    <th>问题描述</th>
                    <td>${(returns.questionDesc)!}</td>
                </tr>
                [#if returns.questionPic??]
                 <tr>
                     <th>反馈图片</th>
                     <td>
                     	<div class="picrecord">
	                        <div class="picbox">
	                            <ul class="items">
		                    	[#list returns.questionPic?split(",") as image]
					           	[#if image != null && image != ""]
					           	<li class="item"><img src="${setting.siteUrlImg!}${image}" alt=""></li>
					           	[/#if]
					           	[/#list]
	                            </ul>
	                        </div>
	                    </div>
                     </td>
                 </tr>
                 [/#if]
            </table>
        </div>
		[#if returns.returnStatus != "unapprove"&&returns.auditReason??]
	        <div class="detailTable">
	            <table>
	                <caption>审核信息</caption>
	                <tr>
	                    <th>审核理由</th>
	                    <td>${(returns.auditReason)!}</td>
	                </tr>
	                <tr>
	                    <th>说明</th>
	                    <td>
	                    [#if returns.serviceType==30]
	                       	[#if (returns.maintainType??&&returns.maintainType==0)]
		                    	寄回维修
		                    [#else]
		                    	上门维修
		                    [/#if]	
	                    [#else]
	                        [#if (returns.returnFlag??&&returns.returnFlag)]
		                    	需要退回原有商品
		                    [#else]
		                    	无需退回原有商品
		                    [/#if]	
	                    [/#if]	
	                    </td>
	                </tr>
	            </table>
	        </div>
		[/#if]
		
		[#if logs?? && logs?size>0]
		<div class="detailTable">
	        <table>
	            <caption>售后跟踪</caption>
	            [#list logs as log]
					[#if log.enabledFlag]
						<tr>
							<td style="width:38%" >${log.createDate?string("yyyy-MM-dd HH:mm:ss")}</td>
							<td>${log.content}</td>
						</tr>
	            	[/#if]
	            [/#list]
	        </table>
		</div>
		[/#if]
    </div>
    <div class="scorepurchaserecopage">
	    <div class="swiperBox ">
			<div class="swiper-container swiper-container-fade">
				<div class="swiper-wrapper">
					<div class="swiper-slide">
						<div><img /></div>
					</div>
				</div>
			</div>
			<span class="closebtn"> × </span>
		</div>
	</div>
   <!-- <script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script> -->
    <script src="${base}/resources/wechat/js/common.js"></script>
	<script type='text/template' id="pop_returns1">
		<div class="fli_pub_pop_form">
			<div class="item">
                <select name="deliveryCorp" id="deliveryCorp" class="input select_chose text_left">
                    <option value="">请选择物流公司</option>
                    <option value="EMS">EMS</option>
                    <option value="顺丰">顺丰</option>
                    <option value="韵达">韵达</option>
                    <option value="中通">中通</option>
                    <option value="百世汇通">百世汇通</option>
                    <option value="圆通">圆通</option>
                    <option value="申通">申通</option>
                    <option value="天天">天天</option>
                    <option value="全峰">全峰</option>
                    <option value="宅急送">宅急送</option>
                    <option value="国通">国通</option>
                    <option value="德邦">德邦</option>
                    <option value="速尔">速尔</option>
                    <option value="能达">能达</option>
                    <option value="优速">优速</option>
                    <option value="快捷">快捷</option>
                    <option value="新邦物流">新邦物流</option>
                    <option value="晟邦物流">晟邦物流</option>
                    <option value="天地华宇">天地华宇</option>
                    <option value="京东物流">京东物流</option>
                    <option value="京东快递">京东快递</option>
                    <option value="中通国际">中通国际</option>
                    <option value="其他">其他</option>
                </select>
			</div>
			<div class="item">
				<input type="text" name="trackingNo" id="trackingNo" class="input text_left" placeholder="请输入快递单号"/>
			</div>
			<div class="item">
				<input type="text" class="input text_left" name="deliverDate" id="deliverDate" value=""  readonly="readonly" placeholder="请选择发货时间" />
			</div>
			<div class="item">
				<input type="tel" name="freightMoney" id="freightMoney" class="input text_left" placeholder="请输入运费"/>
			</div>
		</div>
	</script>
	<script type='text/template' id="pop_yanxuan_returns">
		<div class="fli_pub_pop_form">
			<div class="item">
				<select name="deliveryCorp" id="deliveryCorp" class="input select_chose text_left">
					<option value="">请选择物流公司</option>
					<option value="EMS">EMS</option>
					<option value="顺丰">顺丰</option>
					<option value="韵达">韵达</option>
					<option value="中通">中通</option>
					<option value="百世汇通">百世汇通</option>
					<option value="圆通">圆通</option>
					<option value="申通">申通</option>
					<option value="天天">天天</option>
					<option value="全峰">全峰</option>
					<option value="宅急送">宅急送</option>
					<option value="国通">国通</option>
					<option value="德邦">德邦</option>
					<option value="速尔">速尔</option>
					<option value="能达">能达</option>
					<option value="优速">优速</option>
					<option value="快捷">快捷</option>
					<option value="新邦物流">新邦物流</option>
					<option value="晟邦物流">晟邦物流</option>
					<option value="天地华宇">天地华宇</option>
					<option value="京东物流">京东物流</option>
					<option value="京东快递">京东快递</option>
					<option value="中通国际">中通国际</option>
					<option value="其他">其他</option>
				</select>
			</div>
			<div class="item">
				<input type="text" name="trackingNo" id="trackingNo" class="input text_left" placeholder="请输入快递单号" style="text-align:left;"/>
			</div>
			<div class="item">
				<input type="text" name="deliverDate" class="text_left input" id="deliverDate" value=""  readonly="readonly" placeholder="请选择发货时间" />
			</div>
			<div class="item">
				<input type="tel" name="freightMoney" id="freightMoney" class="input text_left" placeholder="请输入运费"/>
			</div>
		</div>
	</script>
	<script type='text/template' id="pop_returns">
		<div class="fli_pub_pop_form">
				<div class="item">
                        <select name="deliveryCorp" id="deliveryCorp" class="input select_chose text_left">
                            <option value="">请选择物流公司</option>
							[#if tMallExpress??]
								[#list tMallExpress as tMallExpressCompany]
									<option value="${tMallExpressCompany.expressCode}">${tMallExpressCompany.expressName}</option>
								[/#list]
							[#else ]
                            <option value="EMS">EMS</option>
                            <option value="顺丰速运">顺丰速运</option>
                            <option value="圆通快递">圆通快递</option>
                            <option value="中通快递">中通快递</option>
                            <option value="申通快递">申通快递</option>
                            <option value="宅急送">宅急送</option>
                            <option value="天天快递">天天快递</option>
                            <option value="韵达快递">韵达快递</option>
                            <option value="百世快递">百世快递</option>
                            <option value="其他">其他</option>
							[/#if]
                        </select>
				</div>
				<div class="item">
					<input type="text" name="trackingNo" id="trackingNo" class="input text_left" placeholder="请输入快递单号"/>
				</div>
			<div class="item">
				<input type="text" name="deliverDate" id="deliverDate" value="" class="input text_left"  readonly="readonly" placeholder="请选择发货时间" />
			</div>
			<div class="item">
				<input type="tel" name="freightMoney" id="freightMoney" class="input text_left" placeholder="请输入运费"/>
			</div>
			</div>
	</script>
	<script type='text/template' id="pop_eascs_returns">
		<div class="fli_pub_pop_form">
			<div class="item">
				<select name="deliveryCorp" id="deliveryCorp" class="input select_chose text_left">
					<option value="">请选择物流公司</option>
					<option value="EMS">EMS</option>
					<option value="顺丰速运">顺丰速运</option>
					<option value="圆通快递">圆通快递</option>
					<option value="中通快递">中通快递</option>
					<option value="申通快递">申通快递</option>
					<option value="宅急送">宅急送</option>
					<option value="天天快递">天天快递</option>
					<option value="韵达快递">韵达快递</option>
					<option value="百世快递">百世快递</option>
				</select>
			</div>
			<div class="item">
				<input type="text" name="trackingNo" id="trackingNo" class="input text_left" placeholder="请输入快递单号" />
			</div>
			<div class="item">
				<input type="text"  readonly class="input text_left"   id="returnsAddress" placeholder="寄回地址"/>
			</div>
			<div class="item">
				<input type="text" class="input text_left" name="deliverDate" id="deliverDate" value=""  readonly="readonly" placeholder="请选择发货时间" />
			</div>
			<div class="item">
				<input type="tel" name="freightMoney" id="freightMoney" class="input text_left" placeholder="请输入运费"/>
			</div>
		</div>
	</script>
	
    <script type="text/javascript">

		//设置发货日期deliverDate
		function setDeliverDate(){
			var calendar = new LCalendar();
			calendar.init({
				'trigger': '#deliverDate', //标签id
				'type': 'date', //date 调出日期选择 datetime 调出日期时间选择 time 调出时间选择 ym 调出年月选择,
				'minDate': (new Date().getFullYear()-118) + '-' + 1 + '-' + 1, //最小日期
				'maxDate': (new Date().getFullYear()+10) + '-' + 12 + '-' + 31 //最大日期
			});
		}
	    var clipboard = new ClipboardJS('.text_copy');
		clipboard.on('success', function(e) {
			if($(e.trigger).hasClass("disabled")){
				layer.open({
					content: '已复制'
					,skin: 'msg'
					,time: 2 //2秒后自动关闭
				});
			}else{
				layer.open({
					content: '复制成功'
					,skin: 'msg'
					,time: 2 //2秒后自动关闭
				});
			}
			$('.text_copy').removeClass("disabled").text("复制");
			e.trigger.innerText = "已复制"
			e.trigger.classList.add("disabled");
		});
	
		clipboard.on('error', function(e) {
			layer.open({
				content: e
				,skin: 'msg'
				,time: 2 //2秒后自动关闭
			});
		});
	
	    var $timeLeft = $("#timeLeft");
		if($timeLeft != null){
			var endDate = $timeLeft.attr("data-end");
			if(endDate != null){
				var time = new Date(endDate.replace(/-/g, '/')) - new Date();
				time = Math.floor(time/1000);
				setInterval(function(){
					if(time){
						$("#timeLeft").html(setSecToTime(time--));
					}else{
						//请处理
					}
					
				},1000);
			}
		}
		
		//将秒转化为时间形式
		function setSecToTime(time) {
			var sec = 0, hmin = 0, min = 0, hour = 0,day=0;
			sec = time % 60;
			hmin = Math.floor(time / 60);
			if (hmin > 60) {
				hour = Math.floor(hmin / 60);
				min = hmin % 60
			} else {
				min = hmin
			}
			if(hour>24){
				day = Math.floor(hour / 24);
				hour=hour % 24
			}
			hour = setZero(hour);
			min = setZero(min);
			sec = setZero(sec);
		
			return "还剩"+day+"天"+hour + "时" + min + "分" +sec+"秒";
		}
		
		//补0
		function setZero(num) {
			if (num < 10) {
				return "0" + num
			} else {
				return num
			}
		}
	  	//取消申请
		function cancelApply(returnsId, sn){
			layer.open({
			    content: '确定取消对'+ sn +'售后单的申请？'
			    ,btn: ['否', '是']
			    ,skin: 'footer'
			    ,no: function(index){
			      $.ajax({
				  	url:'cancelReturns.jhtml',
				  	type:'POST',
				  	data:"returnsId="+returnsId,
				  	cache:false,
				  	async:false,
				  	success:function(flag){
				  		if(flag){
				  			location.reload();
				  		}else{
				  			layer.open({
							    content: '取消申请失败'
							    ,skin: 'msg'
							    ,time: 2 //2秒后自动关闭
							  });
				  		}
				  	}
				  });
			    }
			});
		}
		
	  	//确认退货
		function confirmReturns(id, isShipper,address,afServiceId,isOweOrder,returnwareAddress,returnwareConsignee,returnwarePhone){
			$("#returnsId").val(id);
			
			if(isShipper){
				//需要物流
				if(afServiceId!=null && afServiceId!='' && typeof(afServiceId)!="undefined"){//京东
					layer.open({
						type:1,
						title:"确认退货",
						style:"width:90%",
						 content:$("#pop_returns1").html(),
						 btn:["确定","取消"],
						 success:function(){
							setDeliverDate();
						 },
						 yes: function(index){
							 submitConfirmReturns();
							 layer.close(index);
						 }
					})
				}else{//非京东
					if(isOweOrder=="8"){
						layer.open({
							type:1,
							title:"确认退货",
							style:"width:90%",
							 content:$("#pop_yanxuan_returns").html(),
							 btn:["确定","取消"],
							success:function(){
								setDeliverDate();
							},
							 yes: function(index){
								 submitConfirmReturns();
								 layer.close(index);
							 }
						})
					}else if(isOweOrder=="32"){
						layer.open({
							type:1,
							title:"确认退货",
							style:"width:90%",
							 content:$("#pop_eascs_returns").html(),
							 btn:["确定","取消"],
							success:function(){
								setDeliverDate();
							},
							 yes: function(index){
								 submitConfirmReturns();
								 layer.close(index);
							 }
						})
						$("#returnsAddress").val(returnwareAddress);
					}else{
						layer.open({
							type:1,
							title:"确认退货",
							style:"width:90%",
							 content:$("#pop_returns").html(),
							 btn:["确定","取消"],
							success:function(){
								setDeliverDate();
							},
							 yes: function(index){
								 submitConfirmReturns();
								 layer.close(index);
							 }
						})
					   $("#address").val(address);
					}
				}
				
			}else{
				//不需要物流，通知卖家退款弹框
				layer.open({
				    content: "是否通知卖家退款？"
				    ,btn: ['否', '是']
				    ,skin: 'footer'
				    ,no: function(index){
				    	$.ajax({
							url:'confirmReturns.jhtml',
							type:'POST',
							data:'returnsId=' + $("#returnsId").val(),
							cache:false,
							async:false,
							success:function(flag){
								if(flag){
						  			location.reload();
						  		}else{
						  			layer.open({
									    content: '退货失败'
									    ,skin: 'msg'
									    ,time: 2 //2秒后自动关闭
									  });
						  		}
							}
						});
				      }
				  });
			}
		}
		
		function submitConfirmReturns(){
			var returnsId = $("#returnsId").val();
			var deliveryCorp = $("#deliveryCorp").val();
			var trackingNo = $("#trackingNo").val();
			var freightMoney=$("#freightMoney").val();
			var deliverDate=$("#deliverDate").val();
			var afServiceId=$("#confirmReturnsBtn").attr("afsServiceId");
		    if(afServiceId!=null && afServiceId!='' && typeof(afServiceId)!="undefined"){//京东
		       if(freightMoney==null || freightMoney=='' || typeof(freightMoney)=="undefined"){
		           layer.msg("请输入运费");
			       return;
		       }
		    }
		    
			if($("#deliveryCorp").val() == ""){
				layer.open({
				    content: '请输入快递公司'
				    ,skin: 'msg'
				    ,time: 2 //2秒后自动关闭
				  });
				return;
			}
			if($("#trackingNo").val() == ""){
				layer.open({
				    content: '请输入快递单号'
				    ,skin: 'msg'
				    ,time: 2 //2秒后自动关闭
				  });
				return;
			}
			if($("#deliverDate").val() == ""){
				layer.open({
					content: '请选择发货日期'
					,skin: 'msg'
					,time: 2 //2秒后自动关闭
				});
				return;
			}
			$.ajax({
				url:'confirmReturns.jhtml',
				type:'POST',
				data:'returnsId=' + returnsId + '&trackingNo=' + trackingNo + '&deliveryCorp=' + deliveryCorp+"&freightMoney="+freightMoney+"&deliverDate="+deliverDate,
				cache:false,
				async:false,
				beforeSend:function(){
	             	$('#submitBtn').prop("disabled", true);
	             },
				success:function(flag){
					if(flag){
			  			layer.open({
						    content: '退货成功'
						    ,skin: 'msg'
						    ,time: 2 //2秒后自动关闭
						  });
						setTimeout("location.reload()",300);
			  		}else{
			  			layer.open({
						    content: '退货失败'
						    ,skin: 'msg'
						    ,time: 2 //2秒后自动关闭
						  });
			  		}
				}
			});
		}
		
		$(".return_trans_info caption").on("click", function () {
	        $(this).parents(".return_trans_info").toggleClass("show")
	    })
	    
	    $(".picrecord .items").on("click",".item",function(){
		    $(".swiperBox .swiper-slide img").attr("src",$(this).find("img").attr("src"));
		    $(".swiperBox ").addClass("show");
		})
		//缩放初始化
		$('.swiperBox .swiper-slide').each(function () {
		    new RTP.PinchZoom($(this), {});
		});
		$(".swiperBox .closebtn").on("click",function(){
		    $(this).parent().removeClass("show");
		});
    </script>

</body>

</html>