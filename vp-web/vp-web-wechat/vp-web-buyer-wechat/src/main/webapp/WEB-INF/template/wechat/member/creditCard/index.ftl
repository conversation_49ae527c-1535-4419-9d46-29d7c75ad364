<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<title>生活服务</title>
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">
		<script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css"/>
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/lifeService.css"/>
		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
		
		<script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js" ></script>
	</head>
	 [#assign current = "mall" /]
	<body class="has_fixed_footer">
		<div class="public_top_header">
			<a href="[#if yphFlag??]${base}/index.jhtml[#else]${base}/travel_service/index.jhtml?sysModuleId=146[/#if]" class="return_back"></a> 
			信用卡还款
			[#include "./wechat/include/head_nav.ftl" /]
		</div>
		
		<div class="creditCardIndexPages">
			<header>
				<div class="btn_line">
					<a href="repayment.jhtml" class="btn_main btn_primary">立即还款</a>
					<a href="add.jhtml" class="btn_main btn_primary">绑定信用卡</a>
				</div>
				<p>积分信用卡还款还款到账时间为2~5个工作日</p>
			</header>
			<input id="pageNumber"  type="hidden" name="pageNumber" value="${page.pageNumber}"/>
			<input id="totalPages"  type="hidden" name="totalPages" value="${page.totalPages}"/>
			<input type="hidden" value="${type!}" name="type" id="type">
			<div class="tab_theme_section">
		 		<div class='item[#if !type??] active[/#if]'><a href="index.jhtml"  >我的还款记录</a></div>
		 		<div class='item[#if type??&&type=="creditCardList"] active[/#if]'><a href="index.jhtml?type=creditCardList" >我的信用卡</a></div>
		 	</div>
		 	[#if type??&&type=="creditCardList"]
				<ul class="list">
					[#if page??&&page.content?size>0]
						[#list page.content as obj]
					 		<li>
					 			<p class="title">
					 				<span class="name">${obj.bankName}</span>
					 				[#if obj.bankAccountNoDec?length>4]
										********${obj.bankAccountNoDec?substring(obj.bankAccountNoDec?length-4,obj.bankAccountNoDec?length)} 
									[#else]
										${obj.bankAccountNoDec} 
									[/#if]
					 			</p>
					 			<p>发卡城市：
					 			[#if obj.bankProvince==obj.bankCity]
	                             ${obj.bankCity}
	                             [#else] 
	                             ${obj.bankProvince} ${obj.bankCity}
	                             [/#if] 
								</p>
					 			
					 			<div class="right">
					 				<a href="repayment.jhtml?id=${obj.id}" class="btn_main btn_primary sm">还款</a>
									<a href="javascript:;" val="${obj.id}" class="btn_main btn_red delete sm">解绑</a>
					 			</div>
					 		</li>
					 	[/#list]
					 	[#if page.totalPages>1&&page.totalPages>page.pageNumber]
				 			<a href="javascript:;" class="long_more">查看更多</a>
				 		[/#if]
					 [#else]
					 	<div class="emptyCart">
							<img src="${base}/resources/wechat/img/empty_cart.png" />
							<p>您还没有绑定信用卡，赶快去绑定吧！</p>
						</div> 	
					 [/#if]
		 		</ul>
		 	[#else]
			 	<ul class="list">
			 		[#if page??&&page.content?size>0]
						[#list page.content as oi]
					 		<li>
					 			<p class="title">
					 				<span class="name">
					 					${oi.getVirtualProductOrderInfoMapValue('bankName')}
					 				</span>
					 				[#if oi.getVirtualProductOrderInfoMapValue('bankAccountNo')?length>4]
										********${oi.getVirtualProductOrderInfoMapValue('bankAccountNo')?substring(oi.getVirtualProductOrderInfoMapValue('bankAccountNo')?length-4,oi.getVirtualProductOrderInfoMapValue('bankAccountNo')?length)} 
									[#else]
										${oi.getVirtualProductOrderInfoMapValue('bankAccountNo')} 
									[/#if]
								</p>
					 			<p>申请日期：${oi.createDate?string("yyyy-MM-dd HH:mm:ss")}</p>
					 			
					 			<div class="right">
					 				<p class="coin">${oi.getVirtualProductOrderInfoMapValue('payAmount')}</p>
					 				<p class="type">
					 					[#if oi.order.orderStatus == "unpaid"]
												交易失败
										[#elseif oi.order.orderStatus == "unconfirmed"]
												交易中
										[#elseif oi.order.orderStatus == "confirmed"]
												交易中
										[#elseif oi.order.orderStatus == "shipped"]
												交易中
										[#elseif oi.order.orderStatus == "completed"]
												交易成功
										[#elseif oi.order.orderStatus == "cancelled"]
												交易失败
										[/#if]
					 				</p>
					 			</div>
					 		</li>
				 		[/#list]
				 		[#if page.totalPages>1&&page.totalPages>page.pageNumber]
				 			<a href="javascript:;" class="long_more">查看更多</a>
				 		[/#if]
				 	[#else]
					 	<div class="emptyCart">
							<img src="${base}/resources/wechat/img/empty_cart.png" />
							<p>这里还没有还款记录，赶快去还款吧！</p>
						</div> 	
				 	[/#if]
			 		
			 	</ul>
		 	[/#if]
			
			<div class="public_info_bottom">
				<div class="title">信用卡还款说明：</div>
				<div class="content">
					<p>1. 仅支持<span class="text_red">预付费积分</span>，信用卡还款收取6%的服务费。</p>
					<p>2. 每日每张信用卡只处理一笔还款申请，未处理的还款申请将自动顺延到下个工作日处理；</p>
					<p>3. 每月每张信用卡最多只处理十笔还款申请，未处理的还款申请将自动顺延到下月处理；</p>
					<p>4. 发卡银行、发卡城市、信用卡卡号、持卡人姓名，必须与银行信息完全匹配，否则将导致还款失败或错还到他人账户</p>
					<p>5. 不同银行还款到账时间可能不同，一般需要2－5个工作日；请自行决定还款时间，并承担可能由于还款不及时带来的后果。</p>
					<p>6. 还款银行需对卡号进行验证，可能导致部分卡还款失败；若出现类似情况，请更换其他信用卡重新还款。</p>
				</div>
			</div>
		</div>
		<!--此处引入公共尾部代码  开始-->
		[#include "./wechat/include/footer.ftl" /]
		<!--此处引入公共尾部代码  结束-->
		
		<script type="text/javascript">
	$(function(){
		$(".long_more").click(function(){
			var $this=$(this);
			var totalPages=Number($("#totalPages").val());
			var pageNumber=Number($("#pageNumber").val());
			var type=$("#type").val();
			if(totalPages>pageNumber){
				$.get("ajaxList.jhtml",{"type":type,"pageNumber":pageNumber+1},function(data){
					if(data&&data.indexOf("<li>")!=-1){
						$("#pageNumber").val(pageNumber+1);
						$this.before(data);
						if(pageNumber+1>=totalPages){
							$this.remove();
						}
					}
				})
			}
		});
		// 删除
		$(".delete").on("click", function() {
			var $this = $(this);
			if ($this.hasClass("disabled")) {
				return false;
			}
			var id = $this.attr("val");
			layer.open({
			    content: '您确定要解绑吗？'
			    ,btn: ['取消', '确定']
			    ,skin: 'footer'
			    ,no:function(){
					$.ajax({
						url: "delete.jhtml",
						type: "POST",
						data: {"id":id},
						dataType: "json",
						cache: false,
						success: function(message) {
							layer.open({
								content:message.content
								,skin: 'msg'
								,time: 2
							})
							 /* layer.msg(message.content, {icon: 1}); */
							if (message.type == "success") {
								$this.closest("li").remove();
								layer.open({
									content:'解绑成功'
									,skin: 'msg'
									,time: 2
								})
							}
						}
					});
			    }
			});
			
		});
	})
	
	</script>
	</body>
</html>
