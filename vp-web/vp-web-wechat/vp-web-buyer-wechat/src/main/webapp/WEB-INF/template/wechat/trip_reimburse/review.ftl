<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>差旅报销审核</title>
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="format-detection" content="telephone=no, email=no" />
    <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/chailvStyle.css" />
        <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/form.css" /> 

    <script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js"></script>
    
</head>

<body>
   <div class="chailvPage">
        <div class="public_top_header">
            <a href="javascript:history.go(-1);" class="return_back"></a>
           	差旅报销审核
        </div>
        <form action="">
            <div class="header_page">
            <h1>
            [#if tripReimburseApply.applyStatusIndex == 0]起草节点
			[#elseif tripReimburseApply.applyStatusIndex gte 1 && tripReimburseApply.applyStatusIndex lte 3]业务审批
			[#elseif tripReimburseApply.applyStatusIndex == 4]财务初审
			[#elseif tripReimburseApply.applyStatusIndex == 5]财务复审
			[#elseif tripReimburseApply.applyStatusIndex == 6]出纳付款
			[#elseif tripReimburseApply.applyStatusIndex == 7]已完成
			[#elseif tripReimburseApply.applyStatusIndex == 8]已拒绝
			[/#if]
               	<span>${tripReimburseApply.createDate?string("yyyy-MM-dd HH:mm:ss")}</span></h1>
            </div>

            <div class="margin_round_section">
                <div class="form_section">
                    <div class="item">
                        <label>报销单号</label>
                        <div class="inputs">
                            ${tripReimburseApply.applySn}
                        </div>
                    </div>
                    <div class="item">
                        <label>填单人</label>
                        <div class="inputs">
                           ${tripReimburseApply.memberId.name} &nbsp;&nbsp; ${tripReimburseApply.memberId.employeeid}
                        </div>
                    </div>
                </div>
            </div>

            <div class="margin_round_section">
                <div class="form_section">
                    <div class="item">
                        <label>报销人</label>
                        <div class="inputs">
                            ${tripReimburseApply.reimburseMemberId.name}
                        </div>
                    </div>
                    <div class="item">
                        <label>出差类型</label>
                        <div class="inputs">
                           [#if tripReimburseApply.applyType == "0"]拜访客户
							[#elseif tripReimburseApply.applyType == "1"]项目出差
							[#elseif tripReimburseApply.applyType == "2"]外出培训
							[#elseif tripReimburseApply.applyType == "3"]参加培训
							[#else]其他
							[/#if]
                        </div>
                    </div>
                    <div class="item">
                        <label>业务类型</label>
                        <div class="inputs">
                            差旅费
                        </div>
                    </div>
                    <div class="item">
                        <label>费用部门</label>
                        <div class="inputs">
                            ${tripReimburseApply.feeCompanyStructure.name}
                        </div>
                    </div>
                    <div class="item">
                        <label>费用项目</label>
                        <div class="inputs">
                            ${tripReimburseApply.projectId.name}
                        </div>
                    </div>
                    <div class="item">
                        <label>发票张数</label>
                        <div class="inputs">
                            ${tripReimburseApply.receiptNumbers}
                        </div>
                    </div>
                    <div class="item">
                        <label>摘要</label>
                        <div class="inputs">
                            ${tripReimburseApply.memo}
                        </div>
                    </div>
                </div>
            </div>

			[#list tripReimburseApply.tripReimburseRecords as record]
			<div class="margin_round_section header_has_arrow">
                <header>
                    <label>行程{record_index + 1 }</label>
                    <h2>报销信息</h2>
                    <span class="arrow active"></span>
                </header>
                <div class="form_section">
                [#if record.tripApply??]
                    <div class="item">
                        <label>申请单</label>
                        <div class="inputs">
                            ${record.tripApply.applyTypeDesc }（${record.tripApply.tripCity}）
                        </div>
                    </div>
               [/#if]
                    <div class="item">
                        <label>出差日期</label>
                        <div class="inputs">
                            ${record.startDate} 至  ${record.endDate}
                        </div>
                    </div>
                    <div class="item">
                        <label>出差城市</label>
                        <div class="inputs">
                            ${record.fromCity} 至 ${record.toCity}
                        </div>
                    </div>

                    <div class="item">
                        <label for="dayTotal">出差天数</label>
                        <div class="inputs">
                            ${record.tripDays}
                        </div>
                    </div>
                    <div class="item">
                        <label>补助天数</label>
                        <div class="inputs">
                            ${record.reimburseDays}
                        </div>
                    </div>
                </div>
                <div class="table_box">
                    <header>消费明细</header>
                    <div class="content">
                    	[#if record.tripApply??]
                        <div class="table_label">
                            <div class="no">单号：<span>${record.tripApply.applySn }</span></div>
                            <label>申请单关联项</label>
                        </div>
                        <section class="table">
                        	[#if record.tripApply.planeCost??]
							<div class="line">
                                <div>
                                    <span class="icon_fly"></span>机票
                                </div>
                                <div>￥${currency(record.tripApply.planeCost) }</div>
                                <div>企业已支付</div>
                            </div>
							[/#if]
							[#if record.tripApply.trainCost??]
							<div class="line">
                                <div>
                                    <span class="icon_train"></span>火车票
                                </div>
                                <div>￥${currency(record.tripApply.trainCost) }</div>
                                <div>企业已支付</div>
                            </div>
							[/#if]
							[#if record.tripApply.taxiCost??]
							<div class="line">
                                <div>
                                    <span class="icon_didi"></span>打车
                                </div>
                                <div>￥${currency(record.tripApply.taxiCost) }</div>
                                <div>企业已支付</div>
                            </div>
							[/#if]
							[#if record.tripApply.hotelCost??]
							<div class="line">
                                <div>
                                    <span class="icon_hotel"></span>住宿
                                </div>
                                <div>￥${currency(record.tripApply.hotelCost) }</div>
                                <div>企业已支付</div>
                            </div>
							[/#if]
                        </section>
                        [/#if]
                        [#if record.tripReimburseAmounts?? && record.tripReimburseAmounts?size > 0]
                        <div class="table_label">
                            <label class="color_y">新增报销项</label>
                        </div>
                        <section class="table">
                        [#list record.tripReimburseAmounts as amount]
                        	<div class="line">
                                <div>
                                    [#if amount.costType == "1"]
									<span class="icon_fly"></span>机票
									[#elseif amount.costType == "2"]
									<span class="icon_train"></span>火车票
									[#elseif amount.costType == "3"]
									<span class="icon_hotel"></span>住宿
									[#elseif amount.costType == "4"]
									<span class="icon_didi"></span>打车
									[#elseif amount.costType == "5"]
									<span class="icon_ship"></span>轮船
									[#elseif amount.costType == "6"]
									<span class="icon_public"></span>公共交通
									[#elseif amount.costType == "8"]
									<span class="icon_butie"></span>补贴
									[#else]
									<span class="icon_other"></span>其他
									[/#if]
                                </div>
                                <div>${currency(amount.amount) }</div>
                                <div>[#if amount.chargeFlag?? && amount.chargeFlag]已支付[#else]待报销[/#if]</div>
                            </div>
                            [/#list]
                        </section>
                        [/#if]
                    </div>
                </div>
            </div>
			[/#list]
            

            <div class="margin_round_section header_has_arrow">
                <header>
                    收款信息
                    <span class="arrow active"></span>
                </header>
                <div class="form_section">
                    <div class="item">
                        <label>报账总额</label>
                        <div class="inputs">
                            ￥${currency(tripReimburseApply.totalAmount) }
                        </div>
                    </div>
                    <div class="item">
                        <label>企业已报</label>
                        <div class="inputs">
                            ￥${currency(tripReimburseApply.companyAmount) }
                        </div>
                    </div>
                    <div class="item">
                        <label>个人报销</label>
                        <div class="inputs">
                            ￥${currency(tripReimburseApply.personalAmount) }
                        </div>
                    </div>
                    <div class="item">
                        <label>支付方式</label>
                        <div class="inputs">
                        [#if tripReimburseApply.receiveType == "0"]积分支付[#else]银行存款[/#if]
                        </div>
                    </div>
                    <div class="item">
                        <label>收款账号</label>
                        <div class="inputs">
                        [#assign bankAccount = tripReimburseApply.receiveAccount?split(";")]
                        [#if bankAccount?size gte 2]
                        ${bankAccount[1] }（${bankAccount[0] }）
                        [#else]
                        ${bankAccount[0] }
                        [/#if]
                        </div>
                    </div>
                </div>
            </div>

            <div class="margin_round_section header_has_arrow">
                <header>
                    	单据信息
                    <span class="arrow active"></span>
                </header>
				[#if tripReimburseApply.tripReimburseReviews?? && tripReimburseApply.tripReimburseReviews?size gt 0]
                <div class="steps_section">
                	[#list tripReimburseApply.tripReimburseReviews?sort_by("createDate")?reverse as review]
                		[#assign reviewResult="${(review.operateType.ordinal()==1)?string('拒绝','通过')}" ]
                    <div class="line">
                        <span class="num">${tripReimburseApply.tripReimburseReviews?size - review_index }</span>
                        <span>${review.member.name}</span>
                        <span>
                        	[#if review.auditGrade.ordinal() == 0]
									起草节点
							[#elseif review.auditGrade.ordinal() == 1 || review.auditGrade.ordinal() == 2 || review.auditGrade.ordinal() == 3]
									业务审批${reviewResult}
							[#elseif review.auditGrade.ordinal() == 4]
									财务初审${reviewResult}
							[#elseif review.auditGrade.ordinal() == 5]
									财务复审${reviewResult}
							[#elseif review.auditGrade.ordinal() == 6]
									出纳付款${reviewResult}
							[#else]
							
							[/#if]
                        </span>
                        [#if review.memo?? && review.memo!=""]
                        <span class="reason" reason="${review.memo}">理由</span>
                        [#else]
                        <span >无</span>
                        [/#if]
                        <span>${review.createDate?string("MM-dd HH:mm")}</span>
                    </div>
                    [/#list]
                </div>
                [#else]
                <div class="steps_section">无</div>
				[/#if]
            </div>
            <div class="margin_round_section">
                <div class="form_section">
                    <div class="item textarea">
                        <label>理由</label>
                        <div class="inputs">
                            <textarea name="memo" id="memo" cols="30" rows="10" maxlength="80"></textarea>
                        </div>
                    </div>
                </div>
            </div>

            <div class="button_box">
                <input type="button" value="拒绝" id="reject" class="btn_theme_empty" >
                <input type="button" value="通过" id="pass" class="btn_theme_empty active">
            </div>
        </form>
    </div>
    <script>
        $(function () {
            $(".arrow").on("click", function () {
                $(this).toggleClass("active").parent("header").nextAll().toggle();
            });

            $(".reason").on("click", function () {
                $that = $(this);
                layer.open({
                    content: $that.attr("reason"),
                    btn: ["我知道了"],
                    yes: function (index, layero) { //确定按钮事件
                        layer.close(index);
                    }
                });
            });

            $(".button_box :button").click(function(){
				var $that = $(this);
				var memo = $("#memo").val();
				var type="1";
				if($that.attr("id")=="pass"){
					//审核通过
					type="2";
					if(memo==null || memo==""){
						memo="同意！";
					}
				}else if($that.attr("id")=="reject"){
					//审核拒绝
					type="1";
					if(memo==null || memo==""){
						layer.open({
	                        content: "请填写审批意见"
	                        ,skin: 'msg'
	                        ,time: 2 //2秒后自动关闭
	                    });
						return ;
					}
				}
				
				$.ajax({
					url: "reviewSubmit.jhtml",
					type: "POST",
					data: {id:${tripReimburseApply.id},memo:memo,type:type},
					dataType: "json",
					cache: false,
					success: function(msg) {
						if(msg){
							if(msg.type=="success"){
								layer.open({
			                        content: "操作成功"
			                        ,skin: 'msg'
			                        ,time: 2 //2秒后自动关闭
			                    });
								setTimeout("location.href='index.jhtml?type=waitAudit'",300)
							}else{
								layer.open({
			                        content: msg.content
			                        ,skin: 'msg'
			                        ,time: 2 //2秒后自动关闭
			                    });
							}
						}else{
							layer.open({
		                        content: "操作失败"
		                        ,skin: 'msg'
		                        ,time: 2 //2秒后自动关闭
		                    });
						}
					}
				});
			});
        })
    </script>
</body>

</html>