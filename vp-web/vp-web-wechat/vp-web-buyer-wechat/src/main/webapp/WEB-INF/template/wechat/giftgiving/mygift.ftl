<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
    <title>我的礼物</title>
    <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

    <script type="text/javascript" src="${base}/resources/wechat/js/dropload.min.js" ></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>

    <link rel="stylesheet" type="text/css"  href="${base}/resources/wechat/css/common.css">
    <link rel="stylesheet" type="text/css"  href="${base}/resources/wechat/css/square.css">
</head>

<body class="square-body">
<div class="gift-style-mine gift-main">
    <div class="public_top_header">
        [#--<a href="${base}/giftGiving/index.jhtml" class="return_back"></a>--]
        <a href="javascript:history.go(-${time});" class="return_back"></a>
        我的礼物
        <div class="gift-icon-display">
            [#--<img class="gift-icon-right" src="${base}/resources/wechat/img/gift/icon_right.png" alt="">--]
            [#include "./wechat/include/head_nav.ftl" /]
        </div>
    </div>
    <div class="gift-style-select">
        <div class="gift-style-item [#if type==1]iactive[/#if]">
            <a href="myGift.jhtml?type=1&time=${time}">收到</a>
        </div>
        <div class="gift-style-item [#if type==2]iactive[/#if]">
            <a href="myGift.jhtml?type=2&time=${time}">送出</a>
        </div>
    </div>
    <div class="gift-style-list">

    </div>
</div>
<script>
    $(function () {
        let lastTime = $(".gift-style-date").last().text().trim();
        //加载数据
        loadData11('${base}/giftGiving/getGiftPages.jhtml',{type: "${type}",lastTime:$(".gift-style-date").last().text().trim()}, 'gift-style-mine', 'gift-style-list', 'POST', 'totalPages', null, null);
    })

    listCurPageSS = 1;//当前的数据页数  全局变量
    flag_first_time = true;//是否是数据第一次加载  全局变量
    function loadData11(url, data, pageClass, appendDataClass, requestType, totalPagesId, beforeCallBackFunction, afterCallBackFunction, element, _pageSize/*,is_refresh_up*/) {
        var elementObj = window;
        var _pageSize = typeof _pageSize == "number" ? _pageSize : 10;//每页的条数
        _pageNumber = 1;
        /*is_refresh_up = is_refresh_up||false;*/
        //如果不是整个页面滑动分页，则取滑动的元素，不取整个页面了
        if (undefined != element) {
            elementObj = element;
        }

        $('.' + pageClass).dropload({
            scrollArea: elementObj,
            /*loadUpFn: function(me){
                if(is_refresh_up){
                    location.reload();
                }
                me.resetload();
            },*/
            loadDownFn: function (me) {
                //判断数据是否为第一次加载
                if (flag_first_time) {
                    pageSize = listCurPageSS * _pageSize;
                    var param = { pageNumber: _pageNumber++, pageSize: pageSize };
                    //合并参数
                    mergeObjPrPos(param, data);
                } else {
                    pageSize = _pageSize;
                    data.lastTime=$(".gift-style-date").last().text().trim();
                    var param = { pageNumber: _pageNumber++, pageSize: pageSize };
                    //合并参数
                    mergeObjPrPos(param, data);
                }

                //合并参数
                $.ajax({
                    url: url,
                    type: requestType,
                    data: param,
                    success: function (data) {
                        //显示数据
                        $("." + appendDataClass).append(data);
                        var totalPages = $("#" + totalPagesId).val();

                        if (param.pageNumber >= totalPages) {
                            // 锁定
                            me.lock();
                            // 无数据
                            me.noData();
                        }

                        //下一页码
                        if (flag_first_time) {
                            listCurPageSS = parseInt(listCurPageSS);
                            _pageNumber = listCurPageSS + 1;
                            flag_first_time = false;
                        } else {
                            listCurPageSS += 1;
                        }
                        param.pageNumber++;


                        //重置之前的回调函数
                        if (undefined != beforeCallBackFunction) {
                            beforeCallBackFunction();
                        }

                        // 每次数据加载完，必须重置
                        me.resetload();

                        //如果没有数据，则不需要任何提示
                        if (totalPages == 0) {
                            me.$domDown.html(me.opts.domDown.domBlankData);
                        }

                        //重置之后的回调函数
                        if (undefined != afterCallBackFunction) {
                            afterCallBackFunction();
                            //防止页面空白提示与下拉空白提示同时出现
                            if (!!$(".public_empty_section").length) {
                                $(".dropload-down").hide()
                            }
                        }

                    },
                    error: function (xhr, type) {
                        //加载数据失败提示
                        me.$domDown.html(me.opts.domDown.domErrorData);

                        //加载出错时，不是第一页就重置
                        if (param.pageNumber > 1) {
                            me.resetload();
                        }
                    }
                });
            }
        });
    }

</script>
</body>
</html>