[#assign fpy=""]
[#if list?size>0]
[#list list as member]
    [#if fpy != member.namePy]
        [#assign fpy="${member.namePy}"]
        [#if member_index == 0]
            <div class="item_city">
            [#if member.namePy == '~']
				<header>#</header>
            [#else]
				<header>${member.namePy}</header>
            [/#if]
                 <ul>
        [#else]
                 </ul>
            </div>
            <div class="item_city">
            [#if member.namePy == '~']
				<header>#</header>
            [#else]
				<header>${member.namePy}</header>
            [/#if]
				<ul>
        [/#if]
    [/#if]

        <li class="people" receiveId="${member.id}" receiveName="${member.name}">
            <div class="state"></div>
            [#if member.image??]
                [#if member.image?contains("dingtalk")||member.image?contains("http")]
                    <img src="${member.image}" alt="">
                [#else]
                    <img src="${base}/${member.image}" alt="">
                [/#if]
            [#else]
                [#if member.gender == 'female']
                    <img src="${base}/resources/wechat/img/girl.png" alt="">
                [#else]
                    <img src="${base}/resources/wechat/img/boy.png" alt="">
                [/#if]
            [/#if]
            <div class="info">
                <div class="line">
                    <strong class="name">${member.name}</strong>
                    [#if member.type=='birth']
                        <span class="day">最近生日</span>
                    [#elseif member.type=='entryDate']
                        <span class="day year">入职周年</span>
                    [#else]
                        <span></span>
                    [/#if]
                </div>
                <div class="number" id="${member.id}">
                    ${member.username}
                    [#if member.type=='birth']
                        &nbsp;&nbsp;还有${member.day}天
                    [#elseif member.type=='entryDate']
                        &nbsp;&nbsp;还有${member.day}天
                    [/#if]
                </div>
            </div>
        </li>

    [#if member_index == list?size]
			</ul>
		</div>
    [/#if]
[/#list]
[#else ]
    <div class="public_empty_section [#if comPersonConf && com_person_EmptyBg != null && com_person_EmptyBg != ""] ${com_person_EmptyBg!}[#else] Public_Bg[/#if]">
        <p class="tip">这里空空如也</p>
    </div>
[/#if]