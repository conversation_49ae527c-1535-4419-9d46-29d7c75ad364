<!DOCTYPE html>
<html lang="en">

<head>
<meta charset="UTF-8">
<title>违反差旅标准</title>
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="black">
<script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/fli_2019.css" />
<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js"></script>
<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
</head>

<body>
	<div class="public_top_header">
		<a href="javascript:history.go(-1);" class="return_back"></a> 违反差旅标准
	</div>
	<div class="chailvReasonPage">
		<section class="lable_reason">
			<span class="icon_tips"></span>
			[#if tripSnapshot.tripRuleBroken??]
				[#list tripSnapshot.tripRuleBroken?split("、") as re]
					[#if re != null && re != ""]
					<p>${re_index + 1 }、${re }</p>
					[/#if]
				[/#list]
			[/#if]
		</section>
		<section class="list_reason">
			<header>选择原因:</header>
			[#if tripReasons??]
				[#list tripReasons as planReason]
					[#if planReason != null && planReason.resultCode != "PLA00"]
					<div class="item">
						<div class="fli_checkbox_blue">
							<input type="radio" name="radioBtn" id="${planReason.resultCode}" desc="${planReason.resultMessage}" value="${planReason.resultCode}" /> <label for="${planReason.resultCode}"></label>
						</div>
						${planReason.resultMessage}
					</div>
					[/#if]
				[/#list]
			[/#if]
			<div class="item">
				<div class="fli_checkbox_blue">
					<input type="radio" name="radioBtn" id="PLA00" value="PLA00" />
					<label for="PLA00"></label>
				</div>
				其它
			</div>
		</section>
		<section class="textarea">
			<textarea placeholder="说明" id="reasonMemo" maxlength="60"></textarea>
			<div class="btns">
				<input type="button" class="btn_half btn_yellow" onclick="javascript:history.go(-1);" value="取消"/>
				<input type="button" class="btn_half btn_blue" onclick="submit()" value="继续预订"/>
			</div>
		</section>

	</div>

	[#if form == "TB"]
		<form id="checkForm" action="tempusCheck.jhtml" method="post">
			<input type="hidden" name="agentMemberId" value="${agentMemberId}"/>
				<input type="hidden" name="isPublic" value="${isPublic}"/>
			[#if planeTicket??]
	        <input type="hidden" name="cabinName" value="${planeTicket.cabinName}"/>
	        <input type="hidden" name="cabinCode" value="${planeTicket.cabinCode}" />
			<input type="hidden" name="dptCity" value="${planeTicket.dptCity}" />
			<input type="hidden" name="dptCode" value="${planeTicket.dptCode}" />
			<input type="hidden" name="arrCode" value="${planeTicket.arrCode}" />
			<input type="hidden" name="arrCity" value="${planeTicket.arrCity}" />
			<input type="hidden" name="carrierName" value="${planeTicket.carrierName}" />
			<input type="hidden" name="flightNum" value="${planeTicket.flightNum}" />
			<input type="hidden" name="dptDate" value="${planeTicket.dptDate}" />
			<input type="hidden" name="arrDate" value="${planeTicket.arrDate}" />
			<input type="hidden" name="dptTime" value="${planeTicket.dptTime}" />
			<input type="hidden" name="arrTime" value="${planeTicket.arrTime}" />
			<input type="hidden" name="flightTimes" value="${planeTicket.flightTimes}" />
			<input type="hidden" name="dptAirport" value="${planeTicket.dptAirport}" />
			<input type="hidden" name="arrAirport" value="${planeTicket.arrAirport}" />
			<input type="hidden" name="dptTerminal" value="${planeTicket.dptTerminal}" />
			<input type="hidden" name="arrTerminal" value="${planeTicket.arrTerminal}" />
			<input type="hidden" name="arf" value="${planeTicket.arf}" />
			<input type="hidden" name="tof" value="${planeTicket.tof}" />
			<input type="hidden" name="inventory" value="${planeTicket.inventory}" />
			<input type="hidden" name="price" value="${planeTicket.price}" />
			<input type="hidden" name="airCode" value="${planeTicket.airCode}" />
			<input type="hidden" name="policyId" value="${planeTicket.policyId}" />
			<input type="hidden" name="ticketPrice" value="${planeTicket.ticketPrice}" />
			<input type="hidden" name="fdPrice" value="${planeTicket.fdPrice}" />
			<input type="hidden" name="cost" value="${planeTicket.cost}" />
			[/#if]
			<input type="hidden" name="flightInfoOtheJson" value="${flightInfoOtheJson}">
			<input type="hidden" name="flightInfoJson" value="${flightInfoJson}">
			[#if tripSnapshot?? ]
				<input type="hidden" name="applyId" value="${tripSnapshot.applyId}"/>
				<input type="hidden" name="tripType" value="${tripSnapshot.tripType}"/>
				<input type="hidden" name="serviceInfo" value="${tripSnapshot.serviceInfo}"/>
				<input type="hidden" name="miniPriceFlight" value="${tripSnapshot.miniPriceFlight}"/>
				<input type="hidden" name="tripReasonCode" value="${tripSnapshot.tripReasonCode}"/>
				<input type="hidden" name="tripRuleBroken" value="${tripSnapshot.tripRuleBroken}"/>
				<input type="hidden" name="tripReason" value="${tripSnapshot.tripReason}"/>
				<input type="hidden" name="tripMemo" value="${tripSnapshot.tripMemo}"/>
				<input type="hidden" name="isBreak" value="${tripSnapshot.isBreak}"/>
			[/#if]
		</form>
	[#else]
		<form action="${base}/plane/check.jhtml" id="checkForm" method="post">
			<input type="hidden" name="agentMemberId" value="${agentMemberId}"/>
				<input type="hidden" name="isPublic" value="${isPublic}"/>
		<input type="hidden" name="cabinName" value="${cabinName}">
		<input type="hidden" name="bookingResult" value="${bookingResult}" >
		<input type="hidden" name="flightTimes" value="${flightTimes}" >
		<input type="hidden" name="stopCityName" value="${stopCityName}" >
		<input type="hidden" name="flightTypeFullName" value="${flightTypeFullName}" >
		<input type="hidden" name="flightInfoOtheJson" value="${flightInfoOtheJson}">
		<input type="hidden" name="isFirstCheckout" value="${isFirstCheckout}" >
		[#if tripSnapshot?? ]
			<input type="hidden" name="applyId" value="${tripSnapshot.applyId}"/>
			<input type="hidden" name="tripType" value="${tripSnapshot.tripType}"/>
			<input type="hidden" name="serviceInfo" value="${tripSnapshot.serviceInfo}"/>
			<input type="hidden" name="miniPriceFlight" value="${tripSnapshot.miniPriceFlight}"/>
			<input type="hidden" name="tripReasonCode" value="${tripSnapshot.tripReasonCode}"/>
			<input type="hidden" name="tripRuleBroken" value="${tripSnapshot.tripRuleBroken}"/>
			<input type="hidden" name="tripReason" value="${tripSnapshot.tripReason}"/>
			<input type="hidden" name="tripMemo" value="${tripSnapshot.tripMemo}"/>
			<input type="hidden" name="isBreak" value="${tripSnapshot.isBreak}"/>
		[/#if]
	</form>
	[/#if]
	
	<script>
		function submit(){
			var reasonCodeRadio = $(".chailvReasonPage").find("input[name='radioBtn']:checked");
        	var resonText = reasonCodeRadio.attr("desc");
        	var reasonCode = reasonCodeRadio.val();
        	var remark = $(".chailvReasonPage").find('#reasonMemo').val();
        	if(reasonCode == null){
        		layer.open({
                    content: '请选择原因!！'
                    ,skin: 'msg'
                    ,time: 2 //2秒后自动关闭
                });
        		return ;
            }
            if(remark.length > 60){
				remark = remark.substring(0, 60);
            }

        	var canSubmit = true;
        	$("#checkForm").find("input[name='tripMemo']").val(remark);
            $("#checkForm").find("input[name='tripReasonCode']").val(reasonCode);
			$("#checkForm").find("input[name='tripReason']").val(resonText);
			
        	[#if form != "TB"]
        	canSubmit = false;
        	$.ajax({
				url: '${base}/plane/booking.jhtml',
				type: 'POST',
				data: {
					carrier : '${bookingRequest.carrier}',
					code : '${bookingRequest.code}',
					depCode : '${bookingRequest.depCode}',
					arrCode : '${bookingRequest.arrCode}',
					date : '${bookingRequest.date}',
					btime : '${bookingRequest.btime}',
					vendorStr : '${bookingRequest.vendorStr}'
				},
				dataType: 'json',
				async: false,
				success: function(data) {
					if(data&&data.type=="success"){
						var bookingResultStr = data.data.data;
						var bookingResult=bookingResultStr.replace(/\%/g,'%25');
						$("#checkForm").find("input[name='bookingResult']").val(bookingResult);
						$("#checkForm").submit();
					}else{
						canSubmit = false;
						layer.open({
							content: "预定失败！",
							skin: 'msg',
							time: 2 //2秒后自动关闭
						});
					}					
				}
			});
			[/#if]
			if(canSubmit){
				$("#checkForm").submit();
			}
		}
	</script>
</body>

</html>