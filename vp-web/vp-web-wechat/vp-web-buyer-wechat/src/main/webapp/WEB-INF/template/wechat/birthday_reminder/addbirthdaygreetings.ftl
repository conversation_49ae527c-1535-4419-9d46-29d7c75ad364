<!DOCTYPE html>
<html style="background: #f9f9f9;">
	<head>
		<meta charset="utf-8" />
		<title>祝福留言</title>
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">
		<script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/activity.css" />

		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/jquery.qqFace.js" ></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/jquery.validate.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
		
	</head>

	<body>
		<div class="commentPage red">
			<div class="public_top_header">
                <span class="return_back" onclick="javascript:history.back();"></span> 祝福留言
				[#include "./wechat/include/head_nav.ftl" /]
			</div>
			<section>
				<form id="inputForm" action="savebirthdaygreetings.jhtml" method="post">
					<div class="textarea">
						<input type="hidden" value="${members.id}" name="mid">
						<textarea placeholder="写下您的祝福吧！不超过50个字！" id="textarea" name="blessingContent"></textarea>
						<span><span id="lengthLeft">50</span>/50</span>
					</div>
					<div class="right">
						<span class="icon_face"></span>
					</div>
					<input type="submit" name="" id="" value="发表留言" class="btn_submit_long" />
				</form>
			</section>
		</div>
		<script>
			// 表单验证
			$(function() {
                getQQimg($('.icon_face'),{"assign":'textarea'});
				//表情包
				/*$('.icon_face').qqFace({
					id : 'facebox',
					assign:'textarea',
					path:'${base}/resources/wechat/img/face/'	//表情存放的路径
				});*/

				shop.base = "${base}";
				jQuery("#inputForm").validate({
					submitHandler: function(form) {
						$(".btn_submit_long").attr("disabled", "disabled");
						form.submit();
					},
					rules: {
						blessingContent: {
							required: true,
							maxlength: 100
						}
					}
				});

				$("#textarea").bind("input propertychange change keyup", function(event) {
					checkWord(this);
				})
				
				//插入表情计数
			    $("body").on("click",".qqFace img",function(){
			    	checkWord($("#textarea")[0]);
			    })
			})

			//文本框字数限制
			var maxstrlen = 50;
			//检查文本
			function checkWord(c) {
				len = maxstrlen;
				var str = c.value;
				myLen = getStrleng(str);
				var wck = document.getElementById("lengthLeft");
				if(myLen > len * 2) {
					c.value = str.substring(0, i - 1);
				}
				wck.innerHTML = Math.floor((len * 2 - myLen) / 2) > 0 ? Math.floor((len * 2 - myLen) / 2) : 0;
			}
			//计算长度
			function getStrleng(str) {
				myLen = 0;
				i = 0;
				for(;
					(i < str.length) && (myLen <= maxstrlen * 2); i++) {
					if(str.charCodeAt(i) > 0 && str.charCodeAt(i) < 128)
						myLen++;
					else
						myLen += 2;
				}
				return myLen;
			}
			
		</script>
	</body>

</html>