<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>确认订单</title>
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta http-equiv="Expires" content="0" />
    <meta http-equiv="Cache-Control" content="no-cache" />
    <meta http-equiv="Pragma" content="no-cache" />
    <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/plusWap.css" />

    <script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/card.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/jquery.validate.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/md5.js" ></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/jsbn.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/prng4.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/rng.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/rsa.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/base64.js"></script>
    <script type="text/javascript">
        var $orderRefreshForm,$memo,$memoMsg;
        $(function(){
            $orderRefreshForm = $("#orderRefreshForm");
            $memo = $("#memo");
            $memoMsg = $("#memoMsg");
            var $submit = $("#submit");
            var $orderForm = $("#orderForm");
            var isOverseas = "${isOverseas}"; //海外商品标识
            var $receiverIdCard = $("#receiverIdCard");
            var $idcardNoOrder = $("#idcardNoOrder");
            var $saveIdcardNo = $("#saveIdcardNo");
            var $receiverId = $("#receiverId");
            var $payPasswordSetBtn = $("#payPasswordSetBtn");

            function submitOrder() {
                var groupId = $("#groupId").val();
                if(typeof(groupId) != "undefined" && groupId != null && groupId != '') {
                    url = "createPurchaseGroup.jhtml";
                } else {
                    url = "create.jhtml";
                }

                $.ajax({
                    url: url,
                    type: "POST",
                    data: $orderForm.serialize(),
                    dataType: "json",
                    cache: false,
                    beforeSend: function() {
                        $submit.prop("disabled", true);
                        $submit.val("提交中");
                    },
                    success: function(message) {
                        if(message.type == "success") {
                            //				location.href = "payment.jhtml?sn=" + message.content + "&type=0";
                            location.href = "${base}/paysRedirect/payment.jhtml?sn=" + message.content + "&type=0";
                        }
                        if(message.type == "warn") {
                            $submit.val("立即付款")
                            layer.open({
                                content: message.content,
                                skin: 'msg',
                                time: 2 //2秒后自动关闭
                            });
                        } else if(message.type == "error") {
                            $submit.val("立即付款");
                            if(message.data.type&&message.data.type==1){
                                var trHtml = '<div class="order_goods_has_changed"> <div class="header">'+message.content+'</div><div class="list"></div> </div>';
                                layer.open({
                                    style: "width:94%;",
                                    title: false,
                                    btn: ["返回购物车"],
                                    content: trHtml,
                                    yes: function(index, layero) {
                                        location.href = "${base}/cart/list.jhtml";
                                    }
                                });
                                return false;
                            }else if(message.data.type&&(message.data.type==2)){
                                layer.open({
                                    content: message.content,
                                    skin: 'msg',
                                    time: 2 //2秒后自动关闭
                                });
                                return false;
                            }else if(message.data.type&&(message.data.type==5||message.data.type==6)){
                                try{
                                    var err_msg=JSON.parse(message.content);
                                    var missfresh_message="商品存在变动";
                                    if(message.data.type==6){
                                        missfresh_message="商品不足或已售罄";
                                    }
                                    var trHtml = '<div class="order_goods_has_changed"> <div class="header">以下'+missfresh_message+'，暂时无法购买，请稍后再试！</div> <div class="list">';
										[@compress single_line = true]
										 $.each(err_msg, function(i, product) {

                                             var imgSrc="${setting.siteUrlImg}"+product[2];
                                             if(!imgSrc){
                                                 imgSrc="[#if com_person_product_default_img != null]${com_person_product_default_img}[#else]${setting.siteUrlImg}${setting.defaultThumbnailProductImage}[/#if]";
                                             }

                                             trHtml += '<div class="item"> <img src='+imgSrc+ '><div class="title">' + product[1] + '</div> </div>';
                                         });
										trHtml += '</div> </div>';
                                        [/@compress]

                                    layer.open({
                                        style: "width:94%;",
                                        title: false,
                                        btn: ["返回购物车"],
                                        content: trHtml,
                                        yes: function(index, layero){
                                            location.href = "${base}/cart/list.jhtml";
                                        }
                                    });
                                }catch(err){
                                    layer.open({
                                        content: "下单失败",
                                        skin: 'msg',
                                        time: 2 //2秒后自动关闭
                                    });
                                }

                                return false;
                            } else if(message.data.type&&message.data.type==15){
                                var trHtml = '<div class="order_goods_has_changed"> <div class="header">商品备货中，请于下个工作日12:00后尝试下单！</div><div class="list"></div> </div>';
                                layer.open({
                                    style: "width:94%;",
                                    title: false,
                                    btn: ["返回购物车"],
                                    content: trHtml,
                                    yes: function(index, layero) {
                                        location.href = "${base}/cart/list.jhtml";
                                    }
                                });
                                return false;
                            }

                            layer.open({
                                content: "下单失败",
                                skin: 'msg',
                                time: 2 //2秒后自动关闭
                            });

                            $.ajax({
                                url: "${base}/member/order/getChange.jhtml",
                                type: "POST",
                                data: {
                                    content: message.content
                                },
                                success: function(data) {
                                    var trHtml = '<div class="order_goods_has_changed"> <div class="header">以下商品有变动，暂时无法购买，请稍后再试！</div><div class="list">';
                                    var type=message.data.type;
                                    if(type!=null){
                                        if(type==7){
                                            var trHtml = '<div class="order_goods_has_changed"> <div class="header">以下商品权限不足，请联系运营人员添加！</div> <div class="list">';
                                        }
                                        if(type==8){
                                            var trHtml = '<div class="order_goods_has_changed"> <div class="header">以下商品权限不足，请联系运营人员添加！</div> <div class="list">';
                                        }
                                        if(type==9){
                                            var trHtml = '<div class="order_goods_has_changed"> <div class="header">以下商品库存不足！</div> <div class="list">';
                                        }
                                        if(type==10){
                                            var trHtml = '<div class="order_goods_has_changed"> <div class="header">以下商品在配送区域内受限，请更换收获地址！</div> <div class="list">';
                                        }
                                        if(type==11){
                                            var trHtml = '<div class="order_goods_has_changed"> <div class="header">您的手机号格式不正确！请重新输入！</div> <div class="list">';
                                        }
                                        if(type==12){
                                            var trHtml = '<div class="order_goods_has_changed"> <div class="header">商品是品类限购，不能下单！</div> <div class="list">';
                                        }
                                        if(type==13){
                                            var trHtml = '<div class="order_goods_has_changed"> <div class="header">商品该地区无货，不能下单！</div> <div class="list">';
                                        }
                                        if(type==14){
                                            var trHtml = '<div class="order_goods_has_changed"> <div class="header">您选择的乡镇不存在，不能下单！</div> <div class="list">';
                                        }
                                    }
									    [@compress single_line = true]
										$.each(data, function(i, product) {

                                            var imgSrc="";
                                            if(product.supplierId.id==68 || product.supplierId.id == 181|| product.supplierId.id == 695){
                                                if(product.attributeValue8!=null){
                                                    imgSrc=product.attributeValue8;
                                                }else if(product.image!=null){
                                                    imgSrc="${setting.siteUrlImg}"+product.image;
                                                }else{
                                                    imgSrc="[#if com_person_product_default_img != null]${com_person_product_default_img}[#else]${setting.siteUrlImg}${setting.defaultThumbnailProductImage}[/#if]";
                                                }
                                            }else{
                                                if(product.image!=null){
                                                    imgSrc="${setting.siteUrlImg}"+product.image;
                                                }else{
                                                    imgSrc="[#if com_person_product_default_img != null]${com_person_product_default_img}[#else]${setting.siteUrlImg}${setting.defaultThumbnailProductImage}[/#if]";
                                                }
                                            }
                                            trHtml += '<div class="item"> <img src='+imgSrc+ ' /><div class="title">' + product.name + '</div> </div>';

                                        });
										trHtml += '</div> </div>';
                                        [/@compress]
                                    layer.open({
                                        style: "width:94%;",
                                        title: false,
                                        btn: ["返回购物车"],
                                        content: trHtml,
                                        yes: function(index, layero) {
                                            location.href = "${base}/cart/list.jhtml";
                                        }
                                    });
                                }
                            });
                        }
                    },
                    complete: function() {
                        $submit.prop("disabled", false);
                    }
                });
            }

            //输入密码事件,引用页面set_payPasswords.ftl的时候需要添加输入密码事件---即提交订单事件的处理逻辑
            $("#password").keyup(function() {
                var l_pwd = this.value.length;
                if(l_pwd >= 1 && l_pwd <= 6 && event.keyCode != 8) {
                    var _input = document.getElementById("number" + l_pwd);
                    _input.value = this.value.charAt(l_pwd - 1);
                    if(l_pwd == 6) { //输入完成动作
                        var _pwd = this.value;
                        $("#paymentPop").hide();
                        this.blur();
                        var device = openDevice();
                        if(device == "android") {
                            vpshop_android.callHideSoftInput();
                        }

                        var $checkedIds = $("#rechargeRecords input[name='coinIds']:enabled:checked");

                        $.ajax({
                            url: "${base}/common/public_key.jhtml",
                            type: "GET",
                            dataType: "json",
                            cache: false,
                            beforeSend: function () {
                                $submit.prop("disabled", true);
                            },
                            success: function (data) {
                                var rsaKey = new RSAKey();
                                rsaKey.setPublic(b64tohex(data.modulus), b64tohex(data.exponent));
                                var enPassword = hex2b64(rsaKey.encrypt(_pwd));
                                $.ajax({
                                    url: "check_paypassword.jhtml",
                                    type: "POST",
                                    data: {
                                        coinIds: $("#coinIds").val(),
                                        couponCodeId: "${couponCodeId}",
                                        paypassword: enPassword,
                                        whiteBarIds: $("#whiteBarIds").val(),
                                        type:1
                                    },
                                    dataType: "json",
                                    cache: false,
                                    success: function (message) {
                                        $(".password_section input").val("");
                                        if (message.type == "success") {
                                            submitOrder();
                                        } else {
                                            $submit.prop("disabled", false);
                                            layer.open({
                                                content: message.content,
                                                skin: 'msg',
                                                time: 2 //2秒后自动关闭
                                            });
                                        }
                                    }
                                });
                            }
                        })




                    }
                } else if(event.keyCode == 8) { //退格键删除
                    var _input = document.getElementById("number" + (l_pwd + 1));
                    _input.value = '';
                }
            });

            //保存身份证号
            $saveIdcardNo.click(function() {
                var receiverId = $receiverId.val();
                var idcardNo = $receiverIdCard.val();
                if(!idCardNoUtil.checkIdCardNo(idcardNo)) {
                    layer.open({
                        content: '请正确填写身份证号！',
                        skin: 'msg',
                        time: 2 //2秒后自动关闭
                    });
                    return false;
                }
                $.ajax({
                    url: "saveReviceIdcard.jhtml",
                    type: "POST",
                    data: {
                        receiverId: receiverId,
                        idcardNo: idcardNo
                    },
                    dataType: "json",
                    cache: false,
                    success: function(msg) {
                        if(msg == true) {
                            layer.open({
                                content: '身份证号码保存成功!',
                                skin: 'msg',
                                time: 2 //2秒后自动关闭
                            });
                        } else {
                            layer.open({
                                content: '身份证号码保存失败',
                                skin: 'msg',
                                time: 2 //2秒后自动关闭
                            });
                        }
                    }
                });

            });

            // 订单提交
            $submit.click(function() {
                var $isLowStocks=$(".outofservice");
                if($isLowStocks.size()>0){
                    layer.open({
                        content: "请检查购买的商品，存在无货商品！",
                        skin: 'msg',
                        time: 2 //2秒后自动关闭
                    });
                    return false;
                }
					[#if receiver?? && receiver.area?? && receiver.area.children?? && receiver.area.children?size>0]
						layer.open({
                            content: '<div style="padding:0 0.32rem 0.4rem"><i class="iconfont icon-tishi" style="font-size:1rem;display:block;margin:0.5rem auto 0.7rem;color:#fe5763"></i>由于地址库升级，请重新编辑当前收货地址中“所在地区”选项，即可顺利下单！</div>',
                            btn: '去完善',
                            yes: function(index){
                                $memoMsg.val($memo.val());
                                $("#orderRefreshForm").attr("action", "editAddress.jhtml");
                                $("#orderRefreshForm").submit();
                            }
                        });
		                return false;
                    [/#if]
                var companyId="${member.companyId.id}";
                var cashPayFlag="${member.companyId.cashPayFlag}";
                if(companyId==675){
                    if($("#_amountPayable").val() > 0) {
                        layer.open({
                            content: '积分余额不足！',
                            skin: 'msg',
                            time: 2 //2秒后自动关闭
                        });
                        return false;
                    }
                }

                if(cashPayFlag!=null && cashPayFlag!="" && cashPayFlag==0){ //不允许使用现金支付
                    if($("#_amountPayable").val() > 0) {
                        layer.open({
                            content: '请使用积分支付！',
                            skin: 'msg',
                            time: 2 //2秒后自动关闭
                        });
                        return false;
                    }
                }


                if(isOverseas == "true") {
                    var receiverIdCard = $receiverIdCard.val();
                    if(receiverIdCard == "" || receiverIdCard == null) {
                        layer.open({
                            content: '存在海外商品请正确填写身份证号！',
                            skin: 'msg',
                            time: 2 //2秒后自动关闭
                        });
                        $("html,body").animate({
                            scrollTop: 0
                        }, 300);
                        return false;
                    } else {
                        if(!idCardNoUtil.checkIdCardNo(receiverIdCard)) {
                            layer.open({
                                content: '存在海外商品请正确填写身份证号！',
                                skin: 'msg',
                                time: 2 //2秒后自动关闭
                            });
                            $("html,body").animate({
                                scrollTop: 0
                            }, 300);
                            return false;
                        }
                        $idcardNoOrder.val(receiverIdCard);
                    }
                }

                var detailAddress=$("#detailAddress").text();
                if(detailAddress!=null && detailAddress.indexOf('请补充')>-1){//用户收货地址为请补充，提示用户修改
                    layer.open({
                        content: '请核对您的收货地址和手机号哦！',
                        skin: 'msg',
                        time: 2 //2秒后自动关闭
                    });
                    return false;
                }

                if($isLowStocks.size()>0){
                    layer.open({
                        content: "请检查购买的商品，存在无货商品！",
                        skin: 'msg',
                        time: 2 //2秒后自动关闭
                    });
                    return false;
                }

                var supplierPaymentMethod="${supplierPaymentMethod}";
                if(supplierPaymentMethod==-1){
                    layer.open({
                        content: "您所购买的商品中含有私有供应商商品，请单独购买！",
                        skin: 'msg',
                        time: 2 //2秒后自动关闭
                    });
                    return false;
                }
                if(supplierPaymentMethod==-2){
                    layer.open({
                        content: "您所购买的商品中，存在不同的支付方式，请单独购买！",
                        skin: 'msg',
                        time: 2 //2秒后自动关闭
                    });
                    return false;
                }
                var rechargeRecordAmount = "${rechargeRecordAmount}"; //勾选了积分支付
                var whiteBarYAmount = "${whiteBarYAmount}"; //勾选了白条支付
                if(supplierPaymentMethod==1 || supplierPaymentMethod==2){
                    if(rechargeRecordAmount>0 || whiteBarYAmount>0){
                        layer.open({
                            content: "您所购买的商品中含有私有供应商商品，不支持积分或白条支付！",
                            skin: 'msg',
                            time: 2 //2秒后自动关闭
                        });
                        return false;
                    }
                }
                var couponCodeId="${couponCodeId}";
					[#if member.companyId.isFreePay ]
						submitOrder();
                    [#else]
					if(rechargeRecordAmount > 0||couponCodeId) { //可用积分大于0，弹出密码框
                        $("#paymentPop").show().find("#password").focus();
                    } else if(whiteBarYAmount > 0) {
                        $("#paymentPop").show().find("#password").focus();
                    } else {
                        submitOrder();
                    }
                    [/#if]
            });
				[#if receiver?? && receiver.area?? && receiver.area.children?? && receiver.area.children?size>0]
				layer.open({
                    content: '<i class="iconfont icon-tishi" style="font-size:1rem;display:block;margin:0.5rem auto 0.7rem;color:#fe5763"></i>由于地址库升级，请重新编辑当前收货地址中“所在地区”选项，即可顺利下单！',
                    btn: '去完善',
                    yes: function(index){
                        $memoMsg.val($memo.val());
                        $("#orderRefreshForm").attr("action", "editAddress.jhtml");
                        $("#orderRefreshForm").submit();
                    }
                });
                [/#if]
        });


        //选择收货地址页面跳转
        function selectAddress() {
            $memoMsg.val($memo.val());
            $("#orderRefreshForm").attr("action", "selectAddress.jhtml");
            $("#orderRefreshForm").submit();
        }

        //选择优惠券页面跳转
        function selectCoupon() {
            $memoMsg.val($memo.val());
            $("#orderRefreshForm").attr("action", "selectCoupon.jhtml");
            $("#orderRefreshForm").submit();
        }

        //选择积分页面跳转
        function selectCoin(){
            $memoMsg.val($memo.val());
            var groupId = $("#groupId").val();
            if(typeof(groupId) != "undefined" && groupId != null && groupId != '') {
                url = "groupPurchaseSelectCoin.jhtml";
            } else {
                url = "selectCoin.jhtml";
            }
            $("#orderRefreshForm").attr("action", url);
            $("#orderRefreshForm").submit();
        }

        //选择积分页面跳转
        function selectWhiteBar(){
            $memoMsg.val($memo.val());
            var groupId = $("#groupId").val();
            if(typeof(groupId) != "undefined" && groupId != null && groupId != '') {
                url = "groupPurchaseSelectWhiteBar.jhtml";
            } else {
                url = "selectWhiteBar.jhtml";
            }
            $("#orderRefreshForm").attr("action", url);
            $("#orderRefreshForm").submit();
        }
    </script>
</head>

<body id="myOrderPage" >
<div class="myOrderPage">
    <div class="public_top_header">
        <span class="return_back" onclick="javascript:window.history.back();"></span>
        确认订单
    </div>
    <form id="orderRefreshForm" action="" method="post">
				[#list cartItems as cartItem]
				<input type="hidden" name="ids" value="${cartItem.id}" /> [/#list]
        <input type="hidden" name="coinIds" value="${coinIds}" id="coinIds" />
        <input type="hidden" name="whiteBarIds" value="${whiteBarIds}" id="whiteBarIds" />
        <input type="hidden" id="receiverId" name="receiverId" [#if receiver??] value="${receiver.id}" [/#if] />
        <input type="hidden" name="couponCodeId" value="${couponCodeId}" />
        <input type="hidden" name="memo" id="memoMsg" value="${order.memo!}" />
        <input type="hidden" name="quantity" value="${quantity}" />
        <input type="hidden" name="pId" value="${productId}" />
        <input type="hidden" name="groupId" value="${groupId}" />
        <input type="hidden" name="toatlAmount" value="${order.toatlAmount}" />
        <input type="hidden" name="coinAmount" value="${order.coinAmount}" />
        <input type="hidden" name="amountPaid" value="${order.amountPaid}" />
        <input type="hidden" name="coinTypeIdsEcho" value="${coinTypeIdsEcho}" />
        <input type="hidden" name="coinTypeIds" value="${coinTypeIds}" />
        <input type="hidden" name="isAlone" value="${isAlone}" />
        <input type="hidden" name="groupPurchaseId" value="${groupPurchaseId}" />
        <input type="hidden" name="thirdCoinAmount" value="${thirdCoinAmount}" />
        <!-- 下面参数为了计算金额 -->
        <input type="hidden" id="price" name="price" value="${order.price}" />
        <input type="hidden" id="freight" name="freight" value="${order.freight}" />
        <input type="hidden" id="couponDiscount" name="couponDiscount" value="${order.couponDiscount}" />
        <input type="hidden" id="promotionDiscount" name="promotionDiscount" value="${order.promotionDiscount}" />
    </form>

    <section class="base_info">
        <a href="javascript:void(0)" onclick="selectAddress()" class="fli_link_line">
					[#if receiver??]
                        <p>
                            收货人：${receiver.consignee}
                            <span class="tel">${receiver.phoneDec}</span>
                        </p>
                        <p class="addr" id="detailAddress">
                            ${receiver.areaName}${receiver.address}[#if receiver!=null]${receiver.number}[/#if]
                        </p>
                    [#else] 马上去添加收货地址 [/#if]
        </a>
        <!--<a href="#">! 马上去添加收货地址</a>-->
        <div class="identify" [#if !isOverseas]style="display: none;" [/#if]>
            <input type="text" name="idcardNo" id="receiverIdCard" [#if receiver??] value="${receiver.idcardNoDec}" [/#if] class="input" placeholder="因海关清关需要，请填写收货人身份证号码。" />
            <input type="button" name="" id="saveIdcardNo" value="保存" class="btn_pill btn_pill_theme" />
        </div>
    </section>

			[#if order.groupPurchase??]
			<section class="infoTuanSection">
                <div class="tuanHeader">
                    <span>[正在参团]</span>完成支付即可团拼成功
                </div>
                <div class="imgs">
					[#list order.groupPurchase.groupPurchaseLogs as log]
                        <div class="img">
                            <img src="[#if log.head??&&log.head?index_of("http")!=-1]${log.head}[#else]${base}${log.head}[/#if]"/>
                        </div>
                    [/#list]
                </div>
            </section>
            [/#if]

    <section class="contentBox">
        <header>
            <span>确认商品</span>
        </header>
        <ul class="order_list_section">
					[#list order.orderItems as orderItem]
                        <li>
                            <div class="img">
                            [#--<img src="${setting.siteUrlImage}/[#if orderItem.product.thumbnail??]${orderItem.product.thumbnail}[#elseif com_person_product_default_img != null]${com_person_product_default_img}[#else]${setting.defaultThumbnailProductImage}[/#if]" />--]
								[#if orderItem.product.supplierId.id==68 || orderItem.product.supplierId.id == 181|| orderItem.product.supplierId.id == 695]
                                    [#if orderItem.product.attributeValue8!=null]
                                        <img src="${orderItem.product.attributeValue8}" />
                                    [#elseif  orderItem.product.image!=null]
                                        <img src="${setting.siteUrlImg}${orderItem.product.image}" />
                                    [#else]
                                        <img src="[#if com_person_product_default_img != null]${com_person_product_default_img}[#else]${setting.siteUrlImg}${setting.defaultThumbnailProductImage}[/#if]" />
                                    [/#if]
                                [#else]
                                    [#if orderItem.product.image!=null]
                                        <img src="${setting.siteUrlImg}${orderItem.product.image}" />
                                    [#else]
                                        <img src="[#if com_person_product_default_img != null]${com_person_product_default_img}[#else]${setting.siteUrlImg}${setting.defaultThumbnailProductImage}[/#if]" />
                                    [/#if]
                                [/#if]
                            </div>
                            <div class="info">
                                <p class="title">
								[#if orderItem.product.name?length>30]${orderItem.product.name[0..28]}...[#else]${orderItem.product.name}[/#if]

                                </p>


                                <p class="price">[#if productShowRate??]${coinConvert(orderItem.price, productShowRate, true) }[#else]${currency(orderItem.price, true)}[/#if]<span>x${orderItem.quantity}</span></p>

							[#if orderItem.product.specificationValues]
							<p class="attr">
								[#list orderItem.product.specificationValues as specificationValue]
                                    <span>
			 					  ${specificationValue.specification.name}：${specificationValue.name}
								</span> [/#list]
                            </p>
                            [/#if] [#if orderItem.isLowStock]
							<p class="outofservice">无货</p>
                            [/#if]
                            </div>

						[#if productShowRate??]
                        [#else]
						<div class="save">
                            <span class="icon_save"></span>
                            <span>
							[#if order.companyId !=null && order.companyId.priceAddFlag==1 && !orderItem.product.supplierId.getIsVirtualSupplier()]
								[#assign addPriceRateBean = product_add_price(order.companyId.id, orderItem.product.id)/]
								[#assign subDis = ((addPriceRateBean.marketPrice - orderItem.price ) * orderItem.quantity)]
                                ${currency(subDis, true)}
                            [#else]
                                ${currency(orderItem.discountAmount, true)}
                            [/#if]
                            </span>
                        </div>
                        [/#if]
                        </li>
                    [/#list]
        </ul>
    </section>
    <section class="message_section">
        <header>
            <span>订单备注</span>
        </header>
        <form id="orderForm" [#if groupId!=null]action="createPurchaseGroup.jhtml" [#else]action="create.jhtml" [/#if] method="post">
            <input type="hidden" name="idcardNoOrder" id="idcardNoOrder" value="" /> [#list cartItems as cartItem]
					<input type="hidden" name="cartItemids" value="${cartItem.id}" /> [/#list]
            <input type="hidden" name="ids" value="${coinIds}" />
            <input type="hidden" name="whiteBarIds" value="${whiteBarIds}" />
            <input type="hidden" name="cartToken" value="${cartToken}" />
            <input type="hidden" name="receiverId" [#if receiver??] value="${receiver.id}" [/#if]/>
            <input type="hidden" name="paymentMethodId" maxlength="200" value="1" />
            <!--支付方式-->
            <input type="hidden" name="shippingMethodId" maxlength="200" value="1" />
            <!--支付方式-->
            <input type="hidden" name="couponCodeId" value="${couponCodeId}" />
            <input type="hidden" name="groupId" value="${groupId}" id="groupId" />
            <input type="hidden" id="productId" name="productId" value="${productId}" />
            <input type="hidden" id="quantity" name="quantity" value="${quantity}" />
            <input type="hidden" name="isAlone" value="${isAlone}" />
            <input type="hidden" name="groupPurchaseId" value="${groupPurchaseId}" />
            <input type="hidden" name="thirdCoinAmount" value="${thirdCoinAmount}" />
            <textarea placeholder="请输入您的留言" name="memo" id="memo" onkeyup="javascript:checkWord(this);" onmousedown="javascript:checkWord(this);">${order.memo!}</textarea>
        </form>
        <span><span id="lengthLeft">0</span>/30</span>
    </section>

    <section class="favourable_section">
        <div class="lines_Section_all">
            <div onclick="selectCoupon()" class="fli_link_line">
                优惠券<span class="like_btn">${couponCodeSize}张可用</span> [#if order.couponDiscount > 0]
						<div class="right_text red">-[#if productShowRate??]${coinConvert(order.couponDiscount, productShowRate, true) }[#else]${currency(order.couponDiscount, true)}[/#if]</div>
            [#else]
						<div class="right_text gray">未使用</div>
            [/#if]
            </div>
        </div>

			[#if (member.companyId?? && (member.companyId.sourceFlag==null || member.companyId.sourceFlag==""))]
				<div class="lines_Section_all">
                    <div id="selectCoin" onclick="selectCoin()" class="fli_link_line">
                        积分支付
                        <div class="like_btn_box">
							[#list coins as coin] [#if coin.coinTypeId.isCredit]
							<span class="like_btn">	${coin.coinTypeId.name?substring(0,1)}${coinConvert(coin.balance, coinShowRate!1)}</span> [/#if] [/#list]

							[#if thirdCoin??]
							<span class="like_btn" thirdCoinAmount="${thirdCoinAmount }">${thirdCoin.coinName?substring(0,1)}${thirdCoin.amount}</span>
                            [/#if]

                        </div>
						[#if order.coinAmount > 0]
						<div class="right_text red">-${coinConvert(order.coinAmount, coinShowRate!1)}</div>
                        [#else]
						<div class="right_text gray">未使用</div>
                        [/#if]
                    </div>
                </div>

				<div class="lines_Section_all" [#if (member.companyId?? && member.companyId.hasWhitebar?? && member.companyId.hasWhitebar)] [#else]style="display:none;" [/#if]>
                    <div id="selectWhiteBar" onclick="selectWhiteBar()" class="fli_link_line">
                        白条支付 [#list whiteBarCoins as coin] [#if coin.coinTypeId.isCredit && coin.enabledFlag && coin.isFreeze]
						<span class="like_btn">	${coin.coinTypeId.name?substring(0,1)}${coinConvert(coin.balance, coinShowRate!1)}</span> [/#if] [/#list] [#if order.whiteBarAmount gte 0]
						<div class="right_text red">-${coinConvert(order.whiteBarAmount, coinShowRate!1)}</div>
                    [#else]
						<div class="right_text gray">未使用</div>
                    [/#if]
                    </div>
                </div>
            [/#if]


    </section>

    <section class="amount_section">
        <p>
            数量
            <span>共${order.quantity}件</span>
        </p>
        <p>
            商品金额
            <span>[#if productShowRate??]${coinConvert(order.price, productShowRate, false) }[#else]${currency(order.price, true, false)}[/#if]</span>
        </p>

        <p class="FreightTxt">
            运费
            <!--  <img src="${base}/resources/wechat/img/angle-down-old.png">  -->
            <i class="img"></i>
            <span>[#if productShowRate??]${coinConvert(order.freight, productShowRate, false) }[#else]${currency(order.freight, true, false)}[/#if]</span>
        </p>


        <div class="freightMask">
            <div class="Content">
                <div class="top">
                    <span class="txtleft"> 运费明细</span>
                    <label class="txtright">
                        <span class="txt"> 运费总计</span>
                        <span class="number">[#if productShowRate??]${coinConvert(order.freight, productShowRate, false) }[#else]${currency(order.freight, true, false)}[/#if]</span>
                    </label>
                </div>
            [#assign supplierAmounts=order.supplierAmount]
                <ul class="lists">
            [#list supplierAmounts.keySet() as key]
   				[#if key==null]
	                <li class="item" data-supplier="0" data-freeFreight="${(setting.freeFreight)!0}" data-freight="${(setting.freight)!0}">
                        <label class="txtleft">
                            <span class="name">平台商品订单总额:</span>
                            <span class="num supplierAmount">[#if productShowRate??]${coinConvert(supplierAmounts.get(key), productShowRate, false, true) }[#else]￥${currency(supplierAmounts.get(key))}元[/#if]</span>
                        </label>
                        <label class="txtright">
	                    [#if supplierAmounts.get(key) gte setting.freeFreight]
                            (满[#if productShowRate??]${coinConvert((setting.freeFreight)!0, productShowRate, false, true) }[#else]${(setting.freeFreight)!0}元[/#if]包邮)
                        [#else]
                            [#if productShowRate??]
	                    	(不满${coinConvert((setting.freeFreight)!0, productShowRate, false, true) }，收取<span class="num">${coinConvert((setting.freeFreight)!0, productShowRate) }</span>积分抵扣运费)
                            [#else]
	                       (不满${(setting.freeFreight)!0}元，收取运费<span class="num">${(setting.freight)!0}</span>元)
                            [/#if]
                        [/#if]
                        </label>
                    </li>
                [#else]
	                <li class="item"  data-supplier="${key.id}"  data-freeFreight="${(key.freeFreight)!0}" data-freight="${(key.freight)!0}">
                        <label class="txtleft">
                            <span class="name">${key.supplierName}订单总额:</span>
                            <span class="num supplierAmount">[#if productShowRate??]${coinConvert(supplierAmounts.get(key), productShowRate, false, true) }[#else]￥${currency(supplierAmounts.get(key))}元[/#if]</span>
                        </label>
                        <label class="txtright">
	                    [#if supplierAmounts.get(key) gte key.freeFreight]
                            (满[#if productShowRate??]${coinConvert((key.freeFreight)!0, productShowRate, false, true) }[#else]${(key.freeFreight)!0}元[/#if]包邮)
                        [#else]
                            [#if productShowRate??]
	                    	(不满${coinConvert((key.freeFreight)!0, productShowRate, false, true) }，收取<span class="num">${coinConvert((key.freight)!0, productShowRate) }</span>积分抵扣运费)
                            [#else]
	                       (不满${(key.freeFreight)!0}元，收取运费<span class="num">${(key.freight)!0}</span>元)
                            [/#if]
                        [/#if]
                        </label>
                    </li>
                [/#if]
            [/#list]
                </ul>
            </div>
        </div>

        <p>
            积分合计抵扣
            <span>-[#if productShowRate??]${coinConvert(order.coinAmount, productShowRate) }[#else]${currency(order.coinAmount, true, false)}[/#if]</span>
        </p>
        <p [#if (member.companyId?? && member.companyId.hasWhitebar?? && member.companyId.hasWhitebar)] [#else]style="display:none;" [/#if]>
            白条抵扣
            <span>-[#if productShowRate??]${coinConvert(order.whiteBarAmount, productShowRate) }[#else]${currency(order.whiteBarAmount, true, false)}[/#if]</span>
        </p>
        <p>
            优惠券抵扣
            <span>-[#if productShowRate??]${coinConvert(order.couponDiscount, productShowRate) }[#else]${currency(order.couponDiscount, true, false)}[/#if]</span>
        </p>

        <p>
            活动优惠
            <span>-[#if productShowRate??]${coinConvert(order.promotionDiscount, productShowRate) }[#else]${currency(order.promotionDiscount, true, false)}[/#if]</span>
        </p>
        <p class="total" [#if member.companyId?? && member.companyId.cashPayFlag?? && member.companyId.cashPayFlag == 0] style="display: none;" [/#if]>
            实付款
            <input type="hidden" id="_amountPayable" value="${order.amountPayable}">
            <span >${currency(order.amountPayable, true, false)}</span>
        </p>
    </section>

    <div class="bottom">
        <input type="button" name="" id="submit" value="立即付款" class="btn_submit_long" />
    </div>
</div>



		[#include "./wechat/include/set_payPasswords.ftl" /]

<script>
    //运费明细
    $('#myOrderPage .FreightTxt').click(function(e) {
        $('.freightMask').slideToggle(200);
        $(this).find('.img').hasClass('up') == true ? $(this).find('.img').removeClass('up').addClass("down") : $(this).find('.img').removeClass("down").addClass('up');
    })

    var maxstrlen = 30;
    checkWord($("#memo")[0]);
    //检查文本
    function checkWord(c) {
        len = maxstrlen;
        var str = c.value;
        myLen = getStrleng(str);
        var wck = document.getElementById("lengthLeft");
        if(myLen > len * 2) {
            c.value = str.substring(0, i - 1);
        }
        wck.innerHTML = Math.floor((len * 2 - myLen) / 2) > 0 ? Math.floor((len * 2 - myLen) / 2) : 0;
    }
    //计算长度
    function getStrleng(str) {
        myLen = 0;
        i = 0;
        for(;
                (i < str.length) && (myLen <= maxstrlen * 2); i++) {
            if(str.charCodeAt(i) > 0 && str.charCodeAt(i) < 128)
                myLen++;
            else
                myLen += 2;
        }
        return myLen;
    }
</script>
</body>
</html>