<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<title>酒店图片</title>
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">
		<script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/plugins/Swiper-3.4.2/css/swiper.css"/>
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/lifeService.css" />
   		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
		<script src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js"></script>
		<script src="${base}/resources/wechat/plugins/Swiper-3.4.2/js/swiper.jquery.min.js"></script>
	</head>
	<body>
		<div class="public_top_header">
			<a href="javascript:history.go(-1);" class="return_back"></a> 
			酒店图片
		</div>
		<div class="photo_list_page">
			<div class="type">
				[#if hotelImages!=null && hotelImages?size>0]<a href="javascript:;" class="active">全部(${hotelImages?size})</a>[/#if]
				[#if outsideScene!=null && outsideScene?size>0]<a href="javascript:;">外景(${outsideScene?size})</a>[/#if]
				[#if roomScene!=null && roomScene?size>0]<a href="javascript:;">房间(${roomScene?size})</a>[/#if]
                [#if restaurantScene!=null && restaurantScene?size>0]<a href="javascript:;">餐饮(${restaurantScene?size})</a>[/#if]
                [#if insideScene!=null && insideScene?size>0]<a href="javascript:;">内景(${insideScene?size})</a>[/#if]
                [#if surroundscene!=null && surroundscene?size>0]<a href="javascript:;">周边(${surroundscene?size})</a>[/#if]
                [#if otherScene!=null && otherScene?size>0]<a href="javascript:;">其他(${otherScene?size})</a>[/#if]
			</div>
			<a href="javascript:history.go(-1)" class="fixed_order_link">立即预订</a>
			<div class="content">
                [#if hotelImages!=null && hotelImages?size>0]
                <section>
                <header>全部</header>
                <ul>
                        [#list hotelImages as pictureBean]
                            <li>
                                <img src="${pictureBean.URL}"/>
                                <p>${pictureBean.caption}</p>

                            </li>
                        [/#list]
                </ul>
                </section>
                [/#if]
            
                [#if outsideScene!=null && outsideScene?size>0]
                <section>
                <header>外景</header>
                <ul>
                        [#list outsideScene as pictureBean]
                            <li>
                                <img src="${pictureBean.URL}"/>
                                <p>${pictureBean.caption}</p>
                            </li>
                        [/#list]
                </ul>
                </section>
                [/#if]
            
            
                [#if roomScene!=null && roomScene?size>0]
                <section>
                <header>房间</header>
                <ul>
                        [#list roomScene as pictureBean]
                            <li>
                                <img src="${pictureBean.URL}"/>
                                <p>${pictureBean.caption}</p>
                            </li>
                        [/#list]
                </ul>
                </section>
                [/#if]
            
            
                [#if restaurantScene!=null && restaurantScene?size>0 ]
                <section>
                <header>餐饮</header>
                <ul>
                        [#list restaurantScene as pictureBean]
                            <li>
                                <img src="${pictureBean.URL}"/>
                                <p>${pictureBean.caption}</p>
                            </li>
                        [/#list]
                </ul>
                </section>
                [/#if]
            
            
                [#if insideScene!=null && insideScene?size>0]
                <section>
                <header>内景</header>
                <ul>
                        [#list insideScene as pictureBean]
                            <li>
                                <img src="${pictureBean.URL}"/>
                                <p>${pictureBean.caption}</p>
                            </li>
                        [/#list]
                </ul>
                </section>
                [/#if]
            
            
                [#if surroundscene!=null && surroundscene?size>0]
                <section>
                <header>周边</header>
                <ul>
                        [#list surroundscene as pictureBean]
                            <li>
                                <img src="${pictureBean.URL}"/>
                                <p>${pictureBean.caption}</p>
                            </li>
                        [/#list]
                </ul>
                </section>
                    [/#if]
            
            
                [#if otherScene!=null && otherScene?size>0]
                <section>
                <header>其他</header>
                <ul>
                        [#list otherScene as pictureBean]
                            <li>
                                <img src="${pictureBean.URL}"/>
                                <p>${pictureBean.caption}</p>
                            </li>
                        [/#list]
                </ul>
                </section>
                [/#if]
				
			</div>
			
            [#if hotelImages!=null && hotelImages?size>0]
            <div class="swiper-container">
			  	<div class="swiper-wrapper">
                    [#list hotelImages as pictureBean]
                        <div class="swiper-slide"><img src="${pictureBean.URL}"/></div>
					[/#list]
                </div>
			  	<div class="swiper-button-prev"></div>
    			<div class="swiper-button-next"></div>
    			<div class="swiper-pagination"></div>
			</div>
						    
			[/#if]
			
		</div>
		
		<script>
            var countTime = null;
			var bodyScroll = 0;
            var _fixedTop = $(".public_top_header")[0].getBoundingClientRect().height+$(".type")[0].getBoundingClientRect().height;
			var mySwiper = new Swiper('.swiper-container', {
				prevButton:'.swiper-button-prev',
				nextButton:'.swiper-button-next',
				pagination : '.swiper-pagination',
				paginationType:"fraction"
			})

            function setActiveStatus(){
				var $boxes = $(".content section");
                for(var i = 0; i<$boxes.length;i++){
                    if(bodyScroll >= $boxes[i].offsetTop - _fixedTop-10){
                        $(".type a").removeClass("active");
                        $(".type a:eq("+i+")").addClass("active");
                    }
                }
			}
			
			
			$(function(){
                //点击顶部分类定位
				$(".type a").click(function(){
                    $(this).siblings().removeClass("active").end().addClass("active");
					var _index = $(this).index();
					var _scrollTop = $("section:eq("+_index+")").offset().top - _fixedTop;
					$("html,body").stop().animate({scrollTop:_scrollTop},1000);
                    return false;
				})

                //页面滚动事件 顶部分类定位
                $(window).scroll(function(){
                    clearTimeout(countTime);
                    bodyScroll = document.documentElement.scrollTop || window.pageYOffset || document.body.scrollTop;
                    countTime = setTimeout(setActiveStatus,1200);
                })


                $("section li").click(function(){
					$(".swiper-container").addClass("show");
                    var slideObj = $(".swiper-slide");
                    var src = $(this).children("img").attr("src");

                    for(var i = 0; i < slideObj.length; i++){
                        if($(slideObj[i]).children("img").attr("src") == src){
                            mySwiper.slideTo(i);
                        }
                    }
				})
				
				$(".swiper-container").on("click",".swiper-slide",function(){
					if($(event.target).hasClass("swiper-slide")){
						$(".swiper-container").removeClass("show");
					}
				})
			})
			
		</script>
	</body>
</html>
