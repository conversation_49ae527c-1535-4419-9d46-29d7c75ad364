<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>互动社区</title>
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="format-detection" content="telephone=no" />
    <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js" ></script>
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/square.css" />
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/activity.css" />
    <link rel="stylesheet" href="${base}/resources/wechat/css/select.css">
    <link rel="stylesheet" href="${base}/resources/wechat/plugins/Swiper-3.4.2/css/swiper.min.css">
    <script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

    <script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/dropload.min.js" ></script>
    <script src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js"></script>
    <script src="${base}/resources/wechat/plugins/Swiper-3.4.2/js/swiper.min.js"></script>
    <script src="${base}/resources/wechat/es6/select.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/jquery.qqFace.js"></script>

</head>
[#assign current = "my" /]


<body class="square-body">
<div class="release-topic-main">
    <div class="public_top_header">
        <a href="javascript:history.go(-1);" class="return_back"></a>
        <strong>发布话题</strong>
    </div>
    <div class="release-topic-entry">
        <div class="title">
            <input id="title" type="text"  maxlength="20" placeholder="请输入标题">
        </div>
        <textarea id="content" placeholder="请输入您的留言" onkeyup="javascript:checkWord(this);" onmousedown="javascript:checkWord(this);"></textarea>
        <span class="number"><span id="lengthLeft">0</span>/1000</span>
        <div class="add">
            <div class="image">
                <form id="uploadForm" enctype="multipart/form-data">
                    <!-- 实际的选择文件input -->
                    <input type="file" name="file" accept="image/*" onchange="change(this)"/>
                    <input type="hidden" name="type" value="image"/>
                </form>
                <!-- 可视图标 -->
                <img src="${base}/resources/wechat/img/gift/icon_pic.png" alt="">
            </div>
            <img class="pic" id="show_face" src="${base}/resources/wechat/img/gift/icon_face.png" alt="">
            <img class="pic" id="private" src="${base}/resources/wechat/img/gift/icon_private.png" alt="">
        </div>

        <div class="picture-section">
        </div>

    </div>
    <div class="release-topic-options">
        <div class="module">
            <div>版块</div>
            <div class="module-select">选择</div>
        </div>
        [#--<div class="module">
            <div>谁可以看见</div>
            <div class="member">全部成员</div>
        </div>--]
    </div>
    <div class="release-topic-button">
        我要发布
    </div>
    <div class="select_box"></div>
    <input type="hidden" value="" id="frontModuleId">

</div>

<script>
    var moduleArr = JSON.parse('${moduleList}');
    var moduleNameArr = [];
    $(function () {

        for (let index in moduleArr) {
            moduleNameArr[index] = moduleArr[index].name;
        }

        $("#private").on("click", function () {
            let src = $(this).attr("src");
            if (src.indexOf("icon_private.png") != -1) {
                $(this).attr("src", $(this).attr("src").replace("icon_private.png", "icon_private_selected.png"));
            } else {
                $(this).attr("src", $(this).attr("src").replace("icon_private_selected.png", "icon_private.png"));
            }
        });

        $(".release-topic-button").on('click',function(){
        	// 1.校验版块
            var frontModuleId = $("#frontModuleId").val();
            if (frontModuleId == '') {
                layer.open({
                    content: '请选择对应版块'
                    ,skin: 'msg'
                    ,time: 1
                });
                return;
            }

        	var title = $("#title").val().trim();
        	// 2.校验标题
            if (title.length == 0 || title.length > 20) {
                layer.open({
                    content: '标题长度需要大于0，小于20！'
                    ,skin: 'msg'
                    ,time: 1
                });
                return;
            }


        	// 3.校验内容
            var getValue = $("#content").val();
            if (getValue.length == 0 || getValue.length > 1000) {
                layer.open({
                    content: '话题内容长度需要大于0，小于1000！'
                    ,skin: 'msg'
                    ,time: 1
                });
                return;
            }
            var content = ((getValue.replace(/<(.+?)>/gi, "&lt;$1&gt;")).replace(/ /gi, "&nbsp;")).replace(/\n/gi, "<br>");
            console.log("=====>>>>\n",content);
        	// 4.获取图片
            var files = "";
            $(".picture-area >input").each(function (i, o) {
                console.log(i, o);
                if (i != 0) {
                    files = files + ',' + $(o).val();
                } else {
                    files = files + $(o).val();
                }
            });
            if(!files){//过滤null undefined
            	files="";
            }

        	// 5.判断是否匿名
            var creatorAnonymous = 0;
            let src = $("#private").attr("src");
            if (src.indexOf("icon_private_selected.png") != -1) {
                creatorAnonymous = 1;
            }

            $.ajax({
                url: "${base}/wx/topicPublish/publish.jhtml",
                data: {frontModuleId: frontModuleId, title: title,content: content, files: files,
                        creatorAnonymous: creatorAnonymous, visibleToAll: 1,allowAnonymousComment:0, pushMessage: 0,
                        publishType: 1},
                type: "POST",
                success: function (data) {
                    console.log(data);
                    if (data.type == "success") {
                        window.location.href = "${base}/wx/topic/toPage.jhtml";
                    } else {
                        // 执行失败
                        alert("操作失败！");
                    }
                }
            });

        });



        var hgS1 = new selectSwiper({
            el: ".select_box",
            data: moduleNameArr,
            init: function (index) {
                if (index !== -1) {
                    $(".module-select").html(this.data[index]);
                }
            },
            okFunUndefind: function () {
                alert("必须选择一项");
                return false;
            },
            // 选中后回调
            okFun: function (index) {
                if (index === -1) {
                    $(".module-select").html("选择");
                }else{
                    $(".module-select").html(this.data[index]);
                    // 找到当前值对应的moduleId 写入对应的input,返回的index和数组中一致，可以直接获取
                    var moduleInfo = moduleArr[index];
                    $("#frontModuleId").val(moduleInfo.id);

                    // 判断匿名图标是否需要显示 -- 每次换版块都将匿名符号重置
                    $("#private").attr("src", $("#private").attr("src").replace("icon_private_selected.png", "icon_private.png"));
                    if (moduleInfo.allowAnonymousPublish == 1) {
                        $("#private").css("display", "block");
                    } else {
                        $("#private").css("display", "none");
                    }
                }
            },
            closeFun: function () {
                // console.log("取消");
            },
        });
        $(".module-select").on("click", function () {
            hgS1.openSelectSwiper();
        });

        getQQimg($('#show_face'),{"assign":'content'});
        /*$('#show_face').qqFace({
            id: 'facebox',
            assign: 'content',
            path: '${base}/resources/wechat/img/face/' //表情存放的路径
        });*/

        $('#show_face').bind("click",function () {
            if(!$('#facebox')||$('#facebox').css('display') === 'none'){
                $('.picture-section').show();
            }else{
                $('.picture-section').hide();
            }
        });

        //点击任意地方下拉消失
        $(window).on("touchstart", function(event) {
            event = event ? event : window.event;
            target = event.target || event.srcElement;
            // var _con1 = $('.comment'); // 设置目标区域
            // var _con2 = $('.bottom_talker_section'); // 设置目标区域
            // if(!_con2.is(target) && _con2.has(target).length === 0 && !_con1.is(target) && _con1.has(target).length === 0) {
            //     _con2.hide();
            //     $("#input_text").blur();
            // }
        });

        /*让textarea自动增高*/
        var textarea = document.getElementById('content');
        $("#content").on("keyup", function() {
            textarea.style.height = textarea.scrollHeight + 'px';
        })


    });

    //文本字数限制
    var maxstrlen = 1000;
    checkWord($("#content")[0]);
    //检查文本
    function checkWord(c) {
        len = maxstrlen;
        var str = c.value;
        myLen = getStrleng(str);
        var wck = document.getElementById("lengthLeft");
        if(myLen > len * 2) {
            c.value = str.substring(0, i - 1);
        }
        wck.innerHTML = Math.floor((len * 2 - myLen) / 2) > 0 ? Math.floor((len * 2 - myLen) / 2) : 0;
    }
    //计算长度
    function getStrleng(str) {
        myLen = 0;
        i = 0;
        for(;
                (i < str.length) && (myLen <= maxstrlen * 2); i++) {
            if(str.charCodeAt(i) > 0 && str.charCodeAt(i) < 128)
                myLen++;
            else
                myLen += 2;
        }
        return myLen;
    }

    // JQ方法加载完。。。

    function change(f){
        $('.picture-section').show();
        //限制9张图片
        if ($(".picture-section").find('.picture-area').length>8) {
            layer.open({
                content: '最多限制9张图片！'
                ,skin: 'msg'
                ,time: 1
            });
            return;
        }
        // 调用后台接口
        var formData = new FormData($('#uploadForm')[0]);
        var imagePath;
        var imageUrl;
        $.ajax({
            type: 'post',
            url: "${base}/wx/file/upload.jhtml",
				data: formData,
				cache: false,
				processData: false,
				contentType: false,
			}).success(function (data) {
				console.log(data)
				// 拿到返回的url
                imagePath = data.path;
                imageUrl = data.url;

                let html = `<div class="picture-area"><input type="hidden" value="`+imagePath+`"><span class="picture-close">×</span><img src="`+imageUrl+`" alt=""></div>`;
				$(".picture-section").append(html);
				//删除图片
				$(".picture-close").on("click", function () {
					$(this).closest('.picture-area').remove()
				});
			}).error(function () {
				alert("上传失败");
			});
		}
</script>
</body>
</html>
