<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>中秋国庆</title>
		<meta name="format-detection" content="telephone=no" />
		<meta content="email=no" name="format-detection">
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="black">
        <script type="text/javascript" src="${base}/resources/greetingCard/js/flexible.js" ></script>
        <link rel="stylesheet" href="${base}/resources/greetingCard/css/swiper.css?v=1.0.0" />
        <link rel="stylesheet" href="${base}/resources/greetingCard/css/animate.css?v=1.0.0" />
        <link rel="stylesheet" type="text/css" href="${base}/resources/greetingCard/css/common.css?v=1.0.0" />
        <link rel="stylesheet" type="text/css" href="${base}/resources/greetingCard/template/midAutumnFestivalAndNationDay/css/style.css" />
        <script type="text/javascript" src="${base}/resources/greetingCard/js/jquery-1.9.1.js" ></script>
		<script type="text/javascript" src="${base}/resources/greetingCard/js/swiper.jquery.min.js" ></script>
        <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
	</head>
	<body class="midAutumnAndNationPage">
		<div class="loading_section">
			<div class="loading"></div>
		</div>
		<div class="public_top_header">
			 [#if flag!=null && flag==0]<a href="${base}/dynamic/index.jhtml" class="return_back"></a>
           [#else]
			<a href="javascript:history.go(-1);" class="return_back"></a>
			 [/#if]
			我的贺卡
		</div>

        <div id="music" class="music" data-frequency="first">
            <audio id="music-audio" class="audio" loop=""  autoplay="autoplay" preload="">
                <source src="${base}/resources/greetingCard/music/midNation.mp3" type="audio/mpeg"></source>
            </audio>
            <div class="control">
                <div class="control-after"></div>
            </div>
        </div>
		
		<!-- Swiper -->
	    <div class="swiper-container">
	        <div class="swiper-wrapper">
                <div class="swiper-slide slide_1 " >
					<img class="img_dounble animated" src="${base}/resources/greetingCard/template/midAutumnFestivalAndNationDay/img/double.png">
	            </div>
	            <div class="swiper-slide slide_2 slide_item " img-url="bg4.jpg">
					<div class="header animated">
                        <img src="${base}/resources/greetingCard/template/midAutumnFestivalAndNationDay/img/headertitle.png">
                       <!--<p>中秋国庆</p>
                       <p>双节同庆</p>-->
					</div>
	            	<div class="textarea animated">
                        亲爱的佛山移动人：
                       <p class="marin-top24">皓月当空盼中秋, 金桂飘香迎华诞。佳节将至, 举国同欢, 公司向辛勤工作在各个岗位的佛山移动人和背后默默支持的家属们致以良好的祝愿和亲切的慰问！</p>
					   <p>历览来时路, 史册载勋业。公司取得的每一个成就, 均凝聚着多少干部员工无私的奉献和一生的追求；每一份沉甸甸的丰收喜悦里, 都有每一个员工努力的汗水和卓越的智慧。我们每一位员工都在为构筑创世界一流“力量大厦”添砖加瓦, 每个人都了不起！</p>
	            	</div>
	            </div>
                <div class="swiper-slide slide_3 slide_item" img-url="bg2.png">
                    <div class="header animated">
                        <img src="${base}/resources/greetingCard/template/midAutumnFestivalAndNationDay/img/headertitle.png">
                    </div>
                    <div class="textarea animated">
                        <p>希望大家继续落实 “责任、拼搏、争先、创新”四个理念, 凝心聚力、奋勇拼搏, 为公司推进数智化转型, 加快高质量发展做出新的贡献！ 以优异的成绩向党的二十大献礼！</p>
                        <p>海上生明月, 天涯共此时。值此中秋、国庆佳节来临之际, 公司为您送上一份心意, 并致以最诚挚的感谢和最深切的祝福：</p>

                        <p class="">祝节日快乐, 身体健康, 生活美满, 阖家幸福。</p>
                        <p>衷心祝愿我们伟大的祖国繁荣、昌盛、富强。</p>
						<div class="signed clearfix marin-top24">
							<div class="left_content ">
								<p>
                                    中国移动通信集团广东有限公司
								</p>
								<p>
                                    佛山分公司总经理:
								</p>
								<img src="${base}/resources/greetingCard/template/midAutumnFestivalAndNationDay/img/c.png">
							</div>
                            <div class="right_content">
                                <p>
                                    中国移动通信集团工会佛山分公司
                                </p>
                                <p>
                                    委员会工会主席:
                                </p>
                                <img src="${base}/resources/greetingCard/template/midAutumnFestivalAndNationDay/img/w.png">
                            </div>
						</div>
                    </div>
                </div>
                <div class="swiper-slide  slide_4"  img-url="bg1.png">
                    <div class="btnOption animated" onclick="goShopping()">
                        前往选购
                    </div>
                </div>
	            [#assign phoneTitle="中秋国庆双节快乐" /]
	            
	        </div>
	        <!-- Add Pagination -->
	        <div class="swiper-pagination"></div>
		</div>
	    <div class="to_next_page animated infinite slideInUpS"></div>
	    
	   <script type="text/javascript" src="${base}/resources/greetingCard/js/swiper.jquery.min.js" ></script>
	    <!-- Initialize Swiper -->
	    <script>
            document.onreadystatechange = function(){
                if(document.readyState == "complete"){
                    $(".loading_section").hide();
                }
            };
            $(function(){
                //音乐控制
                var audio = document.getElementById('music-audio');
                $("#music").click(function(){
                    var $this = $(this);
                    if(audio!==null){
                        if(!audio.paused){
                            $this.addClass("stopped");
                            audio.pause();// 这个就是暂停//audio.play();// 这个就是播放
                        }else{
                            $this.removeClass("stopped");
                            audio.play();
                        }
                    }
                });
                //audio在ios和安卓无法自动播放问题
                document.addEventListener("WeixinJSBridgeReady",function () {
                    document.getElementById('music-audio').play();
                });
                document.addEventListener("touchstart",function () {
                    if($("#music").attr("data-frequency")==='first'){
                        document.getElementById('music-audio').play();
                        $("#music").attr("data-frequency","second")
                    }

                });
                setBackground(".slide_2");
                $(".slide_1").addClass("bounceInUp");
                $(".slide_1").addClass("animated");
                $(".slide_1 .img_dounble").show().addClass("slideInDown");
                $(".slide_2 .header").hide().removeClass("slideInLeft");
                $(".slide_2 .textarea").hide().removeClass("bounceInUp");
                $(".showBoxPage .img_1").hide().removeClass("slideInDown");
            });


		    var swiper = new Swiper('.swiper-container', {
		        pagination: '.swiper-pagination',
		        direction: 'vertical',
		        loop: false,
		        paginationClickable: true,
                onTransitionEnd: function(swiper){
                    if(swiper.activeIndex === 0){
                        setBackground(".slide_2");
                        $(".slide_1").addClass("bounceInUp");
                        $(".slide_1").addClass("animated");
                        $(".slide_1 .img_dounble").show().addClass("slideInDown");
                        $(".slide_2 .header").hide().removeClass("slideInLeft");
                        $(".slide_2 .textarea").hide().removeClass("bounceInUp");
                        $(".slide_4 .btnOption").hide().removeClass("noneTodispaly");
                        $(".showBoxPage .img_1").hide().removeClass("slideInDown");
                    }else if(swiper.activeIndex === 1){
                        $(".slide_1 .img_dounble").hide().removeClass("slideInDown");
                        $(".slide_2 .header").show().addClass("slideInLeft");
                        $(".slide_2 .textarea").show().addClass("bounceInUp");
                        $(".slide_3 .header").hide().removeClass("slideInLeft");
                        $(".slide_3 .textarea").hide().removeClass("bounceInUp");
                        $(".showBoxPage .img_1").hide().removeClass("slideInDown");
                        setBackground(".slide_2");
                        setBackground(".slide_3");
                    }else if(swiper.activeIndex === 2){
                        $(".slide_2 .header").hide().removeClass("slideInLeft");
                        $(".slide_2 .textarea").hide().removeClass("bounceInUp");
                        $(".showBoxPage .img_1").hide().removeClass("slideInDown");
                        $(".slide_3 .header").show().addClass("slideInLeft");
                        $(".slide_3 .textarea").show().addClass("bounceInUp");
                        $(".slide_4 .btnOption").hide().removeClass("noneTodispaly");
                        setBackground(".slide_3");
                        setBackground(".slide_4");
                    }else if(swiper.activeIndex === 3){
                        setBackground(".slide_4");
                        $(".slide_3 .header").hide().removeClass("slideInLeft");
                        $(".slide_3 .textarea").hide().removeClass("bounceInUp");
                        $(".slide_1 .img_dounble").hide().removeClass("slideInDown");
                        $(".slide_4 .btnOption").show().addClass("noneTodispaly");
                        $(".showBoxPage .img_1").show().addClass("slideInDown");
                    }
		            //loop 为ture
                    // if(swiper.activeIndex === 1||swiper.activeIndex === 5){
                     //    setBackground(".slide_2");
			       	// 	$(".slide_1").addClass("bounceInUp");
			        	// $(".slide_1 .img_dounble").show().addClass("zoomInUp");
			        	// $(".slide_2 .header").hide().removeClass("slideInLeft");
			        	// $(".slide_2 .textarea").hide().removeClass("bounceInUp");
                     //    $(".slide_4").removeClass("bounceInUp");
                     //    $(".slide_4 .btnOption").hide().removeClass("noneTodispaly");
			        	// $(".showBoxPage .img_1").hide().removeClass("slideInDown");
                    //
                    // }else if(swiper.activeIndex === 2){
			        	// $(".slide_1").removeClass("bounceInUp");
			        	// $(".slide_1 .img_dounble").hide().removeClass("zoomInUp");
			       	// 	$(".slide_2 .header").show().addClass("slideInLeft");
			        	// $(".slide_2 .textarea").show().addClass("bounceInUp");
                     //    $(".slide_3 .header").hide().removeClass("slideInLeft");
                     //    $(".slide_3 .textarea").hide().removeClass("bounceInUp");
			        	// $(".showBoxPage .img_1").hide().removeClass("slideInDown");
                     //    setBackground(".slide_3")
                    // }else if(swiper.activeIndex === 3){
                     //    $(".slide_2 .header").hide().removeClass("slideInLeft");
                     //    $(".slide_2 .textarea").hide().removeClass("bounceInUp");
                     //    $(".showBoxPage .img_1").hide().removeClass("slideInDown");
                     //    $(".slide_3 .header").show().addClass("slideInLeft");
                     //    $(".slide_3 .textarea").show().addClass("bounceInUp");
                     //    $(".slide_4").removeClass("bounceInUp");
                     //    $(".slide_4 .btnOption").hide().removeClass("noneTodispaly");
                     //    setBackground(".slide_4")
                    // }else if(swiper.activeIndex === 4||swiper.activeIndex === 0){
                     //    $(".slide_3 .header").hide().removeClass("slideInLeft");
                     //    $(".slide_3 .textarea").hide().removeClass("bounceInUp");
                     //    $(".slide_1").removeClass("bounceInUp");
                     //    $(".slide_1 .img_dounble").hide().removeClass("zoomInUp");
                     //    $(".slide_4").addClass("bounceInUp");
                     //    $(".slide_4 .btnOption").show().addClass("noneTodispaly");
                     //    $(".showBoxPage .img_1").show().addClass("slideInDown");
                    // }
			    }
		    });
		    function goShopping() {
		        location.href="${base}/login/mobileLogin.jhtml?companyId=2471"
            }
            //给元素设置背景图片
            function setBackground(ele) {
                let bgurl = $(ele).css("backgroundImage");
                if(bgurl===""||bgurl==="none"){
                    let imgUrl  = $(ele).attr("img-url");
                    $(ele).css("background-image","url('${base}/resources/greetingCard/template/midAutumnFestivalAndNationDay/img/"+imgUrl+"')")
                }
            }
	    </script>
	</body>
</html>
