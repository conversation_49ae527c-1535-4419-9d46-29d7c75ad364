<input type="hidden" id="totalPages" value="${page.totalPages}"/>
[#if page.total> 0]
    <ul class="consume_list">
        [#list page.content as order]
            <li>
                <div class="left">
                    <p class="title">充值${order.toatlAmount}-${order.member.username}</p>
                    <p class="time">${order.createDate?string("yyyy-MM-dd HH:mm:ss")}</p>

                </div>
                <div class="right">
                    <p class="coin">-[#if productShowRate??]${coinConvert(order.toatlAmount, productShowRate, true, false) }[#else]${order.toatlAmount}[/#if]</p>
                    [#if order.orderStatus == 'cancelled']
                        <p class="type">已取消</p>
                    [#elseif order.orderStatus == 'completed']
                        <p class="type">充值成功</p>
                    [#elseif order.orderStatus == 'unpaid']
                        <a class="type" href="${base}/paysRedirect/payment.jhtml?sn=${order.sn}&type=0" >去支付</a>
                    [#else]
                        <p class="type">交易中</p>
                    [/#if]
                </div>
            </li>
        [/#list]
    </ul>
[#else]
    <div class="public_empty_section [#if comPersonConf && com_person_EmptyBg != null && com_person_EmptyBg != ""] ${com_person_EmptyBg!}[#else] Public_Bg[/#if]">
        <p class="tip">这里空空如也，快去充值吧~</p>
        <a href="${base}/qrcode/payment/coinCharge.jhtml?sku=${sku}" class="pub_link_theme">去充值</a>
    </div>
[/#if]