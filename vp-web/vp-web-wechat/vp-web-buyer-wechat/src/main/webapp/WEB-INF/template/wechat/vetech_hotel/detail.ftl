<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<title>酒店预订</title>
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">
		<script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/plugins/swiper-4.5.0/css/swiper.min.css" />
	    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/lifeService.css" />
		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
		<script src="${base}/resources/wechat/plugins/raty-2.5.2/jquery.raty.min.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js"></script>
		<script src="${base}/resources/wechat/plugins/swiper-4.5.0/js/swiper.min.js"></script>
        <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js" type="text/javascript"></script>
		 <!--日期选择-->
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/plugins/mobiscroll-Javascript/css/mobiscroll.javascript.min.css" />
		<script src="${base}/resources/wechat/plugins/mobiscroll-Javascript/js/mobiscroll.javascript.min.js"></script>
        <script type="text/javascript" src="${base}/resources/wechat/es6/goMap.js"></script>
	</head>
	<body class="no_fixed_top">
		<div class="hotel_detail_page">
           [#if hotelImages!=null]
            <div class="img_banner" id="img_banner">
                <div class="transparent">
                    [#--[#if applyId?? ]--]
                    [#--<a href="${base}/ctrip_hotel/list.jhtml?applyId=${applyId}&fromCityName=${fromCityName}&arrivalDate=${arrivalDate}&departureDate=${departureDate}&agentMemberId=${agentMemberId}&isPublic=${isPublic}" class="return_back"></a>--]
                    [#--[#else]--]
                    [#--<a href="${base}/ctrip_hotel/list.jhtml?fromCityName=${fromCityName}&arrivalDate=${arrivalDate}&departureDate=${departureDate}&isPublic=${isPublic}" class="return_back"></a>--]
                    [#--[/#if]--]
                        <a href="javascript:void(0);" onclick="goBack(event)" class="return_back"></a>
                </div>
                <div class="swiper-container">
                    <div class="swiper-wrapper">
                         [#list hotelImages as hotelImage]
                              [#if  hotelImage_index<=6]
                                <div class="swiper-slide" style='background-image: url("${hotelImage.tpdz}")'></div>
                              [/#if]
                         [/#list]
                    </div>
                </div>
                <div class="desc">
                    <div class="num">${hotelImages?size}张</div>
                     ${hotel.hotelName}
                </div>
            </div>
		   [#else]
		        <div class="img_banner">
                <div class="transparent">
                    [#--[#if applyId?? ]--]
                    [#--<a href="${base}/ctrip_hotel/list.jhtml?applyId=${applyId}&fromCityName=${fromCityName}&arrivalDate=${arrivalDate}&departureDate=${departureDate}&agentMemberId=${agentMemberId}&isPublic=${isPublic}" class="return_back"></a>--]
                    [#--[#else]--]
                    [#--<a href="${base}/ctrip_hotel/list.jhtml?fromCityName=${fromCityName}&arrivalDate=${arrivalDate}&departureDate=${departureDate}&isPublic=${isPublic}" class="return_back"></a>--]
                    [#--[/#if]--]
                    <a href="javascript:void(0);" onclick="goBack(event)" class="return_back"></a>
                </div>
                <div class="swiper-container">
                    <div class="swiper-wrapper">
                          <div class="swiper-slide" style='background-image: url("${base}/resources/wechat/img/hotel/hotel_default.jpg")'></div>
                    </div>
                </div>
                <div class="desc">
                     ${hotel.hotelName}
                </div>
            </div>
           [/#if]

            <a class="line line_2">
                <p>${hotel.address}<span>${hotel.district}  [#if hotel.businessZone!=null][#if hotel.businessZone?index_of(",")]|${hotel.businessZone?replace(",","")}[#else]|${hotel.businessZone}[/#if][/#if]</span></p>
            </a>
			
			<div class="line line_1">
				<span class="score">[#if  hotel.commentScore==null ||hotel.commentScore==0]4.5[#else]${(hotel.commentScore)?string('0.0')}[/#if]</span>/5分
				[#if hotel.commentScore!=null]
							[#if hotel.commentScore==4]
                                 <span class="text">"设施完善"</span>
                            [/#if]
                             [#if hotel.commentScore==4.1]
                                 <span class="text">"服务周到"</span>
                            [/#if]
                             [#if hotel.commentScore==4.2]
                                 <span class="text">"性价比高"</span>
                            [/#if]
                            [#if hotel.commentScore==4.3]
                                 <span class="text">"位置很好"</span>
                            [/#if]
                            [#if hotel.commentScore==4.4]
                                 <span class="text">"强烈推荐"</span>
                            [/#if]
                            [#if hotel.commentScore==4.5]
                                 <span class="text">"不错"</span>
                            [/#if]
                              [#if hotel.commentScore==4.6]
                                 <span class="text">"好"</span>
                            [/#if]
                            [#if hotel.commentScore==4.7]
                                 <span class="text">"很好"</span>
                            [/#if]
                            [#if hotel.commentScore==4.8]
                                 <span class="text">"棒"</span>
                            [/#if]
                             [#if hotel.commentScore==4.9]
                                 <span class="text">"超棒"</span>
                            [/#if]
                            [#if hotel.commentScore==5]
                                 <span class="text">"非常棒"</span>
                            [/#if]
							[/#if]
                <div class="stars" data-score="[#if  hotel.commentScore==null ||  hotel.commentScore==0]4.5[#else]${ hotel.commentScore}[/#if]"></div>
                <p class="distance flexbox justify-content-space-between">
                    <a id="distance-isWxPro" style="display: none" class="distance-item" onclick="toMap('${hotel.hotelName}','${hotel.latitude}','${hotel.longitude}')">
                        <img   src="${base}/resources/wechat/img/wechatConsume/other/daohang.png">
                        地图
                    </a>
                    <a id="distance-isnotWxPro" style="display: none" class="distance-item"  href='https://apis.map.qq.com/tools/poimarker?type=0&marker=coord:${hotel.latitude},${hotel.longitude};title:${hotel.hotelName};addr:${hotel.address}&key=ECWBZ-ES7KU-YSEVC-24FWT-R2BDK-NHB3Z&referer=fliplus'>
                        <img   src="${base}/resources/wechat/img/wechatConsume/other/daohang.png">
                        地图
                    </a>
                    <a id="distance-isWxPro"  class="distance-item distance-item-car" onclick="goCard('${hotel.hotelName}','${hotel.latitude}','${hotel.longitude}','${member.name}','${member.mobileDec}')">
                        <img   src="${base}/resources/wechat/img/didi/dace_icon.png">
                        打车
                    </a>
                </p>
			</div>

            <a class="line line_5 arrow">
                [#if hotel.openYear!=null]
                    <p>${hotel.openYear.substring(0,4)}年开业</p>
                [/#if]
                 <!-- 酒店设施-->

					[#if applyId?? ]
					 <span class="text_right" onclick="location.href='hotelIntroduce.jhtml?applyId=${applyId}&hotelId=${hotel.hotelId}&arrivalDate=${arrivalDate}&departureDate=${departureDate}&agentMemberId=${agentMemberId}&isPublic=${isPublic}'">详情/设施</span>
				    [#else]
					 <span class="text_right"  onclick="location.href='hotelIntroduce.jhtml?hotelId=${hotel.hotelId}&arrivalDate=${arrivalDate}&departureDate=${departureDate}&isPublic=${isPublic}'">详情/设施</span>
				   [/#if]
            </a>

			
			<div class="line line_4 arrow">
			  <div>
				<div class="time">
			        <label>入住</label>
					<input type="text" name="arrivalDate" id="arrivalDate" value="${arrivalDate}" class="input_text" readonly="readonly" />
				       
				</div>
				<div class="time">
					<label>离开</label>
				    <input type="text" name="departureDate" id="departureDate" value="${departureDate}" class="input_text" readonly="readonly" />
				</div>
				<span class="total">共1晚</span>
				</div>
			</div>
			[#assign hasShow = false]
			[#if rooms??&&rooms!=null && rooms?size>0]
				[#assign roomList=rooms]
				<ul class="room_ul">
				[#list roomList as roomInfo]
                  [#if roomInfo!=null]
					[#assign levelPrices = ""]
					[#assign msg = ""]
			   		[#assign showFlag = -1]
			   		[#assign isHidden=false]
			   		[#assign sellingPrice = (roomInfo.fxzdj?number)?ceiling]

					[#if sellingPrice?? && priceRate??]<!-- 企业价格 -->
							[#assign sellingPrice = (sellingPrice + sellingPrice * priceRate)?ceiling]
				   [/#if]

			   		[#if tripLevelConfig??]
						[#if tripLevelConfig.hotelFlag]
							[#if tripLevelConfig.firstLevelCities?? && tripLevelConfig.firstLevelCities?index_of(fromCityName!"null") gte 0]
								[#assign levelPrices = levelPrices + ", 1_" + tripLevelConfig.firstLevelPrice + "_" + (tripLevelConfig.firstLevelPrice lt sellingPrice)]
								[#if tripLevelConfig.firstLevelPrice?? && tripLevelConfig.firstLevelPrice lt sellingPrice]
									[#assign msg = msg + "平均每晚房费超标、"]
								[/#if]
								[#if tripLevelConfig.firstLevelStar?? && tripLevelConfig.firstLevelStar gt 0 && tripLevelConfig.firstLevelStar lt hotel.starRate]
									[#assign msg = msg + "酒店星级超标、"]
								[/#if]
							[#elseif tripLevelConfig.secondLevelCities?? && tripLevelConfig.secondLevelCities?index_of(fromCityName!"null") gte 0]
								[#assign levelPrices = levelPrices + ", 2_" + tripLevelConfig.secondLevelPrice]
								[#if tripLevelConfig.secondLevelPrice?? && tripLevelConfig.secondLevelPrice lt sellingPrice]
									[#assign msg = msg + "平均每晚房费超标、"]
								[/#if]
								[#if tripLevelConfig.secondLevelStar?? && tripLevelConfig.secondLevelStar gt 0 && tripLevelConfig.secondLevelStar lt hotel.starRate]
									[#assign msg = msg + "酒店星级超标、"]
								[/#if]
							[#elseif tripLevelConfig.thirdLevelCities?? && tripLevelConfig.thirdLevelCities?index_of(fromCityName!"null") gte 0]
								[#assign levelPrices = levelPrices + ", 3_" + tripLevelConfig.thirdLevelPrice]
								[#if tripLevelConfig.thirdLevelPrice?? && tripLevelConfig.thirdLevelPrice lt sellingPrice]
									[#assign msg = msg + "平均每晚房费超标、"]
								[/#if]
								[#if tripLevelConfig.thirdLevelStar?? && tripLevelConfig.thirdLevelStar gt 0 && tripLevelConfig.thirdLevelStar lt hotel.starRate]
									[#assign msg = msg + "酒店星级超标、"]
								[/#if]
							[#else]
								[#assign levelPrices = levelPrices + ", 4_" + tripLevelConfig.otherLevelPrice]
								[#if tripLevelConfig.otherLevelPrice?? && tripLevelConfig.otherLevelPrice lt sellingPrice]
									[#assign msg = msg + "平均每晚房费超标、"]
								[/#if]
								[#if tripLevelConfig.otherLevelStar?? && tripLevelConfig.otherLevelStar gt 0 && tripLevelConfig.otherLevelStar lt hotel.starRate]
									[#assign msg = msg + "酒店星级超标、"]
								[/#if]
							[/#if]

							[#if msg != ""]
								[#if tripLevelConfig.hotelExceedConctrol == "unLimited"]
								[#assign showFlag = 0]
								[#elseif tripLevelConfig.hotelExceedConctrol == "orderLimit"]
								[#assign showFlag = 1]
								[#elseif tripLevelConfig.hotelExceedConctrol == "displayLimit"]
								[#assign showFlag = 2]
								[/#if]
							[#else]
								[#assign showFlag = -1]
							[/#if]
						[/#if]
					[/#if]
					[#if showFlag != 2]
						[#if company!=null && company.invoiceFlag!=null]
                            [#if company.invoiceFlag==1] <!-- 只要平台统一开票的房间-->
                                [#if roomInfo.invoiceMode == null || roomInfo.invoiceMode != 3]
                                   [#assign isHidden=true]
                                [/#if]
                            [/#if]
                            [#if company.invoiceFlag==2] <!-- 只要酒店自取的房间-->
                               [#if payType=='PP']
                                    [#if roomInfo.invoiceMode?? && roomInfo.invoiceMode != 1]
                                      [#assign isHidden=true]
                                    [/#if]
                               [/#if]

                            [/#if]
                        [/#if]
[#--						roomInfo.commentScore)?ceiling--]
				<li [#if isHidden]style="display:none" [#else][#assign hasShow = true][/#if] levelPrices="${levelPrices}" ice="${(roomInfo.commentScore) }" invoiceType="${invoiceType}">
					<!-- room信息开始-->
					<div class="top">
                        <div class="desc_section" star="${hotel.starRate }">
                        <div class="img">

							[#if roomInfo.stdz!=null]
								<img src="${roomInfo.stdz}"/>
							[#else]
								<img src="${base}/resources/wechat/img/hotel/hotel_default.jpg"/>
							[/#if]

                        </div>

                        <div class="desc">
                            <p class="title">
								${roomInfo.fxmc}
                            </p>
                            <p>
								${roomInfo.cx}
                            </p>
                      </div>
                      </div>
                      <div class="right">
                            <div class="price_section">
                                <div class="price">￥<span>${sellingPrice}</span>起</div>
                            </div>
                        </div>
					</div>
				<!-- room信息结束-->
				[#list roomInfo.jgjhlb as roomInfo1]
					[#assign sellingPrice1 = (roomInfo1.srfj?number)?ceiling]
					[#if sellingPrice1?? && priceRate??]<!-- 企业价格 -->
[#--						(sellingPrice1 + sellingPrice1 * priceRate)?ceiling--]
					[#assign sellingPrice1 = (sellingPrice1 + sellingPrice1 * priceRate)?ceiling]
					[/#if]
					 <div class="bottom">
						<div class="left" >
							<p class="title">${roomInfo1.fyfxmc}</p>
							<p>
								[#if roomInfo.cx??]${roomInfo.cx}[#else ]${roomInfo1.fxBedType}[/#if]
								${roomInfo1.sfhz}
							</p>
							<p>
								[#if roomInfo1.gzms	=='22']
								<!-- 是否允许取消-->
								 <script type = "html/javascript" id = "toggle_li_info${roomInfo1.hotelId}" cancelPolicyMsg="限时取消">
                                        <span class= "layer_close_btn" > </span>
                                        <header style = "opacity: 1.07383;" >${roomInfo1.fyfxmc}</header>
                                        <div  class="simple_bottom_layer">
                                            <label>限时取消 </label>
                                             <div class= "value">
										         最晚取消时间  ${roomInfo1.zwqxsj}，逾期不可取消/变更，如未入住，酒店将扣除全额房费
                                            </div>
                                         </div>
                                    </script>
								<!-- 是否允许取消-->
								[/#if]
								[#if roomInfo1.gzms=='21']
									<span class="clickable">不可取消</span>
								[/#if]
								[#if roomInfo1.gzms=='22']
									<span class="clickable">限时取消</span>
								[/#if]
								[#if roomInfo1.gzms=='23']
									<span class="clickable">免费取消</span>
								[/#if]
								[#if roomInfo1.ljqr == "false"]
									<span class="clickable">非即时确认</span>
								[#else ]
									<span class="clickable">即时确认</span>
								[/#if]
								<span class="clickable">
                                            [#if roomInfo1.invoiceMode??]
                                                [#if roomInfo1.invoiceMode?contains('服务商')]
                                                    平台开票
                                                [#else ]
                                                    ${roomInfo1.invoiceMode}
                                                [/#if]
                                            [/#if]
								</span>
							</p>
						</div>
[#--						 [#if showFlag != 1 && roomInfo1.goodsStatus == 1 ] onclick="reserve('${msg}', '${roomInfo1.goodsId}','${roomInfo1.hotelId}','')" [/#if]--]
						<div class="right"  onclick="reserve('${msg}','${hotel.hotelId}','${roomInfo.fxid}', '${roomInfo1.jgjhid}')" >
                            <div class="price_section">
                                <div class="price">￥
                                <span days="${diffDays}"> <!-- 平均房价，取税后价-->
								    ${sellingPrice1}
                                </span>
                                </div>
						    </div>
						    <div class="btn_section">
								<a class="btn_comp" href="javascript:void(0);">
									<div class="btn_top">预订</div>
									<div class="btn_bottom text_theme">在线付</div>
								</a>
							</div>
						</div>
					  </div>
					  [/#list]
					</li>
					[/#if]
				  [/#if]
				[/#list]
			</ul>
				[#if hasShow == false]
	            <div class= "public_empty_section2">
					<img src="${base}/resources/wechat/img/recharge/img_search_hotel.png" />
					<p>房型未满足条件，建议您更改入住时间或选择其他酒店 </p>
                </div>
                [/#if]
            [#else]
				 <div class= "public_empty_section2">
					<img src="${base}/resources/wechat/img/recharge/img_search_hotel.png" />
					<p> 房型已订完，建议您更改入住时间或选择其他酒店 </p>
                </div>
			[/#if]

			 <input type="hidden" name="hotelId" id="hotelId" value="${hotel.hotelId}"/>
			<input type="hidden" id="agentMemberId" name="agentMemberId" value="${agentMemberId}">
		</div>
		
		<!--日期选拔-->
		<div id="demo"></div>
		<div   id="layer_hotel_intro" type="hidden"></div>

		<script>
			$(function(){
			    addCookie("highConditionSpans", "", {expires: 30*24 * 60 * 60});
			    addCookie("starAndPriceSpans", "", {expires: 30*24 * 60 * 60});
			    addCookie("positionAndDistrictSpans", "", {expires: 30*24 * 60 * 60});
                isShowMap();
				<!-- 酒店图片不为空时才加载-->
				[#if hotelImages!=null && hotelImages?size>0]
				var mySwiper = new Swiper('.swiper-container',{
					on:{
						reachEnd: function(){
							setTimeout('location.href="${base}/vetechHotel/photo.jhtml?hotelId=${hotel.hotelId}&agentMemberId=${agentMemberId}&isPublic=${isPublic}";',1000)
						},
					},
				});
				[/#if]


				$("#img_banner").on("click",function(){
					location.href="${base}/vetechHotel/photo.jhtml?hotelId=${hotel.hotelId}&agentMemberId=${agentMemberId}&isPublic=${isPublic}";
				})
				
				$.fn.raty.defaults.path = '${base}/resources/wechat/plugins/raty-2.5.2/img';
				$('.stars').raty({ 
					score: function() {
					    return $(this).attr('data-score');
					},
					readOnly: true,
				});
				
				$("ul li .top .right").on("click",function(){
				    var hasRoom=$(this).attr("hasRoom");
				    if(hasRoom!="0"){ //表示房间已全部预定完
				        $(this).parents("li").toggleClass("active");
                    }
				})
				
				$("ul li .top").each(function(){
				   if($(this).siblings().length==0){
				       $(this).parent("li").hide();
				   }
				}) 

				$(".room_ul").find("li").each(function(){
				   if($(this).is(':visible')){//只要有一个显示则隐藏
				       $(".public_empty_section2").hide();
				   }
				})

                $("body").on("click",".hotel_facilities .open",function(){
                    $(this).toggleClass("active").parent().prev("ul").toggle();
                    if($(this).children(".text").text() == "收起"){
                        $(this).children(".text").text("更多房型设施");
                    }else{
                        $(this).children(".text").text("收起");
                    }
                })

                $("body").on("click",".policy_service .open",function(){
                    $(this).toggleClass("active").parent().prevAll(".display").toggle();
                    if($(this).children(".text").text() == "收起"){
                        $(this).children(".text").text("加早加床停车场、儿童政策");
                    }else{
                        $(this).children(".text").text("收起");
                    }
                })


                
                $(".bottom .left").on("click",function(){
                    var roomId=$(this).attr("roomId");
                	event.stopPropagation();
                	var cancelPolicyMsg=$("#toggle_li_info"+roomId).attr("cancelPolicyMsg");
                	if(cancelPolicyMsg!=null && cancelPolicyMsg=='限时取消'){
                        layer.open({
                        type: 1,
                        content: $("#toggle_li_info"+roomId).html(),
                        className: 'fli-layer-footer',
                        shadeClose:true,
                        anim:"up",
                        success: function () {
                            $(".layer_close_btn").on("click",function(){
                                layer.closeAll();
                            })
                        }
                    })
					}

                })
				
				
			  var arrivalDate=new Date($("#arrivalDate").val());
		       var departureDate=new Date($("#departureDate").val());
		       var b_time=arrivalDate.getTime();
		       var e_time=departureDate.getTime();
		       var c_time = e_time - b_time;
			   var day_num = c_time/(1000*60*60*24);
			   if(day_num<=0){
			      departureDate.setDate(arrivalDate.getDate()+1); 
			      departureDate=formatDate(departureDate);
			      $("#departureDate").val(departureDate); 
			      $(".total").text("共1晚");
			   }else{
			      $(".total").text("共"+day_num+"晚");
			   }
			   
			   
			   function formatDate(date) {
                var d = new Date(date),
    			month = '' + (d.getMonth() + 1),
    			day = '' + d.getDate(),
    			year = d.getFullYear();
 
  				if (month.length < 2){
  				   month = '0' + month;
  				} 
  				if (day.length < 2){
  				   day = '0' + day;
  				} 
 
  				return [year, month, day].join('-');
			  }

			})
			

			function goBack(ev) {
                var eve =ev||window.event;
                eve.stopPropagation()
				// history.go(-1)
                var url="list.jhtml";
                var previousPageUrl = document.referrer; //获取上一个页面来源
                if(previousPageUrl.indexOf("mapSelection.jhtml")>-1){ //是从酒店列表跳转过来
                    url="mapSelection.jhtml"
                }
                [#if applyId?? ]
                url+="?applyId=${applyId}&fromCityName=${fromCityName}&arrivalDate=${arrivalDate}&departureDate=${departureDate}&agentMemberId=${agentMemberId}&isPublic=${isPublic}&hotelDistrictName=${hotelDistrictName}&longitude=${longitude}&latitude=${latitude}"
                [#else ]
                  url+="?fromCityName=${fromCityName}&arrivalDate=${arrivalDate}&departureDate=${departureDate}&isPublic=${isPublic}&hotelDistrictName=${hotelDistrictName}&longitude=${longitude}&latitude=${latitude}"
                [/#if]
                location.href = "${base}/ctrip_hotel/"+url;
            }
		    function reserve(msg,hotelId,fxid,jgjhid){
                layer.open({type: 2});
		       	var arrivalDate=$("#arrivalDate").val();
		        var departureDate=$("#departureDate").val();

				var applyId='${applyId}';
				var agentMemberId='${agentMemberId}';
				var isPublic='${isPublic}';

				//因私不带申请单和代订人
				if(isPublic=="2"){
					applyId="";
					agentMemberId="";
				}
				var url = "${base}/vetechHotel/roomTypeConfirm.jhtml?"+
						"tripRuleBroken=" + msg +"&hotelId="+hotelId+	"&pricePlanId=" +encodeURIComponent(jgjhid)  +
						"&goodsId=" +encodeURIComponent(fxid) +"&arrivalDate="+arrivalDate+"&departureDate="+departureDate +
						"&fromCityName=${hotel.cityName}";

				if( applyId != ""){
					url+="&applyId="+applyId+"&tripRuleBroken=" + msg;
				}
				//代订
				if(agentMemberId != ""){
					url+="&agentMemberId="+agentMemberId;
				}
				//从新差旅官网过来
				if(isPublic != ""){
					url+="&isPublic="+isPublic;
				}
				location.href=url;

                layer.closeAll();
		    }
		</script>
		
		
		<script>
		   var fromCityName="${fromCityName}";
		   var hotelId=$("#hotelId").val();
		    mobiscroll.range('#demo', {
               theme: 'ios',
               lang: 'zh',
    		   display: 'top',
    		   startInput: '#arrivalDate',
   			   endInput: '#departureDate',
   			   dateFormat:'yy-mm-dd',
    		   fromText: "入住时间",
               toText: "离开时间",
               min:new Date(),
               onBeforeClose: function (event, inst) {
                if(event.button==="set"){ //点击的是确定
                    var startDate=$(".mbsc-range-btn-v-start").text();
                    var endDate=$(".mbsc-range-btn-v-end").text();
                    if(endDate.length == 1){
                        startDate = $("#arrivalDate").val();
                        endDate = $("#departureDate").val();
                    }
                    var arrivalDate=new Date(startDate);
                    var departureDate=new Date(endDate);
                    var b_time=arrivalDate.getTime();
                    var e_time=departureDate.getTime();
                    var c_time = e_time - b_time;
                    var day_num = c_time/(1000*60*60*24);
                    $(".total").text("共"+day_num+"晚");

                    var applyId='${applyId}';
                    var agentMemberId='${agentMemberId}';
                    var isPublic='${isPublic}';

                    //因私不带申请单和代订人
                    if(isPublic=="2"){
                        applyId="";
                        agentMemberId="";
                    }
                    var url = "detail.jhtml?fromCityName="+fromCityName+"&hotelId="+hotelId+"&arrivalDate="+startDate+"&departureDate="+endDate;

                    if( applyId != ""){
                        url+="&applyId="+applyId;
                    }
                    //代订
                    if(agentMemberId != ""){
                        url+="&agentMemberId="+agentMemberId;
                    }
                    //从新差旅官网过来
                    if(isPublic != ""){
                        url+="&isPublic="+isPublic;
                    }
                    location.href=url;
				}


               }
	      });
	      
		</script>
	</body>
</html>
