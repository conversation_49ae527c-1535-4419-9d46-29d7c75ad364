<!DOCTYPE html>
<html style="background: #fff;">
	<head>
		<meta charset="UTF-8">
		<title>火车票</title>
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">
		<script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/form.css" />
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/trainTicket.css" />
		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
		<script src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>

        <script>
            $(function () {
                //链接点击事件响应:active状态
                document.body.addEventListener('touchstart', function () {
                });
            })
        </script>
	</head>
	<body style="background: #fff;">
    [#if com_person_hide_menu?? && com_person_hide_menu == '1']
        <!-- 隐藏底部菜单 -->
    [#else ]
        [#include "./wechat/include/footer.ftl" /]
    [/#if]
    <div class="TransitPage">
        <div class="public_top_header bg_theme">
            <a href="javascript:history.go(-1)" class="return_back"></a>
       [#--     <a href="javascript:;" onclick='location.href="purchase.jhtml?applyId=${applyId}&agentMemberId=${agentMemberId}&isPublic=${isPublic}&trainNumber=${trainNumber}&to=${to}&from=${from}&date=${date}"' class="return_back"></a>--]
            ${trainNumber}时刻表
            [#if com_person_hide_menu?? && com_person_hide_menu == '1']
                <!-- 隐藏右上角菜单目录 -->
            [#else ]
                [#include "./wechat/include/head_nav.ftl" /]
            [/#if]
        </div>
        <div class="content">
            <div class="header">
                <div class="name">车站名称</div>
                <div class="time_reach">到达</div>
                <div class="time_start">出发</div>
                <div class="time_stop">停留</div>
            </div>
            <div class="list">
				 [#if stations!=null && stations?size>0]
                    [#list stations as trainStation]
                        <div class="item  [#if from==trainStation.station]start[/#if] [#if to==trainStation.station]end[/#if]">
                            <div class="name">
                                <div>${trainStation.station}</div>
                            </div>
                            <div class="time_reach">${trainStation.arrivalTime}</div>
                            <div class="time_start">${trainStation.departureTime}</div>
                            <div class="time_stop">${trainStation.stayTimeSpan}[#if trainStation_index!=0 && trainStation_index!=(stations?size)-1]分钟[/#if]</div>
                        </div>
                    [/#list]
				 [/#if]

            </div>
        </div>
    </div>

    <script>
        $(function () {
            var indexStart = $(".start").index();
            var indexEnd = $(".end").index();
            $(".item").each(function(index,element){
                console.log(index)
                if(index > indexStart && index < indexEnd ) {
                    console.log(element)
                    $(element).addClass("end center");
                }
            })
        })
    </script>
</body>
</html>
