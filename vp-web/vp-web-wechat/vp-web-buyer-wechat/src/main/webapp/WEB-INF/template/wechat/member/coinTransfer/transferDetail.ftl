<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="format-detection" content="telephone=no" />
    <title>积分收购</title>
    <meta name="author" content="福利PLUS Team">
    <meta name="copyright" content="福利PLUS">
    <link rel="stylesheet" href="${base}/resources/wechat/css/common.css">
    <link rel="stylesheet" href="${base}/resources/wechat/plugins/swiper4/css/swiper.min.css">
    <link rel="stylesheet" href="${base}/resources/wechat/css/scorepurchase.css">
    <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

    <script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/plugins/swiper4/js/swiper.min.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/plugins/scale/pinchzoom.js"></script>
</head>
 [#assign current = "mall" /]
<body>
	<div class="scorepurchaserecopage">
        <div class="public_top_header">
            <a href="javascript:history.go(-1);" class="return_back"></a>
           	 积分转让
        </div>
        <div class="contentsection">
            <div class="toppanel">
                <p class="status">
                	[#if oi.order.orderStatus == "unconfirmed"]
               		待确认
               		[#elseif  oi.order.orderStatus == "confirmed"]
               		待发货
               		[#elseif  oi.order.orderStatus == "shipped"]
               		待收货
               		[#elseif  oi.order.orderStatus == "completed"]
               		已完成
               		[#elseif  oi.order.orderStatus == "cancelled"]
               		已取消
                  	[/#if]
                </p>
                <p class="nums"><label>订单编号：</label> ${oi.order.sn}
                </p>
                [#if (oi.order.orderStatus == "unconfirmed" || oi.order.orderStatus == "confirmed") && oi.order.expire??]
                <p class="remainTime" data-expire="${oi.order.expire?string("yyyy/MM/dd HH:mm:ss")}">
                    <label>待处理时间：</label>
                    <span class="time">0时0分0秒</span>
                </p>
                [/#if]
                <div class="contents">
                    <ul class="items">
                        <li class="item">
                            <label>下单时间</label>
                            <span>${oi.createDate?string("yyyy-MM-dd HH:mm:ss")}</span>
                        </li>
                        <li class="item">
                            <label>持卡人</label>
                            <span>${oi.getVirtualProductOrderInfoMapValue('bankAccountName')}</span>
                        </li>
                        <li class="item">
                            <label>银行卡号</label>
                            <span>${oi.getVirtualProductOrderInfoMapValue('bankAccountNo')}</span>
                        </li>
                        <li class="item">
                            <label>发卡银行</label>
                            <span>${oi.getVirtualProductOrderInfoMapValue('bankName')}</span>
                        </li>
                        <li class="item">
                            <label>发卡城市</label>
                            <span>
                            	[#if oi.getVirtualProductOrderInfoMapValue('bankProvince') ==oi.getVirtualProductOrderInfoMapValue('bankCity') ]
			                     	 ${oi.getVirtualProductOrderInfoMapValue('bankCity') }
			                     [#else] 
			                     	${oi.getVirtualProductOrderInfoMapValue('bankProvince') } ${oi.getVirtualProductOrderInfoMapValue('bankCity') }
			                     [/#if]
                            </span>
                        </li>
                        <li class="item">
                            <label>收购金额</label>
                            <span>${currency(oi.getVirtualProductOrderInfoMapValue('repaymentAmount'))}元(${currency(oi.getVirtualProductOrderInfoMapValue('transCoin'))!(currency(oi.getVirtualProductOrderInfoMapValue('repaymentAmount')*(1+smvo.fee)))}积分)</span>
                        </li>
                        <li class="item">
                            <label>备注</label>
                            <textarea class="additional" placeholder="请填写备注，不超过60字" id="wordCount" [#if oi.order.orderStatus != "unconfirmed" ]readonly="readonly"[/#if]>${oi.getVirtualProductOrderInfoMapValue('memo')}</textarea>
                            <span class="wordwrap"><var class="word">60</var>/60</span>
                        </li>
                    </ul>
                    <div class="operationhis">
                        <div class="header">
                            <span>操作历史</span>
                            <div class="imgbox">
                                <img src="${base}/resources/wechat/img/icon_arrow_down.png" alt="">
                            </div>
                        </div>
                        <div class="content">
                        [#if logs?? && logs?size > 0]
                            <div class="left">
                        	[#list logs as log]
                                <span>${logs?size - log_index }</span>
                            [/#list]
                            </div>
                            <div class="right">
                                <table class="table">
                                    <tbody>
                                    	[#list logs as log]
                                        <tr>
                                            <td scope="row">${log.operator!"系统"}</td>
                                            <td>
                                            	[#if log.type == "modify"]
			                            		分配订单
			                            		[#elseif  log.type == "confirm"]
			                            		订单确认
			                            		[#elseif  log.type == "shipping"]
			                            		订单发货
			                            		[#elseif  log.type == "shipfailure"]
			                            		收购失败
			                            		[#elseif  log.type == "complete"]
			                            		订单完成
			                            		[#elseif  log.type == "cancelled"]
			                            		取消订单
			                            		[/#if]
                                            </td>
                                            
                                            [#if log.type == "modify" && ((log.content == null) || (log.content.indexOf("超时") > -1))]
					                        <td>无</td>
					                        [#else]
					                        <td class="reason" reason="${log.content!"无" }">理由</td>
					                        [/#if]
					                        
                                            <td>${log.createDate?string("MM-dd HH:mm")}</td>
                                        </tr>
                                        [/#list]
                                    </tbody>
                                </table>
                            </div>
                        [#else]
                        <div>无</div>
                        [/#if]
                        </div>
                    </div>
                    [#if oi.order.orderStatus != "unconfirmed" && oi.order.orderStatus != "confirmed"]
                    <div class="picrecord">
                            	凭证
                        <div class="picbox">
                            <ul class="items">
                    [#if oi.getVirtualProductOrderInfoMapValue('images')?? ]
		            	[#list oi.getVirtualProductOrderInfoMapValue('images')?split(";") as iname]
		            		[#if iname?? && iname != ""]
		                	<li class="item"><img src="${setting.siteUrlImg!}${iname}" alt=""></li>
		                	[/#if]
		                [/#list]
		            [/#if]
                            </ul>
                        </div>
                    </div>
                    [/#if]
					[#if oi.order.orderStatus == "unconfirmed"]
                    <div class="btns" >
                        <button class="recognition btn">确认订单</button>
                    </div>
                    [/#if]
				</div>

            </div>

        </div>
		<div class="swiperBox ">
			<div class="swiper-container swiper-container-fade">
				<div class="swiper-wrapper">
					<div class="swiper-slide">
						<div class="img_loading"></div>
					</div>
				</div>
			</div>
			<span class="closebtn"> × </span>
		</div>
		<!--此处引入公共尾部代码  开始-->
		[#include "./wechat/include/footer.ftl" /]
		<!--此处引入公共尾部代码  结束-->
	</div>
</body>

<script>
$(".remainTime").each(function(i, v) {
    if ($(v).attr("data-expire")) {
        var $Timeindex = i + "range";
        var now = new Date('${.now?string("yyyy/MM/dd HH:mm:ss")}').getTime();
        var expire = new Date($(v).attr("data-expire")).getTime();
        var time = parseInt((expire-now)/1000);
        //var time = parseInt($(v).attr("data-second") / 1000);
        $Timeindex = window.setInterval(function() {
            if (time > 0) {
                var hours = Math.floor(time / 3600),
                    minute = Math.floor(time % 3600 / 60),
                    second = Math.floor(time % 3600 % 60);
                if (minute <= 9) minute = '0' + minute;
                if (hours <= 9 && hours > 0) hours = '0' + hours;
                $(v).find(".time").html(hours + "时" + minute + "分" + second + "秒");
                time--;
            } else {
                clearInterval($Timeindex);
            }
        }, 1000)
    }
});

$(".operationhis .right table tr").on("click", "td.reason", function(e) {
    layer.open({
    	title:"提示",
        content: $(this).attr("reason"),
        btn: '我知道了'
    });
});

$(".operationhis .header .imgbox").on("click", function() {
    var $this = $(this);
    delay_till_last('trigger', function() { //注意 id 是唯一的  
        $this.children().toggleClass("rotate");
        $(".content").slideToggle();
    }, 300);

});

$(".picrecord .items").on("click",".item",function(){
    $(".swiperBox .swiper-slide img").attr("src",$(this).find("img").attr("src"));
    $(".swiperBox ").addClass("show");
})
//缩放初始化
$('.swiperBox .swiper-slide').each(function () {
    new RTP.PinchZoom($(this), {});
});
$(".swiperBox .closebtn").on("click",function(){
    $(this).parent().removeClass("show");
});

var _timer = {};

function delay_till_last(id, fn, wait) {
    if (_timer[id]) {
        window.clearTimeout(_timer[id]);
        delete _timer[id];
    }
    return _timer[id] = window.setTimeout(function() {
        fn();
        delete _timer[id];
    }, wait);
}

//先选出 textarea 和 统计字数 dom 节点
var wordCount = $("#wordCount"),
    word = $(".word");
//调用
statInputNum(wordCount, word);
/*
 * 剩余字数统计
 * 注意 最大字数只需要在放数字的节点哪里直接写好即可 如：<var class="word">200</var>
 */
function statInputNum(wordCount, word) {
    var max = word.text(),
        curLength;
    wordCount[0].setAttribute("maxlength", max);
    curLength = wordCount.val().length;
    word.text(max - curLength);
    wordCount.on('input propertychange', function() {
        word.text(max - $(this).val().length);
    });
}
$(".btns .recognition").click(function() {
	var $this = $(this);
	$this.prop("disabled", true);
    layer.open({
    	title: "提示",
        content: "确认订单?",
        btn: ['确认', '取消'],
        yes: function() {
        	$.post('${base}/member/coinTransfer/confirmOrder.jhtml',
		    		{sn: '${oi.order.sn}', memo: $("#wordCount").val()},
		    		function(msg){
		    			if (msg.type=="success") {
	    				   layer.open({
	    						content: '操作成功'
	    						,skin: 'msg'
	    						,time: 2 
	    					});
	    				   setTimeout('location.reload();', 2000);
	    				} else {
	    					layer.open({
	    						content: msg.content
	    						,skin: 'msg'
	    						,time: 2 
	    					});
	    					$this.prop("disabled", false);
	    				}
		    });
        }
    })
});
</script>

</html>