<!DOCTYPE html>
<html>

	<head>
		<meta charset="UTF-8">
		<title>交通差旅</title>
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">
		<script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/lifeService.css" />
		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
		<script src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js"></script>
		
	</head>
	<script type="text/javascript">
		$(function(){
			$(".form_submit").click(function(){
				var name=$("#name").val();
				if(!name){
					layer.open({
					    content: '请输入姓名'
					    ,skin: 'msg'
					    ,time: 2 //2秒后自动关闭
					});
					$("#name").focus();
					return false;
				}
				var idcardNo=$("#idcardNo").val();
				if(!idcardNo){
					layer.open({
					    content: '请输入身份证'
					    ,skin: 'msg'
					    ,time: 2 //2秒后自动关闭
					});
					$("#idcardNo").focus();
					return false;
				}
				if(!isIdCardNo(idcardNo)){
					layer.open({
					    content: '身份证号码错误'
					    ,skin: 'msg'
					    ,time: 2 //2秒后自动关闭
					});
					$(this).focus();
					flag=false;
					return false;
				}
				$(this).prop("disbled",true);
				$(this).val("提交中...");
		    	$.post("${base}/member/passenger/save.jhtml",{name:name,idcardNo:idcardNo},function(data){
					if(data){
						if(data.type=="success"){
							location.href="passenger.jhtml?sightId=${sightId}&productid=${productid}&num=${num}&passengers=${passengers}&useDate=${useDate}";
						}else{
							$(this).prop("disbled",false);
							$(this).val("提交");
							layer.open({
							    content: data.content
							    ,skin: 'msg'
							    ,time: 2 //2秒后自动关闭
							  });
						}
					}else{
						$(this).prop("disbled",false);
						$(this).val("提交");
						layer.open({
						    content: "请重试"
						    ,skin: 'msg'
						    ,time: 2 //2秒后自动关闭
						  });
					}
				},"json");
				
			});
		});
		function isIdCardNo(num) {
			num = num.toUpperCase(); 
			//身份证号码为15位或者18位，15位时全为数字，18位前17位为数字，最后一位是校验位，可能为数字或字符X。 
			if ( !(/(^\d{15}$)|(^\d{17}([0-9]|X)$)/.test(num)) ){
				return false;
			}
			return true;
		}
	</script>
	<body class="has_fixed_footer">
		<div class="public_top_header">
			<a href="javascript:history.go(-1);" class="return_back"></a>
			新增乘客
		</div>

		<div class="public_form_section">
			<form action="" method="post">
				<div class="items">
					<div class="item">
						<label>姓名</label>
						<div class="inputs standard_input">
							<input type="text" name="name" id="name" value="" placeholder="乘客姓名" class="input" />
							<span class="icon_clear"></span>
						</div>
					</div>
					
					<div class="item">
						<label>身份证</label>
						<div class="inputs standard_input">
							<input type="text" name="idcardNo" id="idcardNo" value="" placeholder="与乘客证件一致" class="input" />
							<span class="icon_clear"></span>
						</div>
					</div>
				</div>

				<div class="btn_submit_box">
					<input type="button" value="提交" class="btn_main btn_primary form_submit"  />
				</div>

			</form>
		</div>
		<!--此处引入公共尾部代码  开始-->
		[#include "./wechat/include/footer.ftl" /]
		<!--此处引入公共尾部代码  结束-->
	</body>

</html>