<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>3.8女人节</title> 
		<meta name="format-detection" content="telephone=no" /> 
		<meta content="email=no" name="format-detection"> 
        <meta name="apple-mobile-web-app-capable" content="yes"> 
        <meta name="apple-mobile-web-app-status-bar-style" content="black">
        <script type="text/javascript" src="${base}/resources/greetingCard/js/flexible.js" ></script>
        <link rel="stylesheet" href="${base}/resources/greetingCard/css/swiper.css?v=1.0.0" />
        <link rel="stylesheet" href="${base}/resources/greetingCard/css/animate.css?v=1.0.0" />
        <link rel="stylesheet" type="text/css" href="${base}/resources/greetingCard/css/common.css?v=1.0.0" /> 
        <link rel="stylesheet" type="text/css" href="${base}/resources/greetingCard/template/womenDay/css/style.css?v=1.0.0" />   
		<script type="text/javascript" src="${base}/resources/greetingCard/js/jquery-1.9.1.js" ></script>
		<script type="text/javascript" src="${base}/resources/greetingCard/js/swiper.jquery.min.js" ></script>
	</head>
	<body>
		<div class="loading_section">
			<div class="loading"></div>
		</div>
		<div class="public_top_header">
			 [#if flag!=null && flag==0]<a href="${base}/dynamic/index.jhtml" class="return_back"></a>
           [#else]
			<a href="javascript:history.go(-1);" class="return_back"></a>
			 [/#if]
			我的贺卡
		</div>
		
		<div id="music" class="music">
			<audio id="music-audio" class="audio" loop="" autoplay="autoplay" preload="">
				<source src="${base}/resources/greetingCard/music/New_Year's_Day.mp3" type="audio/mpeg"></source>
			</audio>
			<div class="control">
				<div class="control-after"></div>
			</div>	
		</div>
		
		<!-- Swiper -->
	    <div class="swiper-container">
	        <div class="swiper-wrapper">
	            <div class="swiper-slide slide_1">
	            	<div class="img_section">
	            		<div class="bird_1 animated"></div>
	            		<div class="bird_2 animated"></div>	
	            		<div class="text animated"></div>
	            		<div class="cloud animated"></div>
	            	</div>
	            	<p class="p1 animated bounceInLeft">3.8女人节快乐</p>
	            	<p class="p2 animated bounceInRight">你永远最美丽</p>
	            </div>
	            <div class="swiper-slide slide_2">
	            	<div class="img_1 animated">
	            	<img src="${base}/resources/greetingCard/template/womenDay/img/img_1.png" />
	            		
	            	</div>
	            	<div class="textarea_section animated">
	            		<div class="red_top animated"></div>
	            		<div class="textarea animated">
		            		${cardContent}
		            	</div>
	            	</div>
	            </div>
	            
	            [#assign phoneTitle="女人节快乐" /]
				[#include "/wechat/member/card/template/commonGift.ftl" /]
	            
	        </div>
	        <!-- Add Pagination -->
	        <div class="swiper-pagination"></div>
	    </div>
	   
	    <div class="to_next_page animated infinite slideInUpS"></div>
	    
	    <!-- Initialize Swiper -->
	    <script type="text/javascript" src="${base}/resources/greetingCard/js/swiper.jquery.min.js" ></script>
	    <script> 
		    var swiper = new Swiper('.swiper-container', {
		        pagination: '.swiper-pagination',
		        direction: 'vertical',
		        loop: true,  
		        paginationClickable: true,
		        onSlideChangeEnd: function(swiper){   
			        if(swiper.activeIndex == 1||swiper.activeIndex == 4){
			       		$(".slide_1 .cloud,.slide_1 .bird_1,.slide_1 .bird_2").show().addClass("wave");
			       		$(".slide_1 .text").show().addClass("fadeInUp");
			        	$(".slide_1 .p1").show().addClass("bounceInLeft");
			        	$(".slide_1 .p2").show().addClass("bounceInRight");
			        	$(".slide_2 .img_1").hide().removeClass("flipInX");
			       		$(".slide_2 .textarea_section").hide().removeClass("bounceIn");
			       		$(".slide_2 .red_top").hide().removeClass("bounceIn");
			        	$(".slide_2 .textarea").hide().removeClass("bounceInUp");
			        	$(".showBoxPage .img_1").hide().removeClass("slideInDown");
			        	
			        }else if(swiper.activeIndex == 2){ 
			        	$(".slide_1 .cloud,.slide_1 .bird_1,.slide_1 .bird_2").hide().removeClass("wave");
			       		$(".slide_1 .text").hide().removeClass("fadeInUp"); 
			        	$(".slide_1 .p1").hide().removeClass("bounceInLeft");
			        	$(".slide_1 .p2").hide().removeClass("bounceInRight");
			       		$(".slide_2 .img_1").show().addClass("flipInX");
			       		$(".slide_2 .textarea_section").show().addClass("bounceIn");
			       		$(".slide_2 .red_top").show().addClass("bounceIn");
			        	$(".slide_2 .textarea").show().addClass("bounceInUp");
			        	$(".showBoxPage .img_1").hide().removeClass("slideInDown");
			        	
			        }else if(swiper.activeIndex == 3||swiper.activeIndex == 0){
			        	$(".slide_1 .cloud,.slide_1 .bird_1,.slide_1 .bird_2").hide().removeClass("wave");
			       		$(".slide_1 .text").hide().removeClass("fadeInUp"); 
			        	$(".slide_1 .p1").hide().removeClass("bounceInLeft");
			        	$(".slide_1 .p2").hide().removeClass("bounceInRight");
			        	$(".slide_2 .img_1").hide().removeClass("flipInX");
			       		$(".slide_2 .textarea_section").hide().removeClass("bounceIn");
			       		$(".slide_2 .red_top").hide().removeClass("bounceIn");
			        	$(".slide_2 .textarea").hide().removeClass("bounceInUp");
			       		$(".showBoxPage .img_1").show().addClass("slideInDown");
			        }
			    }
		    });
		    
	    </script>
	</body>
</html>
