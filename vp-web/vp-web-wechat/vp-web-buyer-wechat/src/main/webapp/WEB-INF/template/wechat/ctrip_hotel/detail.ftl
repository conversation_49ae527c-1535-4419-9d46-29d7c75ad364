<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<title>酒店预订</title>
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">
		<script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/plugins/swiper-4.5.0/css/swiper.min.css" />
	    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/lifeService.css" />

		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
		<script src="${base}/resources/wechat/plugins/raty-2.5.2/jquery.raty.min.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js"></script>
		<script src="${base}/resources/wechat/plugins/swiper-4.5.0/js/swiper.min.js"></script>
        <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js" type="text/javascript"></script>
		 <!--日期选择-->
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/plugins/mobiscroll-Javascript/css/mobiscroll.javascript.min.css" />
		<script src="${base}/resources/wechat/plugins/mobiscroll-Javascript/js/mobiscroll.javascript.min.js"></script>
        <script type="text/javascript" src="${base}/resources/wechat/es6/goMap.js"></script>
	</head>
	<body class="no_fixed_top">
		<div class="hotel_detail_page">
           [#if hotelImages!=null]
            <div class="img_banner" id="img_banner">
                <div class="transparent">
                    [#--[#if applyId?? ]--]
                    [#--<a href="${base}/ctrip_hotel/list.jhtml?applyId=${applyId}&fromCityName=${fromCityName}&arrivalDate=${arrivalDate}&departureDate=${departureDate}&agentMemberId=${agentMemberId}&isPublic=${isPublic}" class="return_back"></a>--]
                    [#--[#else]--]
                    [#--<a href="${base}/ctrip_hotel/list.jhtml?fromCityName=${fromCityName}&arrivalDate=${arrivalDate}&departureDate=${departureDate}&isPublic=${isPublic}" class="return_back"></a>--]
                    [#--[/#if]--]
                    <a href="javascript:void(0);" onclick="goBack(event)" class="return_back"></a>

                </div>
                <div class="swiper-container">
                    <div class="swiper-wrapper">
                         [#list hotelImages as pictureBean]
                              [#if  pictureBean_index<=6]
                                <div class="swiper-slide" style='background-image: url("${pictureBean.URL}")'></div>
                              [/#if]
                         [/#list]
                    </div>
                </div>
                <div class="desc">
                    <div class="num">${hotelImages?size}张</div>
                     ${hotel.hotelName}
                </div>
            </div>
		   [#else]
		        <div class="img_banner">
                <div class="transparent">
                    [#--[#if applyId?? ]--]
                    [#--<a href="${base}/ctrip_hotel/list.jhtml?applyId=${applyId}&fromCityName=${fromCityName}&arrivalDate=${arrivalDate}&departureDate=${departureDate}&agentMemberId=${agentMemberId}&isPublic=${isPublic}" class="return_back"></a>--]
                    [#--[#else]--]
                    [#--<a href="${base}/ctrip_hotel/list.jhtml?fromCityName=${fromCityName}&arrivalDate=${arrivalDate}&departureDate=${departureDate}&isPublic=${isPublic}" class="return_back"></a>--]
                    [#--[/#if]--]
                    <a href="javascript:void(0);" onclick="goBack(event)" class="return_back"></a>
                </div>
                <div class="swiper-container">
                    <div class="swiper-wrapper">
                          <div class="swiper-slide" style='background-image: url("${base}/resources/wechat/img/hotel/hotel_default.jpg")'></div>
                    </div>
                </div>
                <div class="desc">
                     ${hotel.hotelName}
                </div>
            </div>
           [/#if]

            <a class="line line_2">
                <p>${hotel.address}<span>${hotel.district}  [#if hotel.businessZone!=null][#if hotel.businessZone?index_of(",")]|${hotel.businessZone?replace(",","")}[#else]|${hotel.businessZone}[/#if][/#if]</span></p>
                [#assign transportationInfoMap=hotel.getTtransportationInfoBeans()]
                [#if transportationInfoMap!=null]
                   [#list transportationInfoMap.keySet() as key]
                       [#assign transportationInfoBeans=transportationInfoMap[key]]
                        [#if transportationInfoBeans!=null]
                           [#list transportationInfoBeans as transportationInfoBean]
                              <p>${transportationInfoBean.name}</p>
                              <p>${transportationInfoBean.directions}</p>
                           [#break]
                           [/#list]
                        [/#if]
                    [#break]
                   [/#list]
                [/#if]
               [#-- <span class="text_right">地图</span>--]
            </a>
			
			<div class="line line_1">
				[#if  hotel.commentScore!=null ]<span class="score">${ hotel.commentScore}</span>分[/#if]
				[#if hotel.commentScore!=null]
							   [#if hotel.commentScore==4]
                                 <span class="text">"设施完善"</span>
                            [/#if]
                             [#if hotel.commentScore==4.1]
                                 <span class="text">"服务周到"</span>
                            [/#if]
                             [#if hotel.commentScore==4.2]
                                 <span class="text">"性价比高"</span>
                            [/#if]
                            [#if hotel.commentScore==4.3]
                                 <span class="text">"位置很好"</span>
                            [/#if]
                            [#if hotel.commentScore==4.4]
                                 <span class="text">"强烈推荐"</span>
                            [/#if]
                            [#if hotel.commentScore==4.5]
                                 <span class="text">"不错"</span>
                            [/#if]
                              [#if hotel.commentScore==4.6]
                                 <span class="text">"好"</span>
                            [/#if]
                            [#if hotel.commentScore==4.7]
                                 <span class="text">"很好"</span>
                            [/#if]
                            [#if hotel.commentScore==4.8]
                                 <span class="text">"棒"</span>
                            [/#if]
                             [#if hotel.commentScore==4.9]
                                 <span class="text">"超棒"</span>
                            [/#if]
                            [#if hotel.commentScore==5]
                                 <span class="text">"非常棒"</span>
                            [/#if]
							[/#if]
                <div class="stars" data-score="[#if  hotel.commentScore==null ||  hotel.commentScore==0]4.8[#else]${ hotel.commentScore}[/#if]"></div>

                <p class="distance flexbox justify-content-space-between">
                    <a id="distance-isWxPro" style="display: none" class="distance-item" onclick="toMap('${hotel.hotelName}','${hotel.latitude}','${hotel.longitude}')">
                        <img   src="${base}/resources/wechat/img/wechatConsume/other/daohang.png">
                        地图
                    </a>
                    <a id="distance-isnotWxPro" style="display: none" class="distance-item"  href='https://apis.map.qq.com/tools/poimarker?type=0&marker=coord:${hotel.latitude},${hotel.longitude};title:${hotel.hotelName};addr:${hotel.address}&key=ECWBZ-ES7KU-YSEVC-24FWT-R2BDK-NHB3Z&referer=fliplus'>
                        <img   src="${base}/resources/wechat/img/wechatConsume/other/daohang.png">
                        地图
                    </a>
                    <a id="distance-isWxPro"  class="distance-item distance-item-car" onclick="goCard('${hotel.hotelName}','${hotel.latitude}','${hotel.longitude}','${member.name}','${member.mobileDec}')">
                        <img   src="${base}/resources/wechat/img/didi/dace_icon.png">
                        打车
                    </a>
                </p>
			</div>

            <a class="line line_5 arrow">
                [#if hotel.openYear!=null]
                    <p>${hotel.openYear.substring(0,4)}年开业</p>
                [/#if]
                 <!-- 酒店设施-->
                <div class="img_list">
                     [#assign facilities=hotel.getFacilitiesInfo()]
                            [#if facilities!=null]
                                [#list facilities as facilityItemBean]
                                     [#if facilityItemBean.ID=="40" && facilityItemBean.status=="1"]
                                          <img src="${base}/resources/wechat/img/hotel/icon_jijiu.png" alt="医务室">
                                     [/#if]
                                     [#if facilityItemBean.ID=="266" && facilityItemBean.status=="1"]
                                          <img src="${base}/resources/wechat/img/hotel/icon_wifi.png" alt="wifi">
                                     [/#if]
                                     [#if facilityItemBean.ID=="42" && facilityItemBean.status=="1"]
                                          <img src="${base}/resources/wechat/img/hotel/icon_chuizi.png" alt="健身">
                                     [/#if]
                                     [#if facilityItemBean.ID=="97" && facilityItemBean.status=="1"]
                                          <img src="${base}/resources/wechat/img/hotel/icon_suit.png" alt="行李">
                                     [/#if]
                                     [#if facilityItemBean.ID=="100" && facilityItemBean.status=="1"]
                                          <img src="${base}/resources/wechat/img/hotel/icon_park.png" alt="停车">
                                     [/#if]
                                [/#list]
                            [/#if]
                </div>
					[#if applyId?? ]
					 <span class="text_right" onclick="location.href='hotelIntroduce.jhtml?applyId=${applyId}&hotelId=${hotel.hotelId}&arrivalDate=${arrivalDate}&departureDate=${departureDate}'">详情/设施</span>
				    [#else]
					 <span class="text_right"  onclick="location.href='hotelIntroduce.jhtml?hotelId=${hotel.hotelId}&arrivalDate=${arrivalDate}&departureDate=${departureDate}'">详情/设施</span>
				   [/#if]
            </a>

			
			<div class="line line_4 arrow">
			  <div>
				<div class="time">
			        <label>入住</label>
					<input type="text" name="arrivalDate" id="arrivalDate" value="${arrivalDate}" class="input_text" readonly="readonly" />
				       
				</div>
				<div class="time">
					<label>离开</label>
				    <input type="text" name="departureDate" id="departureDate" value="${departureDate}" class="input_text" readonly="readonly" />
				</div>
				<span class="total">共1晚</span>
				</div>
			</div>

			[#if roomPriceInfoMap!=null]
			<ul class="room_ul">
				[#list roomPriceInfoMap.keySet() as key]   <!-- 遍历房型价格接口返回的所有roomTypeId-->
					[#assign roomTypeInfoBean=roomTypeMap[key]]  <!-- 获取roomTypeId对应的房型静态信息-->
					[#assign hasPP=false]
				    [#list roomPriceInfoMap[key] as roomPriceInfo1]
                    	[#assign priceInfoBean1=roomPriceInfo1.priceInfos[0]]
						[#if priceInfoBean1 != null && priceInfoBean1.payType =='PP']
						   [#assign hasPP=true]
						   [#break]
						[/#if]
					[/#list]
					[#if roomTypeInfoBean == null]
						[#assign hasPP=false]
					[/#if]

					[#assign canFGToPP=false]  <!-- 现付是否可以转预付-->
					[#list roomTypeAndRoomsMap[key] as roomInfoBean]
						[#if roomInfoBean != null && roomInfoBean.roomFGToPPInfo !=null && roomInfoBean.roomFGToPPInfo.canFGToPP==true]
							[#assign canFGToPP=true]
							[#break]
						[/#if]
					[/#list]
					
					[#assign roomMinPrice = roomMinPriceInfoMap[key]]
					[#if roomMinPrice?? && priceRate??]<!-- 企业价格 -->
					[#assign roomMinPrice = (roomMinPrice + roomMinPrice * priceRate)?ceiling]
					[/#if]
					
			[#--	[#if hasPP==true || canFGToPP==true]--]
					[#assign msg = ""]
			   		[#assign showFlag = -1]
			   		[#if tripLevelConfig??]
						[#if tripLevelConfig.hotelFlag]
							[#if tripLevelConfig.firstLevelCities?? && tripLevelConfig.firstLevelCities?index_of(fromCityName!"null") gte 0]
								[#if tripLevelConfig.firstLevelPrice?? && tripLevelConfig.firstLevelPrice lt roomMinPrice]
									[#assign msg = msg + "平均每晚房费超标、"]
								[/#if]
								[#if tripLevelConfig.firstLevelStar?? && tripLevelConfig.firstLevelStar gt 0 && tripLevelConfig.firstLevelStar lt hotel.starRate]
									[#assign msg = msg + "酒店星级超标、"]
								[/#if]
							[#elseif tripLevelConfig.secondLevelCities?? && tripLevelConfig.secondLevelCities?index_of(fromCityName!"null") gte 0]
								[#if tripLevelConfig.secondLevelPrice?? && tripLevelConfig.secondLevelPrice lt roomMinPrice]
									[#assign msg = msg + "平均每晚房费超标、"]
								[/#if]
								[#if tripLevelConfig.secondLevelStar?? && tripLevelConfig.secondLevelStar gt 0 && tripLevelConfig.secondLevelStar lt hotel.starRate]
									[#assign msg = msg + "酒店星级超标、"]
								[/#if]
							[#elseif tripLevelConfig.thirdLevelCities?? && tripLevelConfig.thirdLevelCities?index_of(fromCityName!"null") gte 0]
								[#if tripLevelConfig.thirdLevelPrice?? && tripLevelConfig.thirdLevelPrice lt roomMinPrice]
									[#assign msg = msg + "平均每晚房费超标、"]
								[/#if]
								[#if tripLevelConfig.thirdLevelStar?? && tripLevelConfig.thirdLevelStar gt 0 && tripLevelConfig.thirdLevelStar lt hotel.starRate]
									[#assign msg = msg + "酒店星级超标、"]
								[/#if]
							[#else]
								[#if tripLevelConfig.otherLevelPrice?? && tripLevelConfig.otherLevelPrice lt roomMinPrice]
									[#assign msg = msg + "平均每晚房费超标、"]
								[/#if]
								[#if tripLevelConfig.otherLevelStar?? && tripLevelConfig.otherLevelStar gt 0 && tripLevelConfig.otherLevelStar lt hotel.starRate]
									[#assign msg = msg + "酒店星级超标、"]
								[/#if]
							[/#if]
							
							[#if msg != ""]
								[#if tripLevelConfig.hotelExceedConctrol == "unLimited"]
								[#assign showFlag = 0]
								[#elseif tripLevelConfig.hotelExceedConctrol == "orderLimit"]
								[#assign showFlag = 1]
								[#elseif tripLevelConfig.hotelExceedConctrol == "displayLimit"]
								[#assign showFlag = 2]
								[/#if]
							[#else]
								[#assign showFlag = -1]
							[/#if]
						[/#if]
					[/#if]
					[#if showFlag != 2]

				<li ice="${roomMinPriceInfoMap[key] }">
					<!-- room信息开始-->
					<div class="top">
                        <div class="desc_section" roomTypeID="${roomTypeInfoBean.roomTypeID}" start="${hotel.starRate }">
                        <div class="img">
							[#if roomTypeInfoBean.pictures!=null && roomTypeInfoBean.pictures?size>0]
								[#list roomTypeInfoBean.pictures as pictureBean]
									[#if pictureBean_index==0]
                                        <img src="${pictureBean.URL}" id="img${roomTypeInfoBean.roomTypeID}"/>
                                        [#break]
									[/#if]
								[/#list]
							[#else]
                                <img src="${base}/resources/wechat/img/hotel/hotel_default.jpg"  id="img${roomTypeInfoBean.roomTypeID}"/>
							[/#if]

                        </div>

                        <div class="desc">
                            <p class="title">
							    ${roomTypeInfoBean.roomTypeName}
                            </p>
                            <p>
					             [#if roomTypeInfoBean.roomBedInfos!=null && roomTypeInfoBean.roomBedInfos?size>0]
						              [#list roomTypeInfoBean.roomBedInfos[0] as roomBedInfo]
							              [#if roomBedInfo_index==0]
								                 ${roomBedInfo.name}
								                 [#break]
										  [/#if]
									 [/#list ]
								 [/#if] <!-- 床型-->

							   [#if roomTypeInfoBean.floorRange!=null]
								   <input id="floor${roomTypeInfoBean.roomTypeID}" floor="${roomTypeInfoBean.floorRange}" type="hidden"/>
							        [#if roomTypeInfoBean.floorRange!=null]
							            楼层 ${roomTypeInfoBean.floorRange?trim}
							        [/#if]
							   [/#if]
                            </p>
                      </div>
                      </div>
                      <div class="right">
                            <div class="price_section">
                                <div class="price">￥<span>${((roomMinPrice/diffDays)?ceiling)?int}</span></div>
                                [#if ctripHotelRate != null && ctripHotelRate > 0]
								      					<label>立减${(((roomMinPrice-roomMinPrice*ctripHotelRate)/diffDays)?floor)?int}元起</label>
                                [/#if]
                            </div>
                        </div>
					</div>

					<!-- 弹框显示房型详情-->
                   <!-- 房型弹框-->
                   <script type="text/template" id="layer_hotel_intro${roomTypeInfoBean.roomTypeID}">
					<span class="layer_close_btn"></span>
                    <header>${roomTypeInfoBean.roomTypeName}</header>
                    <div class="layer_hotel_intro">
						[#if roomTypeInfoBean.pictures!=null]
                            <div class="swiper-container">
                                <div class="swiper-wrapper">
									[#list roomTypeInfoBean.pictures as pictureBean]
                                        <div class="swiper-slide"
                                             style="background-image:url(${pictureBean.URL})"></div>
									[/#list]
                                </div>
                                <div class="swiper-pagination"></div>
                            </div>
						[/#if]
                        <div class="hotel_facilities">
                            <header>${roomTypeInfoBean.roomTypeName}</header>
                            <div class="content">
							[#--<div class="shortage">房量紧张</div>--]

                                <ul class="simple">
									[#if  roomTypeInfoBean.areaRange!=null]
                                        <li>
                                            <label>面积:</label>
                                            <div class="value">${roomTypeInfoBean.areaRange}</div>
                                        </li>
									[/#if]
									[#if  roomTypeInfoBean.maxOccupancy!=null]
                                        <li>
                                            <label>可住:</label>
                                            <div class="value">${roomTypeInfoBean.maxOccupancy}人</div>
                                        </li>
									[/#if]
									[#if  roomTypeInfoBean.floorRange!=null]
                                        <li>
                                            <label>楼层:</label>
                                            <div class="value">${roomTypeInfoBean.floorRange}层</div>
                                        </li>
									[/#if]

                                    <li>
                                        <label>床型:</label>
										[#if roomTypeInfoBean.roomBedInfos!=null && roomTypeInfoBean.roomBedInfos?size>0]
											[#list roomTypeInfoBean.roomBedInfos as roomBedInfos]
												[#if roomBedInfos_index==0]
													[#list roomBedInfos as roomBedInfo]
														[#if roomBedInfo_index==0]
                                                            <div class="value">${roomBedInfo.name}</div>
															[#break]
														[/#if]
													[/#list ]
													[#break]
												[/#if]
											[/#list]
										[/#if]
                                    </li>

									[#if roomTypeInfoBean.smoking!=null]
                                        <li>
                                            <label>吸烟政策:</label>
											[#if roomTypeInfoBean.smoking.notAllowSmoking=="T"]
                                                <div class="value">禁烟</div>
											[#else]
                                                <div class="value">可吸烟</div>
											[/#if]
                                        </li>
									[/#if]

									[#if roomTypeInfoBean.broadNet.wirelessBroadnet!=null]
                                        <li>
                                            <label>网络:</label>
											[#if roomTypeInfoBean.broadNet.wirelessBroadnet==0]
                                                <div class="value">无 全部房间WIFI<span class="free">免费</span></div>
											[/#if]
											[#if roomTypeInfoBean.broadNet.wirelessBroadnet==1]
                                                <div class="value">全部收费</div>
											[/#if]
											[#if roomTypeInfoBean.broadNet.wirelessBroadnet==2]
                                                <div class="value">全部房间WIFI<span class="free">免费</span></div>
											[/#if]
											[#if roomTypeInfoBean.broadNet.wirelessBroadnet==3]
                                                <div class="value">部分房间有WIFI且收费</div>
											[/#if]
											[#if roomTypeInfoBean.broadNet.wirelessBroadnet==4]
                                                <div class="value">无线宽带 部分房间有且免费</div>
											[/#if]

                                        </li>
									[/#if]
									[#if roomTypeInfoBean.hasWindow!=null]
                                        <li>
                                            <label>窗户:</label>
											[#if roomTypeInfoBean.hasWindow==0]
                                                <div class="value">无窗</div>
											[/#if]
											[#if roomTypeInfoBean.hasWindow==1]
                                                <div class="value">部分有窗</div>
											[/#if]
											[#if roomTypeInfoBean.hasWindow==2]
                                                <div class="value"> 有窗</div>
											[/#if]
											[#if roomTypeInfoBean.hasWindow==4]
                                                <div class="value"> 内窗</div>
											[/#if]
											[#if roomTypeInfoBean.hasWindow==5]
                                                <div class="value">天窗</div>
											[/#if]
											[#if roomTypeInfoBean.hasWindow==6]
                                                <div class="value">封闭窗</div>
											[/#if]
											[#if roomTypeInfoBean.hasWindow==7]
                                                <div class="value">飘窗</div>
											[/#if]
											[#if roomTypeInfoBean.hasWindow==100]
                                                <div class="value"> 未知</div>
											[/#if]
                                        </li>
									[/#if]
                                </ul>

                                <ul class="normal">
                                    <li>
                                        <label>费用政策</label>
                                        <div class="value">
											[#if policieBeans!=null]
												[#list policieBeans as policieBean]
													[#if policieBean.code=="Meal"]
                                                        <p>加早：${policieBean.text}</p>
														[#break]
													[/#if]
												[/#list]
											[/#if]
											[#if roomTypeInfoBean.extraBedFee==null || roomTypeInfoBean.extraBedFee==""]
                                                <p>加床: 不可加床</p>
											[#elseif roomTypeInfoBean.extraBedFee=="Uknown"]
                                                <p>加床: 未知</p>
											[#elseif roomTypeInfoBean.extraBedFee=="0"]
                                                <p>加床: 免费加床</p>
											[#else]
                                                <p>加床: 需额外加床费</p>
											[/#if]
										[#--  <p>停车费：<span class="free">免费</span></p>--] <!--数据待补充-->
                                        </div>
                                    </li>
									[#assign facilities=roomTypeInfoBean.facilities ]
									[#if facilities!=null]
										[#assign amenities=""] <!-- 便利设施-->
										[#assign technology=""] <!-- 媒体科技-->
										[#assign foodAndDrinks=""] <!-- 食品饮品-->
										[#assign bathroom=""] <!-- 浴室-->
										[#assign otherFacilitie=""] <!-- 其他-->
										[#list facilities as facilitieBean]
											[#if facilitieBean.categoryName=="便利设施"]
												[#list facilitieBean.facilityItem as facilityItem]
													[#assign amenities=amenities+"${facilityItem.name},"]
												[/#list]
											[/#if]
											[#if facilitieBean.categoryName=="媒体科技"]
												[#list facilitieBean.facilityItem as facilityItem]
													[#assign technology=technology+"${facilityItem.name},"]
												[/#list]
											[/#if]
											[#if facilitieBean.categoryName=="食品饮品"]
												[#list facilitieBean.facilityItem as facilityItem]
													[#assign foodAndDrinks=foodAndDrinks+"${facilityItem.name},"]
												[/#list]
											[/#if]
											[#if facilitieBean.categoryName=="浴室"]
												[#list facilitieBean.facilityItem as facilityItem]
													[#assign bathroom=bathroom+"${facilityItem.name},"]
												[/#list]
											[/#if]
											[#if facilitieBean.categoryName=="其他"]
												[#list facilitieBean.facilityItem as facilityItem]
													[#assign otherFacilitie=otherFacilitie+"${facilityItem.name},"]
												[/#list]
											[/#if]
										[/#list]
									[/#if]

									[#if amenities!=null]
                                        <li>
                                            <label>便利设施</label>
                                            <div class="value">${amenities?substring(0,amenities?length-1)}</div>
                                        </li>
									[/#if]

									[#if technology!=null]
                                        <li>
                                            <label>媒体技术</label>
                                            <div class="value">${technology?substring(0,technology?length-1)}</div>
                                        </li>
									[/#if]

									[#if foodAndDrinks!=null]
                                        <li>
                                            <label>食品饮品</label>
                                            <div class="value"> ${foodAndDrinks?substring(0,foodAndDrinks?length-1)}</div>
                                        </li>
									[/#if]

									[#if bathroom!=null]
                                        <li>
                                            <label>浴室</label>
                                            <div class="value">${bathroom?substring(0,bathroom?length-1)}</div>
                                        </li>
									[/#if]

									[#if otherFacilitie!=null]
                                        <li>
                                            <label>其他</label>
                                            <div class="value">${otherFacilitie?substring(0,otherFacilitie?length-1)}</div>
                                        </li>
									[/#if]

                                </ul>
                                <div class="center">
                                    <div class="open active">
                                        <span class="text">收起</span><span class="icon"></span>
                                    </div>
                                </div>

                            </div>
                        </div>

                        <div class="policy_service">
							[#if policieBeans!=null]
								[#list policieBeans as policieBean]
									[#if policieBean.code=="CheckInCheckOut"]
                                        <header>政策服务</header>
                                        <div class="item">
                                            <p class="title free">入离时间</p>
                                            <p> ${policieBean.text}</p>
                                        </div>
										[#break]
									[/#if]
								[/#list]
							[/#if]

                            <div class="item display">
                                <p class="title">酒店费用政策</p>

								[#if policieBeans!=null]
									[#list policieBeans as policieBean]
										[#if policieBean.code=="Meal"]
                                            <p>
                                                <label>加早</label>
											${policieBean.text}
                                            </p>
											[#break]
										[/#if]
									[/#list]
								[/#if]


								[#if roomTypeInfoBean.extraBedFee==null || roomTypeInfoBean.extraBedFee==""]
                                    <p>
                                        <label>加床</label>
                                        不可加床
                                    </p>
								[#elseif roomTypeInfoBean.extraBedFee=="Uknown"]
                                    <p>
                                        <label>加床</label>
                                        未知
                                    </p>
								[#elseif roomTypeInfoBean.extraBedFee=="0"]
                                    <p>
                                        <label>加床</label>
                                        免费加床
                                    </p>
								[#else]
                                    <p>
                                        <label>加床</label>
                                        需额外加床费
                                    </p>
								[/#if]

							[#-- <p><label>停车费</label><span class="free">免费</span></p>--]
                            </div>

                            <div class="item display">
							[#--   <p>每间客房最多容纳1名17岁及以下以下儿童，和成人共用现有床铺</p>--]
								[#if policieBeans!=null]
									[#list policieBeans as policieBean]
										[#if policieBean.code=="Child"]
										    <p class="title">儿童政策</p>
                                            <p>${policieBean.text}</p>
											[#break]
										[/#if]
									[/#list]
								[/#if]
                            </div>
                            <div class="center">
                                <div class="open active">
                                    <span class="text">收起</span><span class="icon"></span>
                                </div>
                            </div>

                        </div>
                    </div>
					</script>
                    <!-- 弹框结束-->


				<!-- room信息结束-->


					[#list roomPriceInfoMap[key] as roomPriceInfo]  <!-- 一个roomTypeId下的多有room信息结束-->
						[#assign priceInfoBean=roomPriceInfo.priceInfos[0]]
						[#if priceInfoBean != null]
							[#assign msg = ""]
								[#assign roomPrice = ""]
						   		[#assign showFlag = -1]
						         [#assign invoiceType = ""]
						         [#assign isHidden=false]
						         [#assign invoiceType = ""]
						   		[#if roomPriceInfo!=null && roomPriceInfo.priceInfos!=null && roomPriceInfo.priceInfos?size>0]

									[#assign holdTime=""]
									[#if roomPriceInfo.holdDeadline!=null]
										[#assign holdTime=roomPriceInfo.holdDeadline.holdTime]
									[/#if]
									[#list roomPriceInfo.priceInfos as priceInfoBean]
										[#if priceInfoBean_index==0]
											[#list priceInfoBean.prices as price]
												[#if  priceInfoBean.payType='PP']
													 <!-- 结算方式 -->
													[#if price.type=="DisplayCostCurrency"]
	                                                    [#assign roomPrice=price.inclusiveAmount*ctripHotelPriceRate]
														[#break]
													[/#if]
													<!-- 兼容佣金方式 -->
													[#if roomPrice==null && price.type=="DisplayCurrency"]
														[#assign roomPrice=price.inclusiveAmount]
													[/#if]
												[#else]
													[#if price.type=="DisplayCurrency"]
	                                                    [#assign roomPrice=price.inclusiveAmount]
														[#break]
													[/#if]
												[/#if]
											[/#list]
										    [#assign canReserve=priceInfoBean.getReserve()] <!-- 是否可预订-->
										     [#assign payType=priceInfoBean.payType] <!-- 表示预付还是到付-->
                                             [#assign isGuarantee=priceInfoBean.getGuaranteeStr()] <!-- 表示售卖房型是否需担保，true-需担保，false-不需担保-->
										     [#assign guaranteeCode=priceInfoBean.dailyPrices[0].guaranteeCode] <!-- 表示预付还是到付-->

											[#break]
										[/#if]
									[/#list]
									
									[#if roomPrice?? && priceRate??]<!-- 企业价格 -->
									[#assign roomPrice = (roomPrice + roomPrice * priceRate)?ceiling]
									[/#if]
								[/#if]
						   		[#if tripLevelConfig??]
									[#if tripLevelConfig.hotelFlag]
										[#if tripLevelConfig.firstLevelCities?? && tripLevelConfig.firstLevelCities?index_of(fromCityName!"null") gte 0]
											[#if roomPrice?? && tripLevelConfig.firstLevelPrice?? && tripLevelConfig.firstLevelPrice lt (roomPrice/diffDays)]
												[#assign msg = msg + "平均每晚房费超标、"]
											[/#if]
											[#if tripLevelConfig.firstLevelStar?? && tripLevelConfig.firstLevelStar gt 0 && tripLevelConfig.firstLevelStar lt hotel.starRate]
												[#assign msg = msg + "酒店星级超标、"]
											[/#if]
										[#elseif tripLevelConfig.secondLevelCities?? && tripLevelConfig.secondLevelCities?index_of(fromCityName!"null") gte 0]
											[#if roomPrice?? && tripLevelConfig.secondLevelPrice?? && tripLevelConfig.secondLevelPrice lt (roomPrice/diffDays)]
												[#assign msg = msg + "平均每晚房费超标、"]
											[/#if]
											[#if tripLevelConfig.secondLevelStar?? && tripLevelConfig.secondLevelStar gt 0 && tripLevelConfig.secondLevelStar lt hotel.starRate]
												[#assign msg = msg + "酒店星级超标、"]
											[/#if]
										[#elseif tripLevelConfig.thirdLevelCities?? && tripLevelConfig.thirdLevelCities?index_of(fromCityName!"null") gte 0]
											[#if roomPrice?? && tripLevelConfig.thirdLevelPrice?? && tripLevelConfig.thirdLevelPrice lt (roomPrice/diffDays)]
												[#assign msg = msg + "平均每晚房费超标、"]
											[/#if]
											[#if tripLevelConfig.thirdLevelStar?? && tripLevelConfig.thirdLevelStar gt 0 && tripLevelConfig.thirdLevelStar lt hotel.starRate]
												[#assign msg = msg + "酒店星级超标、"]
											[/#if]
										[#else]
											[#if roomPrice?? && tripLevelConfig.otherLevelPrice?? && tripLevelConfig.otherLevelPrice lt (roomPrice/diffDays)]
												[#assign msg = msg + "平均每晚房费超标、"]
											[/#if]
											[#if tripLevelConfig.otherLevelStar?? && tripLevelConfig.otherLevelStar gt 0 && tripLevelConfig.otherLevelStar lt hotel.starRate]
												[#assign msg = msg + "酒店星级超标、"]
											[/#if]
										[/#if]

										[#if msg != ""]
											[#if tripLevelConfig.hotelExceedConctrol == "unLimited"]
											[#assign showFlag = 0]
											[#elseif tripLevelConfig.hotelExceedConctrol == "orderLimit"]
											[#assign showFlag = 1]
											[#elseif tripLevelConfig.hotelExceedConctrol == "displayLimit"]
											[#assign showFlag = 2]
											[/#if]
										[#else]
											[#assign showFlag = -1]
										[/#if]
									[/#if]
								[/#if]
								[#if yphFlag?? && payType ?? && payType == "FG"]<!-- 优品汇不显示到付 -->
								[#assign showFlag = 2]
								[/#if]
								[#if showFlag != 2]
								[#assign roomInfoBean=roomStaticMap[roomPriceInfo.roomID+""]] <!-- 房型静态信息获取对应的roomId和静态信息map-->
                                     [#assign invoiceType = roomInfoBean.invoiceType]
									[#if company!=null && company.invoiceFlag!=null]
										[#if company.invoiceFlag==1] <!-- 只要平台统一开票的房间-->
											[#if invoiceType?? && invoiceType=2]
												[#assign isHidden=true]
											[/#if]
										    <!-- 到店支付的需要全部隐藏-->
											[#if payType=='FG']
												[#assign isHidden=true]
											[/#if]
										[/#if]
										[#if company.invoiceFlag==2] <!-- 只要酒店自取的房间-->
											[#if payType=='PP']
												[#if invoiceType==null || invoiceType!=2]
													[#assign isHidden=true]
												[/#if]
											[/#if]
										[/#if]
									[/#if]


									[#if company!=null && company.payTypeFlag!=null]
										[#if company.payTypeFlag==1] <!-- 只要在线支付的房间-->
											[#if payType=='FG']
												[#assign isHidden=true]
											[/#if]
										[/#if]

										[#if company.payTypeFlag==2] <!-- 只要在线支付的房间-->
											[#if payType=='PP']
												[#assign isHidden=true]
											[/#if]
										[/#if]
									[/#if]

					  <div class="bottom" [#if isHidden]style="display:none"[/#if]>
						<div class="left" roomId="${roomPriceInfo.roomID}">
							<p class="title">${roomPriceInfo.roomName}</p>
							<p>


								<!-- 解析预订规则，最小起订量和最大起订量-->
								[#if roomInfoBean!=null && roomInfoBean.bookingRules!=null]
									[#list roomInfoBean.bookingRules as bookingRule]
										[#if bookingRule_index==0]
											[#if bookingRule.totalOccupancy!=null]
                                                <input id="minQty${roomPriceInfo.roomID}" type="hidden" name="minQty"
													   [#if bookingRule.totalOccupancy.min!=null]value="${bookingRule.totalOccupancy.min}"
													   [#else]value="WdatePicker.js"></script>1"[/#if]>
                                                <input id="maxQty${roomPriceInfo.roomID}" type="hidden" name="maxQty"
													   [#if bookingRule.totalOccupancy.max!=null]value="${bookingRule.totalOccupancy.max}"
													   [#else]value="10"[/#if]>
											[#else]
											     <input  id="minQty${roomPriceInfo.roomID}"  type="hidden" name="minQty"  value="1" />
								            	<input id="maxQty${roomPriceInfo.roomID}" type="hidden" name="maxQty"  value="10" />
											[/#if]
											[#break]
										[/#if]
									[/#list]
								[#else]
								    <input  id="minQty${roomPriceInfo.roomID}"  type="hidden" name="minQty"  value="1" />
									<input id="maxQty${roomPriceInfo.roomID}" type="hidden" name="maxQty"  value="10" />
								[/#if]

								<!-- 床型信息开始-->
								[#if roomInfoBean != null && roomInfoBean.roomBedInfos!=null && roomInfoBean.roomBedInfos?size>0]
									[#list roomInfoBean.roomBedInfos as roomBedInfoBean]
										[#if roomBedInfoBean_index==0]
											[#if roomBedInfoBean.bedInfo!=null && roomBedInfoBean.bedInfo?size>0]
                                                  <input type="hidden" id="bedInfo${roomPriceInfo.roomID}" value="${roomBedInfoBean.name}">
                                                 ${roomBedInfoBean.name}
											[#else]
                                                未知
											    <input type="hidden" id="bedInfo${roomPriceInfo.roomID}" value="未知">
											[/#if]
											[#break]
										[/#if]
									[/#list]
								[#else]
                                    未知
								    <input type="hidden" id="bedInfo${roomPriceInfo.roomID}" value="未知">
								[/#if]
								<!-- 床型信息结束-->

								&nbsp;&nbsp;&nbsp;
								<!--早餐信息开始-->
								[#if roomInfoBean!=null]
									[#assign roomPriceInfo=roomPriceInfoMap[roomInfoBean.roomID+""]]
								[/#if]
								[#if roomPriceInfo!=null && roomPriceInfo.priceInfos!=null && roomPriceInfo.priceInfos?size>0]
									[#list roomPriceInfo.priceInfos as priceInfoBean]
										[#if priceInfoBean_index==0]
                                            <input type="hidden" id="payType${roomPriceInfo.roomID}"
                                                   value="${priceInfoBean.payType}">
                                            <!-- 房间属于预付还是到付-->
                                            <input type="hidden"
                                                   id="ratePlanCategory${roomPriceInfo.roomID}"
                                                   value="${priceInfoBean.ratePlanCategory}">
                                            <!-- 501-标准预付房型; -->
											[#if priceInfoBean.dailyPrices!=null && priceInfoBean.dailyPrices?size>0]
												[#list priceInfoBean.dailyPrices as dailyPriceBean]
													[#if dailyPriceBean_index==0]
														[#if dailyPriceBean.mealInfo!=null]
														${dailyPriceBean.mealInfo.mealsDesc!"无" }
														<input type="hidden" id="breakfast${roomPriceInfo.roomID}" value="${dailyPriceBean.mealInfo.mealsDesc!"无" }">
														[#else]
                                                            无
														<input type="hidden" id="breakfast${roomPriceInfo.roomID}" value=" 无">
														[/#if]
														[#break]
													[/#if]
												[/#list]
											[/#if]
											[#break]
										[/#if]
									[/#list]
								[/#if]
								<!--早餐信息结束-->


								 
								
							</p>
							
							<p>
								<!-- 是否允许取消-->
								[#if roomPriceInfo.cancelPolicyInfos==null || roomPriceInfo.cancelPolicyInfos?size==0]
                                        限时取消
								        <input type="hidden" value="${arrivalDate} 12:00前可免费取消" id="cancelMsg${roomPriceInfo.roomID}">
                                        <script type = "html/javascript" id = "toggle_li_info${roomPriceInfo.roomID}" cancelPolicyMsg="限时取消">
                                        <span class= "layer_close_btn" > </span>
                                        <header style = "opacity: 1.07383;" >${roomPriceInfo.roomName}</header>
                                        <div  class="simple_bottom_layer">
                                            <label>限时取消 </label>
                                             <div class= "value">
										        ${arrivalDate} 12:00前, 订单可免费取消
                                            </div>
                                         </div>
                                    </script>

								[#else]
									[#list roomPriceInfo.cancelPolicyInfos as cancelPolicyInfo]
										[#if cancelPolicyInfo_index==0]
											[#if cancelPolicyInfo.start!=null]
											     [#if cancelPolicyInfo.getFormatStart()!=null]
												    [#list cancelPolicyInfo.getFormatStart()?split(",") as cancelPolicyMsg]
                                                       [#if cancelPolicyMsg_index==0]
                                                           [#assign cancelPolicyMsg=cancelPolicyMsg ]
													   [/#if]
                                                        [#if cancelPolicyMsg_index==1]
                                                             [#assign cancelPolicyTime=cancelPolicyMsg]
                                                        [/#if]
													[/#list]
												 [/#if]
												  ${cancelPolicyMsg}

												[#if cancelPolicyMsg!=null  && cancelPolicyMsg=='限时取消']
                                                    <input type="hidden" value="${cancelPolicyTime}前可免费取消"
                                                           id="cancelMsg${roomPriceInfo.roomID}">
												[#else]
                                                    <input type="hidden" value="不可取消"
                                                           id="cancelMsg${roomPriceInfo.roomID}">
												[/#if]

											      [#if cancelPolicyMsg!=null  && cancelPolicyMsg=='限时取消']
											     <script type="html/javascript" id="toggle_li_info${roomPriceInfo.roomID}" cancelPolicyMsg="${cancelPolicyMsg}">
						                            <span class="layer_close_btn"></span>
                   	 	                            <header style="opacity: 1.07383;">${roomPriceInfo.roomName}</header>
					                                <div class="simple_bottom_layer">
							                            <label>${cancelPolicyMsg}</label>
						                                <div class="value">
						  	                                ${cancelPolicyTime}前,订单可免费取消
						  	                           </div>
					                              </div>
					                           </script>
												  [/#if]
											[/#if]
											[#break]
										[/#if]
									[/#list]
								[/#if]
								<!-- 是否允许取消-->
								 [#if priceInfoBean.getInstantConfirmStr()!=null && priceInfoBean.getInstantConfirmStr()=='true']
								  <span class="clickable">立即确认</span>
								[/#if]

								<span class="clickable">
									[#if payType=='PP' ]

										[#if invoiceType?? && invoiceType=2]
                                            酒店自取
										[#else]
                                            企业统开
										[/#if]
									[#else]
                                        酒店自取
									[/#if]
								</span>
							</p>
						</div>
						<div class="right" [#if showFlag != 1 && canReserve=="true"] onclick="reserve('${msg }','${roomTypeInfoBean.roomTypeID}','${roomPriceInfo.roomID}','${roomPriceInfo.roomName}','${isGuarantee}','${holdTime}','${guaranteeCode}')"[/#if]>
                            <div class="price_section">
                                <div class="price">￥
                                <span days="${diffDays }"> <!-- 平均房价，取税后价-->
                                    ${(roomPrice/diffDays)?round?string}
                                </span>
                                </div>
                                 [#if ctripHotelRate != null && ctripHotelRate > 0]
                                    [#if payType=='PP']
                                         <label>立减${(((roomPrice-roomPrice*ctripHotelRate)/diffDays)?floor)?int}元</label>
									[#else]
                                         <label>离店返${(((roomPrice-roomPrice*ctripHotelRate)/diffDays)?floor)?int}元</label>
									[/#if]
                                 [/#if]
						    </div>
						    <div class="btn_section">
						    [#if showFlag == 1]
						    <a class="btn_comp dis" msg="${msg }" href="javascript:void(0);">
                                <div class="btn_top">超标</div>
                                <div class="btn_bottom">不可订</div>
                            </a>	
						    [#else]
								[#if canReserve=="true"]
								     <a class="btn_comp"  href="javascript:void(0);">
	                                    <div class="btn_top">预订</div>
										 [#if payType=='PP']
										     <div class="btn_bottom text_theme">在线付</div>
										 [#else]
										     <div class="btn_bottom text_y">
										         [#if isGuarantee=="true"]
	                                                 担保
												 [#else]
	                                                 到店付
										         [/#if]
											 </div>
										 [/#if]
	                                 </a>
								[#else]
								     <a class="btn_comp dis"  href="javascript:void(0);" >
	                                    <div class="btn_top">订完</div>
	                                    [#if payType=='PP']
										     <div class="btn_bottom text_theme">在线付</div>
										 [#else]
										 <div class="btn_bottom text_y">
										      [#if isGuarantee=="true"]
	                                                 担保
												 [#else]
	                                                 到店付
										         [/#if]
										 </div>
										 [/#if]
	                                 </a>
								[/#if]
						    [/#if]
							</div>
						</div>
					  </div>

					  		[/#if]
						[/#if]
					[/#list]

				</li>
					[/#if]
			[#--	[/#if]--]
				[/#list]
			</ul>
            [#else]
				 <div class= "public_empty_section2">
                        <img src="${base}/resources/wechat/img/recharge/img_search_hotel.png" />
						[#if tipMsg!=null]
                            <p> ${tipMsg} </p>
						[#else]
						    <p> 房型已订完，建议您更改入住时间或选择其他酒店 </p>
						[/#if]

                </div>

			[/#if]
            <input type="hidden" id="agentMemberId" name="agentMemberId" value="${agentMemberId}">
			 <input type="hidden" name="hotelId" id="hotelId" value="${hotel.hotelId}"/>
             <input type="hidden" name="newArriveDate" id="newArriveDate" value="${newArriveDate}" class="input_text" readonly="readonly" />
		</div>
		
		<!--日期选拔-->
		<div id="demo"></div>

		<div id="layer_hotel_intro" type="hidden"></div>






		<script>
			$(function(){
			      addCookie("highConditionSpans", "", {expires: 30*24 * 60 * 60});
                  addCookie("starAndPriceSpans", "", {expires: 30*24 * 60 * 60});
                  addCookie("positionAndDistrictSpans", "", {expires: 30*24 * 60 * 60});
                  isShowMap();
				<!-- 酒店图片不为空时才加载-->
				[#if hotelImages!=null && hotelImages?size>0]
				     var mySwiper = new Swiper('.swiper-container',{
                    on:{
                        reachEnd: function(){
                            setTimeout('location.href="photo.jhtml?hotelId=${hotel.hotelId}&agentMemberId=${agentMemberId}&isPublic=${isPublic}";',1000)
                        },
                    },
                 });
				[/#if]


                $("#img_banner").on("click",function(){
                    location.href="photo.jhtml?hotelId=${hotel.hotelId}&agentMemberId=${agentMemberId}&isPublic=${isPublic}";
                })
				
				$.fn.raty.defaults.path = '${base}/resources/wechat/plugins/raty-2.5.2/img';
				$('.stars').raty({ 
					score: function() {
					    return $(this).attr('data-score');
					},
					readOnly: true,
				});
				
				$("ul li .top .right").on("click",function(){
				    var hasRoom=$(this).attr("hasRoom");
				    if(hasRoom!="0"){ //表示房间已全部预定完
				        $(this).parents("li").toggleClass("active");
            }
				})
				
				$("ul li .top").each(function(){
				   if($(this).siblings().length==0){
				       $(this).parent("li").hide();
				   }
				}) 

				$(".room_ul").find("li").each(function(){
				   if($(this).is(':visible')){//只要有一个显示则隐藏
				       $(".public_empty_section2").hide();
				   }
				})

                $("body").on("click",".hotel_facilities .open",function(){
                    $(this).toggleClass("active").parent().prev("ul").toggle();
                    if($(this).children(".text").text() == "收起"){
                        $(this).children(".text").text("更多房型设施");
                    }else{
                        $(this).children(".text").text("收起");
                    }
                })

                $("body").on("click",".policy_service .open",function(){
                    $(this).toggleClass("active").parent().prevAll(".display").toggle();
                    if($(this).children(".text").text() == "收起"){
                        $(this).children(".text").text("加早加床停车场、儿童政策");
                    }else{
                        $(this).children(".text").text("收起");
                    }
                })

                //点击房型弹框
                 $(".desc_section").on("click",function(){
                    var roomTypeID=$(this).attr("roomTypeID");
                    layer.open({
                        type: 1,
                        content: $("#layer_hotel_intro"+roomTypeID).html(),
                        className: 'fli-layer-footer',
                        shadeClose:true,
                        anim:"up",
                        success: function () {
                            var swiper = new Swiper('.swiper-container',{
                                pagination: {
                                    el: '.swiper-pagination',
                                },
                            });

                            $(".layer_hotel_intro").scroll(function(){
                                var selfTop = $(this).scrollTop();
                                var H = $(".layer_hotel_intro .swiper-container").height();
                                var opacity_v = 1-(H-selfTop)/H;
								//console.log(opacity_v);
                                $(".layui-m-layercont>header").css("opacity",opacity_v);
                            })

                            $(".layer_close_btn").on("click",function(){
                                layer.closeAll();
                            })
                        }
                    })
                })
                
                $(".bottom .left").on("click",function(){
                    var roomId=$(this).attr("roomId");
                	event.stopPropagation();
                	var cancelPolicyMsg=$("#toggle_li_info"+roomId).attr("cancelPolicyMsg");
                	if(cancelPolicyMsg!=null && cancelPolicyMsg=='限时取消'){
                        layer.open({
	                        type: 1,
	                        content: $("#toggle_li_info"+roomId).html(),
	                        className: 'fli-layer-footer',
	                        shadeClose:true,
	                        anim:"up",
	                        success: function () {
	                            $(".layer_close_btn").on("click",function(){
	                                layer.closeAll();
	                            })
	                        }
	                    })
					}

                })
				
				
			   var arrivalDate=new Date($("#arrivalDate").val());
		       var departureDate=new Date($("#departureDate").val());
		       var b_time=arrivalDate.getTime();
		       var e_time=departureDate.getTime();
		       var c_time = e_time - b_time;
			   var day_num = c_time/(1000*60*60*24);
			   if(day_num<=0){
			      departureDate.setDate(arrivalDate.getDate()+1); 
			      departureDate=formatDate(departureDate);
			      $("#departureDate").val(departureDate); 
			      $(".total").text("共1晚");
			   }else{
			      $(".total").text("共"+day_num+"晚");
			   }
			   
			   
			   function formatDate(date) {
                var d = new Date(date),
    			month = '' + (d.getMonth() + 1),
    			day = '' + d.getDate(),
    			year = d.getFullYear();
 
  				if (month.length < 2){
  				   month = '0' + month;
  				} 
  				if (day.length < 2){
  				   day = '0' + day;
  				} 
 
  				return [year, month, day].join('-');
			  }

			})
			

            //点击返回
            function goBack(ev) {
                var eve =ev||window.event;
                eve.stopPropagation()
                var url="list.jhtml";
                var previousPageUrl = document.referrer; //获取上一个页面来源
                if(previousPageUrl.indexOf("mapSelection.jhtml")>-1){ //是从酒店列表跳转过来
                    url="mapSelection.jhtml"
                }
                [#if applyId?? ]
                url+="?applyId=${applyId}&fromCityName=${fromCityName}&arrivalDate=${arrivalDate}&departureDate=${departureDate}&agentMemberId=${agentMemberId}&isPublic=${isPublic}&hotelDistrictName=${hotelDistrictName}&longitude=${longitude}&latitude=${latitude}"
                [#else ]
                  url+="?fromCityName=${fromCityName}&arrivalDate=${arrivalDate}&departureDate=${departureDate}&isPublic=${isPublic}&hotelDistrictName=${hotelDistrictName}&longitude=${longitude}&latitude=${latitude}"
                [/#if]
                location.href = "${base}/ctrip_hotel/"+url;
            }
		    //点击预订
		    function reserve(msg,roomTypeId,roomId,roomTypeName,guarantee,holdTime,guaranteeCode){
				$("body").append('<div class="pub_ajax_loading"><div class="box"><span class="icon_loading"></span><p>正在处理，请稍候...</p></div></div>')
		       	var hotelId=$("#hotelId").val();
		       	var arrivalDate=$("#arrivalDate").val();
		        var departureDate=$("#departureDate").val();
		        var fromCityName="${fromCityName}";
		         var bedType=$("#bedInfo"+roomId).val();
		        if(bedType!=null){
		            bedType=bedType.trim();
				}
		        var floor=$("#floor"+roomTypeId).attr("floor");
		        if(floor!=null){
		            floor=floor.trim();
				}
		        var hotelAddress="${hotel.address}";
		        if(hotelAddress!=null){
		            hotelAddress=hotelAddress.trim();
				}
                var roomTypeImg=$("#img"+roomTypeId).attr("src");
				if(roomTypeImg!=null){
				    roomTypeImg=roomTypeImg.trim();
				}
				var hotelName="${hotel.hotelName}";
				if(hotelName!=null){
				    hotelName=hotelName.trim();
				}
				var payType=$("#payType"+roomId).val();  //房间属于预付还是到付
				var ratePlanCategory=$("#ratePlanCategory"+roomId).val();  //房间属于预付还是到付
				var minQty=$("#minQty"+roomId).val();  //最小起订量
				var maxQty=$("#maxQty"+roomId).val();  //最大起订量
				if(minQty==null || typeof(minQty)=="undefined" || minQty==""){
                        minQty=1;
				}

				if(maxQty==null || typeof(maxQty)=="undefined" || maxQty==""){
                        maxQty=10;
				}
				var breakfast=$("#breakfast"+roomId).val(); //早餐
                if(breakfast==null || typeof(breakfast)=="undefined" || maxQty==""){
                        breakfast="无";
				}

				var hourlyRoom= $("#hourlyRoom"+ roomId).val();  //是否钟点房true表示是
                if(hourlyRoom==null || hourlyRoom==""){
                    hourlyRoom=false;
                }

               if(holdTime==null){
                    holdTime="";
                }
               if(guaranteeCode==null){
                    guaranteeCode="";
                }

                var cancelMsg=$("#cancelMsg"+ roomId).val();
               if(cancelMsg==null){
                   cancelMsg="不可取消";
               }

                var applyId='${applyId}';
                var agentMemberId='${agentMemberId}';
                var isPublic='${isPublic}';

                //因私不带申请单和代订人
                if(isPublic=="2"){
                    applyId="";
                    agentMemberId="";
                }
                var url = "${base}/ctrip_hotel/roomTypeConfirm.jhtml?roomTypeId="+roomTypeId+"&roomId="+roomId+"&arrivalDate="+arrivalDate+"&departureDate="+departureDate+"&hotelId="+hotelId+
                    "&roomTypeName="+roomTypeName+"&bedType="+bedType+"&floor="+floor+"&hotelAddress="+hotelAddress+"&roomTypeImg="+roomTypeImg+"&hotelName="+hotelName+
                    "&payType="+payType+"&ratePlanCategory="+ratePlanCategory+"&fromCityName="+fromCityName+"&minQty="+minQty+"&maxQty="+maxQty+"&breakfast="+breakfast+"&guarantee="+guarantee+"&hourlyRoom="+hourlyRoom+"&holdTime="+holdTime+"&guaranteeCode="+guaranteeCode+"&cancelMsg="+cancelMsg;

                if( applyId != ""){
                    url+="&applyId="+applyId+"&tripRuleBroken=" + msg;
                }
                //代订
                if(agentMemberId != ""){
                    url+="&agentMemberId="+agentMemberId;
                }
                //从新差旅官网过来
                if(isPublic != ""){
                    url+="&isPublic="+isPublic;
                }
                location.href=url;
                $("body>.pub_ajax_loading").remove();
		    } 

		    var newDate="${newArriveDate}";
		    var fromCityName="${fromCityName}";
		    var hotelId=$("#hotelId").val();
		    mobiscroll.range('#demo', {
               theme: 'ios',
               lang: 'zh',
    		   display: 'top',
    		   startInput: '#arrivalDate',
   			   endInput: '#departureDate',
   			   dateFormat:'yy-mm-dd',
    		   fromText: "入住时间",
               toText: "离开时间",
               min: new Date("${newArriveDate}"),
               onBeforeClose: function (event, inst) {
                   if(event.button==="set"){ //点击的是确定
                       var startDate=$(".mbsc-range-btn-v-start").text();
                       var endDate=$(".mbsc-range-btn-v-end").text();
                       if(endDate.length == 1){
                           startDate = $("#arrivalDate").val();
                           endDate = $("#departureDate").val();
                       }
                       var arrivalDate=new Date(startDate);
                       var departureDate=new Date(endDate);
                       var b_time=arrivalDate.getTime();
                       var e_time=departureDate.getTime();
                       var c_time = e_time - b_time;
                       var day_num = c_time/(1000*60*60*24);
                       $(".total").text("共"+day_num+"晚");

                       var applyId='${applyId}';
                       var agentMemberId='${agentMemberId}';
                       var isPublic='${isPublic}';

                       //因私不带申请单和代订人
                       if(isPublic=="2"){
                           applyId="";
                           agentMemberId="";
                       }
                       var url = "detail.jhtml?fromCityName="+fromCityName+"&hotelId="+hotelId+"&arrivalDate="+startDate+"&departureDate="+endDate;

                       if( applyId != ""){
                           url+="&applyId="+applyId;
                       }
                       //代订
                       if(agentMemberId != ""){
                           url+="&agentMemberId="+agentMemberId;
                       }
                       //从新差旅官网过来
                       if(isPublic != ""){
                           url+="&isPublic="+isPublic;
                       }
                       location.href=url;
                   }

               }
	        });
	      
		</script>
	</body>
</html>
