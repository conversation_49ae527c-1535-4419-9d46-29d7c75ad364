<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <meta name="apple-mobile-web-app-status-bar-style" content="black"/>
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no"/>
    <title>工业品品牌</title>
    <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
    <link rel="stylesheet" href="${base}/resources/wechat/css/vant/index.css">
    <link type="text/css" rel="stylesheet" href="${base}/resources/wechat/css/common.css">
    <link type="text/css" rel="stylesheet" href="${base}/resources/wechat/css/brandList.css">
    <script src="${base}/resources/wechat/plugins/vue/vue.min.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

    <script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/shopCommon.js"></script>
    <script src="${base}/resources/wechat/js/vant.min.js"></script>
</head>
[#assign couponMode = (couponCountVo?? && couponCountVo.couponMode)]
<body class="bocPage brandListPage industrial-barndlist-page">
<div id="app" @click.stop="hideHomeList">
    <van-sticky>
        <van-nav-bar
                title="工业品品牌"
                left-text=""
                left-arrow
                @click-left.stop="onClickLeft"
                @click-right.stop="onClickRight"
        >
            <template #right>
                <van-icon name="ellipsis"/>
                <ul class="showHomeList  modify_public_right_nav " v-show="showHomeList">
                    <li>
                        <a href="[#if index_link?? && index_link != ""]${index_link}[#else]${base}/index.jhtml[/#if]">
                            <span class="icon_home"></span>
                            <p>首页</p>
                        </a>
                    </li>

                    <li>
                        <a href="${base}/cart/list.jhtml">
                            <span class="icon_shoppingCart"></span>
                            <p class="nav_top_cartNum">
                                购物车
                                <span class="num_circle" id="cart_quantity" v-cloak>
                                   {{ cart_quantity}}
                                </span>
                            </p>
                        </a>
                    </li>

                    <li>
                        <a href="[#if my_home_link?? && my_home_link != ""]${my_home_link}[#else]${base}/member/index.jhtml[/#if]">
                            <span class="icon_user"></span>
                            <p>个人中心</p>
                        </a>
                    </li>
                </ul>
            </template>
        </van-nav-bar>
    </van-sticky>
    <van-index-bar
            :sticky-offset-top="46"
            z-index="0"
            :index-list="indexList"
    >
        <div v-for="(item,index) in  brandList"
             v-if="item!=null"
             :key="'brands_list_'+index">
            <van-index-anchor :index="item.id"></van-index-anchor>
            <ul class="brandList" v-if="item.list.length>0">
                <li class="flexbox align-items-c" v-for="(ite,index) in  item.list"
                    @click="goDetail(ite)"
                    v-if="ite!=null"
                    :key="'brandsitem_list_'+index">
                    <div class="img-box flexbox align-items-c justify-content-c" >
                        <img :src="ite.icon"/>
                    </div>
                    <span v-cloak>{{ite.name}}</span>
                </li>
            </ul>
        </div>
    </van-index-bar>
</div>
<script>
    let vm = new Vue({
        el: "#app",
        data() {
            return {
                cart_quantity:0,//购物车数量
                showHomeList: false,
                indexList: [ //索引字符列表
                	[#list brandKeys as brandKey]
                    "${brandKey}",
                    [/#list]
                ] ,
                brandList: [
                	[#list brandKeys as brandKey]
                    {
                        id: "${brandKey}",
                        list: [
                        	[#list allBrand.get(brandKey) as brand]
                            {
                                icon: "${base}${brand.logo}",
                                name: "${brand.name}",
                                links: "${base}/product/search.jhtml?productCategoryId=${productCategoryId}&keyword=${brand.name}&brandName=${brand.name}",
                            },
                            [/#list]
                        ]
                    },
                    [/#list]
                ]
            };
        },
        computed: {},
        mounted() {
            this.refreshCartConut();
        },

        methods: {
            hideHomeList() {
                this.showHomeList = false;
            },
            goDetail(data) {
                window.location.href = data.links;
            },
            onClickLeft() {
                history.back();
            },
            onClickRight() {
                this.showHomeList = !this.showHomeList;
            },
            refreshCartConut(){
                var that = this;
                jQuery.post("${base}/cart/quantity.jhtml","",function(data){
                    if(data!=null && data.totalQuantity>0){
                        $("#nav_top_cartNum").find("label").remove();
                        if(data.totalQuantity>99){
                            that.cart_quantity = 99;
                        }else{
                            that.cart_quantity = data.totalQuantity;

                        }
                    }
                },"json");
            },
        },
    });
</script>
</body>
</html>
