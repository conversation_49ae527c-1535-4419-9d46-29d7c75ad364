<input type="hidden" id="totalPages" value="${page.totalPages}"/>
[#assign month=""]
[#if page.total > 0]
    [#list page.content as gift_item]
        [#if lastTime == (gift_item.createDate)?string("yyyy年MM月")]
        <div class="style-list-main">
            <div class="style-list-item" onclick="location.href='${base}/giftGiving/myGiftView.jhtml?id=${gift_item.id}'">
                <div class="style-list-left">
                    <img class="style-list-img" src="${base}${gift_item.giftImage}" alt="">
                    <div class="style-list-info">
                            [#if type == 1]
                            <div class="name"><strong>收到${gift_item.memberName}</strong> </div>
                            [#else]
                            <div class="name"><strong>送给${gift_item.receiveName}</strong> </div>
                            [/#if]
                        <div class="type">${gift_item.giftName}</div>
                            [#if type == 1]
                            <div class="name"><div class="type">收到时间：${gift_item.createDate}</div> </div>
                            [#else]
                            <div class="name"><div class="type">赠送时间：${gift_item.createDate}</div> </div>
                            [/#if]
                    </div>
                </div>
            </div>
        </div>
        [#else ]
            [#if gift_item.createDate??]
                [#if month != (gift_item.createDate)?string("yyyy-MM")]
                    [#assign month=(gift_item.createDate)?string("yyyy-MM")]
                    [#if gift_item_index == 0]
                    <div>
                        <div class="gift-style-date">
                            ${(gift_item.createDate)?string("yyyy年MM月")}
                        </div>
                        <div class="style-list-main">
                    [#else]
                        </div>
                    </div>
                    <div>
                        <div class="gift-style-date">
                            ${(gift_item.createDate)?string("yyyy年MM月")}
                        </div>
                        <div class="style-list-main">

                    [/#if]
                [/#if]
            [/#if]

                <div class="style-list-item" onclick="location.href='${base}/giftGiving/myGiftView.jhtml?id=${gift_item.id}'">
                    <div class="style-list-left">
                        <img class="style-list-img" src="${base}${gift_item.giftImage}" alt="">
                        <div class="style-list-info">
                            [#if type == 1]
                            <div class="name"><strong>收到${gift_item.memberName}</strong> </div>
                            [#else]
                            <div class="name"><strong>送给${gift_item.receiveName}</strong> </div>
                            [/#if]
                            <div class="type">${gift_item.giftName}</div>
                            [#if type == 1]
                            <div class="name"><div class="type">收到时间：${gift_item.createDate}</div> </div>
                            [#else]
                            <div class="name"><div class="type">赠送时间：${gift_item.createDate}</div> </div>
                            [/#if]
                        </div>
                    </div>
                </div>

            [#if gift_item_index == page.content?size]
			        </ul>
		        </div>
            [/#if]
        [/#if]
    [/#list]
[#else]
    <div class="public_empty_section [#if comPersonConf && com_person_EmptyBg != null && com_person_EmptyBg != ""] ${com_person_EmptyBg!}[#else] Public_Bg[/#if]">
        <p class="tip">这里空空如也，快去送礼吧~</p>
        <a href="${base}/giftGiving/index.jhtml" class="pub_link_theme">去送礼</a>
    </div>
[/#if]