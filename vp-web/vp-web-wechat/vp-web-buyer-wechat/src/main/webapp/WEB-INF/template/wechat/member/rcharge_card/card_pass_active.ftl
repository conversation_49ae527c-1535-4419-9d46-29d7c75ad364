<!DOCTYPE html>
<html style="background: #fff;">
	<head>
		<meta charset="utf-8" />
		<title>卡密激活</title>
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="black">
        <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js" ></script>
        <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
        <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/login.css" />
        <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/slide.css" />
		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>


		<script type="text/javascript" src="${base}/resources/wechat/js/jquery.validate.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
	    <script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js" ></script>
        <script type="text/javascript" src="${base}/resources/wechat/js/jquery.lgyslide.js"></script>
        <script type="text/javascript" src="${base}/resources/wechat/js/jigsawPuzzle.js"></script>
	</head>
	<body style="background: #f4f7fe;" > 
		<div class="public_top_header">
			<a href="javascript:history.go(-1);" class="return_back"></a>
			卡密
		</div>
		<div class="fli_main_page"> 
			<h2>卡密激活</h2>
			<div class="login_form_section">
				<form id="activeForm" action="" method="post">
					<label for="password" class="text_center">请在下框输入卡密</label>
					<div class="item standard_input">
						<input type="text" id="password" name="password" class="input" placeholder="请输入卡密" maxlength="19" v-model="cardKey" v-on:input="handleInput"/>
						<span class="icon_clear"></span>
					</div> 
					<!--<div class="item hasbutton standard_input">
						<input type="text" name="captcha" id="captcha" value="" placeholder="请输入验证码" class="input" oninput="if(value.length>4){value=value.slice(0,20)}"/>
						<span class="icon_clear"></span>
						<img id="captchaImage" class="captchaImage" src="${base}/common/captcha.jhtml?captchaId=${captchaId}" title="${message(" shop.captcha.imageTitle ")}" >
						<div class="text_error2" style="display: none;" id="memo">卡密错误！</div>
					</div> -->
					<input type="button" value="查询" id="btn_get" class="btn_cornor_long" style="margin-top: 80px"/>
					<input type="submit" value="激活" id="loginSubmit" class="btn_cornor_long" [#if member.name=='游客']disabled="disabled"[/#if]/>
				    [#if member.name=='游客']<span style="color:red;font-size: 0.34rem;">游客模式不支持激活，请用正式账户!</span>[/#if]
				</form>
			</div>
		</div>
	
	</body>
		<script type="text/html" id="slider_code">
			<div id="imgscode"></div>
		</script>
		<script type="text/javascript" src="${base}/resources/wechat/plugins/vue/vue.min.js" ></script>
		<script type="text/javascript">
		
		
			var inputTester = new Vue({
			  	el: '#activeForm',
			  	data: {
			    	cardKey: ''
			  	},
			 	methods: {
			    	handleInput: function (event) {
			      		this.cardKey = event.target.value.replace(/[^A-z0-9+.]/g, '').replace(/....(?=.)/g, '$& ').toUpperCase();
			    	}
			  	}
			})
				var $captcha = $("#captcha");
				var $captchaImage = $("#captchaImage");
				
				// 更换验证码
				$captchaImage.click(function() {
					changeCaptchaImage();
				});
				function changeCaptchaImage(){
					$captchaImage.attr("src", "${base}/common/captcha.jhtml?captchaId=${captchaId}&timestamp=" + (new Date()).valueOf());
				}
				function chechCaptcha(){
					var captchaVal=$captcha.val();
					if(captchaVal==null||captchaVal==""){
						layer.open({
					              content: "请输入验证码"
					              ,skin: 'msg'
					              ,time: 2 //2秒后自动关闭
					           });
					    return true;
					}
				}
				
				
				  
		  var setTimer = function () { 
		    var list = [], 
		      interval; 
		  
		    return function (id, time,operateType) { 
		      if (!interval) 
		        interval = setInterval(go, 1000); 
		      list.push({ ele: document.getElementById(id), time: time ,operateType: operateType}); 
		    } 
		  
		    function go() { 
		      for (var i = 0; i < list.length; i++) { 
		        list[i].ele.value = getTimerString(list[i].time ? list[i].time -= 1 : 0,list[i].operateType); 
		        if (!list[i].time) 
		          list.splice(i--, 1); 
		      } 
		    } 
		  
		    function getTimerString(time,operateType) { 
		      var not0 = !!time, 
		        d = Math.floor(time / 86400), 
		        h = Math.floor((time %= 86400) / 3600), 
		        m = Math.floor((time %= 3600) / 60), 
		        s = time % 60; 
		      if (not0) {
		        return  s + "秒"; 
		        }
		      else {
		      	if(operateType=='query'){
		  			$("#btn_get").prop("disabled", false);
		  			$("#btn_get").css("background", "#346dfc");
		  			return "查询"; 
		  		}else{
		  			$("#loginSubmit").prop("disabled", false);
		  			$("#loginSubmit").css("background", "#346dfc");
		  			return "激活"; 
		  		}
		      	
		      }
		    } 
		  } (); 
		 
		  function operateInterval(operateType){
		  		if(operateType=='query'){
		  			setTimer("btn_get",5,operateType);
		  			$("#btn_get").prop("disabled", true);
		  			
		  			$("#btn_get").css("background", "#999999");
		  		}else{
		  			setTimer("loginSubmit",5,operateType);
		  			$("#loginSubmit").prop("disabled", true);
		  			$("#loginSubmit").css("background", "#999999");
		  		}
		 		
		  }
		  
		  let couponResult = {
		      "0":"充值卡未被激活",
		      "1":"充值卡已绑定!",
		      "2":"充值卡已过期!",
		      "3":"充值卡已被激活!",
		      "4":"充值卡不可用!",
		      "5":"卡密错误!",
		      "6":"验证码错误!",
		      "7":"请勿快速操作!",
		      "8":"由于您操作频繁，功能冻结24小时，请联系客服人员!",
		      "9":"请勿重复提交!",
		      "10":"账号权限不足!",
		      "11":"充值卡不在激活时间范围!",
		      "12":"充值卡绑定数量超过最大限制!",
		   }
		
			$(function(){				 
				var $submit=$("#loginSubmit");
				 
				$("#loginSubmit").click(function(){
					 // var flag=chechCaptcha();
					 //    if(flag){
					 //    	return false;
					 //    }
					var $code=$("#password").val().replace('.','');			    	 
			    	if ($code==null||$code=="") {
                        layer.open({
                            content: "请输入卡密"
                            ,skin: 'msg'
                            ,time: 2 //2秒后自动关闭
                        });
                        return;
                        // $("#memo").text("请输入卡密");
			            // $("#memo").show();
					}

                    layer.open({
                        type: 1,
                        title: false,
                        closeBtn: 0,
                        resize: false,
                        scrollbar: false,
                        area: ['auto', '420px'], //宽高
                        skin:"layer-code",
                        content: $("#slider_code").html(),
                        success: function () {
                            operateInterval("active");
                            createcode(cardPassActive);
                        }
                    });
				   return false;
				});
				
			    //查询
			    $("#btn_get").click(function(){
			   		 // var flag=chechCaptcha();
					    // if(flag){
					    // 	return false;
					    // }
			    	var $code=$("#password").val().replace('.','');		    	 
			    	if ($code==null||$code=="") {
                        layer.open({
                            content: "请输入卡密"
                            ,skin: 'msg'
                            ,time: 2 //2秒后自动关闭
                        });
                        return;
					}
                    layer.open({
                        type: 1,
                        title: false,
                        closeBtn: 0,
                        resize: false,
                        scrollbar: false,
                        area: ['auto', '420px'], //宽高
                        skin:"layer-code",
                        content: $("#slider_code").html(),
                        success: function () {
                            operateInterval("query");
                            createcode(queryPassActive);
                        }
                    });


				    });
			})
			//激活卡密
			function cardPassActive() {
                var $code=$("#password").val().replace('.','');
                let cardResult = {
                    "1":"充值卡已失效,如有疑问请联系发卡方!",
                    "2":"充值卡已过期!",
                    "3":"充值卡密已被激活!",
                    "4":"卡密错误!",
                    "5":"该卡密存在多条记录!",
                    "6":"验证码错误!",
                    "7":"请勿快速操作!",
                    "8":"由于您操作频繁，功能冻结24小时，请联系客服人员!",
                    "9":"请勿重复提交!",
                    "10":"充值卡被锁定,如有疑问请联系发卡方!",
                }
                $.ajax({
                    url:"${base}/recharge/cardPassActive.jhtml",
                    data:{code:$code},
                    type:'POST',
                    cache:false,
                    success:function(data){
                        // changeCaptchaImage();
                        if(data.flag!=0){
                            let str = "";
                            if($code.length==12){//券码
                                str = couponResult[data.flag];
                            }else{
                                str=cardResult[data.flag];
                            }
                            layer.open({
                                content: str
                                ,skin: 'msg'
                                ,time: 2 //2秒后自动关闭
                            });

                        }else{
                            let tipMsg = "";
                            let nextFn = "";
                            if($code.length==12){
                                tipMsg = "'激活成功！";
                                nextFn = "location.href = '${base}/member/my_convert.jhtml'";
                            }else{
                                tipMsg = '激活成功！本次充值金额'+data.cardAmount;
                                nextFn = "location.href = '${base}/member/coinConsume.jhtml'";
                            }
                            layer.open({
                                content: tipMsg
                                ,skin: 'msg'
                                ,time: 2 //2秒后自动关闭
                            });
                            setTimeout(nextFn,1200);
                        }
                    }
                })
            }
			//查询卡密
			function queryPassActive() {
                var $code=$("#password").val().replace('.','');
                $.ajax({
                    url:"${base}/recharge/queryPassActive.jhtml",
                    data:{code:$code},
                    type:'POST',
                    cache:false,
                    success:function(data){
                        // changeCaptchaImage();
                        let cardResult = {
                            "0":"充值卡未被激活，面值金额是:￥",
                            "1":"充值卡已失效,如有疑问请联系发卡方!",
                            "2":"充值卡已过期，面值金额是:￥",
                            "3":"充值卡已被激活，面值金额是：￥",
                            "4":"充值卡不可用,如有疑问请联系发卡方!",
                            "5":"卡密错误!",
                            "6":"验证码错误!",
                            "7":"请勿快速操作!",
                            "8":"由于您操作频繁，功能冻结24小时，请联系客服人员!",
                            "9":"请勿重复提交!",
                            "10":"充值卡被锁定,如有疑问请联系发卡方!",
                        }

                        for(var i in data){//通过定义一个局部变量i遍历获取map里面的所有key值
                            let str = "";
                            if($code.length==12){//券码
                                str = couponResult[i];
                            }else{
                                let msg = data[i];
                                str = cardResult[i]+((msg==0||msg==i)?"":msg);
                            }
                            layer.open({
                                content: str
                                ,skin: 'msg'
                                ,time: 2 //2秒后自动关闭
                            });
                        }
                    }
                });
            }
		</script>
</html>
