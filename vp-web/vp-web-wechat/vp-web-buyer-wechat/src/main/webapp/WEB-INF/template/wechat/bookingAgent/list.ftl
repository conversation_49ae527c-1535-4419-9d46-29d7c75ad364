<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
    <title>出行代订</title>
    <link rel="stylesheet" href="${base}/resources/wechat/css/common.css">
    <link rel="stylesheet" href="${base}/resources/wechat/css/travelapply.css">

    <script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/dropload.min.js" ></script>
    <script src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js"></script>
</head>
<body>
    <div class="selectPersonPage">
        <div class="public_top_header">
            <a href="${base}/travelHome/index.jhtml" class="return_back"></a>
                                    出行代订
            <a class="icon_bill" href="${base}/bookingAgent/tripOrderList.jhtml"></a>
        </div>
        <div class="searchbox">
        	<form id="orderSearchForm" action="${base}/bookingAgent/list.jhtml" method="post">
            <input type="text" name="userName" value="${userName}" class="input" placeholder="搜索出差人员" id="keyword">
            <span class="icon_search"></span>
            <span class="clear"></span>
            <button id="searchMember">搜索</button>
            </form>
        </div>
        <section class="section-box" id="sectionBox">
        	<div class="box">
				<ul class="memberUl">
				</ul>
			</div>
        </section>
    </div>

    <script type="text/html" id="selectTypeContent">
        <div class="radio_list">
		[#assign hasBooking = 0]
		[#if tripModules??]
    		[#list tripModules as module]
    			[#if module??]
					[#if module.code == 'TMCAirTicket']
						[#assign hasBooking = 1]
					<div class="item">
						<div class="fli_checkbox_blue">
							<input type="radio" name="tripType" id="tripType1" value="1" />
							<label for="tripType1"></label>
						</div>
						国内机票预订
					</div>
					[#elseif module.code == 'TMCInternationalTicket']
					[#elseif module.code == 'TMCTrain']
						[#assign hasBooking = 1]
					<div class="item">
						<div class="fli_checkbox_blue">
							<input type="radio" name="tripType" id="tripType2" value="2" />
							<label for="tripType2"></label>
						</div>
						火车票预订
					</div>
					[#elseif module.code == 'TMCHotel']
						[#assign hasBooking = 1]
					<div class="item">
						<div class="fli_checkbox_blue">
							<input type="radio" name="tripType" id="tripType3" value="3" />
							<label for="tripType3"></label>
						</div>
						酒店预订
					</div>
					[#elseif module.code == 'TMCCar']
					[/#if]

    			[/#if]
    		[/#list]
    	[/#if]
		[#if hasBooking == 0]
			<div class="empty">暂无预定项</div>
		[/#if]
        </div>
    </script>
    
    <input type="hidden" id="agentMemberId" value="">
</body>
<script>
	var divTop = 0;
    $(function(){
        //选择人员及类型
        $("#sectionBox").on("click","ul li",function(){
            $("#agentMemberId").val($(this).attr("receiveid"));
            var agentMemberName = $(this).attr("receiveName") + '(' + $(this).attr("receiveUserName") + ')';
            layer.open({
                type:1,
                title:"请选择出行类型",
                content: $("#selectTypeContent").html(),
                btn: ['确定','取消'],
                shadeClose: false,
                skin:"layer_radio_list",
                yes: function(){
                    var tripType = $("input[name='tripType']:checked").val()
                    if(tripType == undefined){
                        layer.open({
                            content: '请选择出行类型'
                            ,skin: 'msg'
                            ,time: 2
                        });
                    }else{
                        var agentMemberId = $("#agentMemberId").val();
                        if(tripType == 1){
                            location.href = "${base}/plane/index.jhtml?agentMemberId="+agentMemberId+ "&agentMemberName=" + agentMemberName + "&isPublic=1";
                        } else if(tripType == 2){
                            location.href = "${base}/ly/train/index.jhtml?agentMemberId="+agentMemberId+ "&agentMemberName=" + agentMemberName + "&isPublic=1";
                        }else if(tripType == 3){
                            location.href = "${base}/ctrip_hotel/index.jhtml?agentMemberId="+agentMemberId+ "&agentMemberName=" + agentMemberName + "&isPublic=1";
                        }else if(tripType == 4){
                            location.href = "${base}/taxi/index.jhtml?agentMemberId="+agentMemberId+ "&agentMemberName=" + agentMemberName + "&isPublic=1";
                        }else if(tripType == 5){
                            location.href = "${base}/plane/international/index.jhtml?agentMemberId="+agentMemberId+ "&agentMemberName=" + agentMemberName + "&isPublic=1";
                        }
                    }
                }
            });
        });

      //加载数据
		loadData('${base}/bookingAgent/agentMemberList.jhtml', {userName:"${userName}"}, 'selectPersonPage', 'memberUl', 'POST', 'totalPages', null, function(){
			$("#totalPages").remove();
		});
    })

    var ids = [];
    //输入框获取焦点时如内容不为空则显示清空按钮
    $(".searchbox input").on("keyup focus", function() {
        if ($(this).val() != "") {
            $(this).siblings(".clear").show();
        }
    });

    //输入框失去焦点时隐藏清空按钮
    $(".searchbox input").on("blur", function() {
        if ($(this).val() == "") {
            $(this).siblings(".clear").hide();
        }
    });

    //点击清空按钮时清空输入框数据并隐藏清空按钮
    $(".searchbox .clear").on("touchend", function() {
        $(this).siblings(".input").val("").end().hide();
    });

    //搜索
    $(".icon_search").click(function(){
        loadMember($("#keyword").val());
    });

    $("#keyword").change(function(){
        var keyword = $("#keyword").val();
        if(keyword == ""){
            loadMember("");
        }
    });

    $("#searchMember").click(function(){
        loadMember($("#keyword").val());
    });

    /*点击名片显示对象个人信息*/
    $(".section-box").on("click", ".head", function(e) {
        e.stopPropagation();
        if ($(this).siblings(".card").css("display") == "block") {
            $(this).siblings(".card").hide();
        } else {
            $(this).parents(".section-box").find("li").children(".card").hide();
            $(this).siblings(".card").show();
        }
    });

    //loadMember(null);

    //加载人员
    function loadMember(keyword){
    	$("#orderSearchForm").submit();
    }
</script>
</html>