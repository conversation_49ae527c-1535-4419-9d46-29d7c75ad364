<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <meta name="apple-mobile-web-app-status-bar-style" content="black"/>
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no"/>
    <title>电影票-排期</title>
    <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
    <link rel="stylesheet" href="${base}/resources/wechat/css/common.css">
    <link rel="stylesheet" href="${base}/resources/wechat/plugins/swiper-4.5.0/css/swiper.min.css">
    <link rel="stylesheet" href="${base}/resources/wechat/css/movie.css">
</head>

<body class="movie-body">
<div id="app" class="movie-schedule" v-cloak>
    <div class="public_top_header">
        <a href="javascript:history.go(-1);" class="return_back"></a>
        <span v-cloak>{{film.filmName}}</span>
    </div>
    <div class="cinema">
        <div class="left">
            <div class="shop" v-cloak>
                {{cinema.cinemaName}}
            </div>
            <div class="address" v-cloak>
                {{cinema.address}}
            </div>
            <!--<div class="score">好评率：100%</div>-->
        </div>
        <div class="dright" v-if="cinema.distance && cinema.distance > 0 && cinema.distance < 1000" v-cloak>
            {{Number(cinema.distance).toFixed(2)}}m
        </div>
        <div class="dright" v-else-if="cinema.distance && cinema.distance >= 1000" v-cloak>
            {{Number(cinema.distance / 1000).toFixed(2)}}km
        </div>
    </div>
    <div class="swiper-container">
        <div class="swiper-wrapper">
            <img class="swiper-slide"
                 v-for="item in imgList"
                 :key="item.filmCode"
                 :src="item.cover"
                 :id="item.filmCode">
        </div>
    </div>
    <div class="info">
        <div class="name" v-cloak>{{film.filmName}}<span v-cloak>评分: {{(film.score/10).toFixed(1)}}</span></div>
        <div v-cloak>{{film.duration}}分钟 / {{film.type}}</div>
    </div>
    <div class="scroll" v-show="dateArray && dateArray.length > 0">
        <div class="date"
             :id="item.id"
             v-for="item in dateArray"
             :key="item.id"
             :class="{active:tab==item.id}"
             @click="chooseDate(item.id)" v-cloak>
            {{item.date}}
        </div>
    </div>
    <div class="film" >
        <div class="data" v-for="item in dataList" :key="item.featureAppNo">
            <div class="time">
                <div v-cloak>{{item.startTimeStr}}</div>
                <div v-cloak>{{item.leaveTime}}</div>
            </div>
            <div class="type">
                <div v-cloak>{{item.copyLanguage}} {{item.copyType}}</div>
                <div v-cloak>{{item.hallName}}</div>
            </div>
            <div class="price">
                <div v-cloak><span>￥</span>{{item.minFliPrice}}</div>
                <div @click="buy(item)"> 购票</div>
            </div>
        </div>
        <div class="empty" v-show="showEmpty">
            <img src="${base}/resources/wechat/img/empty_film_icon.png">
        </div>
    </div>
</div>
<form id="gotoSeat" action="${base}/film/seats.jhtml" method="post">
[#--    <input type="hidden" name="cinemaId" id="cinemaId" value=""/>--]
[#--    <input type="hidden" name="featureAppNo" id="featureAppNo" value=""/>--]

    <input type="hidden" name="filmCover" id="filmCover" value="" />
    <input type="hidden" name="token" id="token" value=""/>
</form>
<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

<script src="${base}/resources/wechat/js/common.js"></script>
<script src="${base}/resources/wechat/plugins/vue/vue.min.js"></script>
<script src="${base}/resources/wechat/plugins/swiper-4.5.0/js/swiper.min.js"></script>
<script>
    let vm = new Vue({
        el: "#app",
        data() {
            return {
                tab: 1,//初始化一个最初选择的日对应id
                showEmpty: false,
                initialSlide: 0,
                film: {
                    [#if film??]
                    cover: '${film.cover?js_string}',
                    publishTime: '${film.publishTime?js_string}',
                    filmName: '${film.filmName?js_string}',
                    filmCode: '${film.filmCode?js_string}',//'影片编码',
                    score: '${film.score?js_string}',
                    duration: '${film.duration}',
                    [#--version: '${film.version}',--]
                    [#--director: '${film.director}',--]
                    [#--cast: '${film.cast}',--]
                    introduction: '${film.introduction?js_string}',
                    area: '${film.area?js_string}',
                    type: '${film.type?js_string}'
                    [/#if]
                },//选中的影片
                cinema: {
                    [#if cinema??]
                    cinemaId: "${cinema.cinemaId}",//选中的影院
                    distance: "${cinema.distance}",
                    cinemaName: "${cinema.cinemaName}",
                    address: "${(cinema.address!"")?replace("\"", "\\\"")}",
                    [/#if]
                },
                selectDate: "",
                imgList: [
                        [#if films??]
                        [#list films as thisFilm]
                    {
                        cover: '${thisFilm.cover?js_string}',//图片
                        publishTime: '${thisFilm.publishTime?js_string}',//'明天 4月21日',
                        filmName: '${thisFilm.filmName?js_string}',//'指环王：护戒使者',
                        filmCode: '${thisFilm.filmCode?js_string}',//'影片编码',
                        score: '${thisFilm.score?js_string}',//'评分9.2',
                        duration: '${thisFilm.duration}',
                        [#--version: '${thisFilm.version?js_string}',//'3D IMAX',--]
                        [#--director: '${thisFilm.director?js_string}',//'导演：杰克逊',--]
                        [#--cast: '${thisFilm.cast?js_string}',//'导演：杰克逊',--]
                        introduction: '${thisFilm.introduction?js_string}',//'介绍',
                        area: '${thisFilm.area?js_string}',//'地区',
                        type: '${thisFilm.type?js_string}',//'制片方式'
                    },
                        [/#list]
                        [/#if]
                ],//电影列表
                dateArray: [
                    {
                        id: "今天 04-20",
                        date: "今天 04-20"
                    },
                ],//排期时间列表
                dataList: [
                    {
                        startTimeStr: "19:30",
                        leaveTime: "20:47散场",
                        fliPrice: "600",//售价
                        featureAppNo: "排期编码",
                        cinemaCode: "影城编码",
                        sourceFilmNo: "影片编码",
                        filmNo: "影片编码11位",
                        filmName: "影片名称",
                        hallNo:"影厅编码",
                        hallName: "影厅名称",
                        startTime: "放映时间 : System.currentTimeMillis() / 1000",
                        copyType: "影片制式",
                        copyLanguage: "语言",
                        totalTime: "时长 ",
                    },
                ],//排期
                plans: {},//所有排期
                filmDates: {}, //影片的日期

            }
        },
        created() {
            // 请求初始数据
            let that = this;
            that.getPlan();

            //获取有影城信息
            if (that.film && that.film.filmCode != null && that.film.filmCode != "") {
                that.getData(that.film.filmCode);
                $.each(that.imgList, function(index, film){
                    if (that.film.filmCode == film.filmCode) {
                        that.initialSlide = index;
                    }
                });
            } else {
                that.film = that.imgList[0];
                that.initialSlide = 0;
                that.getData(that.film.filmCode);
            }
        },
        mounted() {
            this.initPic();
        },
        methods: {
            getPlan() {
                this.dateArray.length = 0;

                let newFilms = [];
                let myFilmCode = [];
                let thisPlans = [];
                let myFilmDates = [];
                [#if plansMaps??]
                    [#if plansMaps.get("dateMap")??]

                    [#list plansMaps.get("dateMap").keySet() as day]
                this.dateArray.push({//日期横条
                    id: "${day}",
                    date: "${day}"
                });
                              console.log(111111111)
                    console.log(this.dateArray+"...................."+this.dateArray.length )
                        [#list plansMaps.get("dateMap").get(day).keySet() as filmCode]
       //         console.log(myFilmCode, "${filmCode}", newFilms);
                if ($.inArray("${filmCode}", myFilmCode) < 0) {
                    myFilmCode.push("${filmCode}")
                    this.imgList.forEach(function (item, index) {
                        if (item.filmCode == "${filmCode}") {
                            newFilms.push(item);
                            return false;
                        }
                    });
                }
                thisPlans = [];
                            [#list plansMaps.get("dateMap").get(day).get(filmCode)?sort_by("startTime") as plan]
                thisPlans.push({//排期
                    startTimeStr: "${plan.startTimeStr?js_string}",
                    leaveTime: "${plan.leaveTime?js_string}",
                    fliPrice: "${plan.fliPrice?js_string}",//售价
                    minFliPrice: "${plan.minFliPrice?js_string}",//售价
                    featureAppNo: "${plan.featureAppNo?js_string}",
                    cinemaCode: "${plan.cinemaCode?js_string}",
                    sourceFilmNo: "${plan.sourceFilmNo?js_string}",
                    filmNo: "${plan.filmNo?js_string}",
                    filmName: "${plan.filmName?js_string}",
                    hallNo: "${plan.hallNo?js_string}",
                    hallName: "${plan.hallName?js_string}",
                    startTime: "${plan.startTime?js_string}",
                    copyType: "${plan.copyType?js_string}",
                    copyLanguage: "${plan.copyLanguage?js_string}",
                    totalTime: "${plan.totalTime?js_string} ",
                });
                            [/#list]
                this.plans["${day}_${filmCode}"] = thisPlans;
                        [/#list]
                    [/#list]
                    [/#if]

                    [#if plansMaps.get("filmMap")??]
                        [#list plansMaps.get("filmMap").keySet() as filmCode]
                myFilmDates = [];
                            [#list plansMaps.get("filmMap").get(filmCode).keySet() as day]
                myFilmDates.push({//日期横条
                    id: "${day}",
                    date: "${day}"
                });
                            [/#list]
                this.filmDates["${filmCode}"] = myFilmDates;
                        [/#list]
                    [/#if]
                [/#if]
                //console.log("this.dateArray:", this.dateArray);
                if (newFilms.length == 0 && this.film) {//没有排期
                    newFilms.push(this.film);
                }
                this.imgList = newFilms;
                console.log(this.dateArray);
            },

            //初始化图片轮播
            initPic() {
                let _this = this;

                var swiper = new Swiper('.swiper-container', {
                    slidesPerView: 5, //设置slider容器能够同时显示的slides数量(carousel模式)。
                    spaceBetween: 4, //在slide之间设置距离（单位px)
                    centeredSlides: true, //设定为true时，active slide会居中，而不是默认状态下的居左。
                    slideToClickedSlide: true, //设置为true则点击slide会过渡到这个slide
                    loop: false,
                    initialSlide : _this.initialSlide,
                    on: {
                        slideChange: function () {
                            _this.$nextTick(function () {
                                // 通过这个获取对应id,并请求数据
                                //document.getElementsByClassName('swiper-slide-active')[0].getAttribute("id")
                                let filmCode = document.getElementsByClassName('swiper-slide-active')[0].getAttribute("id");
                                //console.log("filmCode:", filmCode)
                                _this.getData(filmCode);
                            })
                        },
                    },
                });
            },
            // 选取图片后获取数据
            getData(filmCode) {
                //console.log("点了filmCode：", filmCode);
                let that = this;
                if (filmCode != null && filmCode != "") {
                    if (this.imgList.length > 0) {
                        this.imgList.forEach(function (item, index) {
                            if (item.filmCode == filmCode) {
                                that.film = item;
                                return false;
                            }
                        });
                    }
                } else {
                    that.film = this.imgList[0];//默认取第一个
                }
                this.showPlan();
            },
            // 选择日期更新数据
            chooseDate(id) {
                this.tab = id;
                this.selectDate = id;
                this.showPlan();
            },
            showPlan() {
                let that = this;
                if (this.film == null && this.imgList != null && this.imgList.length > 0) {
                    this.film = this.imgList[0];
                }
                if (that.film != null && that.film.filmCode != null) {
                    that.dateArray = that.filmDates[that.film.filmCode];
                }
                if ((this.selectDate == null || this.selectDate == "") && this.dateArray != null && this.dateArray.length > 0) {
                    this.selectDate = this.dateArray[0].id;
                    this.tab = this.selectDate;
                    // this.chooseDate(this.selectDate);
                    // return false;
                } else if (this.dateArray != null && this.dateArray.length > 0) {
                    let thatDate = this.selectDate;
                    let isChange = true;
                    this.dateArray.forEach(function (item, index) {
                        if (item.id == thatDate) {
                            isChange = false;
                            return false;
                        }
                    });
                    if (isChange) {
                        this.selectDate = this.dateArray[0].id;
                        this.tab = this.selectDate;
                    }
                }

                //console.log("selectDate: ", this.selectDate);
                //console.log("this.film: ", this.film.filmCode);
                //console.log("this.plans: ", this.plans);
                //console.log("that.filmDates: ", that.filmDates);

                if (that.selectDate != null && that.film != null && that.plans) {
                    that.dataList = that.plans[that.selectDate + "_" + that.film.filmCode]
                } else {//无排期
                    that.dataList.length = 0;
                }
                if (that.dataList && that.dataList.length > 0) {
                    that.showEmpty = false;
                } else {
                    that.showEmpty = true;
                }
            },
            buy(plan) {//到选座页面
                //console.log("plan: ", plan);
                //console.log("this.cinema: ", this.cinema);
                if (plan != null && this.cinema != null) {
                    $("#gotoSeat").find("input[flag='planInfo']").remove();//清楚老的数据
                    if (plan != null) {//选中了某个影片
                        //uri = uri + "&filmInfo=" + JSON.stringify(film);
                        $.each(plan, function (key, value) {
                            input = $("<input type='hidden' flag='planInfo'>");
                            input.attr({"name": key});
                            input.val(value);
                            $("#gotoSeat").append(input);
                        });
                    }

                    $("#gotoSeat").find("input[flag='cinemaInfo']").remove();//清楚老的数据
                    if (this.cinema != null) {//选中了某个影院
                        //uri = uri + "&filmInfo=" + JSON.stringify(film);
                        $.each(this.cinema, function (key, value) {
                            input = $("<input type='hidden' flag='cinemaInfo'>");
                            input.attr({"name": key});
                            input.val(value);
                            $("#gotoSeat").append(input);
                        });
                    }
                    if (this.film && this.film.cover != null) {
                        $("#filmCover").val(this.film.cover);
                    }
                    $("#token").val(getCookie("token"));
                    $("#gotoSeat").submit();
                } else {//排期不存在

                }
            }
        }
    });
</script>
</body>
</html>