<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<title>火车票</title>
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">
		<script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/lifeService.css" />
		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

		<script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
		
	</head>
	<body class="has_fixed_footer">
		<div class="public_top_header bg_theme">
			[#if applyId?? ]
				<a href="javascript:;" onclick='location.href="ticketQuery.jhtml?applyId=${applyId}&to=${trainline.currentEndStationName}&from=${trainline.currentStartStationName}&date=${date}"' class="return_back"></a>
			[#else]
				<a href="javascript:;" onclick='location.href="ticketQuery.jhtml?to=${trainline.currentEndStationName}&from=${trainline.currentStartStationName}&date=${date}"' class="return_back"></a>
			[/#if]
			${trainline.currentStartStationName}-${trainline.currentEndStationName}
			[#include "./wechat/include/head_nav.ftl" /]
		</div>
		
		<div class="fly_info_complete_page train">
		<div class="page_header_bg fly train">
			<header>
				<div class="line2">
					<div class="from">
						<p>${trainline.currentStartStationName}</p>
						<p>${trainline.startTime}</p>
						<p>${date} <span>${week}</span></p>
					</div>

					<div class="arrow">
						<p>${trainline.trainNumber}</p>
						<p>${runTime}</p>
					</div>

					<div class="to">
						<p>${trainline.currentEndStationName}</p>
						<p>${trainline.endTime}</p>
						<p>${endDate?string("yyyy-MM-dd")}<span>${endWeek}</span></p>
					</div>
				</div>
			</header>	
			</div>
			<div class="trainCommon_width">
			<div class="select_seat_type">
			<!--无票添加dis-->
			[#list trainline.trainSeats as trainSeat]
				[#assign tripLevelMsg=""]
				[#assign trainShow=-1]
				[#if tripLevelConfig??]
					[#if tripLevelConfig.trainFlag]
						[#if tripLevelConfig.trainSeatType??]
							[#if !tripLevelConfig.trainSeatNames?split(",")?seq_contains(trainSeat.seatName)]
								[#assign tripLevelMsg="未选择差旅标准里的火车票座席、"]
								[#if tripLevelConfig.trainExceedConctrol == "unLimited"]
									[#assign trainShow=0]
								[#elseif tripLevelConfig.trainExceedConctrol == "orderLimit"]
									[#assign trainShow=1]
								[#elseif tripLevelConfig.trainExceedConctrol == "displayLimit"]
									[#assign trainShow=2]
								[/#if]
							[/#if]
						[/#if]
					[/#if]
				[/#if]
				[#if trainShow != 2]
				<div class="item [#if trainSeat.remainderTrainTickets==0 || trainShow == 1]dis[/#if] [#if seatId==trainSeat.seatId||trainline.trainSeats?size==1]active[/#if]" seatId="${trainSeat.seatId}">
					<p>${trainSeat.seatName}</p>
					<p class="ticketStatue" data-count="[#if trainShow == 1]0[#else]${trainSeat.remainderTrainTickets}[/#if]" msg="${tripLevelMsg }">
					[#if trainShow == 1]
				    	超标
				    [#else]
						[#if trainSeat.remainderTrainTickets>20]
							有票
						[#else]
							[#if trainSeat.remainderTrainTickets>0]
								${trainSeat.remainderTrainTickets}张
							[#else]
									无票
							[/#if]		
						[/#if]
					[/#if]
					</p>
					<p class="price">￥${trainSeat.seatPrice}</p>
				</div>
				[/#if]
			[/#list]
			</div>
			
			<div class="section_2">
				<header>乘车人</header>
				<span class="icon_add"></span>
			</div>
			<div class="section_3">
			[#assign passagers=""]
			[#assign passagers1=""]
			[#if passengers??&&passengers?size>0]
				[#list passengers as passenger]
					<div class="item" ids="${passenger.id}" name="${passenger.name}">
						<span class="icon_del"></span>
						
						<span class="name">${passenger.name}</span>
						<div class="info">
							<p>身份证：<span>${passenger.idcardNoDec}</span><span class="type" style="color: #529afe; margin-left:0.24rem;"></span></p>
						</div>
						
					</div>
				[/#list]	
			[/#if]	
			</div>
			<form action="${base}/member/order/info_virtual.jhtml" id="createForm" method="post">
				<div class="public_form_section">
			 		<div class="items">
			 			<div class="item">
			 				<label>联系人</label>
			 				<div class="inputs standard_input">
			 					<input type="text" name="contactName" id="contactName" value="${member.name!}" placeholder="请输入姓名" class="input"/>
			 					<span class="icon_clear"></span>
			 				</div>
			 			</div>
			 			<div class="item">
			 				<label>手机号码</label>
			 				<div class="inputs standard_input">
			 					<input type="tel" name="contactTel" id="contactTel" value="${member.phoneDec!}" placeholder="用于接收行程短信" class="input" />
			 					<span class="icon_clear"></span>
			 				</div>
			 			</div>
			 			<div class="item">
			 				<label>保险</label>
			 				<div class="inputs">
			 					<div class="fli_checkbox_blue">
									<input type="checkbox" name="isInsurance" id="id2" value="true" [#if isInsurance=="true"]checked="checked"[/#if]>
									<label for="id2"></label>
								</div>
								<span>￥${trainInsurePrice}元/人</span>
			 				</div>
			 			</div>
			 		</div>
			 	</div>
			 	<input type="hidden" name="from" value="${trainline.currentStartStationName}"/>
				<input type="hidden" name="to" value="${trainline.currentEndStationName}"/>
				<input type="hidden" name="date" value="${date}"/>
				<input type="hidden" name="trainNumber" value="${trainline.trainNumber}"/>
				<input type="hidden" name="passagers" id="passagers"/>
				<input type="hidden" name="itemIdInsur" id="itemIdInsur"/>
				<input type="hidden" name="startTime" value="${trainline.startTime}" />
				
				<input type="hidden" name="runTime" value="${runTime}" />
				<input type="hidden" name="endWeek" value="${endWeek}" />
				<input type="hidden" name="endDate" value="${endDate?string("yyyy-MM-dd")}" />
				<input type="hidden" name="week" value="${week}" />
				<input type="hidden" name="endTime" value="${trainline.endTime}" />
				<input type="hidden" name="type" value="5" />
				<input type="hidden" name="isFirst" value="1" />
				<input type="hidden" name="passagers1"  id="passagers1"/>
				[#if applyId?? ]
					<input type="hidden" name="applyId" value="${applyId}"/>
					<input type="hidden" name="tripType" value="train"/>
					<input type="hidden" name="serviceInfo" value=""/>
					<input type="hidden" name="tripReasonCode" value=""/>
					<input type="hidden" name="tripRuleBroken" id="tripRuleBroken" value=""/>
					<input type="hidden" name="tripReason" value=""/>
					<input type="hidden" name="tripMemo" value=""/>
					<input type="hidden" name="isBreak" value=""/>
				[/#if]
			</form>
		 	<div class="price_total">
		 		<div class="item">
		 			车票金额
		 			<div class="right" >
		 				￥<span id="trainAmount">0.0</span>
		 			</div>
		 		</div>
		 		<div class="item">
		 			服务费
		 			<div class="right" >
		 				￥<span id="serviceCharge">0.0</span> x<span class="number">0</span>
		 			</div>
		 		</div>
		 		<div class="item premium" [#if !(isInsurance=="true")]style="display: none"[/#if]>
		 			保险
		 			<div class="right">
		 			￥<span id="premium">0.0</span> x<span class="number">0</span>
		 			</div>
		 		</div>
		 	</div>
		 	
		 	
		 	<div class="agreenment_section">
		 		<div class="fli_checkbox_blue">
					<input type="checkbox" name="ids" id="id1" value="true">
					<label for="id1"></label>
				</div>
				<p>我已完成阅读<a href="agreement.jhtml">《火车票平台用户协议》</a>并接受所有条款</p>
		 	</div>
		 	<div class="fixed_bottom_settlement">
	 			<button class="btn_submit" disabled="disabled">提交订单</button>
	 			<div class="payment">
	 				<p>订单总额：<span id="totalAmount">￥0.0</span></p>
	 				<p class="text_gray">共<span class="number">0</span>人</p>
	 			</div>
	 		</div>
		</div>
		</div>
		<div class="popbg" id="selectValue">
			<div class="select_box">
			[#list trainline.trainSeats as trainSeat]
				<div class="item_select" price="${trainSeat.seatPrice}" seatName="${trainSeat.seatName}" >${trainSeat.seatName}</div>
			[/#list]
			</div>
		</div>
		
		<script>
			$(function(){
				calculate();
				
				//选择座位类型
				$(".section_3 .type").on("click",function(){
					
					/* $(this).addClass("this");
					var selectVal=$(this).html();
					if($("#selectValue .item_select").length>1){
						$("#selectValue").show();
						$("#selectValue .item_select[seatName='"+selectVal+"']").addClass("active");
					} */
				})
				
				$("#selectValue .item_select").on("click",function(){
					var seatType = $(this).html();
					$(".section_3 .type.this").attr("price",$(this).attr("price"));
					$(".section_3 .type.this").html(seatType).removeClass("this");
					$(this).siblings().removeClass("active").end().addClass("active").parents("#selectValue").hide();
					calculate();
				})
				
				
				$(".btn_submit").click(function(){
					var seatName=$(".select_seat_type .active p:eq(0)").html();
					var count=$(".select_seat_type .active .ticketStatue").attr("data-count");
					if(!seatName){
						layer.open({
						    content: '请选择座位类型'
						    ,skin: 'msg'
						    ,time: 2 //2秒后自动关闭
						});
						return false;
					}
					
					if($(".section_3 .item").length>count){
						layer.open({
						    content: "很抱歉，订单提交失败！本次列车"+seatName+"仅剩"+count+"张"
						    ,skin: 'msg'
						    ,time: 2 //2秒后自动关闭
						});
						return false;
					}
					
					if($(".section_3 .item").length<=0){
						layer.open({
						    content: '请选择乘车人'
						    ,skin: 'msg'
						    ,time: 2 //2秒后自动关闭
						});
						return false;
					}
					var contactName=$("#contactName").val();
					if(!contactName){
						layer.open({
						    content: '请输入联系人'
						    ,skin: 'msg'
						    ,time: 2 //2秒后自动关闭
						});
						$("#contactName").focus();
						return false;
					}
					var contactTel=$("#contactTel").val();
					if(!contactTel){
						layer.open({
						    content: '请输入联系人手机号码'
						    ,skin: 'msg'
						    ,time: 2 //2秒后自动关闭
						});
						$("#contactTel").focus();
						return false;
					}
					var reg = /^\d{11}$/;
					if(!reg.test(contactTel)){
						layer.open({
						    content: '请输入正确的联系人手机号码'
						    ,skin: 'msg'
						    ,time: 2 //2秒后自动关闭
						});
						$("#contactTel").focus();
						return false;
					}
					
					if(!seatName){
						layer.open({
						    content: '请选择座位类型'
						    ,skin: 'msg'
						    ,time: 2 //2秒后自动关闭
						});
						return false;
					}
					var passagers="";
					var passagers1="";
					var flag = true;
					$(".section_3 .item").each(function (){

						[#if tripApply??]//出差申请的订单
							[#if tripLevelConfig??]//差旅规则限制
								[#if tripLevelConfig.trainFlag]//启用机票舱位限制开启
									console.log("申请人限制：${tripLevelConfig.trainPassengerLimit}");
									var val = $(this).attr("name");
									[#if tripLevelConfig.trainPassengerLimit == "tripApplicantLimit"]//仅可以申请人乘坐
										console.log("申请人限制：" + val + ", ${tripApply.memberId.name}");
										if(val != "${tripApply.memberId.name}"){
											layer.open({
												content:"根据公司差旅政策，仅限申请人乘坐，请重新选择乘客",
												skin:"msg",
												time:2
											});
											flag=false;
											return false;
										}
									[#elseif tripLevelConfig.trainPassengerLimit == "tripPassengerLimit"]//仅申请/出差人乘坐
										var memberNames = new Array([#list tripApply.members as member]"${member.name}",[/#list]"${tripApply.memberId.name}");
										console.log("申请/出差人限制：" + val + ", " + memberNames);
										if($.inArray(val, memberNames) == -1){
											layer.open({
												content:"根据公司差旅政策，仅限申请单出差人乘坐，请重新选择乘客",
												skin:"msg",
												time:2
											});
											flag=false;
											return false;
										}
										
									[/#if]
								[/#if]
							[/#if]
						[/#if]
						
						if(passagers!=""){ 
							passagers+=";"
							passagers1+=";"
						}
						passagers+=$(this).find("span:eq(1)").html()+","
				          +contactTel+","
				          +$(this).find(".info span").html()+","
				          +$(this).find(".type").html();
						
						passagers1+=$(this).find("span:eq(1)").html()+","
				          +contactTel+","
				          +$(this).find(".info span").html()+","
				          +$(this).find(".type").html()+","
				          +$(this).find(".type").attr("price");
					});
					if(!flag){
						return false;
					}

					[#if tripApply??]
					//出差申请规则限制
					var msg = $(".select_seat_type .active .ticketStatue").attr("msg");;
					msg = msg + checkTrip();
					if(msg != ""){//超标
						$("#tripRuleBroken").val(msg);
						$("#createForm").attr("action", "tripReason.jhtml");
					} else {
						$("#createForm").attr("action", "${base}/member/order/info_virtual.jhtml");
					}
					[/#if]
					
					$("#passagers").val(passagers);
					$("#passagers1").val(passagers1);
					if($("#id2").is(":checked")){
						$("#itemIdInsur").val(${trainInsureItemId});
					}
					$(this).prop("disabled",true);
					$(this).val("提交中...");
					$("#createForm").submit();
				});
				//删除乘客信息
				$(".section_3").on("click",".item .icon_del",function(){
					/* if($(this).parents(".section_3").children(".item").length > 1){ */
						$(this).parent(".item").remove();
						calculate();
					/* }else{
						layer.open({
						    content: '至少保留一条乘客信息'
						    ,skin: 'msg'
						    ,time: 2 //2秒后自动关闭
						});
					} */
				})
				$(".select_seat_type .item").click(function(){
					if(Number($(this).find(".ticketStatue").attr("data-count"))>0){
						$(this).siblings().removeClass("active").end().addClass("active");
						 calculate();
						 selectSeat();
					}/* else{
						layer.open({
						    content: '此座位类型无票'
						    ,skin: 'msg'
						    ,time: 2 //2秒后自动关闭
						});
					} */
				});
				//添加乘客信息
				$(".section_2").on("click",".icon_add",function(){
					if($(".section_3").children(".item").length < 9){
						var seatId=$(".select_seat_type .active").attr("seatId");
						var isInsurance=$("#id2:checked").val();
						if(!seatId){
							seatId="";
						}
						if(!isInsurance){
							isInsurance="";
						}
						var ids=","
						$(".section_3 .item").each(function(){
							ids+=$(this).attr("ids")+",";
						});
						[#if applyId?? ]
							location.href="passenger.jhtml?applyId=${applyId}&to=${trainline.currentEndStationName}&from=${trainline.currentStartStationName}&date=${date}&trainNumber=${trainline.trainNumber}&seatId="+seatId+"&isInsurance="+isInsurance+"&ids="+ids;
						[#else]
							location.href="passenger.jhtml?to=${trainline.currentEndStationName}&from=${trainline.currentStartStationName}&date=${date}&trainNumber=${trainline.trainNumber}&seatId="+seatId+"&isInsurance="+isInsurance+"&ids="+ids;
						[/#if]
					}else{
						layer.open({
						    content: '最多添加5条乘客信息'
						    ,skin: 'msg'
						    ,time: 2 //2秒后自动关闭
						});
					}
				});
				$("#id2").click(function(){
					checkId2();
				});
				checkId2();
				check();
				selectSeat();
				$("#id1").click(function(){
					check();
				});
			})
			function selectSeat(){
				var seatNmae=$(".select_seat_type .active p:eq(0)").html();
				
				if(seatNmae){
					var priceHtml=$(".select_seat_type .active .price").html();
					var price=0.0;
					if(priceHtml){
						 price=priceHtml.replace("￥","");
					}
					$(".section_3 .item .type").html(seatNmae);
					$(".section_3 .item .type").attr("price",price);
				}
				calculate();
				
			}
			function checkId2(){
				if($("#id2").is(":checked")){
					$(".premium").show();
				}else{
					$(".premium").hide();
				}
				 calculate();
			}
			function check(){
				if($("#id1").is(":checked")){
					$(".btn_submit").prop("disabled",false);
				}else{
					$(".btn_submit").attr("disabled","disabled");
				}
			}
			function calculate(){
				var number=$(".section_3 .item").length;
				$(".number").html(number);
				var p=$(".select_seat_type .active .price").html();
				if(p){
					var trainAmount=0.0;
					$(".section_3 .item .type").each(function(){
						var price=$(this).attr("price");
						if(price){
							trainAmount+=Number(price);
						}
					});
					var premium=0.0;
					if($("#id2").is(":checked")){
						premium=${trainInsurePrice};
					}
					var serviceCharge=${serviceCharge};
					$("#trainAmount").html(trainAmount);
					$("#premium").html(premium);
					$("#serviceCharge").html(serviceCharge);
					$("#totalAmount").html("￥"+(trainAmount+premium*number+serviceCharge*number));
				}
			}

			[#if tripApply??]
			//行程检查
			function checkTrip(){
				var tripApplyType = "${(tripApply.applyType)!""}";
				if(tripApplyType == "6"){
					//因公出差默认申请单---航班信息不用提示
					return "";
				}
				var fromCity='${trainline.currentStartStationName}';
				var toCity='${trainline.currentEndStationName}';
				var type = 1;
				var tripList = ${tripApply.tripRecordInfo2};
				var msg = "预订的行程与申请行程或时间不符、";
				//未选择规定城市
				for(var i=0;i<tripList.length;i++){
					//城市校验
					if(fromCity.indexOf(tripList[i].fromCity)!=-1 && toCity.indexOf(tripList[i].toCity)!=-1 ){//行程城市的判断
						var flightTime = new Date('${date?replace("-", "/")} ${trainline.startTime}:00');
						//日期时间校验
						if(flightTime>=new Date(tripList[i].fromDate.replace(/-/g,"/")) && new Date(tripList[i].toDate.replace(/-/g,"/"))>=flightTime){//在行程的时间内
							msg = "";//到这里满足行程单中其中一个行程
							break;
						}
					}
				}

				return msg;
			}
			[/#if]
		</script>
	</body>
</html>
