<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <meta name="apple-mobile-web-app-status-bar-style" content="black"/>
    <meta
            name="viewport"
            content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no"
    />
    <title>余额明细</title>
    <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
    <link rel="stylesheet" href="${base}/resources/wechat/css/vant/index.css">
    <link rel="stylesheet" href="${base}/resources/wechat/css/common.css">
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/qr_code.css" />
    <script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

    <script src="${base}/resources/wechat/plugins/vue/vue.min.js"></script>
    <script src="${base}/resources/wechat/js/common.js"></script>
    <script src="${base}/resources/wechat/js/vant.min.js"></script>
</head>

<body class="qrCodeServicePage cashOutPage balancePage">
<div id="app" class="qrCodeService">
    <!-- 头部 -->
    <div class="public_top_header">
        <a href="${base}/qrcode/payment/index.jhtml" class="return_back"></a> 余额明细
			[#include "./wechat/include/head_nav.ftl" /]
    </div>
    <van-popup v-model="showPicker" position="bottom">
        <van-datetime-picker v-model="currentDate" type="year-month" :min-date="minDate" :max-date="maxDate"
                             @cancel="handleTimeCancel" @confirm="handleTimeConfirm">

        </van-datetime-picker>
    </van-popup>
    <van-sticky offset-top="1.18rem">
        <!--提现到银行卡 -->
        <div  class="header_top ">
            <div class="content">
                <header  @click="DataSelect">
                    <span v-cloak>{{year}}</span>年<span v-cloak>{{month}}</span>月 <van-icon class="time_selection" name="play"  color="#333333"></van-icon>
                </header>
                <p >
            <span v-cloak>
                收入￥{{totalCredit}}
            </span v-cloak>
                    <span>
                支出￥{{totalDebit}}
            </span>

                </p>
            </div>
        </div>

    </van-sticky>

    <div class="data_list">
        <van-list
                v-model="loading"
                class="cardBox"
                :finished="finished"
                :finished-text="finishedText"
                @load="init"
                :error.sync="error"
                error-text="请求失败，点击重新加载"
                title-active-color="#233bb7"
        >
            <ul class="recodList">
                <li v-for="(item,index) in dataList" :key="'dataList'+index"
                    class="flexbox align-items-c justify-content-space-between">
                    <div class="InfoLeft flexbox align-items-c">
                        <img class="img_logo" :src="item.src">
                        <div class="content flexbox flex-column justify-content-space-between">
                            <p v-cloak> {{getType(item.type,"text")}}</p>
                            <p v-cloak v-if="item.type==0||item.type==1">单号&nbsp;:&nbsp;{{item.sn}}</p>
                            <p v-cloak v-if="item.type==2">{{item.bank}}({{getAccount(item.bankAccount)}})</p>
                            <p  v-if="item.type==3">退回到余额</p>
                            <p v-cloak class="timeText">{{item.time}}</p>
                        </div>
                    </div>
                    <div class="InfoRight flexbox  justify-content-space-between flex-column">
                        <p :class="getType(item.type,'num')=='+'?'add':'del'" v-cloak>{{getType(item.type,"num")}}{{item.integral}}</p>
                        <p v-cloak>余额&nbsp;{{item.balance}}</p>
                    </div>
                </li>
            </ul>
        </van-list>
        <p v-show="showNoData" class="noData">暂无记录</p>
    </div>

</div>
<script>
    let vm = new Vue({
        el: "#app",
        data() {
            return {
                totalCredit:0,//收入
                totalDebit:0,//支出
                currentDate: new Date(),
                minDate: null,
                maxDate: null,
                year:null,
                month:null,
                showPicker:false,
                showNoData:false,//是否有显示数据
                finishedText:"没有更多了",
                loading:false,
                finished: false,
                error: false,
                page: {
                    pageNo: 1,
                    pageSize: 10,
                    total: 1,
                },
                dataList:[],
            }
        },
        created:function(){
            this.inintTime();

        },
        mounted(){

        },
        computed:{


        },
        methods: {
            inintListData(){
                this.dataList=[];
                this.finished=false;
                this.page.pageNo=1;
                this.page.total=1;
                this.showNoData = false;
                this.error = false;
            },
            getAccount(account){
             return   account.substring(account.length-4,account.length);
            },
            //初始化时间
            inintTime(){
                this.year = this.currentDate.getFullYear();
                this.month = this.formaterMonth(this.currentDate.getMonth()+1);
                this.minDate = new Date(this.year-10, 0, 1);
                this.maxDate = new Date(this.year, this.currentDate.getMonth(), 1);
            },
            formaterMonth(month){ //处理月份 补0
                var str = month.toString();
                if(month<10){
                    str = "0"+month;
                }
                return str;
            },
            DataSelect(){
                this.showPicker = true;
                this.temp = this.currentDate;
            },
            handleTimeConfirm(){
                //判断数据有没有变化
                var selectMonth = this.currentDate.getMonth() + 1;
                if(this.currentDate.getFullYear().toString()===this.year.toString() &&   this.month.toString() === this.formaterMonth(selectMonth)){
                    this.showPicker = false;
                    return false;
                }
                this.showPicker = false;
                this.year = this.currentDate.getFullYear();
                this.month = this.formaterMonth(selectMonth);
                this.inintListData();//初始化数据
                this.init();//加载数据
            },
            handleTimeCancel(){
                this.currentDate = this.temp;
                this.showPicker = false
            },
            //dataType 收款0 退款1 提现2 提现失败3 showType "img"图片地址 "text"显示文字 ”num“+/-
            getType(dataType,showType,data) {
                var str = "";
                if(showType ==="text"){
                    switch (dataType.toString()){
                        case "0":
                            str="订单收款";
                            break;
                        case "1":
                            str="订单退款";
                            break;
                        case "2":
                            str = "银行提现";
                            break;
                        case "3":
                            str = "提现退回";
                            break;
                    }
                }else if (showType ==="num") {
                    switch (dataType.toString()){
                        case "0":
                        case "3":
                            str="+";
                            break;
                        case "1":
                        case "2":
                            str = "-";
                            break;
                    }
                }
                return str
            },
            formatYaMaD(DT){
                var T =new Date(DT);
                var obj={
                    Y:T.getFullYear(),
                    M:parseInt(T.getMonth())<8?"0"+(T.getMonth() + 1):T.getMonth() + 1,
                    D:T.getDate()<10?"0"+T.getDate():T.getDate(),
                    H:T.getHours()<10?"0"+T.getHours():T.getHours(),
                    Ms:T.getMinutes()<10?"0"+T.getMinutes():T.getMinutes(),

                };
                return obj.Y+"-"+obj.M+"-"+obj.D+" "+obj.H+":"+obj.Ms;
            },
            init() {
                this.loading = true;
                var that = this;
                this.finishedText="没有更多了";
                if (this.page.pageNo > this.page.total) {
                    this.finished = true;
                    this.loading = false;
                    if(this.page.total==0){
                        this.finishedText=""
                    }
                    return false;
                }
                var dateTime =  this.year+"-"+this.month;
                $.ajax({
                    url: "${base}/qrcode/payment/balanceDetailList.jhtml",//请求地址
                    type: "POST",
                    dataType: "json",
                    data: {
                        pageNumber: this.page.pageNo,
                        pageSize:10,
                        beginDate:dateTime
                    },
                    cache: false,
                    success: function (response) {
                        that.page.pageNo++;
                        var data = response.content;
                        if(data.length===0){
                            that.showNoData =true;
                        }
                        that.page.total = response.totalPages;
                        that.totalCredit = response.data.totalCredit.toFixed(2);
                        that.totalDebit = response.data.totalDebit.toFixed(2);
                        that.loading = false;
                        data.map(function (item) {
                            var integral = (item.changeType=='0' || item.changeType=='3') ? item.credit:item.debit;
                            var sn= (item.orderId!=null)?item.orderId.sn:'';
                            var bank= (item.storeWithdrawId!=null&&item.storeWithdrawId.bank!=null)?item.storeWithdrawId.bank:
                                ((item.storeId!=null&&item.storeId.bank!=null)?item.storeId.bank:'');
                            var bankAccount= (item.storeWithdrawId!=null&&item.storeWithdrawId.bankAccount!=null)?item.storeWithdrawId.bankAccount:
                                ((item.storeId!=null&&item.storeId.bankAccount!=null)?item.storeId.bankAccount:'');
                            var obj = {
                                
                                sn: sn,
                                time: that.formatYaMaD(item.createDate),
                                bank:bank,
                                bankAccount:bankAccount,
                                integral: parseFloat(integral).toFixed(2),
                                type:item.changeType,
                                src:"${base}/resources/wechat/img/collectionCode/img"+item.changeType+".png",
                                balance:parseFloat(item.operationAmount).toFixed(2)
                            };
                            that.dataList.push(obj)

                        })

                    },
                    error: function (response) {
                        that.showNoData =false;
                        that.finished = true;
                        that.loading = false;
                        that.finishedText="请求失败"
                    }
                });
            },

        },
    });


</script>
</body>

</html>