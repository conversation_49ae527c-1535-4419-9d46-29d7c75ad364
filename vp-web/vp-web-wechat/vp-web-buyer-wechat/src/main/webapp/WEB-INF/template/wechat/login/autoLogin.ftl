<!DOCTYPE html>
<html style="background: #fff;">
	<head>
	<meta charset="utf-8" />
	<title>自动登录</title>
	<meta name="apple-mobile-web-app-capable" content="yes">
	<meta name="apple-mobile-web-app-status-bar-style" content="black">
	<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
	<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
	</head>
	<script type="text/javascript">
	var username=getCookie("username");
	var ua = window.navigator.userAgent.toLowerCase();
	if (ua.indexOf("vpShopApp_android".toLowerCase()) != -1) {// 判断是否为APP打开
	//    vpshop_android.callAutoLogin();
		var username = "${username}";
        if (username != null && username!="") {
		[#if redirectUrl??]
            location.href = "${redirectUrl}";
		[#else]
            location.href = "${base}/index.jhtml";
		[/#if]
        } else {
            location.href = "${base}/login.jhtml?redirectUrl=${redirectUrl}";
        }
	}else if (ua.indexOf("vpShopApp_ios".toLowerCase()) != -1) {
		 window.location='ios://callAutoLogin';
	}else if(ua.indexOf("vpShopApp_huawei".toLowerCase()) != -1){ // 判断是否华为手机打开
        vpshopHuawei.callAutoLogin(); // 调用华为原生端方法
	}else{
		vpshop_android.callAutoLogin();
	}
	function appAutoLoginVerify(timestamp,sign){
		$.post("${base}/login/appAutoLoginVerify.jhtml",{"timestamp":timestamp,"sign":sign},function(data){
			if(data!=null&&data){
			    //app自动登陆成功，调用android原生方法传入用户名用于android端将用户名和设备绑定
     //           vpshop_android.userLogin(username);
				[#if redirectUrl??]
					location.href = "${redirectUrl}";
				[#else]
					location.href = "${base}/index.jhtml";
				[/#if]
			}else{
				location.href = "${base}/login.jhtml?redirectUrl=${redirectUrl}";
			}

		},"json")
	}
	</script>
	<body>
	</body>
</html>
