<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>订单中心</title>
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/fli_2019.css" />

    <script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
    <script type="text/javascript">
    $(function() {
        refreshCount();
    });

    function refreshCount() {
        jQuery.post("${base}/member/refreshCount.jhtml", "", function(data) {
            if (data != null) {
                if (data.totalCard > 0) {
                    if (data.totalCard > 99) {
                        $(".list .icon_tool_heka").parent().append('<label class="num_circle">99</label>');
                    } else {
                        $(".list .icon_tool_heka").parent().append('<label class="num_circle">' + data.totalCard + '</label>');
                    }
                }
                if (data.totalFavorite > 0) {
                    if (data.totalFavorite > 99) {
                        $(".list .icon_tool_xin").parent().append('<label class="num_circle">99</label>');
                    } else {
                        $(".list .icon_tool_xin").parent().append('<label class="num_circle">' + data.totalFavorite + '</label>');
                    }
                }
                if (data.totalCoupon > 0) {
                    if (data.totalCoupon > 99) {
                        $(".list .icon_tool_quan").parent().append('<label class="num_circle">99</label>');
                    } else {
                        $(".list .icon_tool_quan").parent().append('<label class="num_circle">' + data.totalCoupon + '</label>');
                    }
                }
                if (data.totalQuantity > 0) {
                    if (data.totalQuantity > 99) {
                        $(".list .icon_tool_cart").parent().append('<label class="num_circle">99</label>');
                    } else {
                        $(".list .icon_tool_cart").parent().append('<label class="num_circle">' + data.totalQuantity + '</label>');
                    }
                }
            }
        }, "json");
    }


    function logout() {
        $.ajax({
            url: "${base}/logout.jhtml",
            type: "GET",
            dataType: "json",
            cache: false,
            success: function(data) {
                if (data) { //退出成功
                    var device = openDevice();
                    try {
                        if (device == "android") { //android打开
                            vpshop_android.userLoginOut(username);
                        }
                        if (device == "ios") { //ios打开
                            var url = 'ios://userLoginOut?username=' + username;
                            loadURL(url);

                            function loadURL(url) {
                                var iFrame;
                                iFrame = document.createElement("iframe");
                                iFrame.setAttribute("src", url);
                                iFrame.setAttribute("style", "display:none;");
                                iFrame.setAttribute("height", "0px");
                                iFrame.setAttribute("width", "0px");
                                iFrame.setAttribute("frameborder", "0");
                                document.body.appendChild(iFrame);
                                // 发起请求后这个 iFrame 就没用了，所以把它从 dom 上移除掉
                                iFrame.parentNode.removeChild(iFrame);
                                iFrame = null;
                            }
                        }

                    } catch (e) {

                    }
                    location.href = "${base}/login.jhtml";
                }
            }
        })
    }
    </script>
</head>

<body class="userCenterPage has_fixed_footer weixin_coupon_usercenter_page">
    <div class="bg_top_transparent colorFul">
        <div class="public_top_header transparent">
            <a href="${base}/weixinCoupon/index.jhtml" class="return_back"></a>
            个人中心
            <a href="${base}/member/information.jhtml?pageSource=weixincoupon" class="icon_setting"></a>
        </div>
    </div>
    <div class="userCenter_19">
        <div class="userInfo">
            <div class="img" id="user-img">
                [#if member.image]
                    [#if member.image?contains("dingtalk")||member.image?contains("http")]
                        <img src="${member.image}" />
                    [#else]
                        <img src="${base}${member.image}" />
                    [/#if]
                [#else]
                    [#if member.gender == 'female']
                        <img src="${base}/resources/wechat/img/girl.png" />
                    [#else]
                        <img src="${base}/resources/wechat/img/boy.png" />
                    [/#if]
                [/#if]
            </div>

            <div class="info">
                <div>
                    <span class="member-name text-ellipsis">${member.name}</span>
                    <a href="${base}/member/memberLevel.jhtml">
                        [#if (member.point>=0) && (member.point <100)]
                            <div class="level level_1"><i></i>普通会员</div>
                        [#elseif (member.point>=100) && (member.point <600)]
                        <div class="level level_2"><i></i>铜牌会员</div>
                        [#elseif (member.point>=600) && (member.point <1800)]
                        <div class="level level_3"><i></i>银牌会员</div>
                        [#elseif (member.point>=1800) && (member.point <3600)]
                        <div class="level level_4"><i></i>金牌会员</div>
                        [#elseif (member.point>=3600) && (member.point <6000)]
                        <div class="level level_5"><i></i>铂金会员</div>
                        [#elseif (member.point>=6000) && (member.point <10000)]
                        <div class="level level_6"><i></i>钻石会员</div>
                        [#elseif (member.point>=10000) && (member.point <20000)]
                        <div class="level level_7"><i></i>皇冠会员</div>
                        [#elseif (member.point>=20000)]
                        <div class="level level_8"><i></i>至尊会员</div>
                        [/#if]
                    </a>
                </div>
               [#if (member.companyId?? && (member.companyId.sourceFlag==null || member.companyId.sourceFlag==""))]
                  <p>${member.username}</p>
               [#else]
                   <p>${member.attributeValue3}</p>
               [/#if]
            </div>
        </div>
        <section class="order_section">
            <header>
                <span>我的订单</span>
                <a href="${base}/weixinCoupon/order/list.jhtml?type=all">查看全部订单</a>
            </header>
            <div class="list order-list-w100">
                <a href="${base}/weixinCoupon/order/list.jhtml?type=unpaid">待付款[#if waitingPaymentOrderCount??&&waitingPaymentOrderCount>0]<label class="num_circle">[#if waitingPaymentOrderCount>99]99[#else] ${waitingPaymentOrderCount!0}[/#if]</label>[/#if]</a>
                <a href="${base}/weixinCoupon/order/list.jhtml?type=confirmed">待发货[#if waitingConfirmedOrderCount??&&waitingConfirmedOrderCount>0]<label class="num_circle">[#if waitingConfirmedOrderCount>99]99[#else] ${waitingConfirmedOrderCount!0}[/#if]</label>[/#if]</a>
                <a href="${base}/weixinCoupon/order/list.jhtml?type=shipped">待收货[#if waitingShippingOrderCount??&&waitingShippingOrderCount>0]<label class="num_circle">[#if waitingShippingOrderCount>99]99[#else] ${waitingShippingOrderCount!0}[/#if]</label>[/#if]</a>
                <a href="${base}/member/order/returnsList.jhtml?pageSource=weixincoupon">退款/售后</a>
            </div>
        </section>
        <section class="myTools">
            <header>我的工具</header>
            <div class="list">
                <a href="${base}/member/address.jhtml?pageSource=weixincoupon">
                    <span class="icon_tool_add"></span>
                    <p class="text-ellipsis">编辑地址</p>
                </a>
                <a href="${base}/member/favorite/list.jhtml?pageSource=weixincoupon">
                    <span class="icon_tool_xin"></span>
                    <p class="text-ellipsis">我的关注</p>
                </a>
                <a href="${base}/member/getCoupons.jhtml?type=1&pageSource=weixincoupon">
                    <span class="icon_tool_quan"></span>
                    <p class="text-ellipsis">优惠券</p>
                </a>


                <a href="${base}/member/customerService/index.jhtml?pageSource=weixincoupon">
                    <span class="icon_tool_kefu"></span>
                    <p class="text-ellipsis">客服服务</p>
                </a>

            </div>
        </section>
        <a class="logout continue_shopping" href="${base}/weixinCoupon/index.jhtml">继续购物</a>


    </div>
</body>
<script type="text/javascript">
var serviceUid = "${com_person_moreAtts_serviceUid!2307282083}"; //客服qq号
var device = openDevice();
var ua = window.navigator.userAgent.toLowerCase();
var code = getCookie("small_wx_openid");
//判断是否来自小程序
if ((ua.indexOf("micromessenger") > 0 && code != null) || isWxwork()) {
    $("#service").hide();
}
var app = getCookie('memberAppOauthLogin'); //app
var username = getCookie("username");

var device = openDevice();
if (device == "ios") {
    $("#service").attr("href", "mqq://im/chat?chat_type=wpa&uin=" + serviceUid + "&version=1&src_type=web");
} else if (device == "android") {
    try {
        $("#service").attr("href", "javascript:void(0);");
        $("#service").click(function() {

            vpshop_android.openQQService(serviceUid);
        })
    } catch (e) {
        $("#service").attr("href", "http://wpa.qq.com/msgrd?v=3&uin=" + serviceUid + "&site=qq&menu=yes");
    }
} else {
    $("#service").attr("href", "http://wpa.qq.com/msgrd?v=3&uin=" + serviceUid + "");
}
$(function(){
    $("#registrationPromotion").on("click",function () {
        window.location.href="${base}/member/invitation.jhtml"; //点击注册邀请，去到邀请码页面
    });
});
//判断是不是企业微信
function isWxwork() {
    var ua = navigator.userAgent.toLowerCase();
    if (ua.indexOf("wxwork") > -1) {
        return true;
    } else {
        return false;
    }
}
</script>

</html>