		 			<input type="hidden" id="totalPages" value="${page.totalPages}"/>
		 			[#if page.total > 0]
			 			[#list page.content as order]
			 				[#if order.isSplit]<!-- 被拆分的订单 -->
			 					[#list order.childrenOrder as order1]
			 						[#if order1.isSplit]
				   						[#list order1.childrenOrder as order2]
				   							[#if order2.delStatus?? && order2.delStatus?contains('member')]
							 					<li>
									 				<header>
									 					<span class="no">订单号码：${order2.sn}</span>
									 						<span id="icon_del_${order2.sn}" class="icon_del" onclick="deleteOrder('${order2.id}', '${order2.sn}')" [#if order2.orderStatus != "cancelled" || order2.subTable??] style="display:none" [/#if]></span>
									 					[#if order2.orderStatus == "unpaid"]
															[#if order2.expired]
																<span class="status">已取消</span>
															[#else]
																<span class="status">待付款</span>
															[/#if]
														[#elseif order2.orderStatus == "unconfirmed"]
																<span class="status">待确认</span>
														[#elseif order2.orderStatus == "confirmed"]
																<span class="status">待发货</span>
														[#elseif order2.orderStatus == "shipped"]
																<span class="status">待收货</span>
														[#elseif order2.orderStatus == "completed"]
																<span class="status complete">已完成</span>
														[#elseif order2.jdConfirmFlag  && (order2.supplierId.isOweOrder==35|| order2.supplierId.isOweOrder == 68)]
															<span class="status complete">取消中</span>
														[#elseif order2.orderStatus == "cancelled"]
																<span class="status">已取消</span>
														[/#if]
									 				</header>
									 					[#list order2.orderItems as orderItem]
											 				<a href="${base}${orderItem.product.path}" class="item">
											 					<div class="img">
														 			[#--<img src="${setting.siteUrlImg}[#if orderItem.thumbnail??]${orderItem.thumbnail}[#elseif com_person_product_default_img != null]${com_person_product_default_img}[#else]${setting.defaultThumbnailProductImage}[/#if]" />--]
																	[#if orderItem.product.image!=null]
																		<img src="${setting.siteUrlImage}${orderItem.product.image}" />
																	[#else]
																		<img src="[#if com_person_product_default_img != null]${com_person_product_default_img}[#else]${setting.siteUrlImage}${setting.defaultThumbnailProductImage}[/#if]" />
																	[/#if]
																</div>
														 		<div class="title">
														 			<p>${orderItem.name}</p>
														 		</div>
														 		<div class="attrs">
														 			<span>[#if productShowRate??]${coinConvert(orderItem.price, productShowRate, true, false)}[#else]${currency(orderItem.price, true)}[/#if] x${orderItem.quantity}</span>
														 			[#if orderItem.product.specificationValues]
																		[#list orderItem.product.specificationValues as specificationValue]
																			<span>
																				${specificationValue.specification.name}:${specificationValue.name}
																			</span>
																		[/#list]
																	[/#if]
														 		</div>
											 				</a>
											 				<!-- 标记是否有商品还没有评论 -->
											 				[#if orderItem.reviewStatus == "unreview"]
																[#assign isReview="no"]
															[#else]
																[#assign isReview="yes"]
															[/#if]
											 			[/#list]
									 				<footer>
									 					<span class="total_price" order2>总计:[#if productShowRate??]${coinConvert(order2.price, productShowRate, true, false)}[#else]${currency(order2.price, true)}[/#if]</span>
									 					
									 					<div class="btns">
									 						[#if order2.orderStatus == "unpaid"]
																[#if !order2.expired]
																	<input type="button"
																	  [#if domainName==null || domainName==""]
																	      onclick="javascript:location.href='${base}/member/order/payment.jhtml?sn=${order2.sn}'"
															         [#else]
																	      onclick="javascript:location.href='${base}/paysRedirect/payment.jhtml?sn=${order2.sn}'"
															          [/#if]
																		   value="去付款" class="btn_gray"/>
																	<input type="button" onclick="cancelOrder(this, '${order2.id}', '${order2.sn}',true, ${order.freight})" value="取消订单" class="btn_gray"/>
																[/#if]
															[#elseif order2.orderStatus == "unconfirmed"]
																	[#if order2.orderType==0&&!order.groupPurchase??]<input type="button" onclick="cancelOrder(this, '${order2.id}', '${order2.sn}',true, ${order.freight})" value="取消订单" class="btn_gray"/>[/#if]
															[#elseif order2.orderStatus == "shipped"]
																	<input type="button" onclick="confirmOrder('${order2.id}', '${order2.sn}')" value="确认收货" class="btn_gray"/>
															[#elseif order2.orderStatus == "completed"]
															[/#if]
									 						<input type="button" onclick="javascript:location.href='[#if order.paymentQrcodeId??]${base}/qrcode/payment/detail.jhtml?sn=${order2.sn}[#else]${base}/member/order/detail.jhtml?id=${order2.id}[/#if]'" value="订单详情" class="btn_theme"/>
									 					</div>
									 				</footer>
									 			</li>
									 		[/#if]
				   						[/#list]
				   					[#else]
					    			
						    			[#if order1.delStatus?? && order1.delStatus?contains('member')]
						 					<li>
								 				<header>
								 					<span class="no">订单号码：${order1.sn}</span>
								 					[#if order1.supplierId.isOweOrder==24 || order1.supplierId.isOweOrder==5 ||order1.supplierId.isOweOrder==9 ||order1.supplierId.isOweOrder==10
														||order1.supplierId.isOweOrder==11||order1.supplierId.isOweOrder==12||order1.supplierId.isOweOrder==15||order1.supplierId.isOweOrder==16
														||order1.supplierId.isOweOrder==17]
													[#else]
								 						<span id="icon_del_${order1.sn}" class="icon_del" onclick="deleteOrder('${order1.id}', '${order1.sn}')" [#if order1.orderStatus != "cancelled" || order1.subTable?? ] style="display:none" [/#if]></span>
								 					[/#if]
								 					[#if order1.orderStatus == "unpaid"]
														[#if order1.expired]
															<span class="status">已取消</span>
														[#else]
															<span class="status">待付款</span>
														[/#if]
													[#elseif order1.orderStatus == "unconfirmed"]
															<span class="status">待确认</span>
													[#elseif order1.orderStatus == "confirmed"]
															<span class="status">待发货</span>
													[#elseif order1.orderStatus == "shipped"]
															<span class="status">待收货</span>
													[#elseif order1.orderStatus == "completed"]
															<span class="status complete">已完成</span>
													[#elseif order1.jdConfirmFlag  && (order1.supplierId.isOweOrder==35|| order1.supplierId.isOweOrder == 68)]
														<span class="status complete">取消中</span>
													[#elseif order1.orderStatus == "cancelled"]
															<span class="status">已取消</span>
													[/#if]
								 				</header>
								 					[#list order1.orderItems as orderItem]
										 				<a href="${base}${orderItem.product.path}" class="item">
										 					<div class="img">
													 			[#--<img src="${setting.siteUrlImg}[#if orderItem.thumbnail??]${orderItem.thumbnail}[#elseif com_person_product_default_img != null]${com_person_product_default_img}[#else]${setting.defaultThumbnailProductImage}[/#if]" />--]

																	[#if orderItem.product.supplierId.id==68 || orderItem.product.supplierId.id == 181|| product.supplierId.id == 695]
																		[#if orderItem.product.attributeValue8!=null]
                                                                            <img src="${orderItem.product.attributeValue8}" />
																		[#elseif  orderItem.product.image!=null]
                                                                            <img src="${setting.siteUrlImg}${orderItem.product.image}" />
																		[#else]
                                                                            <img src="[#if com_person_product_default_img != null]${com_person_product_default_img}[#else]${setting.siteUrlImg}${setting.defaultThumbnailProductImage}[/#if]" />
																		[/#if]
																	[#else]
																		[#if orderItem.product.image!=null]
                                                                            <img src="${setting.siteUrlImage}${orderItem.product.image}" />
																		[#else]
                                                                            <img src="[#if com_person_product_default_img != null]${com_person_product_default_img}[#else]${setting.siteUrlImage}${setting.defaultThumbnailProductImage}[/#if]" />
																		[/#if]
																	[/#if]

															</div>
													 		<div class="title">
													 			<p>${orderItem.name}</p>
													 		</div>
													 		<div class="attrs">
													 			<span>[#if productShowRate??]${coinConvert(orderItem.price, productShowRate, true, false)}[#else]${currency(orderItem.price, true)}[/#if] x${orderItem.quantity}</span>
													 			[#if orderItem.product.specificationValues]
																	[#list orderItem.product.specificationValues as specificationValue]
																		<span>
																			${specificationValue.specification.name}:${specificationValue.name}
																		</span>
																	[/#list]
																[/#if]
													 		</div>
										 				</a>
										 				<!-- 标记是否有商品还没有评论 -->
										 				[#if orderItem.reviewStatus == "unreview"]
															[#assign isReview="no"]
														[#else]
															[#assign isReview="yes"]
														[/#if]
										 			[/#list]
								 				<footer>
								 					<span class="total_price" order1>总计:[#if productShowRate??]${coinConvert(order1.price, productShowRate, true, false)}[#else]${currency(order1.price, true)}[/#if]</span>
								 					
								 					<div class="btns">
								 						[#if order1.orderStatus == "unpaid"]
															[#if !order1.expired]
																<input type="button"

																	     [#if domainName==null || domainName==""]
																	        onclick="javascript:location.href='${base}/member/order/payment.jhtml?sn=${order1.sn}'"
															             [#else]
																	       onclick="javascript:location.href='${base}/paysRedirect/payment.jhtml?sn=${order1.sn}'"
															             [/#if]
																	   value="去付款" class="btn_gray"/>
																<input type="button" onclick="cancelOrder(this, '${order1.id}', '${order1.sn}',true, ${order.freight})" value="取消订单" class="btn_gray"/>
															[/#if]
														[#elseif order1.orderStatus == "unconfirmed"]
																[#if order1.orderType==0&&!order.groupPurchase??]<input type="button" onclick="cancelOrder(this, '${order1.id}', '${order1.sn}',true, ${order.freight})" value="取消订单" class="btn_gray"/>[/#if]
														[#elseif order1.orderStatus == "shipped"]
																<input type="button" onclick="confirmOrder('${order1.id}', '${order1.sn}')" value="确认收货" class="btn_gray"/>
														[#elseif order1.orderStatus == "completed"]
															[#if isReview == "no"]
																<input type="button" onclick="javascript:location.href='${base}/member/order/evaluateOne.jhtml?orderId=${order1.id}'" value="评价" class="btn_gray"/>
															[/#if]
														[/#if]
								 						<input type="button" onclick="javascript:location.href='[#if order.paymentQrcodeId??]${base}/qrcode/payment/detail.jhtml?sn=${order1.sn}[#else]${base}/member/order/detail.jhtml?id=${order1.id}[/#if]'" value="订单详情" class="btn_theme"/>
								 					</div>
								 				</footer>
								 			</li>
								 		[/#if]
							 		[/#if]
							 	[/#list]
			 				[#else]<!-- 未被拆分的订单 -->
					 			<li>
					 				<header>
					 					<span class="no">订单号码：${order.sn}</span>
						 					[#if order.supplierId.isOweOrder==24 || order.supplierId.isOweOrder==5 ||order.supplierId.isOweOrder==9 ||order.supplierId.isOweOrder==10
												||order.supplierId.isOweOrder==11||order.supplierId.isOweOrder==12||order.supplierId.isOweOrder==15||order.supplierId.isOweOrder==16
												||order.supplierId.isOweOrder==17]
											[#else]
												<span id="icon_del_${order.sn}" class="icon_del" onclick="deleteOrder('${order.id}', '${order.sn}')" [#if order.orderStatus != "cancelled" || order.subTable?? ] style="display:none" [/#if]></span>
						 					[/#if]
						 					[#if order.orderStatus == "unpaid"]
												[#if order.expired]
													<span class="status">已取消</span>
												[#else]
													<span class="status">待付款</span>
												[/#if]
											[#elseif order.jdConfirmFlag  && (order.supplierId.isOweOrder==35|| order.supplierId.isOweOrder == 68)]
												<span class="status complete">取消中</span>
											[#elseif order.orderStatus == "unconfirmed"]
													<span class="status">待确认</span>
											[#elseif order.orderStatus == "confirmed" && order.supplierId.isOweOrder!=35&& order.supplierId.isOweOrder != 68]
												<span class="status">待发货</span>
											[#elseif order.orderStatus == "confirmed" && (order.supplierId.isOweOrder==35|| order.supplierId.isOweOrder == 68)]
													<span class="status">预定成功</span>
											[#elseif order.orderStatus == "shipped"]
													<span class="status">待收货</span>
											[#elseif order.orderStatus == "completed"]
													<span class="status complete">已完成</span>
											[#elseif order.orderStatus == "cancelled"]
													<span class="status">已取消</span>
											[/#if]
					 				</header>
					 				
					 				[#list order.orderItems as orderItem]
						 				<a href="[#if !order.supplierId.isVirtualSupplier && !order.isPaymentOrder && order.orderType != 4]${base}${orderItem.product.path}[#else]javascript:;[/#if]" class="item">
						 					<div class="img">
									 			[#--<img src="${setting.siteUrlImg}[#if orderItem.thumbnail??]${orderItem.thumbnail}[#elseif com_person_product_default_img != null]${com_person_product_default_img}[#else]${setting.defaultThumbnailProductImage}[/#if]" />--]

													[#if orderItem.product.supplierId.id==68 || orderItem.product.supplierId.id == 181|| product.supplierId.id == 695]
														[#if orderItem.product.attributeValue8!=null]
                                                            <img src="${orderItem.product.attributeValue8}" />
														[#elseif  orderItem.product.image!=null]
                                                            <img src="${setting.siteUrlImg}${orderItem.product.image}" />
														[#else]
                                                            <img src="[#if com_person_product_default_img != null]${com_person_product_default_img}[#else]${setting.siteUrlImg}${setting.defaultThumbnailProductImage}[/#if]" />
														[/#if]
													[#else]
													    [#if orderItem.order.supplierId.isOweOrder!=null && orderItem.order.supplierId.isOweOrder==24]
												             [#if orderItem.name=='美团外卖']
                                                                 <img src="${base}/resources/wechat/img/meituan/waimai.png"  />
															 [#elseif orderItem.name=='点评团购' || orderItem.name=='点评闪惠' || orderItem.name=='美团团购'
															 || orderItem.name=='点评团购手续费'  || orderItem.name=='点评闪惠手续费' || orderItem.name=='美团团购手续费' ]
                                                                 <img src="${base}/resources/wechat/img/meituan/canyin.png"  />
															 [#elseif orderItem.name=='美团酒店']
                                                                 <img src="${base}/resources/wechat/img/meituan/hotel.png"  />
															 [#elseif orderItem.name=='美团旅游']
                                                                 <img src="${base}/resources/wechat/img/meituan/ticket.png"  />
															 [#else]
                                                                 <img src="${base}/resources/wechat/img/meituan/waimai.png"  />
															 [/#if]
														[#else]
															[#if orderItem.product.image!=null]
                                                                <img src="${setting.siteUrlImage}${orderItem.product.image}" />
															[#else]
                                                                <img src="[#if com_person_product_default_img != null]${com_person_product_default_img}[#else]${setting.siteUrlImage}${setting.defaultThumbnailProductImage}[/#if]" />
															[/#if]
														[/#if]
													[/#if]
											</div>
									 		<div class="title">
												[#if orderItem.order.supplierId.isOweOrder!=null && orderItem.order.supplierId.isOweOrder==24]
													 [#assign subject=orderItem.getVirtualProductOrderInfoMapValue('subject')]
													[#if subject!=null && subject!=""]
													     [#if subject?index_of("-")]
														    [#list subject?split("-") as name]
                                                                 <p>${name}</p>
															 [#break]
														     [/#list]
														 [#else]
                                                                  <p>${subject}</p>
													     [/#if]

													[#else]
													    <p>${orderItem.name}</p>
													[/#if]

												[#else]
												    <p>${orderItem.name}</p>
												[/#if]
									 		</div>
									 		<div class="attrs">
									 			[#if order.orderType == 4]<!-- 兑换券兑换 -->
									 			[#else]
									 			<span>[#if productShowRate??]${coinConvert(orderItem.price, productShowRate, true, false)}[#else]${currency(orderItem.price, true)}[/#if] x${orderItem.quantity}</span>
									 			[/#if]
									 			[#if orderItem.product.specificationValues]
													[#list orderItem.product.specificationValues as specificationValue]
														<span>
															${specificationValue.specification.name}:${specificationValue.name}
														</span>
													[/#list]
												[/#if]
									 		</div>
						 				</a>
						 				<!-- 标记是否有商品还没有评论 -->
						 				[#if orderItem.reviewStatus == "unreview"]
											[#assign isReview2="no"]
										[#else]
											[#assign isReview2="yes"]
										[/#if]
						 			[/#list]
					 				
					 				<footer>
					 					[#if order.orderType == 4]<!-- 兑换券兑换 -->
						 					[#assign couponCode=order.couponCode]
								            [#if order.parentId??]
								            	[#assign couponCode = order.parentId.couponCode]
								            [/#if]
					 						<span class="total_price" order>[#if couponCode != null && couponCode.coupon != null]${couponCode.coupon.name}[/#if]</span>
					 					[#else]
					 						<span class="total_price" order>总计:[#if productShowRate??]${coinConvert(order.price + order.fee, productShowRate, true, false)}[#else]${currency(order.price + order.fee, true)}[/#if]</span>
					 					[/#if]
					 					<div class="btns">
					 					[#if order.orderType == 4]<!-- 兑换券兑换 -->
					 					<input type="button" onclick="javascript:location.href='${base}/member/order/detail.jhtml?id=${order.id}'" value="订单详情" class="btn_theme"/>
					 					[#else]
					 					[#if !order.supplierId.isVirtualSupplier]
					 						[#if order.orderStatus == "unpaid"]
												[#if !order.expired]
													<input type="button"
														    [#if domainName==null || domainName==""]
																	onclick="javascript:location.href='${base}/member/order/payment.jhtml?sn=${order.sn}'"
															[#else]
																	onclick="javascript:location.href='${base}/paysRedirect/payment.jhtml?sn=${order.sn}'"
															 [/#if]
														   value="去付款" class="btn_gray"/>
													<input type="button" onclick="cancelOrder(this, '${order.id}', '${order.sn}',false)" value="取消订单" class="btn_gray"/>
												[/#if]
											[#elseif order.orderStatus == "unconfirmed"]
													[#if order.orderType==0&&!order.groupPurchase??]<input type="button" onclick="cancelOrder(this, '${order.id}', '${order.sn}',false)" value="取消订单" class="btn_gray"/>[/#if]
											[#elseif order.orderStatus == "shipped"]
													<input type="button" onclick="confirmOrder('${order.id}', '${order.sn}')" value="确认收货" class="btn_gray"/>
											[#elseif order.orderStatus == "completed" && order.paymentQrcodeId == null]
												[#if isReview2 == "no"]
													<input type="button" onclick="javascript:location.href='${base}/member/order/evaluateOne.jhtml?orderId=${order.id}'"  value="评价" class="btn_gray"/>
												[/#if]
											[/#if]
					 								<input type="button" onclick="javascript:location.href='[#if order.paymentQrcodeId??]${base}/qrcode/payment/detail.jhtml?sn=${order.sn}[#else]${base}/member/order/detail.jhtml?id=${order.id}[/#if]'" value="订单详情" class="btn_theme"/>
										[#else]
											[#assign operationOrder=order]
											[#include "/wechat/member/order/order_operation.ftl"]	
										[/#if]
										[/#if]
					 					</div>
					 				</footer>
					 			</li>
				 			[/#if]
			 			[/#list]
					[/#if]
