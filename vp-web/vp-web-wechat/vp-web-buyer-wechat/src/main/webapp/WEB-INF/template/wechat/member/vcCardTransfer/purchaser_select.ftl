<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
    <title>选择银行卡</title>
    <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
    <link rel="stylesheet" href="${base}/resources/wechat/css/vant/index.css">
    <link rel="stylesheet" href="${base}/resources/wechat/css/common.css">
    <link  type="text/css"  rel="stylesheet" href="${base}/resources/wechat/css/vcCardTransferBackSlelect.css">
    <script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

    <script type="text/javascript" src="${base}/resources/wechat/js/jquery.validate.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/md5.js" ></script>
    <script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js" ></script>
    <script type="text/javascript" src="${base}/resources/wechat/plugins/clipboard/clipboard.min.js"></script>
    <script src="${base}/resources/wechat/plugins/vue/vue.min.js"></script>
    <script src="${base}/resources/wechat/js/vant.min.js"></script>
</head>
<body class="has_fixed_footer vcCardTransferBackSlelectPage purchasePage">
<div id="app" >
    <van-sticky>
        <van-nav-bar
                title="选择收购方"
                left-text=""
                left-arrow
                @click-left="jump('card/index')"
        >
            <template #right >
            </template>
        </van-nav-bar>
    </van-sticky>
    <section class="purchaserList">
        <ul>
            <li @click="selectedPurchaser(item)" class="flexbox align-items-c" v-for="(item,index) in purchaserList" :key="'purchaserList'+index">
                <img :src="'${base}/resources/wechat/img/vcCardTransfer/'+item.purchaser+'.png'" >
                <p :class="!item.flag?'setGray':''">{{item.purchaserName}}<span v-if="!item.flag">（暂未对接）</span></p>
            </li>
        </ul>
    </section>
</div>
</body>
<script>
    let vm = new Vue({
        el: "#app",
        data() {
            return {
                purchaserList:[
                    {
                        purchaserName:"卡券邦",
                        purchaser:"kaquanbang",
                        flag:true,//是否对接
                    },
                    {
                        purchaserName:"天猫",
                        purchaser:"tianmao",
                        flag:false,//是否对接
                    },
                    {
                        purchaserName:"闲鱼",
                        purchaser:"xianyu",
                        flag:false,//是否对接
                    }
                ]
            };
        },
        mounted() {

        },
        methods: {
            jump(data){
                location.href="${base}/member/vcCardTransfer/" + data+".jhtml";
            },
            selectedPurchaser(data){
                if(data.flag){ //已对接
                    var arr=["purchaserName","purchaser"];
                    arr.map(function (item) {
                        sessionStorage.setItem(item,data[item]);
                    });
                    location.href="${base}/member/vcCardTransfer/card/index.jhtml";
                    <#--location.href="${base}/member/vcCardTransfer/card/index.jhtml?purchaserName=" + data.purchaserName + "&purchaser=" + data.id;-->
                }else {
                    this.$toast("暂未对接")
                }
            }
        },
    });
</script>
</html>