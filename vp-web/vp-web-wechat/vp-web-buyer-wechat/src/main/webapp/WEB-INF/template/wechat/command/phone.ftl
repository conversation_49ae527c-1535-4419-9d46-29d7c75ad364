<!DOCTYPE html>
<html style="background: #fff;">
	<head>
		<meta charset="utf-8" />
		<title>验证手机</title>
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="black">
        <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js" ></script>
        <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
        <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/login.css" />
        <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/slide.css" />

		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/jquery.validate.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
		
		<script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js" ></script>
        <script type="text/javascript" src="${base}/resources/wechat/js/jquery.lgyslide.js"></script>
        <script type="text/javascript" src="${base}/resources/wechat/js/jigsawPuzzle.js"></script>

	</head>
	<script type="text/javascript">
		$().ready(function() {
			[@flash_message /]
		});
	</script>
	<body style="background: #fff;">

    <div class="public_top_header">
        <span class="return_back" onclick="javascript:window.history.go(-1);"></span>
    </div>

		<div class="loginPage"> 
			<div class="logo_section">
				<img src="[#if comPersonConf && com_person_wechatLoginTopLogo != null&& com_person_wechatLoginTopLogo != ""]${com_person_wechatLoginTopLogo }[#else]${base}/resources/wechat/img/fuli_logo.png[/#if]"/>
			</div>
			
			<div class="tab_section">
				验证手机
			</div>
			
			<div class="login_form_section textLeft"> 
				<form id="inputForm" action="commandNext.jhtml" method="post" enctype="multipart/form-data">
					<input type="hidden" name="enterCode" value="${enterCode}" />
					<input type="hidden" name="companyId" id="companyId" value="${companyId}" />
					<input type="hidden" name="randCode" value="${randCode}" />
					<input type="hidden" name="inviter" value="${inviter}" />
					[#if CDKey??]
						<input type="hidden" name="CDKey" value="${CDKey}"/>
					[/#if]
					<div class="item standard_input">
						<input type="tel" name="phone" id="phone" value="${phone}" placeholder="请输入手机号" class="input" onkeyup="javascript:checkWordPhone(this);" onmousedown="javascript:checkWordPhone(this);"/>
						<span class="icon_clear"></span>
						<div class="text_error2" id="closeTips">
							您已经是该组织会员不需要重复加入。
						</div>
						<div class="text_error2" id="closeTips1">
							您已经是该组织会员不需要重复加入。
						</div>
						<div class="text_error2" id="closeTips2">
							您加入的组织数量已经超过了上限
						</div>
						<div class="text_error2" id="closeTips4">
							该手机号码已达到当天次数上限
						</div>
						[#if errorType == "phone"]
							<div class="text_error2" style="display: block;" id="closeTips3">${errorMsg}</div>
						[/#if]
					</div>
					<!--<div class="item hasbutton standard_input">
						<input type="text" name="captcha" id="captcha" value="" placeholder="请输入图形验证码" class="input" onkeyup="javascript:checkWord(this);" onmousedown="javascript:checkWord(this);"/>
						<span class="icon_clear"></span>
						<img id="captchaImage" class="captchaImage" src="${base}/common/captcha.jhtml?captchaId=${captchaId}" title="">
						<div class="text_error2" id="closeTips6">
							图形验证码错误
						</div>
					</div> -->
					<div class="item hasbutton standard_input long">
						<input type="number" name="code" maxlength="6" oninput="if(value.length>6) value = value.slice(0,6)" id="code" value="" placeholder="请输入短信验证码" class="input" onkeyup="javascript:checkWord(this);" onmousedown="javascript:checkWord(this);"/>
						<span class="icon_clear"></span>
						<input type="button" name="" id="emailCodeBtn" value="获取短信验证码" class="code"/>
						<!--禁用状态 disabled="disabled"-->
					</div>
					[#if errorType == "code"]
						<div class="text_error" style="display: block;">${errorMsg}</div>
					[/#if]
					<input type="submit" value="下一步" class="btn_cornor_long"/>
					<div  class="fli_checkbox_blue corner" >
						<input type="checkbox" id="agreement" name="agreement" />
						<label for="agreement"></label>
						已阅读并同意<a href="${base}/member/secret_agreement.jhtml">《福利平台隐私权政策》</a>
					</div>
				</form>
			</div>
		</div>
		<script type="text/html" id="slider_code">
			<div id="imgscode"></div>
		</script>
		<script type="text/javascript">
			var interval;
			$(function(){
			// var $captcha = $("#captcha");
			//以下两个方法是为了去掉前后空格
			$("input[name='phone']").blur(function(){
				$(this).val($(this).val().trim());
			});
			jQuery.validator.addMethod("phoneFormat",function(value,element,param){
					value = value.trim();
					var regex = commonRule.phone;
				  	if (regex.test(value)){
				  		return true;
				  	}else{
				        return false;
				  	}
		        },$.validator.format("手机号码不存在")  
		    );
				// 表单验证
				$("#inputForm").validate({
					rules: {
						phone: {
							required:true,
							phoneFormat: true,
							maxlength: 11
						},
						code: {
							required:true,
							digits:true
						}
					},
					messages: {
						phone: {
							pattern: "手机号码不存在"
						}
					},
					submitHandler: function(form) {
						if(!$("#agreement").prop("checked")){
	                        layer.open({
	                            content:'请阅读并同意《福利平台隐私权政策》'
	                            ,skin: 'msg'
	                            ,time: 2
	                        })
	                        return false;
	                    }
						
						$("#submit").prop("disabled", true);
						form.submit();
					}
				});
				
				$("#emailCodeBtn").click(function(){
					var phone = $("#phone").val();
					var companyId = $("#companyId").val();
					if(phone == ""){
						layer.open({
						    content: '请输入手机号码'
						    ,skin: 'msg'
						    ,time: 2 //2秒后自动关闭
						  });
						return false; 
					}
				    if(!(commonRule.phone.test(phone))){
				    	layer.open({
						    content: '请输入正确的手机号码'
						    ,skin: 'msg'
						    ,time: 2 //2秒后自动关闭
						  });
				        return false; 
				    }

					// if($captcha.val() == null || $captcha.val() == ""){
				    	// layer.open({
					// 	    content: "请输入图形验证码"
					// 	    ,skin: 'msg'
					// 	    ,time: 2 //2秒后自动关闭
					// 	  });
				     //    return false;
					// }
					if($(this).val() == "获取短信验证码"){
                        layer.open({
                            type: 1,
                            title: false,
                            closeBtn: 0,
                            resize: false,
                            scrollbar: false,
                            area: ['auto', '420px'], //宽高
                            skin:"layer-code",
                            content: $("#slider_code").html(),
                            success: function () {
								createcode(sendPhoneCode);
                            }
                        });
					}
				});

				// 更换验证码
				//$("#captchaImage").click(function() {
				//	$("#captchaImage").attr("src", "${base}/common/captcha.jhtml?captchaId=${captchaId}&timestamp=" + (new Date()).valueOf());
				//});
			});
            //发送短信验证码
            function sendPhoneCode() {
                var phone = $("#phone").val();
                var companyId = $("#companyId").val();
                $.ajax({
                    url:'${base}/command/sendPhoneCode.jhtml',
                    type:'POST',
                    data:{phone: phone,companyId: companyId},
                    cache:false,
                    async:false,
                    success:function(flag){
                        $("#closeTips").hide();
                        $("#closeTips1").hide();
                        $("#closeTips2").hide();
                        $("#closeTips4").hide();
                        $("#closeTips3").hide();
                        $("#closeTips6").hide();

                        if(flag == 0){
                            //验证码发送成功
                            emailCodeBtnVal();
                            interval = setInterval("emailCodeBtnVal()", 1000);
                        }else if(flag == 1){
                            layer.open({
                                content: '60秒内只允许发送一次'
                                ,skin: 'msg'
                                ,time: 2 //2秒后自动关闭
                            });
                        }else if(flag == 2){
                            $("#closeTips").show();
                        }else if(flag==3){
                            $("#closeTips1").show();
                        }else if(flag == 4){
                            $("#closeTips2").show();
                        }else if(flag == 6){
                            $("#closeTips4").show();
                        }
                        // else if(flag == 7){//图形验证码错误
                        //     $("#closeTips6").show();
                        // }
                        else{
                            //验证码发送失败
                            layer.open({
                                content: '短信验证码发送失败'
                                ,skin: 'msg'
                                ,time: 2 //2秒后自动关闭
                            });
                        }

                        if(flag != 7){//不是图形验证码错误
                            $("#captchaImage").click();//刷新图形验证码
                        }
                    }
                });
            }
			//发送验证码按钮显示秒数递减
			function emailCodeBtnVal(){
				if($("#emailCodeBtn").val() == "获取短信验证码"){
					$("#emailCodeBtn").removeClass("active");
					$("#emailCodeBtn").val("59");
				}else if($("#emailCodeBtn").val() == "1"){
					clearInterval(interval);
					$("#emailCodeBtn").addClass("active");
					$("#emailCodeBtn").val("获取短信验证码");
				}else{
					$("#emailCodeBtn").val(Number($("#emailCodeBtn").val()) - 1);
				}
			}
			
			//手机号码隐藏错误信息
			function checkWordPhone(){
				$("#closeTips").hide();
				$("#closeTips1").hide();
				$("#closeTips2").hide();
				$("#closeTips4").hide();
				$("#closeTips3").hide();
			}
			
			//验证码隐藏错误信息
			function checkWord(){
				$(".text_error").hide();
			}
		</script>
	</body>  
</html>
