<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>宝贝来了</title>
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no"> 
		<meta name="format-detection" content="telephone=no" /> 
		<meta content="email=no" name="format-detection"> 
        <meta name="apple-mobile-web-app-capable" content="yes"> 
        <meta name="apple-mobile-web-app-status-bar-style" content="black">
        <script type="text/javascript" src="${base}/resources/greetingCard/js/flexible.js" ></script>
        <link rel="stylesheet" href="${base}/resources/greetingCard/css/swiper.css?v=1.0.0" />
        <link rel="stylesheet" href="${base}/resources/greetingCard/css/animate.css?v=1.0.0" />
        <link rel="stylesheet" type="text/css" href="${base}/resources/greetingCard/css/common.css?v=1.0.0" /> 
        <link rel="stylesheet" type="text/css" href="${base}/resources/greetingCard/template/baby2/css/style.css?v=1.0.0" />   
		<script type="text/javascript" src="${base}/resources/greetingCard/js/jquery-1.9.1.js" ></script> 
	</head>
	<body>
		<div class="loading_section">
			<div class="loading"></div>
		</div>
		<div class="public_top_header">
			[#if flag!=null && flag==0]
				<a href="${base}/dynamic/index.jhtml" class="return_back"></a>
            [#else]
				<a href="javascript:history.go(-1);" class="return_back"></a>
			[/#if]
			我的贺卡
		</div>
		
		<div id="music" class="music">
			<audio id="music-audio" class="audio" loop="" autoplay="autoplay" preload="">
				<source src="${base}/resources/greetingCard/music/New_Year's_Day.mp3" type="audio/mpeg"></source>
			</audio>
			<div class="control">
				<div class="control-after"></div>
			</div>	
		</div>
		
		<!-- Swiper -->
	    <div class="swiper-container">
	        <div class="swiper-wrapper">
	            <div class="swiper-slide slide_1">
					<div class="border">
						<div class="img_1_1 animated">
							<img src="${base}/resources/greetingCard/template/baby2/img/img_1_1.png" />
						</div>
						<div class="img_1_2 animated">
							<img src="${base}/resources/greetingCard/template/baby2/img/img_1_2.png" />
						</div>
						<div class="img_1_3 animated">
							<img src="${base}/resources/greetingCard/template/baby2/img/img_1_3.png" />
						</div>
						<div class="img_1_4 animated">
							<img src="${base}/resources/greetingCard/template/baby2/img/img_1_4.png" />
						</div>
						<div class="img_1_5 animated">
							<img src="${base}/resources/greetingCard/template/baby2/img/img_1_5.png" />
						</div>
						<div class="img_2 animated">
							<img src="${base}/resources/greetingCard/template/baby2/img/img_2.png" />
						</div>
						<div class="img_3 animated">
							<img src="${base}/resources/greetingCard/template/baby2/img/img_3.png" />
						</div>
					</div>
	            </div>
	            <div class="swiper-slide slide_2">
					<div class="textarea">
						<div class="img_2_1 animated zoomIn">
							<img src="${base}/resources/greetingCard/template/baby2/img/img_5.png" />
						</div>
						<div class="textarea animated">
							${cardContent}
						</div>
					</div>
	            </div>
	            [#assign phoneTitle="欢迎小宝贝" /]
				[#include "/wechat/member/card/template/commonGift.ftl" /]
	        </div>
	        <!-- Add Pagination -->
	        <div class="swiper-pagination"></div>
	    </div>
	    <div class="to_next_page animated infinite slideInUpS"></div>
	    
	    <script type="text/javascript" src="${base}/resources/greetingCard/js/swiper.jquery.min.js" ></script>
	    <!-- Initialize Swiper -->
	    <script>
				var timeout = null;
		    var swiper = new Swiper('.swiper-container', {
		        pagination: '.swiper-pagination',
		        direction: 'vertical',
		        loop: true,  
		        paginationClickable: true, 
		        onSlideChangeEnd: function(swiper){  
			        if(swiper.activeIndex == 1||swiper.activeIndex == 4){
			       		$(".slide_1 .img_1").show().addClass("zoomIn");
			       		$(".slide_1 .img_2").show().addClass("fadeInLeft");
						$(".slide_1 .img_3").show().addClass("lightSpeedIn");
						$(".slide_1 .img_4_1").show().addClass("fadeInDown");
						$(".slide_1 .img_4_2").show().addClass("fadeInDown");

			        	$(".slide_2 .img_1").hide().removeClass("zoomIn");
			       		$(".slide_2 .img_2").hide().removeClass("flash");
			        	$(".slide_2 .textarea").hide().removeClass("bounceInUp");
			        	$(".showBoxPage .img_1").hide().removeClass("slideInDown");
			        	$(".showBoxPage .img_2").hide().removeClass("rubberBand");
			        	$(".showBoxPage .button").hide().removeClass("slideInUp");
			        }else if(swiper.activeIndex == 2){
			        	$(".slide_1 .img_1").hide().removeClass("zoomIn");
			        	$(".slide_1 .img_2").hide().removeClass("fadeIn");
			        	$(".slide_1 .p1").hide().removeClass("bounceInLeft");
			        	$(".slide_1 .p2").hide().removeClass("bounceInRight");
			       		$(".slide_2 .img_1").show().addClass("zoomIn");
			       		$(".slide_2 .img_2").show().addClass("flash");
			        	$(".slide_2 .textarea").show().addClass("bounceInUp");
			        	$(".showBoxPage .img_1").hide().removeClass("slideInDown");
			        	$(".showBoxPage .img_2").hide().removeClass("rubberBand");
			        	$(".showBoxPage .button").hide().removeClass("slideInUp");
			        }else if(swiper.activeIndex == 3||swiper.activeIndex == 0){
			        	$(".slide_1 .img_1").hide().removeClass("zoomIn");
			        	$(".slide_1 .img_2").hide().removeClass("flash");
			        	$(".slide_1 .p1").hide().removeClass("bounceInLeft");
			        	$(".slide_1 .p2").hide().removeClass("bounceInRight");
			        	$(".slide_2 .img").hide().removeClass("bounceIn");
			        	$(".slide_2 .textarea").hide().removeClass("bounceInUp");
			       		$(".showBoxPage .img_1").show().addClass("slideInDown");
			        	$(".showBoxPage .img_2").show().addClass("rubberBand");
			        	$(".showBoxPage .button").show().addClass("slideInUp");
			        }
			    }
		    });
		    
	    </script>
	</body>
</html>
