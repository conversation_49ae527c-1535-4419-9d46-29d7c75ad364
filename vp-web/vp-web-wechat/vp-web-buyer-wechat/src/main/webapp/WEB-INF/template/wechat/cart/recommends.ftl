
<ul>
  [@top_product_list  topType = topType]
     [#if products!=null && products?size>0]
       [#list products as product]
	       [#if product != null]
	        <li>
	            <a href="${base}${product.path}">
	               	<div class="img flexbox align-items-c justify-content-c">
						[#--<img src="${setting.siteUrlImage}[#if product.image??]${product.image}[#elseif com_person_product_default_img != null]${com_person_product_default_img}[#else]${setting.defaultThumbnailProductImage}[/#if]">--]
							[#if product.supplierId.id==68 || product.supplierId.id == 181|| product.supplierId.id == 695]
								[#if product.attributeValue8!=null]
	                                <img src="${product.attributeValue8}" />
								[#elseif  product.image!=null]
	                                <img src="${setting.siteUrlImg}${product.image}" />
								[#else]
	                                <img src="[#if com_person_product_default_img != null]${com_person_product_default_img}[#else]${setting.siteUrlImg}${setting.defaultThumbnailProductImage}[/#if]" />
								[/#if]
							[#else]
								[#if product.image!=null]
	                                <img src="${setting.siteUrlImage}${product.image}" />
								[#else]
	                                <img src="[#if com_person_product_default_img != null]${com_person_product_default_img}[#else]${setting.siteUrlImage}${setting.defaultThumbnailProductImage}[/#if]" />
								[/#if]
							[/#if]
	
					</div>
					<p class="title">${product.name}</p>
				</a>
				<p class="price flexbox align-items-c justify-content-space-between flex_wrap">
	
					[#include "/wechat/include/add_price/product.ftl" /]
					 
					[#if productShowRate??]
						[#assign floatValue = productPrice*productShowRate?number]
						[#assign integerPart = floatValue?floor]
						[#assign decimalPart = (floatValue * 100)?round % 100 / 100]
								    <span class="shop" id="productPrice">
										<span class="sales-price">
											<i class="iconfontAli icon-ali-jifen"></i><span>${integerPart}</span>${decimalPart?string(".00")}
										</span>
							        </span>
						[#--<span class="shop">${coinConvert(productPrice, productShowRate, true)}</span>--]
					[#else]
						[#assign floatValue = productPrice?number]
						[#assign integerPart = floatValue?floor]
						[#assign decimalPart = (floatValue * 100)?round % 100 / 100]
                                    <span class="shop" id="productPrice" productShowRate1="${productShowRate }">
										<span class="sales-price">
											￥<span>${integerPart}</span>${decimalPart?string(".00")}
										</span>
							    	</span>
						[#--<span class="shop">${currency(productPrice, true)}</span>--]
						<span class="market"> ${currency(productMarketPrice, true)}</span>
					[/#if]
					[#--<span class="icon_cart" data-id="${product.id}"></span>--]
				</p>
			</li>
			[/#if]
		    [#assign supplierPriceRate=null /]
		    [#assign supplierMarketPriceRate=null /]
     	[/#list]	
     [/#if]	
 [/@top_product_list]
</ul> 
   