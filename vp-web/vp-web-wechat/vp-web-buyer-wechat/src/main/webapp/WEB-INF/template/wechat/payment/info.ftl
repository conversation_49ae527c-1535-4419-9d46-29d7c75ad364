<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>确认订单</title>
		<meta name="apple-mobile-web-app-capable" content="yes" />
		<meta name="apple-mobile-web-app-status-bar-style" content="black" />
		<meta http-equiv="Expires" content="0" />
		<meta http-equiv="Cache-Control" content="no-cache" />
		<meta http-equiv="Pragma" content="no-cache" />
		<script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/plusWap.css" />
		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

		<script type="text/javascript" src="${base}/resources/wechat/js/card.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/jquery.validate.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
		
		<script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js"></script>

		<script type="text/javascript">

			var $orderRefreshForm;
			var $memo;
			var $memoMsg;

			function videoTest(obj){
				 var mobileValue = $("#mobile").val();
				 $("#midMobile").val(mobileValue);
			}

			$().ready(function() {

				$orderRefreshForm = $("#orderRefreshForm");
				$memo = $("#memo");
				$memoMsg = $("#memoMsg");
				var $submit = $("#submit");
				var $orderForm = $("#orderForm");
				var isOverseas = "${isOverseas}"; //海外商品标识
				var $receiverIdCard = $("#receiverIdCard");
				var $idcardNoOrder = $("#idcardNoOrder");
				var $saveIdcardNo = $("#saveIdcardNoDec");
				var $receiverId = $("#receiverId");

				function submitOrder() {
					var url = "createVirtual.jhtml";
					$.ajax({
						url: url,
						type: "POST",
						data: $orderForm.serialize(),
						dataType: "json",
						cache: false,
						beforeSend: function() {
							$submit.prop("disabled", true);
							$submit.val("提交中");
						},
						success: function(message) {
							if(message.type == "success") {
								location.href = "payment.jhtml?sn=" + message.content + "&type=0&basePay=1";
							}
							if(message.type == "warn") {
								$submit.val("立即付款")
								layer.open({
									content: message.content,
									skin: 'msg',
									time: 2 //2秒后自动关闭
								});
							} else if(message.type == "error") {
								$submit.val("立即付款");
								layer.open({
									content: "下单失败",
									skin: 'msg',
									time: 2 //2秒后自动关闭
								});

								$.ajax({
									url: "${base}/member/order/getChange.jhtml",
									type: "POST",
									data: {
										content: message.content
									},
									success: function(data) {
										var trHtml = '<div class="order_goods_has_changed"> <div class="header">以下商品有变动，暂时无法购买，请稍后再试！</div><div class="list">';
										[@compress single_line = true]
										$.each(data, function(i, product) {
											var productImage = "${setting.siteUrlImg + setting.defaultThumbnailProductImage}";
					                        if((product.supplierId.id == 68 || product.supplierId.id == 181) && product.attributeValue8 != null){
					                        	productImage = product.attributeValue8
					                        } else if(product.image != null){
					                        	productImage = "${setting.siteUrlImg }" + product.image;
					                        } else if(com_person_product_default_img != null){
					    						productImage = com_person_product_default_img;
					                        }
											
											trHtml += '<div class="item"> <img src="' + productImage + '"/><div class="title">' + product.name + '</div> </div>';
										});
										trHtml += '</div> </div>';
										[/@compress]
										layer.open({
											style: "width:94%;",
											title: false,
											btn: ["返回购物车"],
											content: trHtml,
											yes: function(index, layero) {
												location.href = "${base}/cart/list.jhtml";
											}
										});
									}
								});

							}
						},
						complete: function() {
							$submit.prop("disabled", false);
						}
					});
				}

				//输入密码事件
				$("#password").keyup(function() {
					var l_pwd = this.value.length;
					if(l_pwd >= 1 && l_pwd <= 6 && event.keyCode != 8) {
						var _input = document.getElementById("number" + l_pwd);
						_input.value = this.value.charAt(l_pwd - 1);
						if(l_pwd == 6) { //输入完成动作
							var _pwd = this.value;
							$("#paymentPop").hide();
							this.blur();
							var device = openDevice();
							if(device == "android") {
								vpshop_android.callHideSoftInput();
							}

							var $checkedIds = $("#rechargeRecords input[name='coinIds']:enabled:checked");
							$.ajax({
								url: "check_paypassword.jhtml",
								type: "POST",
								data: {
									coinIds: $("#coinIds").val(),
									paypassword: _pwd,
									whiteBarIds: $("#whiteBarIds").val()
								},
								dataType: "json",
								cache: false,
								success: function(message) {
									$(".password_section input").val("");
									if(message.type == "success") {
										submitOrder();
									} else {
										layer.open({
											content: message.content,
											skin: 'msg',
											time: 2 //2秒后自动关闭
										});
									}
								}
							});

						}
					} else if(event.keyCode == 8) { //退格键删除
						var _input = document.getElementById("number" + (l_pwd + 1));
						_input.value = '';
					}
				})





				var $payPasswordSetBtn = $("#payPasswordSetBtn");



				//保存身份证号
				$saveIdcardNo.click(function() {
					var receiverId = $receiverId.val();
					var idcardNo = $receiverIdCard.val();
					if(!idCardNoUtil.checkIdCardNo(idcardNo)) {
						layer.open({
							content: '请正确填写身份证号！',
							skin: 'msg',
							time: 2 //2秒后自动关闭
						});
						return false;
					}
					$.ajax({
						url: "saveReviceIdcard.jhtml",
						type: "POST",
						data: {
							receiverId: receiverId,
							idcardNo: idcardNo
						},
						dataType: "json",
						cache: false,
						success: function(msg) {
							if(msg == true) {
								layer.open({
									content: '身份证号码保存成功!',
									skin: 'msg',
									time: 2 //2秒后自动关闭
								});
							} else {
								layer.open({
									content: '身份证号码保存失败',
									skin: 'msg',
									time: 2 //2秒后自动关闭
								});
							}
						}
					});

				});

				// 订单提交
				$submit.click(function() {

					var cashPayFlag="${member.companyId.cashPayFlag}";
				    
				    if(cashPayFlag!=null && cashPayFlag!="" && cashPayFlag==0){ //不允许使用现金支付
						   if($("#_amountPayable").val() > 0) {
				             layer.open({
				                 content: '请使用积分支付！',
				                 skin: 'msg',
				                 time: 2 //2秒后自动关闭
				             });
				             return false;
				         }
					   }
					if(isOverseas == "true") {
						var receiverIdCard = $receiverIdCard.val();

						if(receiverIdCard == "" || receiverIdCard == null) {
							layer.open({
								content: '存在海外商品请正确填写身份证号！',
								skin: 'msg',
								time: 2 //2秒后自动关闭
							});
							$("html,body").animate({
								scrollTop: 0
							}, 300);
							return false;
						} else {
							if(!idCardNoUtil.checkIdCardNo(receiverIdCard)) {
								layer.open({
									content: '存在海外商品请正确填写身份证号！',
									skin: 'msg',
									time: 2 //2秒后自动关闭
								});
								$("html,body").animate({
									scrollTop: 0
								}, 300);
								return false;
							}
							$idcardNoOrder.val(receiverIdCard);
						}
					}

					var rechargeRecordAmount = "${rechargeRecordAmount}"; //勾选了积分支付
					var whiteBarYAmount = "${whiteBarYAmount}"; //勾选了白条支付

					[#if member.companyId.isFreePay ]
						submitOrder();
					[#else]
					if(rechargeRecordAmount > 0) { //可用积分大于0，弹出密码框
						$("#paymentPop").show().find("#password").focus();
					} else if(whiteBarYAmount > 0) {
						$("#paymentPop").show().find("#password").focus();
					} else {
						submitOrder();
					}
					[/#if]
				});
			});

			function submitOrder() {
				var url = "createVirtual.jhtml";
				$.ajax({
					url: url,
					type: "POST",
					data: $orderForm.serialize(),
					dataType: "json",
					cache: false,
					beforeSend: function() {
						$submit.prop("disabled", true);
						$submit.val("提交中");
					},
					success: function(message) {
						if(message.type == "success") {
							location.href = "paymentVirtual.jhtml?sn=" + message.content + "&type=0";
						}
						if(message.type == "warn") {
							$submit.val("立即付款")
							layer.open({
								content: message.content,
								skin: 'msg',
								time: 2 //2秒后自动关闭
							});

						} else if(message.type == "error") {
							$submit.val("立即付款")
							layer.open({
								content: "下单失败",
								skin: 'msg',
								time: 2 //2秒后自动关闭
							});

							$.ajax({
								url: "${base}/member/order/getChange.jhtml",
								type: "POST",
								data: {
									content: message.content
								},
								success: function(data) {
									var trHtml = '<div class="order_goods_has_changed"> <div class="header">以下商品有变动，暂时无法购买，请稍后再试！</div><div class="list">';
									[@compress single_line = true]
									$.each(data, function(i, product) {
										var productImage = "${setting.siteUrlImg + setting.defaultThumbnailProductImage}";
				                        if((product.supplierId.id == 68 || product.supplierId.id == 181) && product.attributeValue8 != null){
				                        	productImage = product.attributeValue8
				                        } else if(product.image != null){
				                        	productImage = "${setting.siteUrlImg }" + product.image;
				                        } else if(com_person_product_default_img != null){
				    						productImage = com_person_product_default_img;
				                        }
										trHtml += '<div class="item"> <img src="' + productImage + '"/><div class="title">' + product.name + '</div> </div>';
									});
									trHtml += '</div> </div>';
									[/@compress]
									layer.open({
										style: "width:94%;",
										title: false,
										btn: ["返回购物车"],
										content: trHtml,
										yes: function(index, layero) {
											location.href = "${base}/cart/list.jhtml";
										}
									});
								}
							});
						}
					},
					complete: function() {
						$submit.prop("disabled", false);
					}
				});
			}



			//选择收货地址页面跳转
			function selectAddress() {
				$memoMsg.val($memo.val());
				$("#orderRefreshForm").attr("action", "selectAddress.jhtml");
				$("#orderRefreshForm").submit();
			}

			//选择积分页面跳转
			function selectCoin() {
				var url = "groupSelectCoin.jhtml";
				$("#orderRefreshForm").attr("action", url);
				$("#orderRefreshForm").submit();
			}

			//选择积分页面跳转
			function selectWhiteBar() {
				var url = "groupSelectWhiteBar.jhtml";
				$("#orderRefreshForm").attr("action", url);
				$("#orderRefreshForm").submit();
			}
			//选择优惠券页面跳转
			function selectCoupon() {
				$memoMsg.val($memo.val());
				$("#orderRefreshForm").attr("action", "selectCoupon.jhtml");
				$("#orderRefreshForm").submit();
			}
			// 验证手机号
			jQuery.validator.addMethod("isTel", function(value,element) {
			    var length = value.length;
			    var mobile = /^(((13[0-9]{1})|14[57]{1}|(15[0-9]{1})|17[378]{1}|(18[0-9]{1}))+\d{8})$/;
			    if(this.optional(element) || (length==11 && mobile.test(value))){
			    	$("#midMobile").val(value);
			    }
			    return this.optional(element) || (length==11 && mobile.test(value));
			   }, "请正确填写您的联系方式");

			$("#orderForm").validate({
			    rules : {
			        mobile : {
			            required : true,
			            minlength : 11

			        }
			    },
			    messages : {
			    	mobile : {
			            required : "请输入手机号",
			            minlength : "确认手机不能小于11个字符",
			            isMobile : "请正确填写您的手机号码"
			        }
			    },
				errorPlacement: function(error, element) {
					error.appendTo(element.parent());
				}
			});




		</script>
	</head>

	<body id="myOrderPage" class="pay-info-page confirm-order-page">
		<div class="myOrderPage simple">
			<div class="public_top_header">
                <span class="return_back" onclick="javascript:window.history.back();"></span>
				确认订单
				[#include "./wechat/include/head_nav.ftl" /]
			</div>

			<form id="orderRefreshForm" action="" method="post">
				[#list cartItems as cartItem]
				<input type="hidden" name="ids" value="${cartItem.id}" /> [/#list]
				<input type="hidden" name="thirdCoinAmount" value="${thirdCoinAmount}" />
				<input type="hidden" name="coinIds" value="${coinIds}" id="coinIds" />
				<input type="hidden" name="whiteBarIds" value="${whiteBarIds}" id="whiteBarIds" />
				<input type="hidden" id="receiverId" name="receiverId" [#if receiver??] value="${receiver.id}" [/#if] />
				<input type="hidden" id="productId" name="productId" value="${productId}" />
				<input type="hidden" name="couponCodeId" value="${couponCodeId}" />
				<input type="hidden" name="memo" id="memoMsg" value="${order.memo!}" />
				<input type="hidden" name="quantity" value="${quantity}" />
				<input type="hidden" name="pId" value="${productId}" />
				<input type="hidden" name="groupId" value="${groupId}" />
				<input type="hidden" name="toatlAmount" value="${order.toatlAmount}" />
				<input type="hidden" name="coinAmount" value="${order.coinAmount}" />
				<input type="hidden" name="amountPaid" value="${order.amountPaid}" />
				<input type="hidden" name="bizType" value="${bizType}" />
				<input type="hidden" id="orderJson" name="orderJson" value="${orderJson}" />
				<input type="hidden" id="midMobile" name="mobile" value="${mobile}" />
				
				<input type="hidden" name="coinTypeIdsEcho" value="${coinTypeIdsEcho}" />
			    <input type="hidden" name="coinTypeIds" value="${coinTypeIds}" />
				
				<!-- 下面参数为了计算金额 -->
				<input type="hidden" id="price"  name="price" value="${order.price}"/>
				<input type="hidden" id="freight"  name="freight" value="${order.freight}"/>
				<input type="hidden" id="couponDiscount"  name="couponDiscount" value="${order.couponDiscount}"/>
				<input type="hidden" id="promotionDiscount"  name="promotionDiscount" value="${order.promotionDiscount}"/>
			</form>

			<div style="display:none">
				<section class="base_info">
					<a href="javascript:void(0)" onclick="selectAddress()" class="fli_link_line">
						[#if receiver??]
						<p>
							收货人：${receiver.consignee}
							<span class="tel">${receiver.phoneDec}</span>
						</p>
						<p class="addr">
							${receiver.areaName}${receiver.address}
						</p>
						[#else] 马上去添加收货地址 [/#if]
					</a>
					<!--<a href="#">! 马上去添加收货地址</a>-->
					<div class="identify" [#if !isOverseas]style="display: none;" [/#if]>
						<input type="text" name="idcardNo" id="receiverIdCard" [#if receiver??] value="${receiver.idcardNoDec}" [/#if] class="input" placeholder="因海关清关需要，请填写收货人身份证号码。" />
						<input type="button" name="" id="saveIdcardNo" value="保存" class="btn_pill btn_pill_theme" />
					</div>
				</section>
			</div>
			<section class="contentBox card-rd">
				<ul class="order_list_section modify-box">
					[#list order.orderItems as orderItem]
					<li>
						<div class="img" name="${orderItem.name}">
							[#assign productImage = setting.siteUrlImg + setting.defaultThumbnailProductImage]
	                        [#if (orderItem.product.supplierId.id == 68 || orderItem.product.supplierId.id == 181) && orderItem.product.attributeValue8 != null]
	                            [#assign productImage = orderItem.product.attributeValue8]
	                        [#elseif orderItem.product.image!=null]
	                            [#assign productImage = setting.siteUrlImg + orderItem.product.image]
	        				[#elseif com_person_product_default_img != null]
	    						[#assign productImage = com_person_product_default_img]
	                        [/#if]
			                        
							<img src="${productImage }" />

						</div>
						<div class="info flexbox flex-column justify-content-c">
							<p class="title">
								${orderItem.name}
							</p>
							<p class="price flexbox align-items-c justify-content-space-between">
								[#assign orderItemPrice = orderItem.price]
								[#if serviceCharge!=null]
									[#assign orderItemPrice = orderItemPrice - serviceCharge]
								[/#if]
								[#if productShowRate??]
									[#assign floatValue =orderItemPrice*productShowRate?number]
									[#assign integerPart = floatValue?floor]
									[#assign decimalPart = (floatValue * 100)?round % 100 / 100]
									<span class="shop" id="productPrice" productShowRate0="${productShowRate }">
                                                    <span class="sales-price">
                                                        <i class="iconfontAli icon-ali-jifen"></i><span>${integerPart}</span>${decimalPart?string(".00")}
                                                    </span>
                                           </span>
								[#else]
									[#assign floatValue = orderItemPrice?number]
									[#assign integerPart = floatValue?floor]
									[#assign decimalPart = (floatValue * 100)?round % 100 / 100]
									<span class="sales-price">
                                                    ￥<span>${integerPart}</span>${decimalPart?string(".00")}
                                            </span>
								[/#if]
								<span>x${orderItem.quantity}</span>
							</p>
							[#if orderItem.product.specificationValues]
							<p class="attr">
								[#list orderItem.product.specificationValues as specificationValue]
								<span>
			 					  ${specificationValue.specification.name}：${specificationValue.name}
								</span> [/#list]
							</p>
							[/#if] [#if orderItem.isLowStock]
							<p class="outofservice">无货</p>
							[/#if]
						</div>
					</li>
					[/#list]
				</ul>
			</section>

 			<section class="form_input_item card-rd">

 				<form id="orderForm" action="createVirtual.jhtml" method="post">
					<input type="hidden" name="idcardNoOrder" id="idcardNoOrder" value="" /> [#list cartItems as cartItem]
					<input type="hidden" name="cartItemids" value="${cartItem.id}" /> [/#list]
					<input type="hidden" name="thirdCoinAmount" value="${thirdCoinAmount}" />
					<input type="hidden" name="ids" value="${coinIds}" />
					<input type="hidden" name="whiteBarIds" value="${whiteBarIds}" />
					<input type="hidden" name="cartToken" value="${cartToken}" />
					<input type="hidden" name="receiverId" [#if receiver??] value="${receiver.id}" [/#if]/>
					<input type="hidden" name="paymentMethodId" maxlength="200" value="1" />
					<!--支付方式-->
					<input type="hidden" name="shippingMethodId" maxlength="200" value="1" />
					<!--支付方式-->
					<input type="hidden" name="couponCodeId" value="${couponCodeId}" />
					<input type="hidden" name="groupId" value="${groupId}" id="groupId" />
					<input type="hidden" id="productId" name="productId" value="${productId}" />
					<input type="hidden" id="quantity" name="quantity" value="${quantity}" />
					<input type="hidden" id="orderJson" name="orderJson" value="${basePayInfo}" />
                    <input type="hidden" id="bizType" name="bizType" value="${bizType}" />
				</form>	
			</section>
			<section class="amount_section card-rd favourable_section">
						<p class="item flexbox align-items-c justify-content-space-between">
							<label>数量</label>
							<span>共${order.quantity}件</span>
						</p>
						<p class="item flexbox align-items-c justify-content-space-between">
							<label>商品金额</label>
							<span>
								[#if productShowRate??]
									${coinConvert(order.price,productShowRate,true, false)}
								[#else ]
									${currency(order.price, true, false)}
								[/#if]
							</span>
						</p>
						<p class="item flexbox align-items-c justify-content-space-between">
							<label>服务费</label>
							<span>
								[#if productShowRate??]
									${coinConvert(serviceFee,productShowRate, true, false)}
								[#else ]
									${currency(serviceFee, true, false)}
								[/#if]
							</span>
						</p>
						<div class="lines_Section_all">
							<div onclick="selectCoupon()" class="fli_link_line">
								<label>优惠券</label>
								[#if order.couponDiscount > 0]
									<div class="right_text red couponDiscountHtml">
										-[#if productShowRate??]${coinConvert(order.couponDiscount, productShowRate, true) }
										[#else]${currency(order.couponDiscount, true)}[/#if]
									</div>
								[#else]
									<div class="right_text red">
										<span class="like_btn couponCodeSize">${couponCodeSize}张可用</span>
									</div>
								[/#if]
							</div>
						</div>

						[#if member.companyId.sourceFlag == null || member.companyId.sourceFlag==""]
							<div class="lines_Section_all fli_link_line" onclick="selectCoin()">
								[#--<a href="javascript:void(0)"  class="fli_link_line">--]
								<label>积分支付</label>
								<div class="right_text red">
									[#if order.coinAmount > 0]
										-[#if productShowRate??]${coinConvert(order.coinAmount, coinShowRate!1,true)}[#else]${coinConvert(order.coinAmount, coinShowRate!1)}[/#if]
									[#else]
										[#list coins as coin] [#if coin.coinTypeId.isCredit]
											<span class="like_btn">	${coin.coinTypeId.name?substring(0,1)}${coinConvert(coin.balance, coinShowRate!1)}</span> [/#if] [/#list]
										[#if thirdCoin??]
											<span class="like_btn">${thirdCoin.coinName?substring(0,1)}${thirdCoin.amount}</span>
										[/#if]
									[/#if]
								</div>
								[#--</a>--]
							</div>
							[#if order.whiteBarAmount > 0 || whiteBarCoins??&&whiteBarCoins?size>0]
								<div class="lines_Section_all fli_link_line" onclick="selectWhiteBar()" [#if (member.companyId?? && member.companyId.hasWhitebar?? && member.companyId.hasWhitebar)] [#else]style="display:none;" [/#if]>
									[#--<a href="javascript:void(0)" onclick="selectWhiteBar()" class="fli_link_line">--]
									<label>白条支付</label>
									<div class="right_text red">
										[#if order.whiteBarAmount > 0]
											-[#if productShowRate??]${coinConvert(order.whiteBarAmount, coinShowRate!1,true)}[#else]${coinConvert(order.whiteBarAmount, coinShowRate!1)}[/#if]
										[#else]
											[#list whiteBarCoins as coin] [#if coin.coinTypeId.isCredit && coin.enabledFlag && coin.isFreeze]
												<span class="like_btn">	${coin.coinTypeId.name?substring(0,1)}${coinConvert(coin.balance, coinShowRate!1)}</span> [/#if] [/#list]
										[/#if]
									</div>
									[#--</a>--]
								</div>
							[/#if]
						[/#if]
						<p class="item flexbox align-items-c justify-content-space-between">
							<label>活动优惠</label>
							<span>-[#if productShowRate??]${coinConvert(order.promotionDiscount,productShowRate, true, false)}[#else ]${currency(order.promotionDiscount, true, false)}[/#if]</span>
						</p>

					</section>

					<div class="bottom info-footer flexbox align-items-c justify-content-space-between">
						<div class="total">
							[#if !couponMode][#--非兑换模式--]
								[#if productShowRate??]
									[#assign floatValue =coinConvert(order.amountPayable,productShowRate)?number]
								[#else]
									[#assign floatValue = order.amountPayable?number]
								[/#if]
								<input type="hidden" id="_amountPayable" value="${order.amountPayable }"/>
							[#else ]
								[#assign floatValue = 0?number]
							[/#if]
							[#assign integerPart = floatValue?floor]
							[#assign decimalPart = (floatValue * 100)?round % 100 / 100]
							<span class="money-total" id="money-total">
							[#if productShowRate??]
								<i class="iconfontAli icon-ali-jifen"></i>
							[#else]
								￥
							[/#if]
							<span>${integerPart}</span>${decimalPart?string(".00")}</span>
									<p>实付款</p>
						</div>
						<input type="button" name="" id="submit" value="[#if couponMode]立即兑换[#else]立即付款[/#if]" class="btn_submit_long" />
					</div>
			
			</section>
		</div>

		[#include "./wechat/include/set_payPasswords.ftl" /]

	</body>

</html>