<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>确认订单</title>
		<meta name="apple-mobile-web-app-capable" content="yes" />
		<meta name="apple-mobile-web-app-status-bar-style" content="black" />
		<meta http-equiv="Expires" content="0" />
		<meta http-equiv="Cache-Control" content="no-cache" />
		<meta http-equiv="Pragma" content="no-cache" />
    	<meta name="format-detection" content="telephone=no, email=no" />
		<script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/plusWap.css" />
		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/card.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/jquery.validate.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
		
		<script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js"></script>
		<script type="text/javascript">
			var $orderRefreshForm;
			var $memo;
			var $memoMsg;
			
			function videoTest(obj){
				 var mobileValue = $("#mobile").val();
				 $("#midMobile").val(mobileValue);
			}

			$().ready(function() {
				$orderRefreshForm = $("#orderRefreshForm");
				$memo = $("#memo");
				$memoMsg = $("#memoMsg");
				var $submit = $("#submit");
				var $orderForm = $("#orderForm");
				var isOverseas = "${isOverseas}"; //海外商品标识
				var $receiverIdCard = $("#receiverIdCard");
				var $idcardNoOrder = $("#idcardNoOrder");
				var $saveIdcardNo = $("#saveIdcardNo");
				var $receiverId = $("#receiverId");

				function submitOrder() {
					var url = "createVirtual.jhtml";
					$.ajax({
						url: url,
						type: "POST",
						data: $orderForm.serialize(),
						dataType: "json",
						cache: false,
						beforeSend: function() {
							$submit.prop("disabled", true);
							$submit.val("提交中");
						},
						success: function(message) {
							if(message.type == "success") {
								//location.href = "payment.jhtml?sn=" + message.content + "&type=0";
								location.href = "${base}/paysRedirect/payment.jhtml?sn=" + message.content + "&type=0";
							}
							if(message.type == "warn") {
								$submit.val("立即付款")
								layer.open({
									content: message.content,
									skin: 'msg',
									time: 2 //2秒后自动关闭
								});
							} else if(message.type == "error") {
								$submit.val("立即付款");
								layer.open({
									content: "下单失败",
									skin: 'msg',
									time: 2 //2秒后自动关闭
								});

								$.ajax({
									url: "${base}/member/order/getChange.jhtml",
									type: "POST",
									data: {
										content: message.content
									},
									success: function(data) {
										var trHtml = '<div class="order_goods_has_changed"> <div class="header">以下商品有变动，暂时无法购买，请稍后再试！</div><div class="list">';
										[@compress single_line = true]
										$.each(data, function(i, product) {
											trHtml += '<div class="item"> <img src="${setting.siteUrlImage}/' + product.image + '"/><div class="title">' + product.name + '</div> </div>';
										});
										trHtml += '</div> </div>';
										[/@compress]
										layer.open({
											style: "width:94%;",
											title: false,
											btn: ["返回购物车"],
											content: trHtml,
											yes: function(index, layero) {
												location.href = "${base}/cart/list.jhtml";
											}
										});
									}
								});

							}
						},
						complete: function() {
							$submit.prop("disabled", false);
						}
					});
				}

				//输入密码事件
				$("#password").keyup(function() {
					var l_pwd = this.value.length;
					if(l_pwd >= 1 && l_pwd <= 6 && event.keyCode != 8) {
						var _input = document.getElementById("number" + l_pwd);
						_input.value = this.value.charAt(l_pwd - 1);
						if(l_pwd == 6) { //输入完成动作
							var _pwd = this.value;
							$("#paymentPop").hide();
							this.blur();
							var device = openDevice();
							if(device == "android") {
								vpshop_android.callHideSoftInput();
							}

							var $checkedIds = $("#rechargeRecords input[name='coinIds']:enabled:checked");
							$.ajax({
								url: "check_paypassword.jhtml",
								type: "POST",
								data: {
									coinIds: $("#coinIds").val(),
									paypassword: _pwd,
									whiteBarIds: $("#whiteBarIds").val()
								},
								dataType: "json",
								cache: false,
								success: function(message) {
									$(".password_section input").val("");
									if(message.type == "success") {
										submitOrder();
									} else {
										layer.open({
											content: message.content,
											skin: 'msg',
											time: 2 //2秒后自动关闭
										});
									}
								}
							});

						}
					} else if(event.keyCode == 8) { //退格键删除
						var _input = document.getElementById("number" + (l_pwd + 1));
						_input.value = '';
					}
				})

				//点击输入框获取焦点
				$(".password_section .input_box").click(function() {
					$("#password").focus();
				})

				$(".popbg .icon_close").click(function() {
					$(this).parents(".popbg").hide();
					$("#payPassword").val("");
					$("#rePayPassword").val("");
					validator_pwd.resetForm();
					$(".password_section input").val("");
				})

				$(".btn_concel").click(function() {
					$("#paymentset").hide();
					$("#payPassword").val("");
					$("#rePayPassword").val("");
					validator_pwd.resetForm();
				});

				var $payPasswordSetBtn = $("#payPasswordSetBtn");

				// 表单验证
				var validator_pwd = $("#inputForm").validate({
					rules: {
						payPassword: {
							required: true,
							digits: true,
							minlength: 6,
							maxlength: 6
						},
						rePayPassword: {
							required: true,
							digits: true,
							minlength: 6,
							maxlength: 6,
							equalTo: "#payPassword"
						}
					},
					messages: {
						payPassword: {
							pattern: "格式错误,请输入六位数字密码",
							digits: "格式错误,请输入六位数字密码"
						},
						rePayPassword: {
							pattern: "格式错误,请输入六位数字密码",
							digits: "格式错误,请输入六位数字密码"
						}
					},
					submitHandler: function(form) {
						$.ajax({
							url: '${base}/member/payPasswordSet.jhtml',
							type: 'POST',
							data: $("#inputForm").serialize(),
							cache: false,
							async: false,
							beforeSend: function() {
								$payPasswordSetBtn.prop("disabled", true);
							},
							success: function(flag) {
								if(flag == 1) {
									layer.open({
										content: '设置成功',
										skin: 'msg',
										time: 2 //2秒后自动关闭
									});
									$("#paymentset").hide();
									$("#payPassword").val("");
									$("#rePayPassword").val("");
									validator_pwd.resetForm();
									$("#setPwd").hide();
									$("#fogetPwd").show();

								} else if(flag == 2) {
									layer.open({
										content: '两次输入的支付密码不一致',
										skin: 'msg',
										time: 2 //2秒后自动关闭
									});
									$payPasswordSetBtn.prop("disabled", false);
								} else {
									layer.open({
										content: '设置失败',
										skin: 'msg',
										time: 2 //2秒后自动关闭
									});
									$payPasswordSetBtn.prop("disabled", false);
								}
							}
						});
					}

				});

				//保存身份证号
				$saveIdcardNo.click(function() {
					var receiverId = $receiverId.val();
					var idcardNo = $receiverIdCard.val();
					if(!idCardNoUtil.checkIdCardNo(idcardNo)) {
						layer.open({
							content: '请正确填写身份证号！',
							skin: 'msg',
							time: 2 //2秒后自动关闭
						});
						return false;
					}
					$.ajax({
						url: "saveReviceIdcard.jhtml",
						type: "POST",
						data: {
							receiverId: receiverId,
							idcardNo: idcardNo
						},
						dataType: "json",
						cache: false,
						success: function(msg) {
							if(msg == true) {
								layer.open({
									content: '身份证号码保存成功!',
									skin: 'msg',
									time: 2 //2秒后自动关闭
								});
							} else {
								layer.open({
									content: '身份证号码保存失败',
									skin: 'msg',
									time: 2 //2秒后自动关闭
								});
							}
						}
					});

				});

				// 订单提交
				$submit.click(function() {
					// 如果是视频卡，则要判断手机号
					 if($("#type").val() ==3){
						 var mobile = /^(((13[0-9]{1})|14[57]{1}|(15[0-9]{1})|17[378]{1}|(18[0-9]{1}))+\d{8})$/;
						 var mobileValue = $("#mobile").val();
						 if(mobileValue.length !=11){
                            layer.open({
									content: '请输入正确的手机号',
									skin: 'msg',
									time: 2 //2秒后自动关闭
								});
								return false;
						 }
					 }

	
					if($("#_amountPayable").val() > 0) {
						layer.open({
							content: '积分余额不足，本商品仅支持积分和白条支付！',
							skin: 'msg',
							time: 2 //2秒后自动关闭
						});
						return false;
					}

					var cashPayFlag="${member.companyId.cashPayFlag}";
				    
				    if(cashPayFlag!=null && cashPayFlag!="" && cashPayFlag==0){ //不允许使用现金支付
						   if($("#_amountPayable").val() > 0) {
				             layer.open({
				                 content: '积分余额不足！',
				                 skin: 'msg',
				                 time: 2 //2秒后自动关闭
				             });
				             return false;
				         }
					   }
					
					if(isOverseas == "true") {
						var receiverIdCard = $receiverIdCard.val();

						if(receiverIdCard == "" || receiverIdCard == null) {
							layer.open({
								content: '存在海外商品请正确填写身份证号！',
								skin: 'msg',
								time: 2 //2秒后自动关闭
							});
							$("html,body").animate({
								scrollTop: 0
							}, 300);
							return false;
						} else {
							if(!idCardNoUtil.checkIdCardNo(receiverIdCard)) {
								layer.open({
									content: '存在海外商品请正确填写身份证号！',
									skin: 'msg',
									time: 2 //2秒后自动关闭
								});
								$("html,body").animate({
									scrollTop: 0
								}, 300);
								return false;
							}
							$idcardNoOrder.val(receiverIdCard);
						}
					}

					var rechargeRecordAmount = "${rechargeRecordAmount}"; //勾选了积分支付
					var whiteBarYAmount = "${whiteBarYAmount}"; //勾选了白条支付
					[#if member.companyId.isFreePay ]
						submitOrder();
					[#else]
					if(rechargeRecordAmount > 0) { //可用积分大于0，弹出密码框
						$("#paymentPop").show().find("#password").focus();
					} else if(whiteBarYAmount > 0) {
						$("#paymentPop").show().find("#password").focus();
					} else {
						submitOrder();
					}
					[/#if]
				});
			});

			function submitOrder() {
				var url = "createVirtual.jhtml";
				$.ajax({
					url: url,
					type: "POST",
					data: $orderForm.serialize(),
					dataType: "json",
					cache: false,
					beforeSend: function() {
						$submit.prop("disabled", true);
						$submit.val("提交中");
					},
					success: function(message) {
						if(message.type == "success") {
							location.href = "paymentVirtual.jhtml?sn=" + message.content + "&type=0";
						}
						if(message.type == "warn") {
							$submit.val("立即付款")
							layer.open({
								content: message.content,
								skin: 'msg',
								time: 2 //2秒后自动关闭
							});

						} else if(message.type == "error") {
							$submit.val("立即付款")
							layer.open({
								content: "下单失败",
								skin: 'msg',
								time: 2 //2秒后自动关闭
							});

							$.ajax({
								url: "${base}/member/order/getChange.jhtml",
								type: "POST",
								data: {
									content: message.content
								},
								success: function(data) {
									var trHtml = '<div class="order_goods_has_changed"> <div class="header">以下商品有变动，暂时无法购买，请稍后再试！</div><div class="list">';
									[@compress single_line = true]
									$.each(data, function(i, product) {
										trHtml += '<div class="item"> <img src="${setting.siteUrlImage}/' + product.image + '"/><div class="title">' + product.name + '</div> </div>';
									});
									trHtml += '</div> </div>';
									[/@compress]
									layer.open({
										style: "width:94%;",
										title: false,
										btn: ["返回购物车"],
										content: trHtml,
										yes: function(index, layer) {
											location.href = "${base}/cart/list.jhtml";
										}
									});
								}
							});
						}
					},
					complete: function() {
						$submit.prop("disabled", false);
					}
				});
			}

			function payPassword() {
				$("#paymentPop").hide()
				$("#paymentset").show();
			}

			//选择收货地址页面跳转
			function selectAddress() {
				$memoMsg.val($memo.val());
				$("#orderRefreshForm").attr("action", "selectAddress.jhtml");
				$("#orderRefreshForm").submit();
			}

			//选择积分页面跳转
			function selectCoin() {
				var url = "groupSelectCoin.jhtml";
				$("#orderRefreshForm").attr("action", url);
				$("#orderRefreshForm").submit();
			}

			//选择积分页面跳转
			function selectWhiteBar() {
				var url = "groupSelectWhiteBar.jhtml";
				$("#orderRefreshForm").attr("action", url);
				$("#orderRefreshForm").submit();
			}
			
			// 验证手机号
			jQuery.validator.addMethod("isTel", function(value,element) {   
			    var length = value.length;   
			    var mobile = /^(((13[0-9]{1})|14[57]{1}|(15[0-9]{1})|17[378]{1}|(18[0-9]{1}))+\d{8})$/;
			    if(this.optional(element) || (length==11 && mobile.test(value))){
			    	$("#midMobile").val(value);
			    }	    
			    return this.optional(element) || (length==11 && mobile.test(value));   
			   }, "请正确填写您的联系方式"); 
			
			$("#orderForm").validate({  
			    rules : {  
			        mobile : {  
			        	isTel:"#mobile",
			            required : true,  
			            minlength : 11 
						
			        }
			    },  
			    messages : {  
			    	mobile : {  
			            required : "请输入手机号",  
			            minlength : "确认手机不能小于11个字符",  
			            isMobile : "请正确填写您的手机号码"  
			        }
			    },  
				errorPlacement: function(error, element) {
					error.appendTo(element.parent());
				}				
			}); 
			

			
			
		</script>
	</head>

	<body id="myOrderPage" class="pay-info-page confirm-order-page">
		<div class="myOrderPage common-order-info-page simple">
			<div class="public_top_header">
                <span class="return_back" onclick="javascript:history.back();"></span>
				确认订单
				[#include "./wechat/include/head_nav.ftl" /]
			</div>

			<form id="orderRefreshForm" action="" method="post">
				[#list cartItems as cartItem]
				<input type="hidden" name="ids" value="${cartItem.id}" /> [/#list]
				<input type="hidden" name="coinIds" value="${coinIds}" id="coinIds" />
				<input type="hidden" name="whiteBarIds" value="${whiteBarIds}" id="whiteBarIds" />
				<input type="hidden" id="receiverId" name="receiverId" [#if receiver??] value="${receiver.id}" [/#if] />
				<input type="hidden" id="productId" name="productId" value="${productId}" />
				<input type="hidden" name="couponCodeId" value="${couponCodeId}" />
				<input type="hidden" name="memo" id="memoMsg" value="${order.memo!}" />
				<input type="hidden" name="quantity" value="${quantity}" />
				<input type="hidden" name="pId" value="${productId}" />
				<input type="hidden" name="groupId" value="${groupId}" />
				<input type="hidden" name="toatlAmount" value="${order.toatlAmount}" />
				<input type="hidden" name="coinAmount" value="${order.coinAmount}" />
				<input type="hidden" name="amountPaid" value="${order.amountPaid}" />
				<input type="hidden" id="orderJson" name="orderJson" value="${orderJson}" />
				<input type="hidden" id="midMobile" name="mobile" value="${mobile}" />
				<input type="hidden" name="type" value="${type}" />
				<input type="hidden" id="gasType" name="gasType" value="${gasType}" />
				<input type="hidden" id="gasCard" name="gasCard" value="${gasCard}" />
				<input type="hidden" id="userName" name="userName" value="${userName}" />
				<input type="hidden" id="phone" name="phone" value="${phone}" />
				
				<input type="hidden" name="coinTypeIdsEcho" value="${coinTypeIdsEcho}" />
			    <input type="hidden" name="coinTypeIds" value="${coinTypeIds}" />
				<input type="hidden" name="thirdCoinAmount" value="${thirdCoinAmount}" />
				
				<!-- 下面参数为了计算金额 -->
				<input type="hidden" id="price"  name="price" value="${order.price}"/>
				<input type="hidden" id="freight"  name="freight" value="${order.freight}"/>
				<input type="hidden" id="couponDiscount"  name="couponDiscount" value="${order.couponDiscount}"/>
				<input type="hidden" id="promotionDiscount"  name="promotionDiscount" value="${order.promotionDiscount}"/>
				
			</form>

			<div style="display:none">
				<section class="base_info">
					<a href="javascript:void(0)" onclick="selectAddress()" class="fli_link_line">
						[#if receiver??]
						<p>
							收货人：${receiver.consignee}
							<span class="tel">${receiver.phoneDec}</span>
						</p>
						<p class="addr">
							${receiver.areaName}${receiver.address}
						</p>
						[#else] 马上去添加收货地址 [/#if]
					</a>
					<!--<a href="#">! 马上去添加收货地址</a>-->
					<div class="identify" [#if !isOverseas]style="display: none;" [/#if]>
						<input type="text" name="idcardNo" id="receiverIdCard" [#if receiver??] value="${receiver.idcardNoDec}" [/#if] class="input" placeholder="因海关清关需要，请填写收货人身份证号码。" />
						<input type="button" name="" id="saveIdcardNo" value="保存" class="btn_pill btn_pill_theme" />
					</div>
				</section>
			</div>
			<section class="contentBox">
				<ul class="order_list_section">
					[#list order.orderItems as orderItem]
					<li>
						<div class="img">
							<img src="${setting.siteUrlImage}/[#if orderItem.product.image??]${orderItem.product.image}[#elseif com_person_product_default_img != null]${com_person_product_default_img}[#else]${setting.defaultThumbnailProductImage}[/#if]" />
						</div>
						<div class="info">
							<p class="title">
								${orderItem.product.name}
							</p>
							<p  class="price flexbox align-items-c justify-content-space-between">
							    [#assign floatValue = orderItem.price?number]
								[#assign integerPart = floatValue?floor]
								[#assign decimalPart = (floatValue * 100)?round % 100 / 100]
								<span class="sales-price">
										￥<span>${integerPart}</span>${decimalPart?string(".00")}
								</span>
								<span>x${orderItem.quantity}</span>
							</p>
							[#if orderItem.product.specificationValues]
							<p class="attr">
								[#list orderItem.product.specificationValues as specificationValue]
								<span>	
			 					  ${specificationValue.specification.name}：${specificationValue.name}
								</span> [/#list]
							</p>
							[/#if] [#if orderItem.isLowStock]
							<p class="outofservice">无货</p>
							[/#if]
						</div>

						[#--<div class="save">--]
							[#--<span class="icon_save"></span>--]
							[#--<span>${currency(orderItem.discountAmount, true)}</span>--]
						[#--</div>--]
					</li>
					[/#list]
				</ul>
			</section>
			
 			<section class="form_input_item tel-card">
				<!--<label>手机号码</label>
				<div class="inputs">
 					<input type="tel" name="mobile" id="" value="" placeholder="请输入手机号码" class="input_text"/>
 				</div>-->
 				<form id="orderForm" action="createVirtual.jhtml" method="post">
					<input type="hidden" name="idcardNoOrder" id="idcardNoOrder" value="" /> [#list cartItems as cartItem]
					<input type="hidden" name="cartItemids" value="${cartItem.id}" /> [/#list]
					<input type="hidden" name="thirdCoinAmount" value="${thirdCoinAmount}" />
					<input type="hidden" name="ids" value="${coinIds}" />
					<input type="hidden" name="whiteBarIds" value="${whiteBarIds}" />
					<input type="hidden" name="cartToken" value="${cartToken}" />
					<input type="hidden" name="receiverId" [#if receiver??] value="${receiver.id}" [/#if]/>
					<input type="hidden" name="paymentMethodId" maxlength="200" value="1" />
					<!--支付方式-->
					<input type="hidden" name="shippingMethodId" maxlength="200" value="1" />
					<!--支付方式-->
					<input type="hidden" name="couponCodeId" value="${couponCodeId}" />
					<input type="hidden" name="groupId" value="${groupId}" id="groupId" />
					<input type="hidden" id="productId" name="productId" value="${productId}" />
					<input type="hidden" id="quantity" name="quantity" value="${quantity}" />
					<input type="hidden" id="orderJson" name="orderJson" value="${orderJson}" />
					<input type="hidden" id="type" name="type" value="${type}" />
					[#if type == 1 || type == 2]
						<label>手机号码</label>
						<div class="inputs">
 							<input type="tel" name="mobile" id="mobile" value="${mobile}"  readonly="readonly" class="input_text"/>
 						</div>
					[#elseif type == 99]
						<label>手机号码</label>
						<div class="inputs">
							<input type="tel" name="phone" id="phone" value="${phone}"  readonly="readonly" class="input_text"/>
						</div>
					[#elseif type == 3]
						<label>手机号码</label>
						<div class="inputs">
 							<input type="tel" name="mobile" id="mobile" value="${mobile}" placeholder="请输入手机号码" onkeyup="videoTest(this)" class="input_text"/>
 						</div>
					[#else]
					[#if gasType == "cnpc"]
					<section class="message_section">
						<header>
							<span>油卡充值信息</span>
						</header>
						
						<input type="hidden" id="gasType" name="gasType" value="${gasType}" />
						<input type="hidden" id="gasCard" name="gasCard" value="${gasCard}" />
						<input type="hidden" id="userName" name="userName" value="${userName}" />
						<input type="hidden" id="phone" name="phone" value="${phone}" />
						<div class="textarea" >
							[#if gasType == "cnpc"]中石油直充[#else]中石化直充[/#if] <br/>
							油卡号：${gasCard } <br/>
							联系人：${userName} <br/>
							联系电话：${phone}<br/>
						</div>
 					</section>	
 						
					[/#if]
					[/#if]
					<!-- [if mobile??]
					充值手机号：{mobile}
					[else]
					[if gasCardCnpcVo.gasType == "cnpc"]中石油[else]中石化[/if] {gasCardCnpcVo.gasCard } 
					{gasCardCnpcVo.userName} {gasCardCnpcVo.phone}
					[/if] -->
				</form>
			</section> 

			[#--<section class="favourable_section">--]
             [#--[#if member.companyId.sourceFlag == null || member.companyId.sourceFlag==""]--]
				[#--<div class="lines_Section_all">--]
					[#--<a href="javascript:void(0)" onclick="selectCoin()" class="fli_link_line">--]
						[#--积分支付 [#list coins as coin] [#if coin.coinTypeId.isCredit]--]
						[#--<span class="like_btn">	${coin.coinTypeId.name?substring(0,1)}${coinConvert(coin.balance, coinShowRate!1)}</span> [/#if] [/#list] [#if order.coinAmount > 0]--]
							[#--[#if thirdCoin??]--]
							[#--<span class="like_btn">${thirdCoin.coinName?substring(0,1)}${thirdCoin.amount}</span>--]
							[#--[/#if]--]
						[#--<div class="right_text red">-${coinConvert(order.coinAmount, coinShowRate!1)}</div>--]
						[#--[#else]--]
						[#--<div class="right_text gray">未使用</div>--]
						[#--[/#if]--]
					[#--</a>--]
				[#--</div>--]

				[#--<div class="lines_Section_all" [#if (member.companyId?? && member.companyId.hasWhitebar?? && member.companyId.hasWhitebar)] [#else]style="display:none;" [/#if]>--]
					[#--<a href="javascript:void(0)" onclick="selectWhiteBar()" class="fli_link_line">--]
						[#--白条支付 [#list whiteBarCoins as coin] [#if coin.coinTypeId.isCredit && coin.enabledFlag && coin.isFreeze]--]
						[#--<span class="like_btn">	${coin.coinTypeId.name?substring(0,1)}${coinConvert(coin.balance, coinShowRate!1)}</span> [/#if] [/#list] [#if order.whiteBarAmount gte 0]--]
						[#--<div class="right_text red">-${coinConvert(order.whiteBarAmount, coinShowRate!1)}</div>--]
						[#--[#else]--]
						[#--<div class="right_text gray">未使用</div>--]
						[#--[/#if]--]
					[#--</a>--]
				[#--</div>--]
			 [#--[/#if]--]
			[#--</section>--]

			<section class="amount_section card-rd favourable_section">
				<p class="item flexbox align-items-c justify-content-space-between">
					<label>数量</label>
					<span>共${order.quantity}件</span>
				</p>
				<p class="item flexbox align-items-c justify-content-space-between">
					<label>商品金额</label>
					<span>${currency(order.price, true, false)}</span>
				</p>
				<p class="item flexbox align-items-c justify-content-space-between">
					<label>运费</label>
					<span>${currency(order.freight, true, false)}</span>
				</p>
				[#--<p>--]
					[#--积分抵扣--]
					[#--<span>-${currency(order.coinAmount, true, false)}</span>--]
				[#--</p>--]
				[#--<p [#if (member.companyId?? && member.companyId.hasWhitebar?? && member.companyId.hasWhitebar)] [#else]style="display:none;" [/#if]>--]
					[#--白条抵扣--]
					[#--<span>-${currency(order.whiteBarAmount, true, false)}</span>--]
				[#--</p>--]
				   [#if member.companyId.sourceFlag == null || member.companyId.sourceFlag==""]
				<div class="lines_Section_all fli_link_line"  onclick="selectCoin()">
                    [#--<a href="javascript:void(0)" class="fli_link_line">--]
					<label>积分支付</label>
                    <div class="right_text red">
						[#if order.coinAmount > 0]
							-${coinConvert(order.coinAmount, coinShowRate!1)}
						[#else]
							[#list coins as coin]
								[#if coin.coinTypeId.isCredit]
								<span class="like_btn">	${coin.coinTypeId.name?substring(0,1)}${coinConvert(coin.balance, coinShowRate!1)}</span> [/#if]
							[/#list]
							[#if thirdCoin??]
								<span class="like_btn">${thirdCoin.coinName?substring(0,1)}${thirdCoin.amount}</span>
							[/#if]
						[/#if]
                    </div>
                    [#--</a>--]
                </div>

				<div class="lines_Section_all fli_link_line" onclick="selectWhiteBar()" [#if (member.companyId?? && member.companyId.hasWhitebar?? && member.companyId.hasWhitebar)] [#else]style="display:none;" [/#if]>
                    [#--<a href="javascript:void(0)" onclick="selectWhiteBar()" class="fli_link_line">--]
						<label> 白条支付</label>
						<div class="right_text red">
							[#if order.whiteBarAmount > 0]
								-${coinConvert(order.whiteBarAmount, coinShowRate!1)}
							[#else]
								[#list whiteBarCoins as coin] [#if coin.coinTypeId.isCredit && coin.enabledFlag && coin.isFreeze]
								<span class="like_btn">	${coin.coinTypeId.name?substring(0,1)}${coinConvert(coin.balance, coinShowRate!1)}</span> [/#if] [/#list]
							[/#if]
						</div>
                    [#--</a>--]
                </div>
				   [/#if]
				<p class="item flexbox align-items-c justify-content-space-between">
					<label>优惠券抵扣</label>
					<span>-${currency(order.couponDiscount, true, false)}</span>
				</p>
				
				<p class="item flexbox align-items-c justify-content-space-between">
					<label>活动优惠</label>
					<span>-${currency(order.promotionDiscount, true, false)}</span>
				</p>
				[#--<p class="total" [#if member.companyId?? && member.companyId.cashPayFlag?? && member.companyId.cashPayFlag == 0] style="display: none;" [/#if]>--]
                    [#--<label>实付款</label>--]
					[#--<span>${currency(order.amountPayable, true, false)}</span>--]
					[#--<input type="hidden" id="_amountPayable" value="${order.amountPayable }" />--]
				[#--</p>--]
			</section>
            <div class="bottom info-footer flexbox align-items-c justify-content-space-between">
                <div class="total">
				<input type="hidden" id="_amountPayable" value="${order.amountPayable }" />
				[#if productShowRate??]
					[#assign floatValue =coinConvert(order.amountPayable,productShowRate)?number]
				[#else]
					[#assign floatValue = order.amountPayable?number]
				[/#if]
                [#assign integerPart = floatValue?floor]
                [#assign decimalPart = (floatValue * 100)?round % 100 / 100]
                    <span class="money-total" id="money-total">
						[#if productShowRate??]
							<i class="iconfontAli icon-ali-jifen"></i>
						[#else]
					  		￥
						[/#if]
					<span>${integerPart}</span>${decimalPart?string(".00")}</span>
                    <p>实付款</p>
                </div>
                <input type="button" name=""  [#if  order??&&order.orderItems??&&order.orderItems?size>0&& order.orderItems.get(0).isLowStock]disabled="disabled"[/#if] id="submit" value="立即付款" class="btn_submit_long" />
            </div>
			[#--<div class="bottom">--]
				[#--<input type="button" name="" id="submit" value="立即付款" class="btn_submit_long" />--]
			[#--</div>--]
		</div>

		<div class="popbg" id="paymentPop">
			<div class="close"></div>

			<div class="pop">
				<header>输入支付密码<span class="icon_close"></span></header>
				<div class="password_section">
					<div class="input_box clearfix" id="inputs_box">
						<input type="number" id="number1" readonly="readonly" />
						<input type="number" id="number2" readonly="readonly" />
						<input type="number" id="number3" readonly="readonly" />
						<input type="number" id="number4" readonly="readonly" />
						<input type="number" id="number5" readonly="readonly" />
						<input type="number" id="number6" readonly="readonly" />
					</div>
					<input type="tel" name="" id="password" value="" maxlength="6" class="pwd" /> [#if member.payPassword]
					<a href="javascript:void(0)" onclick="payPassword()" class="forgotton" id="setPwd" style="display:none">设置密码</a>
					<a href="${base}/payPassword/find.jhtml?type=2" class="forgotton" id="fogetPwd">忘记密码？</a>
					[#else]
					<a href="javascript:void(0)" onclick="payPassword()" class="forgotton" id="setPwd">设置密码</a>
					<a href="${base}/payPassword/find.jhtml?type=2" class="forgotton" id="fogetPwd" style="display:none">忘记密码？</a>
					[/#if]

				</div>
				<!-- <div class="footer">
			 		<input type="button" name="" id="" value="确定" class="btn_submit"/>
			 		<input type="button" name="" id="" value="取消" class="btn_concel"/>
			 	</div> -->
			</div>
		</div>
		<div class="popbg" id="paymentset">
			<div class="close"></div>
			<div class="pop">
				<form id="inputForm" action="payPasswordSet.jhtml" method="post">
					<header>设置支付密码<span class="icon_close"></span></header>
					<div class="fli_pub_pop_form">
						<div class="item">
							<input type="tel" name="payPassword" id="payPassword" class="input pwd" placeholder="请输入新密码"/>
						</div>
	
						<div class="item">
							<input type="tel" name="rePayPassword" id="rePayPassword" class="input pwd"  placeholder="请确认新密码"/>
						</div>
					</div>
					<div class="footer">
						<input type="button" name="" id="" value="取消" class="btn_concel" />
						<input type="submit" name="" id="payPasswordSetBtn" value="确定" class="btn_submit" />
					</div>
				</form>
			</div>
		</div>

		<script>
			var maxstrlen = 30;
			/* checkWord($("#memo")[0]);
			//检查文本
			function checkWord(c) {
				len = maxstrlen;
				var str = c.value;
				myLen = getStrleng(str);
				var wck = document.getElementById("lengthLeft");
				if(myLen > len * 2) {
					c.value = str.substring(0, i - 1);
				}
				wck.innerHTML = Math.floor((len * 2 - myLen) / 2) > 0 ? Math.floor((len * 2 - myLen) / 2) : 0;
			} */
			//计算长度
			function getStrleng(str) {
				myLen = 0;
				i = 0;
				for(;
					(i < str.length) && (myLen <= maxstrlen * 2); i++) {
					if(str.charCodeAt(i) > 0 && str.charCodeAt(i) < 128)
						myLen++;
					else
						myLen += 2;
				}
				return myLen;
			}
		</script>

	</body>

</html>