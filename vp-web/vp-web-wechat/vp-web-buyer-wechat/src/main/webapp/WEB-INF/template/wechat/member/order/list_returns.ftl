<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>售后单</title>
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="black">
        <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js" ></script>
        <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/plusWap.css" />
		<link rel="stylesheet" href="${base}/resources/wechat/plugins/LCalendar/css/LCalendar.css">

		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>

		<script type="text/javascript" src="${base}/resources/wechat/js/dropload.min.js" ></script>
		<script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js"></script>
        <script src="${base}/resources/wechat/plugins/LCalendar/js/LCalendar.min.js"></script>
		<!-- 列表页面缓存数据 -->
	    <script type="text/javascript" src="${base}/resources/wechat/js/listPageSessionStorage.js" ></script>
	</head>
	<body class="has_fixed_footer order_list_returns_page">
	[#assign current = "my" /]

	[#if pageSource??&&pageSource=='weixincoupon']
	[#else ]
		[#include "./wechat/include/footer.ftl" /]
	[/#if]

		<div class="orderPage"> 
		 	<div class="public_top_header">
                <span class="return_back" onclick="goBack()"></span>
				售后单
			</div>
		 	
		 	<div class="order_items_section">
		 		<ul class="returnsUl"></ul>
		 	</div>
		</div>
		
		<input type="hidden" id="returnsId">
		
		<script type='text/template' id="pop_yanxuan_returns">
			<div class="fli_pub_pop_form">
					<div class="item">
						<select name="deliveryCorp" id="deliveryCorp" class="input select_chose text_left">
							<option value="">请选择物流公司</option>
							<option value="EMS">EMS</option>
							<option value="顺丰">顺丰</option>
							<option value="韵达">韵达</option>
							<option value="中通">中通</option>
							<option value="百世汇通">百世汇通</option>
							<option value="圆通">圆通</option>
							<option value="申通">申通</option>
							<option value="天天">天天</option>
							<option value="全峰">全峰</option>
							<option value="宅急送">宅急送</option>
							<option value="国通">国通</option>
							<option value="德邦">德邦</option>
							<option value="速尔">速尔</option>
							<option value="能达">能达</option>
							<option value="优速">优速</option>
							<option value="快捷">快捷</option>
							<option value="新邦物流">新邦物流</option>
							<option value="晟邦物流">晟邦物流</option>
							<option value="天地华宇">天地华宇</option>
							<option value="京东物流">京东物流</option>
							<option value="京东快递">京东快递</option>
							<option value="中通国际">中通国际</option>
							<option value="其他">其他</option>
						</select>
					</div>
					<div class="item">
						<input type="text" name="trackingNo" id="trackingNo" class="input text_left" placeholder="请输入快递单号" />
					</div>
				<div class="item">
					<input type="text" class="input text_left" name="deliverDate" id="deliverDate" value=""  readonly="readonly" placeholder="请选择发货时间" />
				</div>
				<div class="item">
					<input type="tel" name="freightMoney" id="freightMoney" class="input text_left" placeholder="请输入运费"/>
				</div>
				</div>
		</script>
		<script type='text/template' id="pop_eascs_returns">
			<div class="fli_pub_pop_form">
				<div class="item">
					<select dir="ltr" name="deliveryCorp" id="deliveryCorp" class="input select_chose text_left" >
						<option value="">请选择物流公司</option>
						<option value="EMS">EMS</option>
						<option value="顺丰速运">顺丰速运</option>
						<option value="圆通快递">圆通快递</option>
						<option value="中通快递">中通快递</option>
						<option value="申通快递">申通快递</option>
						<option value="宅急送">宅急送</option>
						<option value="天天快递">天天快递</option>
						<option value="韵达快递">韵达快递</option>
						<option value="百世快递">百世快递</option>
						<option value="其他">其他</option>
					</select>
				</div>
				<div class="item">
					<input type="text" name="trackingNo" id="trackingNo" class="input text_left" placeholder="请输入快递单号" />
				</div>
				<div class="item">
					<input type="text"  readonly class="input text_left"   id="returnsAddress" placeholder="寄回地址"/>
				</div>
				<div class="item">
                    <input type="text" name="deliverDate" id="deliverDate"
                           value="" placeholder="请选择发货时间" readonly="readonly"  class="input text_left" unselectable="on"  />
				</div>
				<div class="item">
					<input type="tel" name="freightMoney" id="freightMoney" class="input text_left" placeholder="请输入运费"/>
				</div>
			</div>
		</script>
		<script type='text/template' id="pop_returns">
			<div class="fli_pub_pop_form">
				<div class="item">
                    <select name="deliveryCorp" id="deliveryCorp" class="input select_chose text_left">
                        <option value="">请选择物流公司</option>
                        <option value="EMS">EMS</option>
                        <option value="顺丰速运">顺丰速运</option>
                        <option value="圆通快递">圆通快递</option>
                        <option value="中通快递">中通快递</option>
                        <option value="申通快递">申通快递</option>
                        <option value="宅急送">宅急送</option>
                        <option value="天天快递">天天快递</option>
                        <option value="韵达快递">韵达快递</option>
                        <option value="百世快递">百世快递</option>
                        <option value="百世快递">其他</option>
                    </select>

				</div>
				<div class="item">
					<input type="text" name="trackingNo" id="trackingNo" class="input text_left" placeholder="请输入快递单号"/>
				</div>
				<div class="item">
					<input type="text" name="deliverDate" id="deliverDate"
						   value="" placeholder="请选择发货时间" readonly="readonly"  class="input text_left" unselectable="on"  />
				</div>
				<div class="item">
					<input type="tel" name="freightMoney" id="freightMoney" class="input text_left" placeholder="请输入运费"/>
				</div>
			</div>
		</script>
		<script type='text/template' id="pop_returns1">
			<div class="fli_pub_pop_form">
					<div class="item">
                        <select name="deliveryCorp" id="deliveryCorp" class="input select_chose text_left">
                            <option value="">请选择物流公司</option>
                            <option value="EMS">EMS</option>
                            <option value="顺丰">顺丰</option>
                            <option value="韵达">韵达</option>
                            <option value="中通">中通</option>
                            <option value="百世汇通">百世汇通</option>
                            <option value="圆通">圆通</option>
                            <option value="申通">申通</option>
                            <option value="天天">天天</option>
                            <option value="全峰">全峰</option>
                            <option value="宅急送">宅急送</option>
                            <option value="国通">国通</option>
                            <option value="德邦">德邦</option>
                            <option value="速尔">速尔</option>
                            <option value="能达">能达</option>
                            <option value="优速">优速</option>
                            <option value="快捷">快捷</option>
                            <option value="新邦物流">新邦物流</option>
                            <option value="晟邦物流">晟邦物流</option>
                            <option value="天地华宇">天地华宇</option>
                            <option value="京东物流">京东物流</option>
                            <option value="京东快递">京东快递</option>
                            <option value="中通国际">中通国际</option>
                            <option value="其他">其他</option>
                        </select>
					</div>
					<div class="item">
						<input type="text" name="trackingNo" id="trackingNo" class="input text_left" placeholder="请输入快递单号"/>
					</div>
				<div class="item">
					<input type="text" value="" class="input text_left" id="deliverDate" readonly placeholder="请选择发货时间">
				</div>
					<div class="item">
						<input type="tel" name="freightMoney" id="freightMoney" class="input text_left" placeholder="请输入运费"/>
					</div>
				</div>
		</script>

		<script type='text/template' id="pop_tmall_returns">
			<div class="fli_pub_pop_form">
				<div class="item">
					<select name="deliveryCorp" id="deliveryCorp" class="input select_chose text_left">
						<option value="">请选择物流公司</option>
						[#list tMallExpress as tMallExpressCompany]
							<option value="${tMallExpressCompany.expressCode}">${tMallExpressCompany.expressName}</option>
						[/#list]
					</select>
	
				</div>
				<div class="item">
					<input type="text" name="trackingNo" id="trackingNo" class="input text_left" placeholder="请输入快递单号"/>
				</div>
				<div class="item">
					<input type="text" name="deliverDate" id="deliverDate"
						   value="" placeholder="请选择发货时间" readonly="readonly"  class="input text_left" unselectable="on"  />
				</div>
				<div class="item">
					<input type="tel" name="freightMoney" id="freightMoney" class="input text_left" placeholder="请输入运费"/>
				</div>
			</div>
		</script>
		
			<script type="text/javascript">
			$(function(){
			    let sn = getQueryVariable("sn");
			    let loadUrl = '${base}/member/order/returnsListPage.jhtml';
				[#if pageSource??&&pageSource=='weixincoupon']
					loadUrl = '${base}/member/order/returnsListPage.jhtml?pageSource=weixincoupon';
				[/#if]
				//加载数据
				loadData(loadUrl, {type: "${type}",sn:sn}, 'orderPage', 'returnsUl', 'POST', 'totalPages', null, function(){
					if(tag_scroll_top){
						$(window).scrollTop(tag_scroll_top);
					}
					$("#totalPages").remove();
				});
			});
			
			//设置发货日期deliverDate
			function setDeliverDate(){
                    var calendar = new LCalendar();
					calendar.init({
						'trigger': '#deliverDate', //标签id
						'type': 'date', //date 调出日期选择 datetime 调出日期时间选择 time 调出时间选择 ym 调出年月选择,
						'minDate': (new Date().getFullYear()-118) + '-' + 1 + '-' + 1, //最小日期
						'maxDate': (new Date().getFullYear()+10) + '-' + 12 + '-' + 31 //最大日期
					});
			}
			
			//点击返回
			function goBack() {
			    var url = "${base}/member/order/list.jhtml?type=all";
				[#if pageSource??&&pageSource=='weixincoupon']
					url ="${base}/weixinCoupon/order/list.jhtml?type=all";
				[/#if]
				window.location.href = url;
            }
			function reason(auditReason,isOweOrder,obj){
			    if(isOweOrder!=null){
			        if(isOweOrder==32){ //怡亚通
						if(auditReason!=null && auditReason!=""){
                            auditReason="审核原因:"+auditReason+"<br/>"
						}
						var returnwareAddress= $(obj).attr("returnwareAddress");
                        if (returnwareAddress != null && returnwareAddress != "") {
                            auditReason += "寄回地址:" + returnwareAddress;
                        }
					}
				}

			    if(auditReason==null ||  auditReason==""){
                    auditReason="请联系客服处理";
				}
				layer.open({
				    content: auditReason
				    ,btn: ['确定']
				    ,skin: 'footer'
				});
			}
				
			//取消申请
			function cancelApply(returnsId, sn){
				layer.open({
				    content: '确定取消对'+ sn +'售后单的申请？'
				    ,btn: ['否', '是']
				    ,skin: 'footer'
				    ,no: function(index){
				      $.ajax({
					  	url:'cancelReturns.jhtml',
					  	type:'POST',
					  	data:"returnsId="+returnsId,
					  	cache:false,
					  	async:false,
					  	success:function(flag){
					  		if(flag){
					  			location.reload();
					  		}else{
					  			layer.open({
								    content: '取消申请失败'
								    ,skin: 'msg'
								    ,time: 2 //2秒后自动关闭
								  });
					  		}
					  	}
					  });
				    }
				  });
			}
			
			//确认退货
			function confirmReturns(id, isShipper,address,afServiceId,isOweOrder){

				$("#returnsId").val(id);
				
				if(isShipper){
					//需要物流
					if(afServiceId!=null && afServiceId!='' && typeof(afServiceId)!="undefined"){//京东
						layer.open({
							type:1,
							title:"确认退货",
							style:"width:90%",
							content:$("#pop_returns1").html(),
							btn:["确定","取消"],
                            success:function(){
                                setDeliverDate();
                            },
							yes: function(index){
								 submitConfirmReturns();
								 layer.close(index);
							}
						})
					}else{//非京东
						if(isOweOrder=="8"){
							layer.open({
								type:1,
								title:"确认退货",
								style:"width:90%",
								content:$("#pop_yanxuan_returns").html(),
								btn:["确定","取消"],
								success:function(){
                                    setDeliverDate();
								},
								yes: function(index){
									 submitConfirmReturns();
									 layer.close(index);
								}
							})
						}else if(isOweOrder=="32"){
							layer.open({
								type:1,
								title:"确认退货",
								style:"width:90%",
							    content:$("#pop_eascs_returns").html(),
                                success:function(){
                                    setDeliverDate();
                                },
								btn:["确定","取消"],
								yes: function(index){
									 submitConfirmReturns();
									 layer.close(index);
								}
							})
							$("#returnsAddress").val($("#confirmReturnsBtn").attr("returnwareAddress"));
						}
						// 天猫
						else if (isOweOrder=="70"){
							layer.open({
								type:1,
								title:"确认退货",
								style:"width:90%",
								content:$("#pop_tmall_returns").html(),
								btn:["确定","取消"],
								success:function(){
									setDeliverDate();
								},
								yes: function(index){
									submitConfirmReturns();
									layer.close(index);
								}
							})
							$("#address").val(address);
						}
						else{
							layer.open({
								type:1,
								title:"确认退货",
								style:"width:90%",
								 content:$("#pop_returns").html(),
								 btn:["确定","取消"],
								 success:function(){
                                    setDeliverDate();
								 },
								 yes: function(index){
									 submitConfirmReturns();
									 layer.close(index);
								 }
							})
						   $("#address").val(address);
						}
					}
					
				}else{
					//不需要物流，通知卖家退款弹框
					layer.open({
					    content: "是否通知卖家退款？"
					    ,btn: ['否', '是']
					    ,skin: 'footer'
					    ,no: function(index){
					    	$.ajax({
								url:'confirmReturns.jhtml',
								type:'POST',
								data:'returnsId=' + $("#returnsId").val(),
								cache:false,
								async:false,
								success:function(flag){
									if(flag){
							  			location.reload();
							  		}else{
							  			layer.open({
										    content: '退货失败'
										    ,skin: 'msg'
										    ,time: 2 //2秒后自动关闭
										  });
							  		}
								}
							});
					      }
					  });
				}
			}
			
			function submitConfirmReturns(){
				var returnsId = $("#returnsId").val();
				var deliveryCorp = $("#deliveryCorp").val();
				var trackingNo = $("#trackingNo").val();
				var freightMoney=$("#freightMoney").val();
				var deliverDate=$("#deliverDate").val();
				var afServiceId=$("#confirmReturnsBtn").attr("afsServiceId");
			    if(afServiceId!=null && afServiceId!='' && typeof(afServiceId)!="undefined"){//京东
			       if(freightMoney==null || freightMoney=='' || typeof(freightMoney)=="undefined"){
			           layer.msg("请输入运费");
				       return;
			       }
			    }
			    
				if($("#deliveryCorp").val() == ""){
					layer.open({
					    content: '请输入快递公司'
					    ,skin: 'msg'
					    ,time: 2 //2秒后自动关闭
					  });
					return;
				}
				if($("#trackingNo").val() == ""){
					layer.open({
					    content: '请输入快递单号'
					    ,skin: 'msg'
					    ,time: 2 //2秒后自动关闭
					  });
					return;
				}
				if($("#deliverDate").val() == ""){
					layer.open({
						content: '请选择发货日期'
						,skin: 'msg'
						,time: 2 //2秒后自动关闭
					});
					return;
				}
				$.ajax({
					url:'confirmReturns.jhtml',
					type:'POST',
					data:'returnsId=' + returnsId + '&trackingNo=' + trackingNo + '&deliveryCorp=' + deliveryCorp+"&freightMoney="+freightMoney+"&deliverDate="+deliverDate,
					cache:false,
					async:false,
					beforeSend:function(){
	                 	$('#submitBtn').prop("disabled", true);
	                 },
					success:function(flag){
						if(flag){
				  			layer.open({
							    content: '退货成功'
							    ,skin: 'msg'
							    ,time: 2 //2秒后自动关闭
							  });
							setTimeout("location.reload()",300);
				  		}else{
				  			layer.open({
							    content: '退货失败'
							    ,skin: 'msg'
							    ,time: 2 //2秒后自动关闭
							  });
				  		}
					}
				});
			}
		</script>
		 
	</body> 
</html>
