<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<title>酒店预订</title>
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">
		<script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/plugins/Swiper-3.4.2/css/swiper.css"/>
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/lifeService.css" />
   		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
		<script src="${base}/resources/wechat/plugins/Swiper-3.4.2/js/swiper.jquery.min.js"></script>
	</head>
	<body>
		<div class="public_top_header">
			<a href="javascript:history.go(-1);" class="return_back"></a> 
			酒店详情
		</div>
		<div class="photo_list_page">
			<div class="type">
				<a href="javascript:;" class="active">全部</a>
				<a href="javascript:;">房间</a>
				<a href="javascript:;">外观</a>
				<a href="javascript:;">内景</a>
			</div>
			<a href="javascript:history.go(-1)" class="fixed_order_link">立即预订</a>
			<div class="content">
				<section id="section_1">
					<header>全部</header>
					<ul>
					  [#if urls!=null && urls?size>0]
					     [#list urls as url]
					        <li>
							   <img src="${url}"/>
						    </li>
					     [/#list]
					  [/#if]
						
					
					</ul>
				</section>
				<section id="section_2">
					<header>房间</header>
					<ul>
					   [#if roomUrls!=null && roomUrls?size>0]
					     [#list roomUrls as roomUrl]
					        <li>
							   <img src="${roomUrl}"/>
						    </li>
					     [/#list]
					  [/#if]
					</ul>
				</section>
				<section id="section_3">
					<header>外景</header>
					<ul>
						[#if houseUrls!=null && houseUrls?size>0]
					     [#list houseUrls as houseUrl]
					        <li>
							   <img src="${houseUrl}"/>
						    </li>
					     [/#list]
					    [/#if]
					</ul>
				</section>
				<section id="section_4">
					<header>内景</header>
					<ul>
						[#if houseInnerUrls!=null && houseInnerUrls?size>0]
					     [#list houseInnerUrls as houseInnerUrl]
					        <li>
							   <img src="${houseInnerUrl}"/>
						    </li>
					     [/#list]
					    [/#if]
					</ul>
				</section>
			</div>
			
			
			
			<div class="swiper-container" style="display: none;">
			  	<div class="swiper-wrapper">
			    	<div class="swiper-slide"><img src="../img/recharge/pic_hotel/1.jpg"/></div>
			    	<div class="swiper-slide"><img src="../img/recharge/pic_hotel/2.jpg"/></div>
			    	<div class="swiper-slide"><img src="../img/recharge/pic_hotel/3.jpg"/></div>
			  	</div>
			  	<div class="swiper-button-prev"></div>
    			<div class="swiper-button-next"></div>
    			<div class="swiper-pagination"></div>
			</div>
		</div>
		
		<script>
			var mySwiper = new Swiper('.swiper-container', {
				prevButton:'.swiper-button-prev',
				nextButton:'.swiper-button-next',
				pagination : '.swiper-pagination',
				paginationType:"fraction"
			})
			
			
			$(function(){
				$(".type a").click(function(){
					var _index = $(this).index();
					var _scrollTop = $("section:eq("+_index+")").offset().top - $(".public_top_header").height() - $(".type").height();
					//console.log(_scrollTop)
					$("html,body").stop().animate({scrollTop:_scrollTop},1000);
				})
				
			/*	$("section li").click(function(){
					$(".swiper-container").show();
					mySwiper.removeAllSlides(); //移除全部
					//图片数组
					var photos = [];
					//获取相册json数据
					$.getJSON("../js/hotelImg.json",function(json){
						photos = json.data;
						//console.log(photos);
						$.each(photos, function(i, e){
						    mySwiper.appendSlide('<div class="swiper-slide"><img src="'+e.src+'"/></div>'); //加到Swiper的最后
						});
					});
				})*/
				
				$(".swiper-container").on("click",".swiper-slide",function(){
					if($(event.target).hasClass("swiper-slide")){
						$(".swiper-container").hide();
					}
				})
			})
			
		</script>
	</body>
</html>
