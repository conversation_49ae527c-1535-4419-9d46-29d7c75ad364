<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>[#if tripReimburseApply?? && tripReimburseApply.id??]编辑[#else]新增[/#if]报销-报销明细</title>
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/chailvStyle.css" />
        <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/form.css" /> 

    <script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js"></script>
</head>

<body>
    <div class="chailvPage">
        <div class="public_top_header">
            <a href="javascript:goBack();" class="return_back"></a>
            [#if tripReimburseApply.id??]编辑[#else]新增[/#if]报销单
        </div>
        <form action="addReimburseInfo.jhtml" id="reimburseForm" method="post">
	    	<input type="hidden" name="id" value="${tripReimburseApply.id}" />
        	<input type="hidden" name="applySn" value="${tripReimburseApply.applySn}" />
        	<input type="hidden" name="applyStatus" id="applyStatus" value="submited" />
	        <input type="hidden" name="memberId.id" value="${tripReimburseApply.memberId.id}" /> 
	        <input type="hidden" value="[#if reimburseAccount?? && reimburseAccount.tripLevelConfig??]${reimburseAccount.tripLevelConfig.tripAllowancePrice}[#else]0[/#if]" class="input" readonly />
			<input type="hidden" name="reimburseMemberId.id" value="${tripReimburseApply.reimburseMemberId.id}" /> 
	        <input type="hidden" name="applyType" value="${tripReimburseApply.applyType}"/>
	        <input type="hidden" name="bussinessType" value="${tripReimburseApply.bussinessType}"/>
	        <input type="hidden" name="feeCompanyStructure.id" value="${tripReimburseApply.feeCompanyStructure.id}"/>
	        <input type="hidden" name="projectId.id" value="${tripReimburseApply.projectId.id}"/>
	        <input type="hidden" name="receiptNumbers" value="${tripReimburseApply.receiptNumbers}"/>
	        <input type="hidden" name="memo" value="${tripReimburseApply.memo}"/>
	        <input type="hidden" name="reimburseMemberId.name" id="reimburseMemberIdName" value="${tripReimburseApply.reimburseMemberId.name}" />
	        <input type="hidden" name="feeCompanyStructure.name" id="feeCompanyStructureName" value="${tripReimburseApply.feeCompanyStructure.name}"/>
	        <input type="hidden" name="projectId.name" id="projectIdName" value="${tripReimburseApply.projectId.name}"/>
	        [#list tripReimburseApply.tripReimburseRecords as record]
		        <input type="hidden" name="tripReimburseRecords[${record_index }].id" value="${record.id }"/>
				<input type="hidden" name="tripReimburseRecords[${record_index }].tripApply.id" class="tripApplyId" value="[#if record.tripApply??]${record.tripApply.id }[/#if]"/>
				<input type="hidden" name="tripReimburseRecords[${record_index }].tripApply.applySn" class="tripApplySn" value="[#if record.tripApply??]${record.tripApply.applySn }[/#if]"/>
				<input type="hidden" name="tripReimburseRecords[${record_index }].startDate" value="${record.startDate }"/>
				<input type="hidden" name="tripReimburseRecords[${record_index }].endDate" value="${record.endDate }"/>
				<input type="hidden" name="tripReimburseRecords[${record_index }].tripDays" value="${record.tripDays }" />
				<input type="hidden" name="tripReimburseRecords[${record_index }].fromCity" value="${record.fromCity }"/>
				<input type="hidden" name="tripReimburseRecords[${record_index }].toCity" value="${record.toCity }"/>
				<input type="hidden" name="tripReimburseRecords[${record_index }].reimburseDays" value="${record.reimburseDays }" />
				[#list record.tripReimburseAmounts as amount]
				<input type="hidden" class="tripReimburseAmount_enabledFlag" name="tripReimburseRecords[${record_index }].tripReimburseAmounts[${amount_index }].enabledFlag" value="[#if amount.id?? && !amount.enabledFlag]false[#else]true[/#if]"/>
				<input type="hidden" name="tripReimburseRecords[${record_index }].tripReimburseAmounts[${amount_index }].id" value="${amount.id }"/>
				<input type="hidden" class="tripReimburseAmount_costType" name="tripReimburseRecords[${record_index }].tripReimburseAmounts[${amount_index }].costType" value="${amount.costType }"/>
				<input type="hidden" class="tripReimburseAmount_amount" name="tripReimburseRecords[${record_index }].tripReimburseAmounts[${amount_index }].amount" value="${currency(amount.amount) }"/>
				<input type="hidden" class="tripReimburseAmount_invoiceType" name="tripReimburseRecords[${record_index }].tripReimburseAmounts[${amount_index }].invoiceType" value="${amount.invoiceType }"/>
				<input type="hidden" class="tripReimburseAmount_chargeFlag" name="tripReimburseRecords[${record_index }].tripReimburseAmounts[${amount_index }].chargeFlag" value="[#if amount.chargeFlag?? && amount.chargeFlag]1[#else]0[/#if]"/>
				<input type="hidden" class="tripReimburseAmount_memo" name="tripReimburseRecords[${record_index }].tripReimburseAmounts[${amount_index }].memo" value="${amount.memo }"/>
				[/#list]		
	        [/#list]
            <div class="xingcheng_box">
                <header>
                    <span class="split"></span>收款信息
                </header>

                <div class="form_section">
                    <div class="item">
                        <label>报销总额</label>
                        <div class="inputs">
                            	￥${currency(tripReimburseApply.totalAmount) }
                        </div>
                    </div>
                    <div class="item">
                        <label>企业已付</label>
                        <div class="inputs">
                            	￥${currency(tripReimburseApply.companyAmount) }
                        </div>
                    </div>
                    <div class="item">
                        <label>个人报销</label>
                        <div class="inputs">
                            	￥${currency(tripReimburseApply.personalAmount) }
                        </div>
                    </div>
                    <div class="item arrow_right_line">
                        <label for="zffs"><span>*</span>支付方式</label>
                        <div class="inputs">
                        	<input type="hidden" name="receiveAccount" id="receiveAccountInput" value="${tripReimburseApply.receiveAccount}" /> 
                        	<select class="input dropdown" name="receiveType" id="zffs">
								<option value="0" [#if tripReimburseApply.receiveType == "0"] selected="selected"[/#if]>积分账户</option>
								<option value="1" [#if tripReimburseApply.receiveType == "1"] selected="selected"[/#if]>银行存款</option>
							</select>
                        </div>
                    </div>
                    <div class="item arrow_right_line" id="select_account">
                        <label for="receiveAccountDiv"><span>*</span>收款账户</label>
                        <div class="inputs" selAccount="[#if tripReimburseApply.receiveType == "1"]${tripReimburseApply.receiveAccount }[/#if]" coinAccount="[#if reimburseAccount??]${reimburseAccount.employeeid};${reimburseAccount.name}[/#if]" id="receiveAccountDiv">
                        </div>
                    </div>
                </div>
            </div>

            <div class="button_box">
                <input type="button" value="返回" class="btn_theme_empty" onclick="javascript:goBack();">
                <input type="button" value="下一步" class="btn_theme_empty active" onclick="javascript:goNext();return false;">
            </div>
            
        </form>
    </div>
    <script>
    	//返回上一页
	    function goBack(){
			$("#reimburseForm").attr("action", "addReimburseInfo.jhtml");
			$("#reimburseForm").submit();
	    }
	    function goNext(){
	    	var accountType = $("#zffs").val();
	    	if(accountType == null){
	    		layer.open({
                    content: "支付方式不能为空"
                    ,skin: 'msg'
                    ,time: 2 //2秒后自动关闭
                });
	    		$("#zffs").focus();
	    		return false;
		    } else if(accountType == "0"){
            	$("#receiveAccountInput").val($("#receiveAccountDiv").attr("coinAccount"));
            } else {
            	var selAccount = $("#receiveAccountDiv").attr("selAccount");
	            if(selAccount == null || selAccount == ""){
	            	layer.open({
	                    content: "收款账号不能为空"
	                    ,skin: 'msg'
	                    ,time: 2 //2秒后自动关闭
	                });
		    		$("#receiveAccountDiv").focus();
		    		return false;
	            } else {
	            	$("#receiveAccountInput").val(selAccount);
		        }
            }

	    	$("#reimburseForm").attr("action", "submitView.jhtml");
			$("#reimburseForm").submit();
		}
	    function showAccount(){
	    	var coinAccount = $("#receiveAccountDiv").attr("coinAccount");
            var selAccount = $("#receiveAccountDiv").attr("selAccount");
            var accountType = $("#zffs").val();
            var receiveAccount = "";
            if(accountType == "0"){
            	receiveAccount = coinAccount;
            } else {
	            if(selAccount != null && selAccount != ""){
	            	receiveAccount = selAccount;
	            } else {
	            	$("#zffs").val("0");
	            	receiveAccount = coinAccount;
	            }
            }
            var accounts = receiveAccount.split(";");
            $("#receiveAccountDiv").html(accounts[1] +"（" + accounts[0] + "）");
		}
        $(function () {
        	showAccount();
            $("#zffs").change(function(){
                if($(this).val() == "1"){
                    $("#select_account").addClass("arrow_right_line");
                    var selAccount = $("#receiveAccountDiv").attr("selAccount");
                    if(selAccount == null || selAccount == ""){
                    	$("#receiveAccountDiv").html("请选择收款账户");
                    } else {
	                    var accounts = selAccount.split(";");
	                    $("#receiveAccountDiv").html(accounts[1] +"（" + accounts[0] + "）");
                    }
                }else{
                    $("#select_account").removeClass("arrow_right_line");
                    var coinAccount = $("#receiveAccountDiv").attr("coinAccount");
                    var accounts = coinAccount.split(";");
                    $("#receiveAccountDiv").html(accounts[1] +"（" + accounts[0] + "）");
                }
            });

            $("#select_account").on("click",function(){
            	var accountType = $("#zffs").val();
            	if(accountType == "1"){
            		$("#reimburseForm").attr("action", "selectAccount.jhtml");
        			$("#reimburseForm").submit();
                } else {
					return false;
                }
            });

        })
    </script>
</body>

</html>