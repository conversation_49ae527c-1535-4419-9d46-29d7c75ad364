<!DOCTYPE html>
<html lang="en">

<head>
<meta charset="UTF-8">
<title>违反差旅标准</title>
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="black">
<script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/fli_2019.css" />
<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
<script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js"></script>
<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
</head>

<body>
	<div class="public_top_header">
		<a href="javascript:history.go(-1);" class="return_back"></a> 违反差旅标准
	</div>
	<div class="chailvReasonPage">
		<section class="lable_reason">
			<span class="icon_tips"></span>
			[#if tripSnapshot.tripRuleBroken??]
				[#list tripSnapshot.tripRuleBroken?split("、") as re]
					[#if re != null && re != ""]
					<p>${re_index + 1 }、${re }</p>
					[/#if]
				[/#list]
			[/#if]
		</section>
		<section class="list_reason">
			<header>选择原因:</header>
			[#if tripReasons??]
				[#list tripReasons as hotleReason]
					[#if hotleReason != null && hotleReason.resultCode != "HOT00"]
					<div class="item">
						<div class="fli_checkbox_blue">
							<input type="radio" name="radioBtn" id="${hotleReason.resultCode}" desc="${hotleReason.resultMessage}" value="${hotleReason.resultCode}" /> <label for="${hotleReason.resultCode}"></label>
						</div>
						${hotleReason.resultMessage}
					</div>
					[/#if]
				[/#list]
			[/#if]
			<div class="item">
				<div class="fli_checkbox_blue">
					<input type="radio" name="radioBtn" id="HOT00" value="HOT00" />
					<label for="HOT00"></label>
				</div>
				其它
			</div>
		</section>
		<section class="textarea">
			<textarea placeholder="说明" id="reasonMemo" maxlength="60"></textarea>
			<div class="btns">
				<input type="button" class="btn_half btn_yellow" onclick="javascript:history.go(-1);" value="取消"/>
				<input type="button" class="btn_half btn_blue" onclick="submit()" id="submitButton" value="继续预订"/>
			</div>
		</section>

	</div>

	<form action="${base}/member/order/info_virtual.jhtml" id="createForm" method="post">
		<input type="hidden" name="applyId" value="${tripSnapshot.applyId}"/>
		<input type="hidden" name="tripType" value="${tripSnapshot.tripType!"hotel"}"/>
		<input type="hidden" name="serviceInfo" value="${tripSnapshot.serviceInfo}"/>
		<input type="hidden" name="tripReasonCode" value="${tripSnapshot.tripReasonCode}"/>
		<input type="hidden" name="tripRuleBroken" value="${tripSnapshot.tripRuleBroken}"/>
		<input type="hidden" name="tripReason" value="${tripSnapshot.tripReason}"/>
		<input type="hidden" name="tripMemo" value="${tripSnapshot.tripMemo}"/>
		<input type="hidden" name="isBreak" value="${tripSnapshot.isBreak}"/>
		<input type="hidden" name="tripCityName" id="tripCityName" value="${tripSnapshot.tripCityName}"/>
		
		<input type="hidden" name="HotelCd" id="hotelId" value="${homeHotelInfoVO.hotelCd}"/>
		<input type="hidden" name="ArrDate"  value="${homeHotelInfoVO.arrDate}"/>
		<input type="hidden" name="DepDate"  value="${homeHotelInfoVO.depDate}"/>
		<input type="hidden" name="MobileTel" id="mobile"  value="${homeHotelInfoVO.mobileTel}"/>
		<input type="hidden" name="CRSRmType" id="roomTypeId" value="${homeHotelInfoVO.CRSRmType}"/>
		<input type="hidden" name="RmNum" id="numberOfRooms" value="${homeHotelInfoVO.rmNum}"/>
		<input type="hidden" name="GuestNum" id="guestCount" value="${homeHotelInfoVO.guestNum}"/>
		<input type="hidden" name="MustPayMoney" id="totalPrice1"  value="${homeHotelInfoVO.mustPayMoney}"/>
		<input type="hidden" name="apiPrice" id="apiPrice"  value="${homeHotelInfoVO.apiPrice}"/>
		<input type="hidden" name="GustName" id="customNames"  value="${homeHotelInfoVO.gustName}"/>
		<input type="hidden" id="productId" name="productId"  value="${productId}"/>
		<input type="hidden" id="agentMemberId" name="agentMemberId" value="${agentMemberId}">
		<input type="hidden" id="isPublic" name="isPublic" value="${isPublic}">
		<!-- 新增参数-->
		[#-- 
		<input type="hidden" id="minAmount" value="${minQty}"/>
        <input type="hidden" id="maxAmount" value="${maxQty}"/>
        --]
		<input type="hidden" name="dailyPriceInfo" value="${homeHotelInfoVO.dailyPriceInfo }"/>
		<input type="hidden" name="hotelName" value="${homeHotelInfoVO.hotelName}"/>
		<input type="hidden" name="address" value="${homeHotelInfoVO.address}"/>
		<input type="hidden" name="roomTypeName" value="${homeHotelInfoVO.roomTypeName}"/>
		<input type="hidden" name="bedType" value=" ${homeHotelInfoVO.bedType}"/>
		<input type="hidden" name="fromCityName" value="${homeHotelInfoVO.fromCityName}"/>
		<input type="hidden" name="contactName" value="${homeHotelInfoVO.contactName}"/>
		<input type="hidden" name="diffDays"  value="${homeHotelInfoVO.diffDays}"/>
		<input type="hidden" name="whetherExceedStandard"  value="${homeHotelInfoVO.whetherExceedStandard}"/>
		<input type="hidden" name="sku" value="${sku }"/>
		<input type="hidden" name="isFirst" value="${isFirst}"/>
		<input type="hidden" id="paymentMethodId" name="paymentMethodId" maxlength="200" value="1"/>  <!--支付方式-->
		<input type="hidden" id="shippingMethodId" name="shippingMethodId" maxlength="200" value="1"/>  <!--支付方式-->
        <input type="hidden" id="quantity"  name="quantity" value="1"/>

		<input type="hidden" name="LastTime"  value="${homeHotelInfoVO.lastTime}" id="LastTime"/>
		<input type="hidden" name="cancelCheckRule"  value="${homeHotelInfoVO.cancelCheckRule}" id="cancelCheckRule"/>
		<input type="hidden" name="roomBedInfo"  value="${roomBedInfo}" id="roomBedInfo"/>
		
	</form>
	
	
	
	<script>
	function submit(){
		$("#submitButton").prop("disabled", true);
		var reasonCodeRadio = $(".chailvReasonPage").find("input[name='radioBtn']:checked");
    	var resonText = reasonCodeRadio.attr("desc");
    	var reasonCode = reasonCodeRadio.val();
    	var remark = $(".chailvReasonPage").find('#reasonMemo').val();
    	if(reasonCode == null){
    		layer.open({
                content: '请选择原因!！'
                ,skin: 'msg'
                ,time: 2 //2秒后自动关闭
            });
    		return false;
        }
        if(remark.length > 60){
			remark = remark.substring(0, 60);
        }

		$("#createForm").find("input[name='tripMemo']").val(remark);
        $("#createForm").find("input[name='tripReasonCode']").val(reasonCode);
		$("#createForm").find("input[name='tripReason']").val(resonText);

		$("#submitButton").prop("disabled", true);
		$("#submitButton").val("提交中");
			
		$("#createForm").submit();
	}
	</script>
</body>

</html>