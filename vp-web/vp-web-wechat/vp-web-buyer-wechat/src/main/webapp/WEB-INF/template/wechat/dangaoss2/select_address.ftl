<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>收货地址</title>
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="black">
        <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js" ></script>
        <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
        <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/plusWap.css" /> 
		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
		
		<script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js" ></script>
	</head>
	<body class="select_address_page">
		<div class="addrList"> 
			<div class="public_top_header">
                <span class="return_back"></span>
				收货地址
				[#include "./wechat/include/head_nav.ftl" /]
			</div>
			<ul class="addr_select">
			[#list receivers as receiver]
				<li  class="flexbox align-items-c justify-content-space-between [#if receiver.id==.data_model['dangaoss2_receiver_' + member.id]]active[/#if]  ">
					<div class="addr_info">
                        <input type="hidden" class="nowAddress" value="${receiver.address}${(receiver.number)!}" />
                        <input type="hidden" class="areaName" value="${receiver.cityName?replace(',', ' ')}" />
                        <input type="hidden" class="areaId" value="${receiver.area}" />
                        <input type="hidden" class="treePath" value="${receiver.treePath}${receiver.area}" />
                        <input type="hidden" class="receiverId" value="${receiver.id}" />
						<p class="user flexbox align-items-c">
							${receiver.consignee}<span>${receiver.phone}</span>
						</p>
						<p class="addr">${receiver.areaName}${receiver.address}${(receiver.number)!}</p>
					</div>
					<div class="addr_edit">
					  <a href="javascript:void(0)" onclick="editAddress(${receiver.id})" class="icon_edit"></a>
					</div>
				</li>
			[/#list]
			</ul>
			<a href="javascript:void(0);" id="addAdressBtn" onclick="addAddress()" class="btn_submit_long"><span>+</span>新建地址</a>
		</div>	
		<script>
		var $orderRefreshForm = $("#orderRefreshForm");
			$(function(){
				$(".return_back").on('click',function () {
			        //关闭地址选择
                    window.top.closeCondition();
                });
				$(".addr_select").on("click",".addr_info",function(){

                    $(this).parent("li").siblings().removeClass("active").end().addClass("active");
                    var receiverId = $(this).find(".receiverId").val();
                    var areaName = $(this).find(".areaName").val();
                    var areaId = $(this).find(".areaId").val();
                    var treePath = $(this).find(".treePath").val();
                    var addressDetail = $(this).find(".nowAddress").val();
                    addPageCookies(areaName,addressDetail,areaId,treePath);
                    saveAddress(receiverId).then(function (result) {
							if(result.type==='success'){
                                window.top.location.reload();
							}
					})
				})
			})
        function  addPageCookies(areaName,addressDetail,areaId,treePath) {
            addCookie("cityCookie", '{"city":"' + areaName + '","addressDetail":"' +addressDetail+ '","areaId":"' + areaId + '","treePath":"' + treePath + '"}', {
                path: "/vp-web-buyer-wechat/", expires: 100 * 365 * 24 * 60 * 60
            });
        }
        //选择地址之后需要向后端发请求 匹配地址
        function  saveAddress(receiverId){
            return   new Promise(function(resolve,reject){
                $.ajax({
                    url: "${base}/dangaoss2/updateDanGaoAddrId.jhtml",
                    type: "POST",
                    data: {
                        receiverId: receiverId
                    },
                    success: function (data) {
                        resolve(data);
                    },
					error:function (err) {
                        reject(err);
                    }
                })
			})

        }
		function editAddress(receiverId){
				location.href="${base}/member/editAddress.jhtml?receiverId=" + receiverId+"&formPage=dangaoss";
         }
		function addAddress(receiverId){
			var count = '${receivers?size}';
			if(count == '${maxCount}'){
				layer.open({
				    content: '地址数量已满'
				    ,skin: 'msg'
				    ,time: 2 //2秒后自动关闭
				  });
				return;
			}
        	location.href="${base}/member/addAddressInit.jhtml?formPage=dangaoss"
        }
		</script>
		 
	</body>  
</html>
