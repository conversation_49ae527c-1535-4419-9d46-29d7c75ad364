<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>收货地址</title>
        <meta name="format-detection" content="telephone=no">
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="black">
        <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js" ></script>
        <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
        <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/plusWap.css" /> 

		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
		
		<script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js" ></script>
	</head>
	<body class="weixinCouponSelectAddressPage">
		<div class="addrList"> 
			<div class="public_top_header">
                <span class="return_back"></span>
				收货地址
			</div>
			<form id="orderRefreshForm" method="post">
				[#if links??]
					<input type="hidden"  id="links" value="${links}"  name="links" />
				[/#if]
				[#if productNum??]
					<input type="hidden" id="productNum"  value="${productNum}" name="productNum" />
				[/#if]
				[#if productId??]
					<input type="hidden" id="productId"  value="${productId}" name="productId" />
				[/#if]
				[#if groupOrAlone??]
				    <input type="hidden" id="groupOrAlone" value="${groupOrAlone}"   name="groupOrAlone" />
				[/#if]
                <input type="hidden" name="receiverId" id ="receiverIdM" value="${receiverId}" />
			</form>
			<ul class="addr_select">
			[#list receivers as receiver]
				<li  class="flexbox align-items-c justify-content-space-between [#if receiver.id == receiverId] active [/#if]">
                    <input type="hidden" id="receiverId" value="${receiver.id}" />
                    <div class="fli_checkbox_blue">
						<input type="checkbox" name="ids" id="id${receiver_index}" [#if receiver.id == receiverId] checked [/#if]" value="${receiver.id}" />
						<label for="id${receiver_index}"></label>
					</div>
					<div class="addr_info">
						<p class="user flexbox align-items-c">
							${receiver.consignee}<span>${receiver.phoneDec}</span>
							[#if receiver.isDefault]	<span class="default">默认</span> [/#if]
						</p>
						<p class="addr">

							${receiver.areaName}${receiver.address}${(receiver.number)!}
						</p>
					</div>
					<div class="addr_edit" onclick="editAddress(${receiver.id});event.cancelBubble =true">
					  <a href="javascript:void(0)"  class="icon_edit"></a>
					</div>
				</li>
			[/#list]
			</ul>
			<a href="javascript:void(0);" id="addAdressBtn" onclick="addAddress()" class="btn_submit_long"><span>+</span>新建地址</a>
		</div>	
		<script>
		var $orderRefreshForm = $("#orderRefreshForm");
			$(function(){
				$(".return_back").on('click',function () {
                    var receiverId = $(".addr_select li.active").find("#receiverId").val();
                    var backUrl = "${base}"+ $('#links').val();
                    if($("#productNum")&&$("#productNum").length>0){
                        backUrl += "?type=soon&updateSelectAddress=true&productNum=" + $("#productNum").val()+"&groupOrAlone="+$("#groupOrAlone").val()
                        if(receiverId&&receiverId!==undefined){
                            backUrl += "&receiverId="+receiverId;
                        }
                    }
                    location.href = backUrl
                });
				$(".addr_select").on("click","li",function(){
                    $(this).siblings().find(".fli_checkbox_blue input").prop("checked",false);
					$(this).siblings().removeClass("active").end().addClass("active");
                    $(this).find(".fli_checkbox_blue input").prop("checked",true);
					var receiverId = $(this).find("#receiverId").val();
					$("#receiverIdM").val(receiverId);
					var backUrl ="${base}" + $('#links').val();
					if($("#productNum")&&$("#productNum").length>0){
						backUrl += "?type=soon&updateSelectAddress=true&productNum=" + $("#productNum").val()+"&groupOrAlone="+$("#groupOrAlone").val()+"&receiverId="+receiverId;
					}
					location.href = backUrl
				})
			});
			
		function editAddress(receiverId){
        	  $("#receiverIdM").val(receiverId);
        	   $("#orderRefreshForm").attr("action", "editAddress.jhtml");
        	  $("#orderRefreshForm").submit();
         }
		function addAddress(receiverId){
			var count = '${receivers?size}';
			if(count == '${maxCount}'){
				layer.open({
				    content: '地址数量已满'
				    ,skin: 'msg'
				    ,time: 2 //2秒后自动关闭
				  });
				return;
			}
			
        	$("#orderRefreshForm").attr("action", "addAddress.jhtml");
        	$("#orderRefreshForm").submit();
        }
		</script>
		 
	</body>  
</html>
