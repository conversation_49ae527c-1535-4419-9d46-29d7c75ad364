<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>确认订单</title>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <meta name="apple-mobile-web-app-status-bar-style" content="black"/>
    <meta http-equiv="Expires" content="0"/>
    <meta http-equiv="Cache-Control" content="no-cache"/>
    <meta http-equiv="Pragma" content="no-cache"/>
    [#assign  isRmt = false]
    [#if thirdLoginFlag??&&thirdLoginFlag=='rmt']
        [#assign  isRmt = true]
    [/#if]
    <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css"/>
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/plusWap.css"/>
    <script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/card.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/jquery.validate.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
    [#if isRmt ]
        <script type="text/javascript" src="https://dapp.tmuyun.com/oss-static/app/jcommind.js"></script>
    [/#if]
    <script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js"></script>
    <script type="text/template" id="walletPayment">
        <div class="walletPaymentContent">
            <div class="flexbox align-items-c item_title">
                <span data-row="down">
                        钱包账户
                    </span>
            </div>
            <div class="walletList">
                <div class="item flexbox align-items-c justify-content-space-between">
                    <p class="label">建设银行钱包</p>
                    <p id="ccbValue" class="value">余额: ￥<span id="CCBBlance">0</span> ></p>

                </div>
                <div class="item flexbox align-items-c justify-content-space-between">
                    <p class="label">湖州银行钱包</p>
                    <p id="hzccbValue" class="value">余额: ￥<span id="HZCCBBlance">0</span> ></p>
                </div>
            </div>
            <!--三方支付-->

            <div class="tripartitePayment">
                <div class="flexbox align-items-c item_title">
                    您将使用以下方式支付全款
                </div>
                <div class="tripartitePaymentList flexbox align-items-c justify-content-space-between">
                    <div class="flexbox align-items-c">
                        <img src="${base}/resources/wechat/img/icon_weixin.png"/>微信支付
                    </div>
                    /
                    <div class="flexbox align-items-c">
                        <img src="${base}/resources/wechat/img/zhifubao.png"/>支付宝支付
                    </div>
                </div>
            </div>
        </div>

    </script>
    <script type="text/javascript">

        var $orderRefreshForm, $submit, $orderForm, isOverseas, $receiverIdCard, $idcardNoOrder, $saveIdcardNo,
            $receiverId;
        var $memo;
        var $memoMsg;
        var isWallet = false;//是否开通建行钱包
        var isHzWallet = false;//是否开通建行钱包
        var isWalletBlance = 0;//建行钱包总额
        var isHzWalletBlance = 0;//建行钱包总额
        var totalBlance = 0;//钱包总额
        var hzccbStatus;//是否开通湖州银行
        function videoTest(obj) {
            var mobileValue = $("#mobile").val();
            $("#midMobile").val(mobileValue);
        }

        $().ready(function () {
            $orderRefreshForm = $("#orderRefreshForm");
            $memo = $("#memo");
            $memoMsg = $("#memoMsg");
            $submit = $("#submit");
            $orderForm = $("#orderForm");
            isOverseas = "${isOverseas}"; //海外商品标识
            $receiverIdCard = $("#receiverIdCard");
            $idcardNoOrder = $("#idcardNoOrder");
            $saveIdcardNo = $("#saveIdcardNo");
            $receiverId = $("#receiverId");
            [#if isRmt]
            $submit.prop("disabled", true);
            $.ajax({
                url: '${base}/member/order/queryNxAccount.jhtml',
                type: 'POST',
                success: function (data) {
                    /** 成功且开通了钱包则显示钱包信息,否则显示支付宝和微信支付且提示用户未开通钱包功能*/
                    if (data.type === "success" && data.data) {
                        if (data.data.svcId) { //开启建行钱包
                            isWallet = true;
                            isWalletBlance = data.data.ccbBalance;
                        }
                        if (data.data.unionStatus && data.data.unionStatus.toString() === '1') { //开启湖州银行钱包
                            isHzWallet = true;
                            isHzWalletBlance = data.data.hzccbBalance;
                        }
                        totalBlance = isWalletBlance + isHzWalletBlance;
                        hzccbStatus = data.data.hzccbStatus;
                    }
                    if (totalBlance == 0) {
                        isLayer();
                    }
                    $submit.prop("disabled", false);
                },
                error: function () {
                    $submit.prop("disabled", false);
                }
            });
            [/#if]


            //输入密码事件
            $("#password").keyup(function () {
                var l_pwd = this.value.length;
                if (l_pwd >= 1 && l_pwd <= 6 && event.keyCode != 8) {
                    var _input = document.getElementById("number" + l_pwd);
                    _input.value = this.value.charAt(l_pwd - 1);
                    if (l_pwd == 6) { //输入完成动作
                        var _pwd = this.value;
                        $("#paymentPop").hide();
                        this.blur();
                        var device = openDevice();
                        if (device == "android") {
                            vpshop_android.callHideSoftInput();
                        }

                        var $checkedIds = $("#rechargeRecords input[name='coinIds']:enabled:checked");
                        $.ajax({
                            url: "check_paypassword.jhtml",
                            type: "POST",
                            data: {
                                coinIds: $("#coinIds").val(),
                                paypassword: _pwd,
                                whiteBarIds: $("#whiteBarIds").val()
                            },
                            dataType: "json",
                            cache: false,
                            success: function (message) {
                                $(".password_section input").val("");
                                if (message.type == "success") {
                                    submitOrder();
                                } else {
                                    layer.open({
                                        content: message.content,
                                        skin: 'msg',
                                        time: 2 //2秒后自动关闭
                                    });
                                }
                            }
                        });

                    }
                } else if (event.keyCode == 8) { //退格键删除
                    var _input = document.getElementById("number" + (l_pwd + 1));
                    _input.value = '';
                }
            })

            //保存身份证号
            $saveIdcardNo.click(function () {
                var receiverId = $receiverId.val();
                var idcardNo = $receiverIdCard.val();
                if (!idCardNoUtil.checkIdCardNo(idcardNo)) {
                    layer.open({
                        content: '请正确填写身份证号！',
                        skin: 'msg',
                        time: 2 //2秒后自动关闭
                    });
                    return false;
                }
                $.ajax({
                    url: "saveReviceIdcard.jhtml",
                    type: "POST",
                    data: {
                        receiverId: receiverId,
                        idcardNo: idcardNo
                    },
                    dataType: "json",
                    cache: false,
                    success: function (msg) {
                        if (msg == true) {
                            layer.open({
                                content: '身份证号码保存成功!',
                                skin: 'msg',
                                time: 2 //2秒后自动关闭
                            });
                        } else {
                            layer.open({
                                content: '身份证号码保存失败',
                                skin: 'msg',
                                time: 2 //2秒后自动关闭
                            });
                        }
                    }
                });

            });

            // 订单提交
            $submit.click(function () {
                var isRmtFlag = '${isRmt}';
                if (isRmtFlag.toString() === 'true') { //是融媒体
                    rmtLayer();
                    return false
                }
                // 如果是视频卡，则要判断手机号
                if ($("#type").val() == 3) {
                    var mobile = commonRule.phone;
                    var mobileValue = $("#mobile").val();
                    if (mobileValue.length == 11 && mobile.test(mobileValue)) {

                    } else {
                        layer.open({
                            content: '请输入正确的手机号',
                            skin: 'msg',
                            time: 2 //2秒后自动关闭
                        });
                        return false;
                    }
                }

                var cashPayFlag = "${member.companyId.cashPayFlag}";
                var amountPayable = "${order.amountPayable}"
                if (cashPayFlag != null && cashPayFlag != "" && cashPayFlag == 0) { //不允许使用现金支付
                    if (amountPayable > 0) {
                        layer.open({
                            content: '积分余额不足！',
                            skin: 'msg',
                            time: 2 //2秒后自动关闭
                        });
                        return false;
                    }
                }
                if (isOverseas == "true") {
                    var receiverIdCard = $receiverIdCard.val();

                    if (receiverIdCard == "" || receiverIdCard == null) {
                        layer.open({
                            content: '存在海外商品请正确填写身份证号！',
                            skin: 'msg',
                            time: 2 //2秒后自动关闭
                        });
                        $("html,body").animate({
                            scrollTop: 0
                        }, 300);
                        return false;
                    } else {
                        if (!idCardNoUtil.checkIdCardNo(receiverIdCard)) {
                            layer.open({
                                content: '存在海外商品请正确填写身份证号！',
                                skin: 'msg',
                                time: 2 //2秒后自动关闭
                            });
                            $("html,body").animate({
                                scrollTop: 0
                            }, 300);
                            return false;
                        }
                        $idcardNoOrder.val(receiverIdCard);
                    }
                }

                var rechargeRecordAmount = "${rechargeRecordAmount}"; //勾选了积分支付
                var whiteBarYAmount = "${whiteBarYAmount}"; //勾选了白条支付

                [#if member.companyId.isFreePay ]
                submitOrder();
                [#else]
                if (rechargeRecordAmount > 0) { //可用积分大于0，弹出密码框
                    $("#paymentPop").show().find("#password").focus();
                } else if (whiteBarYAmount > 0) {
                    $("#paymentPop").show().find("#password").focus();
                } else {
                    submitOrder();
                }
                [/#if]
            });
        });

        function isLayer() { //是否需要弹窗
            var formUrl = getQueryVariable("formUrlBack");
            if (formUrl === 'walletActivation') { //是从融媒体指导页返回的
                rmtLayer();
            }
        }

        [#if isRmt]//是融媒体
        function rmtLayer() {
            //钱包余额大于0，无需弹框
            if (totalBlance > 0) {
                $("#iswallet").val('true');
                submitOrder();
                return false;
            }
            if (totalBlance == 0 && hzccbStatus == 'no') {
                $("#iswallet").val('false');
                submitOrder();
                return false;
            }
            layer.open({
                title: "支付提示",
                style: "width:90%;",
                skin: "fliplus_layer_skin_wallet",
                closeBtn: 0,
                content: $("#walletPayment").html(),
                success: function (layero, index) {
                    //已开通建行钱包
                    if (isWallet) {
                        $("#CCBBlance").html(isWalletBlance);
                    } else {
                        $("#ccbValue").addClass("dredge");
                        $("#ccbValue").html("立即开通  >");
                    }
                    //已开通湖州钱包
                    if (isHzWallet) {
                        $("#HZCCBBlance").html(isWalletBlance);
                    } else {
                        $("#hzccbValue").addClass("dredge");
                        $("#hzccbValue").html("立即开通  >");
                    }
                    [#if com_person_moreAtts_activeUrl??]
                    $("#ccbValue").on('click', function () {
                        location.href = '${com_person_moreAtts_activeUrl}'
                    })
                    $("#hzccbValue").on('click', function () {
                        location.href = '${com_person_moreAtts_activeUrl}'
                    })
                    [/#if]
                    //钱包总额为0
                    if (isWalletBlance == 0) {
                        $("#iswallet").val('false');
                    }
                },
                btn: ["确认", "取消"],
                yes: function (indexthis, layero) {
                    layer.close(indexthis); //关掉自身layer
                    submitOrder();
                },
                btn2: function (indexthis, layero) {
                    layer.close(indexthis); //关掉自身layer
                }
            });
        }

        [/#if]

        function submitOrder() {
            var url = "createVirtual.jhtml";
            $.ajax({
                url: url,
                type: "POST",
                data: $orderForm.serialize(),
                dataType: "json",
                cache: false,
                beforeSend: function () {
                    $submit.prop("disabled", true);
                    $submit.val("提交中");
                },
                success: function (message) {
                    if (message.type == "success") {
                        location.href = "${base}/paysRedirect/payment.jhtml?sn=" + message.content + "&type=0";
                    }
                    if (message.type == "warn") {
                        $submit.val("立即付款")
                        layer.open({
                            content: message.content,
                            skin: 'msg',
                            time: 2 //2秒后自动关闭
                        });
                    } else if (message.type == "error") {
                        $submit.val("立即付款");
                        layer.open({
                            content: "下单失败",
                            skin: 'msg',
                            time: 2 //2秒后自动关闭
                        });

                        $.ajax({
                            url: "${base}/member/order/getChange.jhtml",
                            type: "POST",
                            data: {
                                content: message.content
                            },
                            success: function (data) {
                                var trHtml = '<div class="order_goods_has_changed"> <div class="header">以下商品有变动，暂时无法购买，请稍后再试！</div><div class="list">';
                                [@compress single_line = true]
                                $.each(data, function (i, product) {
                                    trHtml += '<div class="item"> <img src="${setting.siteUrlImage}/' + product.image + '"/><div class="title">' + product.name + '</div> </div>';
                                });
                                trHtml += '</div> </div>';
                                [/@compress]
                                layer.open({
                                    style: "width:94%;",
                                    title: false,
                                    btn: ["返回购物车"],
                                    content: trHtml,
                                    yes: function (index, layero) {
                                        location.href = "${base}/cart/list.jhtml";
                                    }
                                });
                            }
                        });

                    }
                },
                complete: function () {
                    $submit.prop("disabled", false);
                }
            });
        }


        //选择收货地址页面跳转
        function selectAddress() {
            $memoMsg.val($memo.val());
            $("#orderRefreshForm").attr("action", "selectAddress.jhtml");
            $("#orderRefreshForm").submit();
        }

        //选择积分页面跳转
        function selectCoin() {
            var url = "groupSelectCoin.jhtml";
            $("#orderRefreshForm").attr("action", url);
            $("#orderRefreshForm").submit();
        }

        //选择积分页面跳转
        function selectWhiteBar() {
            var url = "groupSelectWhiteBar.jhtml";
            $("#orderRefreshForm").attr("action", url);
            $("#orderRefreshForm").submit();
        }

        // 验证手机号
        jQuery.validator.addMethod("isTel", function (value, element) {
            var length = value.length;
            var mobile = /^(((13[0-9]{1})|14[57]{1}|(15[0-9]{1})|17[378]{1}|(18[0-9]{1}))+\d{8})$/;
            if (this.optional(element) || (length == 11 && mobile.test(value))) {
                $("#midMobile").val(value);
            }
            return this.optional(element) || (length == 11 && mobile.test(value));
        }, "请正确填写您的联系方式");

        $("#orderForm").validate({
            rules: {
                mobile: {
                    required: true,
                    minlength: 11

                }
            },
            messages: {
                mobile: {
                    required: "请输入手机号",
                    minlength: "确认手机不能小于11个字符",
                    isMobile: "请正确填写您的手机号码"
                }
            },
            errorPlacement: function (error, element) {
                error.appendTo(element.parent());
            }
        });


    </script>
</head>

<body id="myOrderPage" class="pay-info-page confirm-order-page">
<div class="myOrderPage common-order-info-page simple">
    <div class="public_top_header">
        <span class="return_back" [#if isRmt ][#else]onclick="javascript:window.history.back();"[/#if]></span>
        确认订单
    </div>

    <form id="orderRefreshForm" action="" method="post">
        [#list cartItems as cartItem]
            <input type="hidden" name="ids" value="${cartItem.id}"/> [/#list]
        <input type="hidden" name="coinIds" value="${coinIds}" id="coinIds"/>
        <input type="hidden" name="whiteBarIds" value="${whiteBarIds}" id="whiteBarIds"/>
        <input type="hidden" id="receiverId" name="receiverId" [#if receiver??] value="${receiver.id}" [/#if] />
        <input type="hidden" id="productId" name="productId" value="${productId}"/>
        <input type="hidden" name="couponCodeId" value="${couponCodeId}"/>
        <input type="hidden" name="memo" id="memoMsg" value="${order.memo!}"/>
        <input type="hidden" name="quantity" value="${quantity}"/>
        <input type="hidden" name="pId" value="${productId}"/>
        <input type="hidden" name="groupId" value="${groupId}"/>
        <input type="hidden" name="toatlAmount" value="${order.toatlAmount}"/>
        <input type="hidden" name="coinAmount" value="${order.coinAmount}"/>
        <input type="hidden" name="amountPaid" value="${order.amountPaid}"/>
        <input type="hidden" id="orderJson" name="orderJson" value="${orderJson}"/>
        <input type="hidden" id="midMobile" name="mobile" value="${mobile}"/>
        <input type="hidden" name="type" value="${type}"/>
        <input type="hidden" id="gasType" name="gasType" value="${gasType}"/>
        <input type="hidden" id="gasCard" name="gasCard" value="${gasCard}"/>
        <input type="hidden" id="userName" name="userName" value="${userName}"/>
        <input type="hidden" id="phone" name="phone" value="${phone}"/>
        <input type="hidden" name="bizType" value="${bizType}"/>
        <input type="hidden" name="sku" value="${sku}"/>
        <input type="hidden" name="coinTypeIdsEcho" value="${coinTypeIdsEcho}"/>
        <input type="hidden" name="coinTypeIds" value="${coinTypeIds}"/>
        <input type="hidden" name="thirdCoinAmount" value="${thirdCoinAmount}"/>

        <!-- 下面参数为了计算金额 -->
        <input type="hidden" id="price" name="price" value="${order.price}"/>
        <input type="hidden" id="freight" name="freight" value="${order.freight}"/>
        <input type="hidden" id="couponDiscount" name="couponDiscount" value="${order.couponDiscount}"/>
        <input type="hidden" id="promotionDiscount" name="promotionDiscount" value="${order.promotionDiscount}"/>
    </form>

    <div style="display:none">
        <section class="base_info card-rd">
            <a href="javascript:void(0)" onclick="selectAddress()" class="fli_link_line">
                [#if receiver??]
                    <p>
                        收货人：${receiver.consignee}
                        <span class="tel">${receiver.phoneDec}</span>
                    </p>
                    <p class="addr">
                        ${receiver.areaName}${receiver.address}
                    </p>
                [#else] 马上去添加收货地址 [/#if]
            </a>
            <!--<a href="#">! 马上去添加收货地址</a>-->
            <div class="identify" [#if !isOverseas]style="display: none;" [/#if]>
                <input type="text" name="idcardNo"
                       id="receiverIdCard" [#if receiver??] value="${receiver.idcardNoDec}" [/#if] class="input"
                       placeholder="因海关清关需要，请填写收货人身份证号码。"/>
                <input type="button" name="" id="saveIdcardNo" value="保存" class="btn_pill btn_pill_theme"/>
            </div>
        </section>
    </div>
    <section class="contentBox card-rd">
        <ul class="order_list_section">
            [#list order.orderItems as orderItem]
                <li>
                    <div class="img">
                        <img src="${orderItem.thumbnail}"/>
                    </div>
                    <div class="info">
                        <p class="title">
                            ${orderItem.name}
                        </p>
                        <p class="price flexbox align-items-c justify-content-space-between">
                            [#assign floatValue = orderItem.price?number]
                            [#assign integerPart = floatValue?floor]
                            [#assign decimalPart = (floatValue * 100)?round % 100 / 100]
                            <span class="sales-price">
                            ￥<span>${integerPart}</span>${decimalPart?string(".00")}
                        </span>
                            <span>x${orderItem.quantity}</span>
                        </p>
                        [#if orderItem.product.specificationValues]
                            <p class="attr">
                                [#list orderItem.product.specificationValues as specificationValue]
                                    <span>
			 					  ${specificationValue.specification.name}：${specificationValue.name}
								</span> [/#list]
                            </p>
                        [/#if] [#if orderItem.isLowStock]
                            <p class="outofservice">无货</p>
                        [/#if]
                    </div>
                </li>
            [/#list]
        </ul>
    </section>

    <form id="orderForm" action="createVirtual.jhtml" method="post">
        [#if isRmt]<input type="hidden" name="iswallet" value="" id="iswallet"/>[/#if]
        <input type="hidden" name="idcardNoOrder" id="idcardNoOrder" value=""/> [#list cartItems as cartItem]
            <input type="hidden" name="cartItemids" value="${cartItem.id}"/> [/#list]
        <input type="hidden" name="ids" value="${coinIds}"/>
        <input type="hidden" name="whiteBarIds" value="${whiteBarIds}"/>
        <input type="hidden" name="thirdCoinAmount" value="${thirdCoinAmount}"/>
        <input type="hidden" name="cartToken" value="${cartToken}"/>
        <input type="hidden" name="receiverId" [#if receiver??] value="${receiver.id}" [/#if]/>
        <input type="hidden" name="paymentMethodId" maxlength="200" value="1"/>
        <!--支付方式-->
        <input type="hidden" name="shippingMethodId" maxlength="200" value="1"/>
        <!--支付方式-->
        <input type="hidden" name="couponCodeId" value="${couponCodeId}"/>
        <input type="hidden" name="groupId" value="${groupId}" id="groupId"/>
        <input type="hidden" id="productId" name="productId" value="${productId}"/>
        <input type="hidden" id="quantity" name="quantity" value="${quantity}"/>
        <input type="hidden" id="orderJson" name="orderJson" value="${orderJson}"/>
        <input type="hidden" id="type" name="type" value="${type}"/>
        <input type="hidden" name="bizType" value="${bizType}"/>
    </form>

    <section class="amount_section card-rd favourable_section">
        <p class="item flexbox align-items-c justify-content-space-between">
            <label>数量</label>
            <span>共${order.quantity}件</span>
        </p>
        [#if productShowRate??]
            <p class="item flexbox align-items-c justify-content-space-between">
                <label>商品金额</label>
                [#if serviceFee!=null]
                    <span>${coinConvert(order.price-serviceFee, productShowRate, true, false)}</span>
                [#else]
                    <span>${coinConvert(order.price, productShowRate, true, false)}</span>
                [/#if]
            </p>
            <p class="item flexbox align-items-c justify-content-space-between">
                <label>服务费</label>
                <span>${coinConvert(serviceFee, productShowRate, true, false)}</span>
            </p>
            [#if !isRmt]
                [#if member.companyId.sourceFlag == null || member.companyId.sourceFlag==""]
                    <div class="lines_Section_all fli_link_line" onclick="selectCoin()">
                        <label>积分支付</label>
                        <div class="right_text red">
                            [#if order.coinAmount > 0]
                                -${coinConvert(order.coinAmount, coinShowRate!1)}
                            [#else]
                                [#list coins as coin] [#if coin.coinTypeId.isCredit]
                                    <span class="like_btn">	${coin.coinTypeId.name?substring(0,1)}${coinConvert(coin.balance, coinShowRate!1)}[#if isUnifiedDeduct](企)[/#if]</span> [/#if] [/#list]
                                [#if thirdCoin??]
                                    <span class="like_btn"
                                          thirdCoinAmount="${thirdCoinAmount }">${thirdCoin.coinName?substring(0,1)}${thirdCoin.amount}</span>
                                [/#if]
                            [/#if]
                        </div>
                    </div>

                    <div class="lines_Section_all fli_link_line"
                         onclick="selectWhiteBar()" [#if (member.companyId?? && member.companyId.hasWhitebar?? && member.companyId.hasWhitebar)] [#else]style="display:none;" [/#if]>
                        <label>白条支付</label>
                        <div class="right_text red">
                            [#if order.whiteBarAmount > 0]
                                -${coinConvert(order.whiteBarAmount, coinShowRate!1)}
                            [#else]
                                [#list whiteBarCoins as coin] [#if coin.coinTypeId.isCredit && coin.enabledFlag && coin.isFreeze]
                                    <span class="like_btn">	${coin.coinTypeId.name?substring(0,1)}${coinConvert(coin.balance, coinShowRate!1)}[#if isUnifiedDeduct](企)[/#if]</span> [/#if] [/#list]
                            [/#if]
                        </div>
                    </div>
                [/#if]
            [/#if]
            <p class="flexbox align-items-c justify-content-space-between">
                <label>优惠券抵扣</label>
                <span>-${coinConvert(order.couponDiscount, productShowRate, true, false)}</span>
            </p>

            <p class="item flexbox align-items-c justify-content-space-between">
                <label>活动优惠</label>
                <span>-${coinConvert(order.promotionDiscount, productShowRate, true, false)}</span>
            </p>
        [#else]
            <p class="item flexbox align-items-c justify-content-space-between">
                <label>商品金额</label>
                [#if serviceFee!=null]
                    <span>${currency(order.price-serviceFee, true, false)}</span>
                [#else]
                    <span>${currency(order.price, true, false)}</span>
                [/#if]
            </p>
            <p class="item flexbox align-items-c justify-content-space-between">
                <label>服务费</label>
                <span>${currency(serviceFee, true, false)}</span>
            </p>
            [#if !isRmt]
                [#if member.companyId.sourceFlag == null || member.companyId.sourceFlag==""]
                    <div class="lines_Section_all fli_link_line" onclick="selectCoin()">
                        <label>积分支付</label>
                        [#if order.coinAmount > 0]
                            <div class="right_text red">-${coinConvert(order.coinAmount, coinShowRate!1)}</div>
                        [#else]
                            [#list coins as coin] [#if coin.coinTypeId.isCredit]
                                <span class="like_btn">	${coin.coinTypeId.name?substring(0,1)}${coinConvert(coin.balance, coinShowRate!1)}[#if isUnifiedDeduct](企)[/#if]</span> [/#if] [/#list]
                            [#if thirdCoin??]
                                <span class="like_btn"
                                      thirdCoinAmount="${thirdCoinAmount }">${thirdCoin.coinName?substring(0,1)}${thirdCoin.amount}</span>
                            [/#if]
                        [/#if]
                    </div>

                    <div class="lines_Section_all fli_link_line"
                         onclick="selectWhiteBar()" [#if (member.companyId?? && member.companyId.hasWhitebar?? && member.companyId.hasWhitebar)] [#else]style="display:none;" [/#if]>
                        <label>白条支付</label>
                        <div class="right_text red">
                            [#if order.whiteBarAmount > 0]
                                -${coinConvert(order.whiteBarAmount, coinShowRate!1)}
                            [#else]
                                [#list whiteBarCoins as coin] [#if coin.coinTypeId.isCredit && coin.enabledFlag && coin.isFreeze]
                                    <span class="like_btn">	${coin.coinTypeId.name?substring(0,1)}${coinConvert(coin.balance, coinShowRate!1)}[#if isUnifiedDeduct](企)[/#if]</span> [/#if] [/#list]
                            [/#if]
                        </div>
                    </div>
                [/#if]
            [/#if]
            <p class="item flexbox align-items-c justify-content-space-between">
                <label>优惠券抵扣</label>
                <span>-${currency(order.couponDiscount, true, false)}</span>
            </p>

            <p class="item flexbox align-items-c justify-content-space-between">
                <label>活动优惠</label>
                <span>-${currency(order.promotionDiscount, true, false)}</span>
            </p>
        [/#if]
    </section>

    <div class="bottom info-footer flexbox align-items-c justify-content-space-between">
        <div class="total">
            <input type="hidden" id="_amountPayable" value="${order.amountPayable}">

            [#if productShowRate??]
                [#assign floatValue =coinConvert(order.amountPayable,productShowRate)?number]
            [#else]
                [#assign floatValue = order.amountPayable?number]
            [/#if]
            [#assign integerPart = floatValue?floor]
            [#assign decimalPart = (floatValue * 100)?round % 100 / 100]
            <span class="money-total" id="money-total">
                 [#if productShowRate??]
                <i class="iconfontAli icon-ali-jifen"></i>
                 [#else]
                ￥
                [/#if]<span>${integerPart}</span>${decimalPart?string(".00")}</span>
            <p>实付款</p>
        </div>
        <input type="button" name="" id="submit" value="立即付款" class="btn_submit_long"/>
    </div>
</div>

[#include "./wechat/include/set_payPasswords.ftl" /]

<script>
    [#include "./wechat/include/categoryCheck.ftl" /]
    var maxstrlen = 30;

    //计算长度
    function getStrleng(str) {
        myLen = 0;
        i = 0;
        for (;
            (i < str.length) && (myLen <= maxstrlen * 2); i++) {
            if (str.charCodeAt(i) > 0 && str.charCodeAt(i) < 128)
                myLen++;
            else
                myLen += 2;
        }
        return myLen;
    }

    [#if isRmt ]
    $(function () {
        window.CP2.defineMenu({
            navigationBar: 0,
            shareBtn: '0',
        }).then((res) => {
            console.log(res);
        });
    });
    $('.return_back').on('click', function () {
        window.CP2.defineMenu({
            navigationBar: 1,
            shareBtn: '0',
        }).then((res) => {
            console.log(res);
        });
        window.history.back();
    });
    [/#if]
</script>

</body>

</html>