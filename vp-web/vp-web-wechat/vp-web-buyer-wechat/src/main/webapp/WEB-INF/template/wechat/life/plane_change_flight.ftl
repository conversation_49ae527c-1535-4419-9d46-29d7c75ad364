<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>飞机票</title>
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">
    <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
   	<script src="https://cdn.bootcss.com/jquery/1.8.3/jquery.min.js"></script> 
    <script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/plugins/switch/simple.switch.min.js"></script>
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/lifeService.css" />
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/simple.switch.three.css" />
    <script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js"></script>
</head>

<body class="has_fixed_footer modify-plane-change-flight-page">
    <div class="public_top_header">
    	[#if applyId?? ]
			[#if source??]
				<span class="return_back" onclick="location.href='${base}/plane/planeChange.jhtml?applyId=${applyId}&source=${source}&sn=${sn}&reason=${reason}&insuranceChecks=${insuranceChecks}&date=${date}&agentMemberId=${agentMemberId}&isPublic=${isPublic}'"></span>
			[#else]
				<span class="return_back" onclick="location.href='${base}/plane/planeChange.jhtml?applyId=${applyId}&sn=${sn}&reason=${reason}&insuranceChecks=${insuranceChecks}&date=${date}&agentMemberId=${agentMemberId}&isPublic=${isPublic}'"></span>
			[/#if]
		[#else]
        	<span class="return_back" onclick="location.href='${base}/plane/planeChange.jhtml?sn=${sn}&reason=${reason}&insuranceChecks=${insuranceChecks}&date=${date}'"></span>
      	[/#if]
      	  申请改签
        [#if com_person_hide_menu?? && com_person_hide_menu == '1']
            <!-- 隐藏右上角菜单目录 -->
        [#else ]
            [#include "./wechat/include/head_nav.ftl" /]
        [/#if]
    </div>
    <div class="fly_search_detail_page">

        <header class="flexbox align-items-c">
            <!--
            <a href="javascript:;" class="prev" onclick="updateDate('reduce')">前一天</a>
            <a href="javascript:;" class="next" onclick="updateDate()">后一天</a>
            -->
            <input type="text" name="" id="dataShow" value="${date}" class="input_text" readonly="readonly" onfocus="this.blur()" onclick="selectDate()" />
            <div class="change-reason flexbox align-items-c" onclick="changeReason()">
                <p id="mySelect" data-value="${tgqReason.code}" data-needupload="${tgqReason.needUploadProof?string("true","false")}" class="reason flex1">
                ${tgqReason.msg}
                </p>
                <img data-id="180" src="${base}/resources/wechat/img/bulejiantou.png" class="curImg">
            </div>
        </header>

        <form id="searchForm" action="searchChangeFlight.jhtml" method="get">
            <!-- 表单提交数据 -->
            <input type="hidden" id="sn" name="sn" value="${sn}" />
            <input type="hidden" id="reason" name="reason" value="${reason}" />
            <input type="hidden" id="date" name="date" value="${date}" />
            <input type="hidden" id="passengers" name="passengers" value="${passengers}" />
            <input type="hidden" id="passengerIds" name="passengerIds" value="${passengerIds}" />
            <input type="hidden" id="insuranceChecks" name="insuranceChecks" value="${insuranceChecks}" />
            <input type="hidden" id="changePassengersStr" name="changePassengersStr" value="${changePassengersStr}" />
            
            [#if applyId?? ]
            	 <input type="hidden" name="applyId" value="${applyId}" />
            	 <input type="hidden" name="agentMemberId" value="${agentMemberId}"/>
				 <input type="hidden" name="isPublic" value="${isPublic}"/>
           	[/#if]
           	[#if source?? ]
            	 <input type="hidden" name="source" value="${source}" />
           	[/#if]
        </form>
        
        <form id="changeInfoForm" action="planeChangeInfo.jhtml" method="post">
            <!-- 表单提交数据 -->
             <input type="hidden" id="sn" name="sn" value="${sn}" />
            <input type="hidden" id="code" name="code" value="${reason}" />
            <input type="hidden" id="jsonInfo" name="jsonInfo" value="" />
            <input type="hidden" id="needupload" name="needUpload" value="${tgqReason.needUploadProof?string("true","false")}" />
            <input type="hidden" id="date" name="date" value="${date}" />
            <input type="hidden" id="passengerIds" name="passengerIds" value="${passengerIds}" />
            <input type="hidden" id="passengers" name="passengers" value="${passengers}" />
            <input type="hidden" id="insuranceChecks" name="insuranceChecks" value="${insuranceChecks}" />
            <input type="hidden" id="insurances" name="insurances" value="" />
            <input type="hidden" id="insuranceOrderSn" name="insuranceOrderSn" value="" />
            <input type="hidden" id="insuranceOrderId" name="insuranceOrderId" value="" />
            <input type="hidden" id="insuranceOrderUri" name="insuranceOrderUri" value="" />
            <input type="hidden" id="insuranceOrders" name="insuranceOrders" value="" />
            <input type="hidden" id="changePassengersStr" name="changePassengersStr" value="${changePassengersStr}" />
			<input type="hidden" id="needUpload" name="needUpload" value="false" />				
            [#if applyId?? ]
            	 <input type="hidden" name="applyId" value="${applyId}" />
            	 <input type="hidden" name="agentMemberId" value="${agentMemberId}"/>
				<input type="hidden" name="isPublic" value="${isPublic}"/>
           	[/#if]
           	[#if source?? ]
            	 <input type="hidden" name="source" value="${source}" />
           	[/#if]
        </form>

		[#if tgqReason??&&tgqReason.changeFlightSegmentList??&&tgqReason.changeFlightSegmentList?size>0]
        [#list change.result[0].changeSearchResult.tgqReasons as tgqReason]
            <article class="planeList planeList_${tgqReason_index}" data-code="${tgqReason.code}" [#if tgqReason.code!=reason]style="display: none;"[/#if]>
            [#list tgqReason.changeFlightSegmentList as changeFlightSegment]
                <div class="item" data-jsoninfo="${changeFlightSegment.getJsonInfo(changeFlightSegment,changeRuleInfo.tgqText)}" data-flightNo="${changeFlightSegment.flightNo}" data-startTime="${changeFlightSegment.startTime}" data-endTime="${changeFlightSegment.endTime}" [#if changeFlightSegment.getIsAcrossDay(date)] data-acrossDay="1" [/#if]>
                    <div class="left">
                        <div class="line1">
                            <div class="from">
                                <p>${changeFlightSegment.startTime}</p>
                                <p>${changeFlightSegment.startPlace}</p>
                            </div>

                            <div class="arrow">
                               <p class="arriveBybox">
                                  [#if changeFlightSegment.stopFlightInfo.stopType == 2]
                                     <span>经停</span>
                                  [/#if]
                               </p>
                               <p class="line2">
                                    <span>${changeFlightSegment.flightNo}</span>
                                    <span>${changeFlightSegment.flightType}</span>
                               </p>
                            </div>

                            <div class="to">
                                <p>${changeFlightSegment.endTime}</p>
                                <p>${changeFlightSegment.endPlace}</p>
                            </div>
                        </div>
                      <!--<div class="line2">
                            <span>${changeFlightSegment.flightNo}</span>
                            <span>${changeFlightSegment.flightType}</span>
                        </div>  -->
                    </div>
                    <div class="right">
                        <p>
                            [#if changeFlightSegment.allFee==0]
                                <span class="price"><span>免费</span></span>
                            [#else]
                                <span class="price">￥<span>${changeFlightSegment.allFee}</span></span>起
                            [/#if]
                        </p>
                        <p class="free">
                              改签费总额
                        </p>
                        <p class="free">
                             ${changeFlightSegment.cabin}
                        </p>
                    </div>
                </div>
            [/#list]
            </article>
        [/#list]

  <!--       <footer>
            <a href="javascript:;">
                <span class="icon_filter"></span>
                <p>筛选</p>
            </a>
            <a href="#">
                <span class="icon_time "></span>
                <p>从晚到早</p>
                <p>从早到晚</p>
            </a>
            <a href="#">
                <span class="icon_money"></span>
                <p>从高到低</p>
                <p>从低到高</p>

            </a>
        </footer> -->
       <!--  <div class="popbg">
            <div class="filter_pop_section">
                <div class="topbtn">
                    <a href="#" class="concel">取消</a>
                    <a href="#" class="reset">重置</a>
                    <a href="#" class="submit">确定</a>
                </div>
                <div class="arrivesoon">
                    <label>仅看直飞</label>
                    <input type="checkbox" name="" id="isStops" value="" />
                </div>
                <article class="filter_pop_main">

                    <ul class="type">
                        <li class="active" data-id="carriers">航空公司</li>
                        <li data-id="departureTime">起飞时段</li>
                    </ul>
                    <ul class="list carriers">
                        <li>
                            <span>不限</span>
                            <div class="fli_checkbox_blue">
                                <input type="checkbox" name="ids" id="carriersUnlimited" value="" checked="checked">
                                <label for="carriersUnlimited"></label>
                            </div>
                        </li>
                    </ul>
                    <ul class="list departureTime" style="display:none;">
                        <li>
                            <span>不限</span>
                            <div class="fli_checkbox_blue">
                                <input type="checkbox" name="ids" id="departureTimeUnlimited" value="" checked="checked">
                                <label for="departureTimeUnlimited"></label>
                            </div>
                        </li>
                        <li>
                            <span>上午（00:00~10:00）</span>
                            <div class="fli_checkbox_blue">
                                <input type="checkbox" class="time" name="" id="t1" value="t1">
                                <label for="t1"></label>
                            </div>
                        </li>
                        <li>
                            <span>中午（10:00~14:00）</span>
                            <div class="fli_checkbox_blue">
                                <input type="checkbox" class="time" name="" id="t2" value="t2">
                                <label for="t2"></label>
                            </div>
                        </li>
                        <li>
                            <span>下午（14:00~19:00）</span>
                            <div class="fli_checkbox_blue">
                                <input type="checkbox" class="time" name="" id="t3" value="t3">
                                <label for="t3"></label>
                            </div>
                        </li>
                        <li>
                            <span>晚上（19:00~24:00）</span>
                            <div class="fli_checkbox_blue">
                                <input type="checkbox" class="time" name="" id="t4" value="t4">
                                <label for="t4"></label>
                            </div>
                        </li>
                    </ul>
                </article>
            </div>
        </div> -->
        [#else]
        <div class="public_empty_section [#if comPersonConf && com_person_EmptyBg != null && com_person_EmptyBg != ""] ${com_person_EmptyBg!}[#else] Public_Bg[/#if]">
            <p class="tip">这里空空如也</p>
            [#if applyId?? ]
				[#if source??]
					<a href="${base}/plane/queryList.jhtml?applyId=${applyId}&source=${source}&agentMemberId=${agentMemberId}&isPublic=${isPublic}" class="pub_link_theme">
				[#else]
					<a href="${base}/plane/queryList.jhtml?applyId=${applyId}&agentMemberId=${agentMemberId}&isPublic=${isPublic}" class="pub_link_theme">
				[/#if]
			[#else]
	            <a href="${base}/plane/queryList.jhtml" class="pub_link_theme">
	        [/#if]
	            [#if canChange??&&!canChange]
	            	不可改签
	            [#else]
	         	   抱歉，没有找到
	            [/#if]	
	            </a>
        </div>
        [/#if]
    </div>
    <div class="layer_baoxian" id="layerBaoxian" >
		<div class="public_top_header" style="position:absolute">
			<a href="javascript:void(0)" onclick="$(this).parents('.layer_baoxian').hide()" class="return_back"></a>
			航意险投保
		</div>
		<div class="pub_ajax_loading" style="z-index:9"><div class="box"><span class="icon_loading"></span><p>正在前往投保...</p></div></div>
		<iframe style="position:relative;z-index:99" src='' id="insuranceIframe" frameborder="0"></iframe>
	</div>
    <div class="popbg" id="pop_select">
        <div class="close"></div>
        <div class="select-reason-lists">
            [#if change.result[0].changeSearchResult.tgqReasons ?? ]
                [#list change.result[0].changeSearchResult.tgqReasons as changeReason ]
                    <p class="item"
                       data-value="${changeReason.code}"
                       data-needUpload="${changeReason.needUploadProof?string("true","false")}">
                        ${changeReason.msg}
                    </p>
                [/#list]
            [/#if]
        </div>
    </div>
</body>
<script>
    var insurancesCheck = [#if insuranceChecks??]false[#else]true[/#if];//保险投保确认
    var planeTicketObject = new Object();//航班信息
    $("article").on("click",".item",function(){
        $("#jsonInfo").val($(this).attr("data-jsoninfo"));

		[#if insuranceChecks != null && insuranceChecks != ""]//选了保险
			planeTicketObject.flightNum = $(this).attr("data-flightNo");
  			planeTicketObject.dptDate = "${date}";
  			planeTicketObject.dptTime = $(this).attr("data-startTime");
  			planeTicketObject.arrTime = $(this).attr("data-endTime");
  			planeTicketObject.arrDate = "${date}";
	  		if($(this).attr("data-acrossDay") != null){//跨天了
                var dateTime = new Date("${date}");
                dateTime = dateTime.setDate(dateTime.getDate() + 1);
                dateTime=new Date(dateTime);
                planeTicketObject.arrDate = dateTime.Format("yyyy-MM-dd");
            }

			submitForm($(this));
        [#else]
			$("#changeInfoForm").submit();
        [/#if]
    })
    function changeReason() {
        const curImg = $(".curImg");
        var angle =parseInt(curImg.attr("data-id"));
        if(angle===0){
            curImg.attr("data-id",180);
            curImg.removeClass("rotateB");
            hidePop();
        }else {
            curImg.attr("data-id",0);
            curImg.addClass("rotateB");
            $("#pop_select").show().children(".select-reason-lists").animate({"bottom":"0"});
        }

    }
    function hidePop() {
        $(".select-reason-lists").animate({"bottom":"-100%"},function(){
            $("#pop_select").hide();
        })
        $(".curImg").attr("data-id",180);
        $(".curImg").removeClass("rotateB");
    }
    function submitForm(obj){
        $("#insurances").val("");
        $("#insuranceOrderId").val("");//保单订单号
        $("#insuranceOrderUri").val("");//uri
        $("#insuranceOrders").val("");
        var hostName = location.origin;//https://wx.fliplus.com
        $.ajax({
            url: "getAccidentUrl.jhtml",
            type: "POST",
            data: {hostName: hostName, orderJson: JSON.stringify(planeTicketObject), insuranceChecks: "${insuranceChecks}", changePassengersStr: $("#changePassengersStr").val()},
            dataType: "json",
            cache: false,
            success: function(message) {
                if (message.type == "success") {
                    var data = JSON.parse(message.content);

                    if(data.uri != null){

                        var insurances = new Array();
                        var insuranceCodes = "${insuranceChecks}".split(",");
                        for(var i = 0; i < insuranceCodes.length; i ++){
                            if(insuranceCodes[i] != null && insuranceCodes[i] != ""){
                                var insurance = new Object();
                                insurance.insuranceCode =  insuranceCodes[i];
                                insurance.lpType ="0";
                                insurance.num =  "1";
                                insurances.push(insurance);
                            }
                        }

                        $("#insurances").val(JSON.stringify(insurances));
                        $("#insuranceOrderId").val(data.orderId);//保单订单号
                        $("#insuranceOrderUri").val(data.uri);//uri

                        var insuranceOrdersStr = "";
                        if(data.insuranceOrders != null && data.insuranceOrders != ""){
                            insuranceOrdersStr = JSON.stringify(data.insuranceOrders);
                            console.log("insuranceOrdersStr:", insuranceOrdersStr);
                        }
                        $("#insuranceOrders").val(insuranceOrdersStr);

                        $("#insuranceIframe").attr("src", data.uri);
                        $(".layer_baoxian").show();
                    } else {
                        layer.open({
                            content:"保险生单失败~",
                            skin:"msg",
                            time:2
                        });
                    }
                } else {
                    layer.open({
                        content: message.content,
                        skin:"msg",
                        time:2
                    });
                    insurancesCheck = false;
                }
            }
        });

    }


    //  以下逻辑来源于plane_search_flight.ftl的页面  请修改
    //switch 开关
    $(".arrivesoon input").simpleSwitch({
        "theme": "FlatCircular"
    });


    var date = "${date}";

    var a = new Array("日", "一", "二", "三", "四", "五", "六");
    var week = new Date(date).getDay();
    var str = "星期" + a[week];
    $("#dataShow").val(date + "  " + str);

    // 选择出发日期
    function selectDate() {
        const reason = $("#mySelect").attr("data-value")
        location.href = "selectChangeDate.jhtml?type=changeFlight&sn=${sn}&reason="+reason+"&date=${date}&passengers=${passengers}&passengerIds=${passengerIds}";
    }

    // 前一天后一天查看
    function updateDate(str) {
        var nowDate = new Date(date);
        if (str == "reduce") {
            nowDate.setDate(nowDate.getDate() - 1);
            var today = new Date();
            if (new Date(date) < today.getTime()) {
                return false;
            } else {
                $("#date").val(crtTimeFtt(nowDate));

                document.getElementById('searchForm').submit();
            }
        } else {
            nowDate.setDate(nowDate.getDate() + 1);
            $("#date").val(crtTimeFtt(nowDate));

            document.getElementById('searchForm').submit();
        }
    }
    //初始化显示列表 如果返回的原因能匹配上，则显示第一条
    function initLits() {
        const list = $(".planeList").length;
        const hisList =  $(".planeList:hidden").length;
        if(list.length>0){
            if(list==hisList){ //全部隐藏
                $(".planeList_0").show();
            }
        }
    }
    // 时间格式化
    function crtTimeFtt(val) {
        if (val != null) {
            var date = new Date(val);
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            if (month < 10) {
                month = "0" + month;
            }
            var day = date.getDate();
            if (day < 10) {
                day = "0" + day;
            }
            return year + '-' + month + '-' + day;
        }
    }

    $(function() {
        initLits();
        $(".close").on("click",function () {
            hidePop();
        })
        $(".select-reason-lists").on("click","p",function () {
            $("#mySelect").text($(this).text());
            $("#mySelect").attr("data-value",$(this).attr("data-value"));
            $("#mySelect").attr("data-needupload",$(this).attr("data-needupload"));
            $("#needupload").val($(this).attr("data-needupload"));
            var code=$(this).attr("data-value");
            if($(".planeList .item").length>0&&code){
                $(".planeList").hide();
                var $select=$(".planeList[data-code='"+code+"']");
                $select.show();
            }else {
                $(".planeList").hide();
            }
            hidePop();
        })
        $(".carriers :checkbox").click(function() {
            if ($(this).prop("id") == "carriersUnlimited") {
                if ($(this).prop("checked")) {
                    $(".carriers input[id!='carriersUnlimited']").prop("checked", false);
                }
                if ($(".carriers input[id!='carriersUnlimited']:checked").size() == 0) {
                    $(".carriers input[id='carriersUnlimited']").prop("checked", true);
                }
            } else {
                if ($(".carriers input[id!='carriersUnlimited']:checked").size() == 0) {
                    $(".carriers input[id='carriersUnlimited']").prop("checked", true);
                } else {
                    $(".carriers input[id='carriersUnlimited']").prop("checked", false);
                }
            }
        });
        $(".departureTime :checkbox").click(function() {
            if ($(this).prop("id") == "departureTimeUnlimited") {
                if ($(this).prop("checked")) {
                    $(".departureTime input[id!='departureTimeUnlimited']").prop("checked", false);
                }
                if ($(".departureTime input[id!='departureTimeUnlimited']:checked").size() == 0) {
                    $(".departureTime input[id='departureTimeUnlimited']").prop("checked", true);
                }
            } else {
                if ($(".departureTime input[id!='departureTimeUnlimited']:checked").size() == 0) {
                    $(".departureTime input[id='departureTimeUnlimited']").prop("checked", true);
                } else {
                    $(".departureTime input[id='departureTimeUnlimited']").prop("checked", false);
                }
            }
        });

        //筛选
        function filtrate() {
            if ($(".carrier:checked").size() > 0 || $(".departureTime .time:checked").size() > 0 || $("#isStops").is(":checked")) {
                $("article .item").hide();

                var isStopsFilter = "";

                if ($("#isStops").is(":checked")) {
                    isStopsFilter = "[data-stop='false']";
                }
                if ($(".carrier:checked").size() > 0) {
                    $(".carrier:checked").each(function() {
                        var filter = "[data-carrier='" + $(this).val() + "']" + isStopsFilter;
                        departureTimeFiltrate(filter);
                    });
                } else {
                    departureTimeFiltrate(isStopsFilter);
                }
            } else {
                $("article .item").show();
            }
        }

        function departureTimeFiltrate(filter, isStopsFilter) {
            var $departureTime = $(".departureTime .time:checked");
            if ($departureTime.size() > 0) {
                $departureTime.each(function() {
                    var departureTimeFilter = "[data-timeType='" + $(this).val() + "']" + filter;
                    $("article .item" + departureTimeFilter).show();
                });
            } else {
                $("article .item" + filter).show();
            }
        }
        //底部切换
        $("footer a").on("click", function() {
            // $(this).siblings().removeClass("active").end().addClass("active");
            if ($(this).index() == 0) {
                $(this).siblings().removeClass("active").end().addClass("active");
                $(".popbg").show();
                return false;
            }
            var text = $(this).children(".icon_time, .icon_money").next().text();
            if (text == "从早到晚" || text == "从晚到早") {
                if (text == "从早到晚" && $(this).hasClass("active")) {
                    $("#orderType").val("dptTimeDesc");
                } else {
                    $("#orderType").val("dptTimeAsc");
                }
            } else {
                if (text == "从低到高" && $(this).hasClass("active")) {
                    $("#orderType").val("priceDesc");
                } else {
                    $("#orderType").val("priceAsc");
                }
            }

            document.getElementById('searchForm').submit();

        })

        //筛选类型切换
        $(".filter_pop_section .type").on("click", "li", function() {
            if (!$(this).hasClass("active")) {
                $(this).siblings().removeClass("active").end().addClass("active");
                var dataId = $(this).attr("data-id");
                $(".filter_pop_main .list").hide();
                $("." + dataId).show();
                /*  var _html = '';
                 //模拟数据 请删除
                 var _text = $(this).html();
                 for (var i = 0; i < 20; i++) {
                     _html += '<li>' +
                         '<span>' + _text + (i + 1) + '</span>' +
                         '<div class="fli_checkbox_blue">' +
                         '<input type="checkbox" name="ids" id="id' + (i + 1) + '" value="">' +
                         '<label for="id' + (i + 1) + '"></label>' +
                         '</div>' +
                         '</li>';
                 }
                 //模拟数据 请删除 */

                /*  $(".filter_pop_section .list li:gt(0)").remove(); */
                /*        $(".filter_pop_section .list").append(_html); */
            }
        })

        //弹窗隐藏
        $(".filter_pop_section .topbtn").on("click", ".concel,.submit", function() {
            $(".popbg").hide();
            if ($(this).hasClass("submit")) {
                filtrate();
            }
        })
        $(".filter_pop_section .topbtn").on("click", ".reset", function() {
            $(".popbg :checkbox").prop("checked", false);
            $(".departureTime input[id='departureTimeUnlimited']").prop("checked", true);
            $(".carriers input[id='carriersUnlimited']").prop("checked", true);
        })
    })

    function CpLayerClose(insOrderSn, insOrderId){
        //var res = window["layui-layer-iframe" + index].callbackdata();

        //planeTicketObject.insuranceOrderSn = insOrderSn;//订单号

        //planeTicketObject.insuranceOrderId = insOrderId;//保单订单号
        // 变成json格式传给后台
        //$planeTicketStr.val(JSON.stringify(planeTicketObject));
        planeTicketObject.insuranceOrderSn = insOrderSn;//订单号

        $("#insuranceOrderSn").val(insOrderSn);
        insurancesCheck = true;
        $("#changeInfoForm").submit();
    }

</script>
</html>