<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta
            name="viewport"
            content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no"
    />
    <title>支付成功</title>
    <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
    <link rel="stylesheet" href="${base}/resources/wechat/css/vant/index.css">
    <link type="text/css"  rel="stylesheet" href="${base}/resources/wechat/css/common.css">
    <link type="text/css"  rel="stylesheet" href="${base}/resources/wechat/css/rechargePay.css">
    <script src="${base}/resources/wechat/plugins/vue/vue.min.js"></script>
    <script type="text/javascript"  src="${base}/resources/wechat/plugins/jquery/jquery-1.9.1.min.js"></script>
    <script type="text/javascript"  src="${base}/resources/wechat/js/common.js"></script>
    <script src="${base}/resources/wechat/js/vant.min.js"></script>
</head>
<body class="oilPaySuccessPage wechatConsumePage">
<div id="app">
    <van-sticky>
        <van-nav-bar
                title="支付成功"
                left-text=""
                left-arrow
                @click-left="onClickLeft"
                @click-right="onClickRight"
        >
            <template #right>
                <van-icon name="wap-home" color="rgb(51,51,51)" />
            </template>
        </van-nav-bar>
    </van-sticky>
    <div class="content">
        <div class="flexbox justify-content-c align-items-c flex-column imgBox">
            <img src="${base}/resources/wechat/img/wechatConsume/other/success1.png">
            <p >
                恭喜您，支付成功
            </p>
        </div>
        <p class="noteBold">
            科技园加油站
        </p>
        <p class="detailAddress">
            深圳市南山区粤海大道数字技术园B1
        </p>
        <div class="flexbox align-items-c justify-content-space-between noteBold">
            <p>
                支付金额：
            </p>
            <p>
                ¥100
            </p>
        </div>
        <div class="flexbox useInstruction">
            <van-icon class="questionIcon" name="question-o" ></van-icon>
            <p>
                支付成功后，请在光汇云油小票机打印加油小票出示小票加油，或咨询工作人员请说明通过光汇云油支付
            </p>
        </div>
        <div class="option">
            <van-button type="info" round  class="btnoption" @click="Jump('xf')">返回消费</van-button>
            <van-button plain type="info" round  class="btnoption" @click="Jump('dd')">查看订单</van-button>
        </div>
    </div>
</div>
<script>
    let vm = new Vue({
        el: "#app",
        data() {
            return {

            };
        },
        computed:{

        },
        methods: {
            onClickLeft() {
                history.back();
            },
            onClickRight() {
                location.href="${base}/brightoilonline/index.jhtml";
            },
            //点击跳转，参数为地址
            Jump(data){
                location.href="${base}" + data+".jhtml";
            }
        },
    });
</script>
</body>
</html>
