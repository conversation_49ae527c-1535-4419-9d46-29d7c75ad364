<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <title>离职</title>
    <meta name="format-detection" content="telephone=no" />
    <meta content="email=no" name="format-detection">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <script type="text/javascript" src="${base}/resources/greetingCard/js/flexible.js" ></script>
    <link rel="stylesheet" href="${base}/resources/greetingCard/css/swiper.css?v=1.0.0" />
    <link rel="stylesheet" href="${base}/resources/greetingCard/css/animate.css?v=1.0.0" />
    <link rel="stylesheet" type="text/css" href="${base}/resources/greetingCard/css/common.css?v=1.0.0" />
    <link rel="stylesheet" type="text/css" href="${base}/resources/greetingCard/template/dimission1/css/style.css?v=1.0.0" />
    <script type="text/javascript" src="${base}/resources/greetingCard/js/jquery-1.9.1.js" ></script>
    <script type="text/javascript" src="${base}/resources/greetingCard/js/swiper.jquery.min.js"></script>
</head>

<body>
    <div class="loading_section">
        <div class="loading"></div>
    </div>
    <div class="public_top_header">
        <!-- [#if flag!=null && flag==0] -->
        <a href="${base}/dynamic/index.jhtml" class="return_back"></a>
        <!-- [#else] -->
        <a href="javascript:history.go(-1);" class="return_back"></a>
        <!-- [/#if]  -->
        我的贺卡
    </div>

    <div id="music" class="music">
        <audio id="music-audio" class="audio" loop="" autoplay="autoplay" preload="">
            <source src="${base}/resources/greetingCard/music/New_Year's_Day.mp3" type="audio/mpeg"></source>
        </audio>
        <div class="control">
            <div class="control-after"></div>
        </div>
    </div>

    <!-- Swiper -->
    <div class="swiper-container">
        <div class="swiper-wrapper">
            <div class="swiper-slide slide_1">
                <div class="img">
                    <img src="${base}/resources/greetingCard/template/dimission1/img/icon.png" alt="" class="slideInDown animated title">
                    <img src="${base}/resources/greetingCard/template/dimission1/img/txt.png" alt="" class="zoomIn animated man">
                </div>
            </div>
            <div class="swiper-slide slide_2">
                <div class="textarea animated">
                    <div class="img">
                        <img src="${base}/resources/greetingCard/template/dimission1/img/toptxt.png" class="img_1 animated " />
                    </div>
                    ${cardContent}
                </div>
            </div>
			[#assign phoneTitle="离职祝福" /]
			[#include "/wechat/member/card/template/commonGift.ftl" /]            
        </div>
        <!-- Add Pagination -->
        <div class="swiper-pagination"></div>
    </div>
    <div class="to_next_page animated infinite slideInUpS"></div>

    <!-- Initialize Swiper -->
    <script>
        var swiper = new Swiper('.swiper-container', {
            pagination: '.swiper-pagination',
            direction: 'vertical',
            loop: true,
            paginationClickable: true,
            onSlideChangeEnd: function(swiper) {
                if (swiper.activeIndex == 1 || swiper.activeIndex == 4) {

                    // <img src="./img/icon.png" alt="" class="slideInDown animated title">
                    // <img src="./img/txt.png" alt="" class="zoomIn animated man">

                    $(".slide_1 .title").show().addClass("slideInDown");
                    $(".slide_1 .man").show().addClass("zoomIn");

                    $(".slide_2 .img_1").hide().removeClass("fadeInLeft");
                    $(".slide_2 .img_2").hide().removeClass("zoomIn");
                    $(".slide_2 .textarea").hide().removeClass("bounceInUp");
                    // $(".showBoxPage .img_1").hide().removeClass("slideInDown");
                } else if (swiper.activeIndex == 2) {
                    $(".slide_1 .title").hide().removeClass("slideInDown");
                    $(".slide_1 .man").hide().removeClass("zoomIn");
                    $(".slide_2 .img_1").show().addClass("fadeInLeft");
                    $(".slide_2 .textarea").show().addClass("bounceInUp");
                    // $(".showBoxPage .img_1").hide().removeClass("slideInDown");
                } else if (swiper.activeIndex == 3 || swiper.activeIndex == 0) {
                    $(".slide_1 .title").hide().removeClass("slideInDown");
                    $(".slide_1 .man").hide().removeClass("zoomIn");
                    $(".slide_2 .img_1").hide().removeClass("fadeInLeft");
                    $(".slide_2 .textarea").hide().removeClass("bounceInUp");
                    // $(".showBoxPage .img_1").show().addClass("slideInDown");
                }
            }
        });
        
    </script>
</body>

</html>