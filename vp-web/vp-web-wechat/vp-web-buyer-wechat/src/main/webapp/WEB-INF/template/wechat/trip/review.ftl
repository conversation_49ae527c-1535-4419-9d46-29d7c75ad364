<!DOCTYPE html>
<head>
    <meta charset="UTF-8">
    <title>申请单审核</title>
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">
    <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css">
    <link rel="stylesheet" href="${base}/resources/wechat/css/travelapply.css">

    <script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
</head>

<body>
	<div class="travelistinfopage">
        <div class="public_top_header">
            <a href="${base}/trip/index.jhtml?type=${type!}" class="return_back"></a>
            	申请单审核
        </div>
        
        [#assign showClass = "" /]
   		[#assign status = "其他" /]
   		[#if tripApply.applyStatus == "wait_submit"]
   			[#assign showClass = "checking" /]
   			[#assign status = "待提交" /]
		[#elseif tripApply.applyStatus == "wait_review"]
			[#assign showClass = "checking" /]
            [#assign status = "待审核" /]
		[#elseif tripApply.applyStatus == "pass"]
			[#assign showClass = "success" /]
            [#assign status = "已通过" /]
		[#elseif tripApply.applyStatus == "reject"]
			[#assign showClass = "fail" /]
            [#assign status = "未通过" /]
		[#elseif tripApply.applyStatus == "revoke"]
			[#assign showClass = "cancel" /]
            [#assign status = "已撤销" /]
        [#elseif tripApply.applyStatus == "finish"]
			[#assign showClass = "success" /]
            [#assign status = "已完成" /]
		[#else]
			[#assign showClass = "" /]
            [#assign status = tripApply.applyStatus! /]
		[/#if]
					    
        <div class="toppan ${showClass! }">
            <p>
                <span class="type">${status! }</span>
                <span class="date">${tripApply.createDate?string("yyyy-MM-dd HH:mm:ss") }</span>
            </p>
        </div>
        <div class="applylists">
            <div class="container">
                <ul class="lists">
                	[#if tripApply.tripRecords?size == 2]
                		<li class="item">
	                        <div class="left">
	                            <span>往返行程</span>
	                        </div>
	                        <div class="right">
	                            <div class="section1 section">
	                                <span class="from">${tripApply.tripRecords[0].fromCity! }</span>
	                                <span class="txt">至</span>
	                                <span class="to">${tripApply.tripRecords[0].toCity! }</span>
	                            </div>
	                            <div class="section2 section">
	                                <div class="Begin">
	                                    <p>${tripApply.tripRecords[0].startDate! }</p>
	                                    <p>${tripApply.tripRecords[0].startPeriod! }</p>
	                                </div>
	                                <div class="txt">至</div>
	                                <div class="finish">
	                                    <p>${tripApply.tripRecords[1].startDate! }</p>
	                                    <p>${tripApply.tripRecords[1].startPeriod! }</p>
	                                </div>
	
	                            </div>
	
	                        </div>
	                    </li>
                	[#else]
	                	[#list tripApply.tripRecords as tripRecord]
	                	<li class="item">
	                    	<div class="left">
	                		[#if tripRecord_has_next]
	                    		[#if tripRecord_index == 0]
	                    			<span>开始行程</span>
	                    		[#else]
	                       			<span>中间行程${tripRecord_index }</span>
	                        	[/#if]
	                        [#else]
	                        	<span>结束行程</span>
	                        [/#if]
	                        </div>
	                        <div class="right">
	                            <div class="section1 section">
	                                <span class="from">${tripRecord.fromCity! }</span>
	                                <span class="txt">至</span>
	                                <span class="to">${tripRecord.toCity! }</span>
	                            </div>
	                            <div class="section2 section">
	                                <div class="tag">
	                                    	出发时间
	                                </div>
	                                <div class="finish">
	                                    <p>${tripRecord.startDate! }</p>
	                                    <p>${tripRecord.startPeriod! }</p>
	                                </div>
	                            </div>
	                        </div>
	                    </li>
	                	[/#list]
	                [/#if]
                </ul>

                <div class="desc">
                    <div class="memberbox"><label class="left">出差员工：</label>
                        <div class="lists left">
                        	[#list tripApply.members as member]
                        		<span data-num="${member.username }" data-dep="${member.companyStructureId.name }">${member.name }</span>
                        		<!-- [#if member_has_next]、[/#if] -->
                        	[/#list]
                        </div>
                    </div>
                    <p><label>出差类型：</label> 
                    	<span>
                    		[#if tripApply.applyType == "0"]
									拜访客户
							[#elseif tripApply.applyType == "1"]
									项目出差
							[#elseif tripApply.applyType == "2"]
									外出培训
							[#elseif tripApply.applyType == "3"]
									参加会议
							[#elseif tripApply.applyType == "5"]
								加班用车                 
							[#elseif tripApply.applyType == "4"]
									其他
							[/#if]
                    	</span>
                    </p>
                    <p><label>核算项目：</label> <span>[#if tripApply.projectId??]${tripApply.projectId.name }[/#if]</span></p>
                    <p><label>预算费用：</label> <span>${currency(tripApply.estimatedCost)}</span></p>
                    <p><label>出行方式：</label> 
                    	<span>
                    [#if tripApply.transport??]
                    	[#list tripApply.transport.split(",") as ts]
                    		[#if ts=="1"]飞机[#elseif ts=="2"]火车[#elseif ts=="3"]打车[#elseif ts=="4"]酒店[#elseif ts=="5"]其他[/#if][#if ts_has_next]、[/#if]
                    	[/#list]
                    [/#if]
                    	</span>
                    </p>
                    <p><label>出差说明：</label> <span>${tripApply.memo!"无" }</span></p>
                    <p><label>审核人：</label> <span>${tripApply.reviewer!myReviews!"无" }</span></p>
                </div>
                <div class="process">
                    <div class="title">
                        <span>单据信息</span>
                        <div class="imgbox">
                            <img src="${base}/resources/wechat/img/icon_arrow_down.png" alt="">
                        </div>
                    </div>
                    <div class="content">
                    	[#if tripReviews?? && tripReviews?size > 0]
                    	<div class="left">
                    		[#list tripReviews as tripReview]
                    			<span>${tripReviews?size - tripReview_index }</span>
                    		[/#list]
                        </div>
                        <div class="right">
                            <table class="table">
                                <tbody>
                                [#list tripReviews as tripReview]
	                    			<tr>
                                        <td scope="row">${tripReview.member.name }</td>
                                        <td>
                                        [#if tripReview.operateType == "submit"]
		                                	提交申请
		                                [#elseif tripReview.operateType == "reject"]
		                                	审核未通过
		                                [#elseif tripReview.operateType == "pass"]
		                                	审核通过
		                                [#elseif tripReview.operateType == "change"]
		                                	变更 
		                                [#elseif tripReview.operateType == "revoke"]
		                                	撤销
		                                [/#if]
                                        </td>
                                        [#if tripReview.memo??]
                                        <td class="reason" reason="${tripReview.memo!"无" }">理由</td>
                                        [#else]
                                        <td reason="无">无</td>
                                        [/#if]
                                        <td>${tripReview.createDate?string("MM-dd HH:mm")}</td>
                                    </tr>
	                    		[/#list]
                                </tbody>
                            </table>
                        </div>
                    	[#else]
                    	<div> 无 </div>
                    	[/#if]
                    </div>
                </div>
                [#if tripApply.applyStatus == "wait_review"]
                <div class="reasons">
                    <div class="title">
                        <span>审批理由</span>
                    </div>
                    <textarea name="suggest" id="suggest" cols="" rows="4" placeholder="请填写审批理由，不超过60字"></textarea>
                    <span class="wordwrap"><var class="word">60</var>/60</span>
                </div>
                <div class="btns">
                    <button class="checkbtn btn" applyid="${tripApply.id }">通过</button>
                    <button class="cancelbtn btn" applyid="${tripApply.id }">拒绝</button>
                </div>
                [#else]
                <div class="reasons">
                    <div class="title">
                        <span>审批理由</span>
                    </div>
                    <textarea name="suggest" id="suggest" readonly="readonly" cols="" rows="4" placeholder="请填写审批理由，不超过60字"></textarea>
                    <span class="wordwrap"><var class="word">60</var>/60</span>
                </div>
                [/#if]
            </div>
        </div>
    </div>
    <input id="type" type="hidden" value="${type! }"/>
</body>
<script>
    //先选出 textarea 和 统计字数 dom 节点
    var wordCount = $("#suggest"),
        word = $(".word");
    statInputNum(wordCount, word);
   
    /*
     * 剩余字数统计
     * 注意 最大字数只需要在放数字的节点哪里直接写好即可 如：<var class="word">200</var>
     */
    function statInputNum(wordCount, word) {
        var max = word.text(),
            curLength;
        wordCount.attr("maxlength", max);
        var str = wordCount.val();
        if(str != null && str != ""){
        } else {
        	curLength = 0;
        }
        curLength = str.length;
        word.text(max - curLength);
        wordCount.on('input propertychange', function() {
            word.text(max - $(this).val().length);
        });
    }

    //滚动启用与禁用
    var methods = {
        /** 禁用滚动*/
        forbidScroll: function() {
            document.querySelector("html").classList.add("lock");
            window.addEventListener("mousewheel", this.forbidScroll);
            window.addEventListener("touchmove", this.forbidScroll, {
                passive: false
            });
        },
        /** 启用滚动*/
        enabledScroll: function() {
            document.querySelector("html").classList.remove("lock");
            window.removeEventListener("mousewheel", this.forbidScroll);
            window.removeEventListener("touchmove", this.forbidScroll, {
                passive: false
            });
        }

    }


    $(".desc .memberbox .lists").on("click", "span", function() {
        //console.log("kkk")
        var str = '<p>姓名：' + $(this).html() + '</p><p>工号：' + $(this).attr("data-num") + '</p><p>部门：' + $(this).attr("data-dep") + '</p>'
        layer.open({
            content: str,
            skin: 'msg',
            time: 2 //2秒后自动关闭
        });
    });


    $(".process .title .imgbox").on("click", function() {
        var $this = $(this);
        delay_till_last('trigger', function() { //注意 id 是唯一的  
            $this.children().toggleClass("rotate");
            $(".content").slideToggle();
        }, 300);

    });

    var _timer = {};

    function delay_till_last(id, fn, wait) {
        if (_timer[id]) {
            window.clearTimeout(_timer[id]);
            delete _timer[id];
        }
        return _timer[id] = window.setTimeout(function() {
            fn();
            delete _timer[id];
        }, wait);
    }


    $(".right table tr").on("click", "td.reason", function(e) {
        layer.open({
            content: $(this).attr("reason"),
            skin: 'msg',
            btn: '我知道了'
        });
    });

    //审核
    $(".checkbtn").on("click", function() {
    	var id = $(this).attr("applyid");
    	var suggest = $("#suggest").val();
    	/**
    	if(suggest == null || suggest.trim() == ""){
    		layer.open({
				content: '审批理由不能为空'
				,skin: 'msg'
				,time: 2 
			});
			return false;
        }
**/
    	$.post("review.jhtml",
		   {
			suggest:suggest,
		    type:"pass",
		    id:id
		   },
		   function(msg){
				if (msg.type=="success") {
				   layer.open({
						content: '操作成功'
						,skin: 'msg'
						,time: 2 
					});
				   setTimeout('window.location.href = "index.jhtml";', 2000);
				} else {
					layer.open({
						content: msg.content
						,skin: 'msg'
						,time: 2 
					});
				}
		   });
    });
    
    //审核不通过
    $(".cancelbtn").on("click", function() {
    	var id = $(this).attr("applyid");
    	
   		var suggest = $("#suggest").val();
       	//alert(suggest);
       	if(suggest == null || suggest.trim() == ""){
       		layer.open({
   				content: '审批理由不能为空'
   				,skin: 'msg'
   				,time: 2 
   			});
   			return false;
           }

       	$.post("review.jhtml",
   		   {
   			suggest:suggest,
   		    type:"reject",
   		    id:id
   		   },
   		   function(msg){
   				if (msg.type=="success") {
   				   layer.open({
   						content: '操作成功'
   						,skin: 'msg'
   						,time: 2 
   					});
   				   setTimeout('window.location.href = "index.jhtml";', 2000);
   				} else {
   					layer.open({
   						content: msg.content
   						,skin: 'msg'
   						,time: 2 
   					});
   				}
   		   });
    	
    });
</script>
</html>
