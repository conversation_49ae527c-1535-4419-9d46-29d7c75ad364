<!DOCTYPE html>
<html style="background: #fff;">
	<head>
		<meta charset="utf-8" />
		<title>支付失败</title>
        <meta name="apple-mobile-web-app-capable" content="yes"/>
        <meta name="apple-mobile-web-app-status-bar-style" content="black"/>
        <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js" ></script>
        <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />

		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/plugins/eraser/jquery.eraser.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js" ></script>
		<script type="text/javascript" src="https://res.wx.qq.com/open/js/jweixin-1.3.2.js"></script>

	</head>
	<body style="background: #fff;" > 
		<div class="paymentPage">
			<div class="public_top_header">
				[#if order.applyId?? ]
					<a href="${base}/trip/view.jhtml?id=${order.applyId.id}" class="return_back"></a>
				[#else]
					<a href="${base}/index.jhtml" class="return_back"></a>
				[/#if]
				订单号：${order.sn}     支付失败，原因：${msg}
				<a href="javascript:;" class="icon_detail_share" style="display: none;"></a>

			</div>
		 	<div class="suc_section">
				<a href="${base}/weixinCoupon/order/list.jhtml?type=all" class="btn_index_long">查看我的订单</a>
		 	</div>
		</div>
	</body>
</html>
