<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
     <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <title>国际机票</title>
    <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/airWay.css">
    <script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

    <script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>

</head>
 [#assign current = "mall" /]
<body class="airwayIndexPage">

    <div class="public_top_header">
        [#if tag == "newTrip"]
            <a href="${base}/travelHome/index.jhtml" class="return_back"></a>
        [#else ]
                <!-- [#if applyId?? ] -->
            <a href="${base}/trip/index.jhtml" class="return_back"></a>
                <!-- [#else] -->
            <a href="[#if yphFlag??]${base}/index.jhtml[#else]${base}/travel_service/index.jhtml?sysModuleId=162[/#if]" class="return_back"></a>
            <!-- [/#if] -->
        [/#if]

        	国际机票
        [#include "./wechat/include/head_nav.ftl" /] 
    </div>
    <div class="toptag">
        <img src="${base}/resources/wechat/img/airway/toptag.png" alt="">
    </div>
    <form action="${base}/plane/international/searchFlight.jhtml" method="get" id="airWayForm">
    	<!-- 表单提交数据 -->
				<input  name="depCity" id="depCity" type="hidden" value="${depCity}" />
				<input  name="arrCity" id="arrCity" type="hidden" value="${arrCity}" />
				<input  name="depCityName" id="depCityName" type="hidden" value="${depCityName}" />
				<input  name="arrCityName" id="arrCityName" type="hidden" value="${arrCityName}" />
				<input  name="depDate" id="depDate" type="hidden" value="${depDate}" />
    			<input  name="adultNum" id="adultNum" type="hidden" [#if adultNum??]value="${adultNum }"[#else]value="1"[/#if]" />
    			[#if applyId?? ]
					<input type="hidden" name="applyId" value="${applyId}"/>
				[/#if]
    			
        <div class="bgbox">
            <div class="inputBox">
                <div class="place_form_to">
                    <span class="from" id="depCityNameSpan" onclick="selectCity(true)">请选择出发城市</span>
                    <span class="icon_change"></span>
                    <span class="to" id="arrCityNameSpan" onclick="selectCity(false)">请选择目的城市</span>
                </div>
                <div class="select_time">
                    <input type="text" name="" readonly="readonly" onfocus="this.blur()" id="dateInput" value="请选择出发日期" onclick="selectDate()">
                    <label id="week"></label>
                </div>
                <div class="select_num">
                    <select name="" id="personNum">
                                <option value="1" [#if adultNum==1] selected="selected" [/#if]>1</option>
                                <option value="2" [#if adultNum==2] selected="selected" [/#if]>2</option>
                                <option value="3" [#if adultNum==3] selected="selected" [/#if]>3</option>
                                <option value="4" [#if adultNum==4] selected="selected" [/#if]>4</option>
                                <option value="5" [#if adultNum==5] selected="selected" [/#if]>5</option>
                                <option value="6" [#if adultNum==6] selected="selected" [/#if]>6</option>
                                <option value="7" [#if adultNum==7] selected="selected" [/#if]>7</option>
                                <option value="8" [#if adultNum==8] selected="selected" [/#if]>8</option>
                                <option value="9" [#if adultNum==9] selected="selected" [/#if]>9</option>
                            </select>
                    <label for="personNum">成人</label>
                </div>
            </div>
        </div>
        <div class="commitBox">
            <p class="notice">准确选择乘客人数，可享受最优价格</p>

            <input type="submit" name="" id="searchFilght" value="搜索" class="fli_btn_long fli_btn_primary">
            <input type="button" name="" id="" value="我的订单" onclick="getOrderList()" class="fli_btn_long fli_btn_yellow">

        </div>

    </form>
   [#include "./wechat/include/footer.ftl" /]

</body>
<script type="text/javascript">
var $searchFilght = $("#searchFilght");
var $searchForm = $("#searchForm");
var depCity = "${depCity}";
var arrCity = "${arrCity}";
var depCityName = "${depCityName}";
var arrCityName = "${arrCityName}";
var depDate = "${depDate}";
var adultNum="${adultNum}";
try{
	var planeSearchInfo=getCookie("planeSearchInfo");
	if(planeSearchInfo){
		var json=$.parseJSON( planeSearchInfo);
		if(json.depCity&&!depCity){
			depCity=json.depCity;
			$("#depCity").val(depCity);
		}
		if(json.arrCity&&!arrCity){
			arrCity=json.arrCity;
			$("#arrCity").val(arrCity);
		}
		if(json.depCityName&&!depCityName){
			depCityName=json.depCityName;
			$("#depCityName").val(depCityName);
		}
		if(json.arrCityName&&!arrCityName){
			arrCityName=json.arrCityName;
			$("#arrCityName").val(arrCityName);
		}
	
		if(json.depDate&&!depDate){
			 var old=Date.parse(json.depDate);
			 if(old>new Date()){
				 depDate=json.depDate;
			 }else{
				 depDate='${.now?string("yyyy-MM-dd")}';
			 }
			 $("#depDate").val(depDate);
		}
		if(json.adultNum&&!adultNum){
			adultNum=json.adultNum;
			$("#personNum option").each(function(){
				if($(this).val()==adultNum){
					$(this).attr("selected","selected");
				}
			})
		}
	}
}catch(err){
	
}

//选择出发城市
function selectCity(isDpt){
	[#if applyId?? ]
		location.href="${base}/plane/international/selectCity.jhtml?applyId=${applyId}&isdepCity="+isdepCity+"&depCity="+depCity+"&arrCity="+arrCity+"&depCityName="+depCityName+"&arrCityName="+arrCityName+"&date="+date+"&adultNum="+adultNum;
	[#else]
		location.href="${base}/plane/international/selectCity.jhtml?isDpt="+isDpt+"&depCity="+depCity+"&arrCity="+arrCity+"&depCityName="+depCityName+"&arrCityName="+arrCityName+"&depDate="+depDate+"&adultNum="+adultNum;    	  	
	[/#if]	    	 
} 



// 选择出发日期
function selectDate(){
	[#if applyId?? ]
		location.href="${base}/plane/international/selectDate.jhtml?applyId=${applyId}&depCity="+depCity+"&arrCity="+arrCity+"&depCityName="+depCityName+"&arrCityName="+arrCityName+"&date="+date+"&adultNum="+adultNum;  
	[#else]
	 	location.href="${base}/plane/international/selectDate.jhtml?depCity="+depCity+"&arrCity="+arrCity+"&depCityName="+depCityName+"&arrCityName="+arrCityName+"&depDate="+depDate+"&adultNum="+adultNum;    	  	
	[/#if]
}

// 初始化城市，日期
if(depCityName!=null && depCityName != ""){
	$("#depCityNameSpan").text(depCityName);
}
if(arrCityName!=null && arrCityName != ""){
	$("#arrCityNameSpan").text(arrCityName);
}
if(depDate!=null && depDate != ""){
	$("#dateInput").val(depDate);
}else{
	depDate='${.now?string("yyyy-MM-dd")}';
	$("#dateInput").val(depDate);
}
//初始化星期
var a = new Array("日", "一", "二", "三", "四", "五", "六");
var week=new Date(depDate).getDay();
$("#week").text("周"+a[week]);


//乘客人数改变事件
$("#personNum").change(function(){
	$("#adultNum").val($("#personNum").val())
	adultNum=$("#personNum").val();
});


// 我的订单，去订单列表
var getOrderList = function(){
	[#if applyId?? ]
		location.href ="getOrderList.jhtml?applyId=${applyId}";
	[#else]
		location.href ="getOrderList.jhtml";
	[/#if]
}


//出发城市与到达城市交换事件
$(".icon_change").on("click",function(){
	
	var _from = $("#depCityNameSpan").text();
	var _to = $("#arrCityNameSpan").text();
	$("#depCityNameSpan").text(_to);
	$("#arrCityNameSpan").text(_from);
					
	var _depCityName = $("#depCityName").val();
	var _arrCityName = $("#arrCityName").val();
	$("#depCityName").val(_arrCityName);
	$("#arrCityName").val(_depCityName);
	
	var _depCity = $("#depCity").val();
	var _arrCity = $("#arrCity").val();
	$("#depCity").val(_arrCity);
	$("#arrCity").val(_depCity);
})

			$searchFilght.click(function(){
				var depCity  =$("#depCity").val();
				var arrCity =$("#arrCity").val();
				var depDate = $("#depDate").val();
				
				var depCityName = $("#depCityName").val();
				var arrCityName = $("#arrCityName").val();
				
				if(depCityName == ""){
					layer.open({
					    content: '请填写出发地'
					    ,skin: 'msg'
					    ,time: 2 //2秒后自动关闭
					});
					return false;
				}
				if(arrCityName == ""){
					layer.open({
					    content: '请填写目的地'
					    ,skin: 'msg'
					    ,time: 2 //2秒后自动关闭
					});
					return false;
				}
				if(depDate == ""){
					layer.open({
					    content: '请填写出发时间'
					    ,skin: 'msg'
					    ,time: 2 //2秒后自动关闭
					});
					return false;
				}
				if(depCityName == arrCityName){
					layer.open({
					    content: '您选择的出发城市和到达城市相同，请重新选择'
					    ,skin: 'msg'
					    ,time: 2 //2秒后自动关闭
					});
					return false;
				}
				if(depCity.substring(0,1)==arrCity.substring(0,1)&&arrCity.substring(0,1)=="i"){
		    		layer.open({
					    content: '您搜索的为国内航班，请前往国内机票版块预订！'
					    ,skin: 'msg'
					    ,time: 2 //2秒后自动关闭
					});
		    		return false;
		    	}
				addCookie("planeSearchInfo", '{"depCity":"'+depCity+'","arrCity":"'+arrCity+'","depDate":"'+depDate+'","depCityName":"'+depCityName+'","arrCityName":"'+arrCityName+'","adultNum":"'+adultNum+'"}', {expires: 30*24 * 60 * 60});
				
			})
			
</script>
</html>