<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>我的订单</title>
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="black">
          <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">
        <!--忽略将页面中的数字识别为电话号码-->
		<meta name="format-detection" content="telephone=no" />
		<!--忽略Android平台中对邮箱地址的识别-->
		<meta content="email=no" name="format-detection"/>
        <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js" ></script>
        <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/plusWap.css" />
		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

		<script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js" ></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/dropload.min.js" ></script>
	   
	</head>
	<body>
		<div class="orderPage">
			<div class="public_top_header">
					<a href="${base}/plane/international/index.jhtml" class="return_back"></a>
				我的订单
				[#include "./wechat/include/head_nav.ftl" /]
			</div>  
		 	
		 	<div class="order_items_section">
		 		<ul class="orderUl">
		 			
		 		</ul>
		 	</div>
		 	<!-- <div class="order_list_end_tips">2019年及之前的订单可在我的订单中查询</div> -->
            [#include "./wechat/include/old_order_tips.ftl" /]
		</div>
		<script>
		$(function(){
			//加载数据
			loadData('getOrderList.jhtml', 
					{
				"isAjax":true
			}, 
					'orderPage', 'orderUl', 'POST', 'totalPages', null, function(){
				/* if(tag_scroll_top){
					$(window).scrollTop(tag_scroll_top);
				} */
				$("#totalPages").remove();
			});
		})
			// 取消订单(退票)
			function cancelOrder(orderId){
				
				
			// 看是否可退款，可退款跳退款页面
				$.ajax({
					url: '${base}/plane/canRefund.jhtml',
					type: 'GET',
					data: {
						orderId : orderId
					},
					async: true,
					success : function(data){
						if(data == true){
							location.href = "${base}/plane/refundSearch.jhtml?orderId=" + orderId;
						}else{
							layer.open({
							    content: '很抱歉该订单不能退票'
							    ,skin: 'msg'
							    ,time: 2 //2秒后自动关闭
							});
						}
					}
				});	
			}
		
			$(function() {

			})
		</script>
	</body> 
</html>
