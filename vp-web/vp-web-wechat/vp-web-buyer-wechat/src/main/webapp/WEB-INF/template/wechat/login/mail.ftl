<!DOCTYPE html>
<html>
<head>
<meta http-equiv="X-UA-Compatible" content="IE=edge" >
<meta name="renderer" content="webkit">
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("shop.password.mailTitle")}[#if systemShowPowered] - ${setting.siteName}[/#if]</title>
<meta name="author" content="${setting.siteName} Team" />
<meta name="copyright" content="${setting.siteName}" />
</head>
<body>
	<p>福利平台有新的企业申请，请查收！</p>
	<p>企业名称：${companyName}</p>
	<p>申请时间：${.now?string("yyyy-MM-dd")}</p>
	<p>所属行业：
		[#if companyIndustry == 1]
			互联网
		[#elseif companyIndustry == 2]
			房地产/建筑业
		[#elseif companyIndustry == 3]
			通信行业
		[#elseif companyIndustry == 4]
			运营商
		[#elseif companyIndustry == 5]
			金融行业
		[#elseif companyIndustry == 6]
			医疗行业
		[#elseif companyIndustry == 7]
			教育行业
		[#elseif companyIndustry == 8]
			生产/加工/制造
		[#elseif companyIndustry == 9]
			物流仓储
		[#elseif companyIndustry == 10]
			能源行业
		[#elseif companyIndustry == 11]
			政府机构/公益组织
		[#elseif companyIndustry == 12]
			休闲娱乐
		[#elseif companyIndustry == 13]
			文化传媒
		[#elseif companyIndustry == 14]
			餐饮行业
		[#elseif companyIndustry == 15]
			农林牧渔
		[#elseif companyIndustry == 16]
			旅游行业
		[#elseif companyIndustry == 17]
			环保行业
		[#elseif companyIndustry == 18]
			其他
		[/#if]
	</p>
	<p>公司规模：
		[#if companyNum == 1]
			50人以下
		[#elseif companyNum == 2]
			50-100人
		[#elseif companyNum == 3]
			100-200人
		[#elseif companyNum == 4]
			200-500人
		[#elseif companyNum == 5]
			500-1000人
		[#elseif companyNum == 6]
			1000-5000人
		[#elseif companyNum == 7]
			5000人及以上
		[/#if]</p>
	<p>联系人：${contacts}</p>
	<p>联系电话：${phone}</p>
	<p>公司邮箱：${email!'无'}</p>
	<p>联系地址：${areaId!'无'}</p>
	<p>介绍人姓名：${supplierName!'无'}</p>
	<p>介绍人账号：${supplierUsername!'无'}</p>
	<p>介绍人联系方式：${supplierMobile!'无'}</p>
	[#if enterCompany??]
		<p>申请来源：${enterCompany.companyAbbreviation!enterCompany.companyName}</p>
	[#else]
		<p>申请来源：官网</p>
	[/#if]
</body>
</html>