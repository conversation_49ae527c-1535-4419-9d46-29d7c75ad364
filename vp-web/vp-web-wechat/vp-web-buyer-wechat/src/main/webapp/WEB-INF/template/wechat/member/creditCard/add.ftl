<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<title>生活服务</title>
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">
		<script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/lifeService.css" />
		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

		<script type="text/javascript" src="${base}/resources/wechat/js/jquery.validate.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
		
		<script src="${base}/resources/wechat/js/yespayArea.js"></script>
		
	</head>
	 [#assign current = "mall" /]
	<body class="has_fixed_footer">
		<div class="public_top_header">
            <span class="return_back" onclick="javascript:history.back();"></span>
			绑定信用卡
		</div>
		
		<div class="creditCardPages">
		 	<div class="public_form_section">
		 		<form action="save.jhtml" id="inputForm" method="post">
			 		<div class="items">
			 			<div class="item">
			 				<label>姓名</label>
			 				<div class="inputs standard_input">
			 					<input type="text" name="bankAccountName" id="bankAccountName" value="" placeholder="持卡人姓名" class="input"/>
			 					<span class="icon_clear"></span>
			 				</div>
			 			</div>
			 			<div class="item">
			 				<label>发卡城市</label>
			 				<div class="inputs city_select">
					 			<select name="bankProvince" id="province" class="input_select">
					 				<option value="">请选择</option>
					 			</select>
					 			<select name="bankCity" id="bankCity" style="display: none;" class="input_select">
					 				<option value="">请选择</option>
					 			</select>
					 		</div>
			 			</div>
			 			<div class="item">
			 				<label>发卡银行</label>
			 				<div class="inputs city_select">
					 			<select name="bankName" id="bankName" class="input_select">
					 				<option value="">请选择开户行</option>
									<option value="工商银行">工商银行 </option>
									<option value="农业银行">农业银行 </option>
									<option value="中国银行">中国银行 </option>
									<option value="建设银行">建设银行 </option>
									<option value="农业发展银行">农业发展银行 </option>
									<option value="交通银行">交通银行 </option>
									<option value="中信银行">中信银行 </option>
									<option value="光大银行">光大银行 </option>
									<option value="华夏银行">华夏银行 </option>
									<option value="民生银行">民生银行 </option>
									<option value="广发银行">广发银行 </option>
									<option value="平安银行">平安银行 </option>
									<option value="招商银行">招商银行 </option>
									<option value="兴业银行">兴业银行 </option>
									<option value="浦发银行">浦发银行 </option>
									<option value="北京银行">北京银行 </option>
									<option value="吉林银行">吉林银行 </option>
									<option value="哈尔滨银行">哈尔滨银行 </option>
									<option value="龙江银行">龙江银行 </option>
									<option value="上海银行">上海银行 </option>
									<option value="江苏银行">江苏银行 </option>
									<option value="宁波银行">宁波银行 </option>
									<option value="海南银行">海南银行 </option>
									<option value="江苏江南农村商业银行">江苏江南农村商业银行 </option>
									<option value="天津农村商业银行">天津农村商业银行 </option>
									<option value="徽商银行">徽商银行 </option>
									<option value="上海农村商业银行">上海农村商业银行 </option>
									<option value="北京农村商业银行">北京农村商业银行 </option>
									<option value="河北省农村信用社">河北省农村信用社 </option>
									<option value="山西省农村信用社">山西省农村信用社 </option>
									<option value="辽宁省农村信用社">辽宁省农村信用社 </option>
									<option value="吉林省农村信用社">吉林省农村信用社 </option>
									<option value="黑龙江省农村信用社">黑龙江省农村信用社 </option>
									<option value="江苏省农村信用社">江苏省农村信用社 </option>
									<option value="浙江省农村信用社">浙江省农村信用社 </option>
									<option value="安徽省农村信用社">安徽省农村信用社 </option>
									<option value="福建省农村信用社">福建省农村信用社 </option>
									<option value="江西省农村信用社">江西省农村信用社 </option>
									<option value="山东省农村信用社">山东省农村信用社 </option>
									<option value="河南省农村信用社">河南省农村信用社 </option>
									<option value="湖北省农村信用社">湖北省农村信用社 </option>
									<option value="湖南省农村信用社">湖南省农村信用社 </option>
									<option value="广东省农村信用社">广东省农村信用社 </option>
									<option value="深圳农村商业银行">深圳农村商业银行 </option>
									<option value="海南省农村信用社">海南省农村信用社 </option>
									<option value="贵州省农村信用社">贵州省农村信用社 </option>
									<option value="云南省农村信用社">云南省农村信用社 </option>
									<option value="陕西省农村信用社">陕西省农村信用社 </option>
									<option value="甘肃省农村信用社">甘肃省农村信用社 </option>
									<option value="青海省农村信用社">青海省农村信用社 </option>
									<option value="宁夏农村信用社">宁夏农村信用社 </option>
									<option value="新疆农村信用社">新疆农村信用社 </option>
									<option value="邮政银行">邮政银行 </option>
									<option value="村镇银行">村镇银行 </option>
									<option value="其它银行">其它银行 </option>
					 			</select>
					 		</div>
			 			</div>
			 		</div>
			 		
			 		<div class="items">
			 			<div class="item">
			 				<label>银行卡号</label>
			 				<div class="inputs standard_input">
			 					<input type="number" name="bankAccountNo" id="bankAccountNo" value="" class="input"/>
			 					<span class="icon_clear"></span>
			 				</div>
			 			</div>
			 			<div class="item">
			 				<label>确认卡号</label>
			 				<div class="inputs standard_input">
			 					<input type="number" name="bankAccountNo1" id="bankAccountNo1" value="" class="input"/>
			 					<span class="icon_clear"></span>
			 				</div>
			 			</div>
			 		</div>
			 		
			 		<div class="btn_submit_box">
			 			<input type="button" value="取消" class="btn_main btn_primary form_submit_half" onclick="location.href='index.jhtml'"/>
			 			<input type="submit" value="提交" class="btn_main btn_primary form_submit_half"/>
			 		</div>
		 		</form>
		 	</div>
			
			<div class="public_info_bottom">
				<div class="title">信用卡还款说明：</div>
				<div class="content">
					<p>1. 仅支持<span class="text_red">预付费积分</span>，信用卡还款收取6%的服务费。</p>
					<p>2. 每日每张信用卡只处理一笔还款申请，未处理的还款申请将自动顺延到下个工作日处理；</p>
					<p>3. 每月每张信用卡最多只处理十笔还款申请，未处理的还款申请将自动顺延到下月处理；</p>
					<p>4. 发卡银行、发卡城市、信用卡卡号、持卡人姓名，必须与银行信息完全匹配，否则将导致还款失败或错还到他人账户</p>
					<p>5. 不同银行还款到账时间可能不同，一般需要2－5个工作日；请自行决定还款时间，并承担可能由于还款不及时带来的后果。</p>
					<p>6. 还款银行需对卡号进行验证，可能导致部分卡还款失败；若出现类似情况，请更换其他信用卡重新还款。</p>
				</div>
			</div>
		</div>
		<!--此处引入公共尾部代码  开始-->
		[#include "./wechat/include/footer.ftl" /]
		<!--此处引入公共尾部代码  结束-->
		
		
		<script> 
			
		   
			$(function(){
				$.each(baseArea,function(i,val){
					$("#province").append('<option value="'+val.name+'" areacode="'+val.areacode+'">'+val.name+'</option>');
				});
				$("#province").change(function(){
					$("#bankCity").show();
					$("#bankCity").empty();
					$("#bankCity").append('<option value="">请选择</option>');
					var areaCode=$(this).find("option:selected").attr("areacode");
					$.each(childArea["y"+areaCode],function(i,val){
						
						$("#bankCity").append('<option value="'+val.name+'">'+val.name+'</option>');
					});
				});
				$("#inputForm").validate({
					rules: {
						bankAccountName: {
							required:true
						},
						bankCity: {
							required:true
						},
						bankName: {
							required:true
						},
						bankAccountNo: {
							required:true,
							digits:true,
							rangelength:[15,16],
							remote: {
			                    type: "post",
			                    url: "isExists.jhtml",
			                    data: {
			                    	bankAccountNo: function() {
			                            return $("#bankAccountNo").val();
			                        }
			                    }
							}
						},
						bankAccountNo1: {
							required:true,
							equalTo: "#bankAccountNo"
						}
					},
					submitHandler: function(form) {
						$("input[type='submit']").prop("disabled", true);
						form.submit();
				    },messages:{
				    	bankAccountNo:{remote:"卡号已经存在"}
				    },
					errorPlacement: function(error, element) {  
					    error.appendTo(element.parent().parent());  
					}
				});
				
				
				
			
				
				
				
				
			})
			
		</script>
	</body>
</html>
