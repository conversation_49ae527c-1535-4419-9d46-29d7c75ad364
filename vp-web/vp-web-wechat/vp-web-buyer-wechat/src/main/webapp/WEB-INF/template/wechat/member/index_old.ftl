<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>个人中心</title>
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="black">
        <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js" ></script>
        <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/plusWap.css" />
		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>

		<script type="text/javascript" src="${base}/resources/wechat/js/swiper.jquery.js" ></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
		
		<script type="text/javascript">
		   	$(function(){
		      refreshCount();
		   	});
		 
		   	function refreshCount(){
		     	jQuery.post("${base}/member/refreshCount.jhtml","",function(data){
		        	if(data!=null){ 
		           		if(data.totalCard>0){
		           			if(data.totalCard>99){
		           				$("#myCard").append('<label class="num_circle">99</label>');
		           			}else{
		           			 	$("#myCard").append('<label class="num_circle">'+data.totalCard+'</label>');
		           			}
		           		}
			           	if(data.totalFavorite>0){
			           		if(data.totalFavorite>99){
			           			$("#totalFavorite").append('<label class="num_circle">99</label>');
			           		}else{
			           			$("#totalFavorite").append('<label class="num_circle">'+data.totalFavorite+'</label>');
			           		}
				        }
		           		if(data.totalCoupon>0){
			           		if(data.totalCoupon>99){
			           			$("#myCoupon").append('<label class="num_circle">99</label>');
			           		}else{
			           			 $("#myCoupon").append('<label class="num_circle">'+data.totalCoupon+'</label>');
			           		}
		           		}
			           	if(data.totalQuantity>0){
			           		if(data.totalQuantity>99){
			           			$("#myCart").append('<label class="num_circle">99</label>');
			           		}else{
			           			 $("#myCart").append('<label class="num_circle">'+data.totalQuantity+'</label>');
			           		}
			           	}
		        	}
		     	},"json"); 
		   	}
		</script>
	</head>
	<body class="has_fixed_footer">
	[#assign current = "my" /]
	[#include "./wechat/include/footer.ftl" /]
		<div class="userCenterPage">
			<div class="public_top_header">
				<a href="javascript:history.go(-1);" class="return_back"></a>
				个人中心 
			</div>
			<section class="user_info">
				<div class="img">
					[#if member.image]
						<img src="${base}${member.image}" />
					[#else]
						[#if member.gender == 'female']
							<img src="${base}/resources/wechat/img/girl.png" />
						[#else]
							<img src="${base}/resources/wechat/img/boy.png" />
						[/#if]
					[/#if]
				</div>
				<div class="info">
					<p>姓名:${member.name}</p>
					<p>工号:${member.username}</p>
				</div>
			</section>
			
			<section class="coin_section">
				<label>当前积分</label>
				[#if coins!=null && coins?size>0]
				   	[#list coins as coin]
				   		<div class="coin_list">
				   			<div class="item text-ellipsis">
					   			<span>${coin.coinTypeId.name}<b>${coin.balance?string('#.##')}</b></span>
					   		</div>
				   		</div>
				   	[/#list]
				[#else]
				   <span>积分余额<b>0</b></span>
				[/#if]
				
			</section> 
			
			<section>
				<ul class="list">
					<li>
						<a href="${base}/member/order/list.jhtml?type=unpaid">
							<span class="icon_header_1"></span>
							<div class="text">待付款</div> 
						</a>
					</li>
					<li>
						<a href="${base}/member/order/list.jhtml?type=confirmed">
							<span class="icon_header_2"></span>
							<div class="text">待发货</div> 
						</a>
					</li>
					<li>
						<a href="${base}/member/order/list.jhtml?type=shipped">
							<span class="icon_header_3"></span>
							<div class="text">待收货</div> 
						</a>
					</li>
					<li>
						<a href="${base}/member/order/list.jhtml?type=evaluation">
							<span class="icon_header_4"></span>
							<div class="text">待评价</div> 
						</a>
					</li>
					<li>
						<a href="${base}/member/order/returnsList.jhtml">
							<span class="icon_header_5"></span>
							<div class="text">退款/售后</div> 
						</a>
					</li>
				</ul>
				
			</section>
			
			
			<section class="functionSection">
				<a href="${base}/member/order/list.jhtml?type=all" class="fli_link_line">
					<span class="icon_list_1"></span>我的订单
				</a>

				<a href="receivedCard.jhtml" id="myCard" class="fli_link_line" [#if  member!=null && member.companyId.hiddenModules==0]style="display: none"[/#if]>
					<span class="icon_list_2"></span>我的贺卡
				</a>

				<a href="${base}/member/coin.jhtml" class="fli_link_line">
					<span class="icon_list_3"></span>我的积分
				</a>
				[#if coinBuyerFlag?? && coinBuyerFlag == "true"]
				<a href="${base}/member/coinTransfer/transferList.jhtml" class="fli_link_line">
					<span class="icon_list_12"></span>积分收购
				</a>
				[/#if]

				<a href="${base}/member/white_bar.jhtml" class="fli_link_line" [#if  member!=null && member.companyId.hiddenModules==0]style="display: none"[/#if]>
					<span class="icon_list_10"></span>我的白条
				</a>

				<a href="${base}/member/favorite/list.jhtml" id="totalFavorite" class="fli_link_line">
					<span class="icon_list_9"></span>我的关注
				</a>
				<a href="${base}/cart/list.jhtml" id="myCart" class="fli_link_line">
					<span class="icon_list_4"></span>购物车
				</a>
				
				<a href="getCoupons.jhtml?type=1" id="myCoupon" class="fli_link_line" [#if  member!=null && member.companyId.hiddenModules==0]style="display: none"[/#if]>
					<span class="icon_list_5"></span>优惠券
				</a>
				[#if yhshFlag??] [#else]
				<a href="${base}/member/information.jhtml" class="fli_link_line">
					<span class="icon_list_6"></span>个人资料
				</a>
				[/#if]
				<a href="${base}/member/address.jhtml" class="fli_link_line">
					<span class="icon_list_7"></span>地址管理
				</a>
				<a href="${base}/recharge/cardActive.jhtml" class="fli_link_line">
					<span class="icon_list_8"></span>卡密激活
				</a>
				[#if comPersonConf && com_person_moreAtts_qrcode != null && "close" == com_person_moreAtts_qrcode]
				[#else]
				<a href="${base}/member/qrcode.jhtml" class="fli_link_line" [#if  member!=null && member.companyId.hiddenModules==0]style="display: none"[/#if]>
					<span class="icon_list_11"></span>企业注册码
				</a>
				<a href="http://wpa.qq.com/msgrd?v=3&uin=2307282083" id="kefu" class="fli_link_line">
					<span class="icon_list_9"></span>联系客服
				</a>
				[/#if]
			</section>
			
			[#if yhshFlag??]
			
			[#else]
				<input type="button" onclick="javascript:location.href='${base}/logout.jhtml'" value="退出当前账号" class="logout"/>
			[/#if]

		</div>
		
		
	</body>  
</html>
