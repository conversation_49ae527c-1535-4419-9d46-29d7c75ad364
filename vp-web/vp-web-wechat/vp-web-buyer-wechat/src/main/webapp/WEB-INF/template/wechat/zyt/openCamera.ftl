<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>扫描二维码</title>
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
    <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/lifeService.css" />

    <script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
    <script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script> 
    <script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js" ></script>
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    <script>
        $(document).ready(function () {
            var ua = navigator.userAgent.toLowerCase();
            var isWeixin = ua.indexOf('micromessenger') !== -1;
            if (!isWeixin) {
                layer.open({
                    title: '提示',
                    content: '请前往福利平台公众号或小程序操作。',
                    btn: ['确定'],
                    yes:function () {
                        window.history.back(-1);
                    }
                });
            }
            var link = location.href;
            $.ajax({
                url: "${base}/act/getSignature.jhtml?url=" + link,
                type: "GET",
                cache: false,
                dataType: "json",
                success: function (data) {
                    wx.config(data);
                    wx.error(function (res) {
                        window.location.reload()
                    })
                    wx.ready(function () {
                        try {
                            wx.scanQRCode({
                                needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
                                scanType: ["qrCode"], // 可以指定扫二维码还是一维码，默认二者都有
                                success: function (res) {
                                    var result = res.resultStr; // 当needResult 为 1 时，扫码返回的结果
                                    $.ajax({
                                        url: "${base}/zyt/scanQrCode.jhtml?result="+result,
                                        type: "GET",
                                        //data: {'result':result},
                                        cache: false,
                                        dataType: "json",
                                        success: function (msg) {
                                            if (msg.type == "success") {
                                                window.location.href = msg.content;
                                            } else {
                                                layer.open({
											  	    type:1,
												    content: '二维码无效，请重新扫码！',
												    skin: 'msg',
												    time: 2 ,
												    end:function(){
												    	window.location.reload();
												    }
												});
                  
                                            }
                                        }
                                    })
                                },
                                cancel: function () {
                                    window.history.back(-1);
                                }
                            });
                        }catch (e) {
                            
                        }
                    })
                }
            })
        })
    </script>
</head>
<body>
</body>
</html>
