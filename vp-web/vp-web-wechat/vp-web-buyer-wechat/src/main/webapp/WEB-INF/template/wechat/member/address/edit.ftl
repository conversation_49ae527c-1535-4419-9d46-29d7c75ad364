<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>收货地址</title>
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="black">
        <script type="text/javascript" src="${base}/resources/wechat/js/flexible.js" ></script>
        <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
        <link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/form.css" />
		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/jquery.validate.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/es6/fliplus_city_selector.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js" ></script>
	</head>
	<body> 
		<div class="loginPage edit_info"> 
			<div class="public_top_header">
				<a href="javascript:history.go(-1);" class="return_back"></a>
				[#--<span class="return_back" onclick="javascript:location.href='${base}/member/address.jhtml[#if pageSource??&&pageSource=="weixincoupon"]?pageSource=weixincoupon[/#if]';"></span>--]
				修改收货地址
				[#if !(pageSource??&&pageSource=="weixincoupon")]
					[#include "./wechat/include/head_nav.ftl" /]
				[/#if]

			</div>
			
			
			<form id="inputForm" method="post">
				<div class="form_section"> 
					<input type="hidden" name="id" value="${receiver.id}">
					<div class="item standard_input">
						<label>收货人:</label>
						<div class="inputs">
							<input type="text" name="consignee" value="${receiver.consignee}" class="input"/>
							<span class="icon_clear"></span>
						</div>
					</div>
					<div class="item standard_input">
						<label>手机号码:</label>
						<div class="inputs">
							<input type="tel" name="phone" value="${receiver.phoneDec}" class="input Tel"/>
							<span class="icon_clear"></span>
						</div>
					</div>
					<div class="item arrow_right_line">
						<label>所在地区:</label>
						<div class="inputs">
							<input type="text" name="areaName" id="areaName" value="${receiver.areaName}" class="input fliplus_city_selector" readonly="readonly"/>
							<input type="hidden" name="areaId" id="areaId" value="${(receiver.area.id)!}" />
							<input type="hidden" id="treePath" value="${(receiver.area.treePath)!}${(receiver.area.id)!}" />
						</div>
					</div>
					<div class="item standard_input">
						<label>详细地址:</label>
						<div class="inputs">
							[#--<input type="text" name="address" id="address" value="${receiver.address}" placeholder="街道、门牌号等" class="input" readonly="readonly"--]
							[#--onclick="selectAddress()" unselectable="on" onfocus="this.blur()"/>--]
                            <input type="text" name="address" id="address" value="${receiver.address}" placeholder="街道、门牌号等" class="input"/>
							<span class="icon_clear"></span>
						</div>
					</div>
					<div class="item standard_input">
						<label>楼号/门牌:</label>
						<div class="inputs">
							<input type="text" name="number" value="${(receiver.number)!}" placeholder="" class="input"/>		
						</div>
					</div>
					<div class="item standard_input">
						<label>邮编:</label>
						<div class="inputs">
							<input type="number" name="zipCode" value="${receiver.zipCode}" class="input"/>
							<span class="icon_clear"></span>
						</div>
					</div>
					<div class="item default" [#if receiver.isDefault]style="display: none;" [/#if]>
						<p>默认地址</p>
						<p>注：每次下单时会使用该地址</p>
						<div class="switch off"> 
							<div class="sButton"></div>
							[#if receiver.isDefault]
								<input type="hidden" id="isDefault" name="isDefault" id="" value="true" />
							[#else]
								<input type="hidden" id="isDefault" name="isDefault" id="" value="false" />
							[/#if]
						</div> 
					</div>
				</div>
				<input type="hidden" name="lat" id="lat" value="${receiver.lat}">
				<input type="hidden" name="lng" id="lng" value="${receiver.lng}">
				<input type="submit" value="保存并使用" class="btn_submit_long"/> <!--disabled="disabled"-->
			</form>
			
		</div>
		
		<script>
			$(function(){
                $('.Tel').on('input', function() {
                    $(this).val($(this).val().trim().toString().replace(/\s/g,""));
                });
                $("#areaName").fliplusCitySelector();
				var mapAddress="${mapAddress!}";
				if(mapAddress){
					var mapAddressArr=mapAddress.split("_");
					if(mapAddressArr.length>=3){
						$("#address").val(mapAddressArr[3]+" "+mapAddressArr[2]);
						$("#lng").val(mapAddressArr[0]);
						$("#lat").val(mapAddressArr[1]);
					}
				}
				var $submit = $(":submit");
				
				jQuery.validator.addMethod("isTel", function(value,element) {   
			        var length = value.length;   
			        // var mobile = /^(((13[0-9]{1})|(15[0-9]{1})|(18[0-9]{1}))+\d{8})$/;
                    var mobile = commonRule.phone;
			        var tel = /^(\d{3,4}-?)?\d{7,9}$/g;       
			        return this.optional(element) || tel.test(value) || (length==11 && mobile.test(value));   
			    }, "请正确填写您的联系方式"); 
			    
				// 表单验证
				$("#inputForm").validate({
					rules: {
						consignee: {
							required:true,
							maxlength:40
						},
						phone: {
							required:true,
							minlength: 11,
						    maxlength: 11
						},
						areaId: {
							required:true
						},
						address: {
							required:true,
							maxlength:100
						},
						zipCode: {
						    number:true,
							maxlength:20
						}
					},
					submitHandler: function() {
				      	var param = $("#inputForm").serialize();
						$.ajax({
							url:'${base}/member/updateAddress.jhtml',
							type:'POST',
							data:param,
							cache:false,
							async:false,
							beforeSend:function(){
								$submit.prop("disabled", true);
							},
							success:function(msg){
								if(msg){
									layer.open({
									    content: '编辑地址成功'
									    ,skin: 'msg'
									    ,time: 2 //2秒后自动关闭
									  });

                                    var timer = setTimeout(function () {
                                        var formPage = getQueryVariable("formPage");
                                        var jumpUrl = "${base}/member/address.jhtml";
                                        if(formPage&&formPage==='dangaoss'){
                                            jumpUrl = "${base}/dangaoss2/selectAddress.jhtml";
										}
										[#if pageSource??&&pageSource=="weixincoupon"]
											jumpUrl += "?pageSource=weixincoupon";
										[/#if]
                                        location.href = jumpUrl;
                                        clearTimeout(timer);
                                    },300);
								}else{
									$submit.prop("disabled", false);
									layer.open({
									    content: '编辑地址失败'
									    ,skin: 'msg'
									    ,time: 2 //2秒后自动关闭
									  });
								}
							}
						});
				    },
				   	errorPlacement: function(error, element) {  
					    error.appendTo(element.parent().parent());  
					}
				});
			
				$(".btn_cornor_long").click(function(){
					location.href="personInfo.html";
					return false;
				})
				
				$(".switch").click(function(){
					$(this).toggleClass("off")
					var isDefault = $(this).hasClass("off");
					if(!isDefault){
						//默认地址
						$("#isDefault").val("true");
					}else{
						//不是默认地址
						$("#isDefault").val("false");
					}
				})
				
			})
			function selectAddress(){
				var areaName="${receiver.area.splitArea}";
				if(areaName){
					var arr=areaName.split(",");
					var area=arr[arr.length-1];
					var areaNameInput=$("input[name='areaName']").val();
					if(areaNameInput&&areaNameInput.indexOf(" ")!=-1){
						arr=areaNameInput.split(" ");
						area=arr[arr.length-1];
					}
					var province=arr[0];
					var city=arr[1];
					if(province.indexOf("北京")!=-1||
							province.indexOf("上海")!=-1||
							province.indexOf("重庆")!=-1||
							province.indexOf("天津")!=-1){
						city=province;
					}
					
					
					var currentUrl = "${base}/member/editAddress.jhtml";
					[#if pageSource??&&pageSource=="weixincoupon"]
						currentUrl+="?pageSource=weixincoupon";
					[/#if]
					if(currentUrl.indexOf("?")!=-1){
						currentUrl+="&"
					}else{
						currentUrl+="?"
					}
					currentUrl+=$("#inputForm").serialize();
					var lat=$("#lat").val();
					if(!lat){
						lat="";
					}
					var lng=$("#lng").val();
					if(!lng){
						lng="";
					}
					var url='selectAddress.jhtml?province='+province+'&city='+city+"&area="+area+"&url="+encodeURIComponent(currentUrl)+"&lat="+lat+"&lng="+lng;
					location.href=url;
				}else{
					
					layer.open({
					    content: '请先选择区域'
					    ,skin: 'msg'
					    ,time: 2 //2秒后自动关闭
					  });
				}
			}
		</script>
	</body>  
</html>
