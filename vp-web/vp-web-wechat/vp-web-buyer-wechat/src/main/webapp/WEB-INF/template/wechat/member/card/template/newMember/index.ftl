<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>新人体验</title> 
		<meta name="format-detection" content="telephone=no" /> 
		<meta content="email=no" name="format-detection"> 
        <meta name="apple-mobile-web-app-capable" content="yes"> 
        <meta name="apple-mobile-web-app-status-bar-style" content="black">
        <script type="text/javascript" src="${base}/resources/greetingCard/js/flexible.js" ></script>
        <link rel="stylesheet" href="${base}/resources/greetingCard/css/swiper.css?v=1.0.0" />
        <link rel="stylesheet" href="${base}/resources/greetingCard/css/animate.css?v=1.0.0" />
        <link rel="stylesheet" type="text/css" href="${base}/resources/greetingCard/css/common.css?v=1.0.0" /> 
        <link rel="stylesheet" type="text/css" href="${base}/resources/greetingCard/template/newMember/css/style.css?v=1.0.0" />  
		<script type="text/javascript" src="${base}/resources/greetingCard/js/jquery-1.9.1.js" ></script> 
	</head>
	<body>
		<div class="loading_section">
			<div class="loading"></div>
		</div>
		<div class="public_top_header">
			<a href="javascript:history.go(-1);" class="return_back"></a>
			我的贺卡
		</div>
		
		<div id="music" class="music">
			<audio id="music-audio" class="audio" loop="" autoplay="autoplay" preload="">
				<source src="${base}/resources/greetingCard/music/New_Year's_Day.mp3" type="audio/mpeg"></source>
			</audio>
			<div class="control">
				<div class="control-after"></div>
			</div>	
		</div>
		
		<!-- Swiper -->
	    <div class="swiper-container custom">
	        <div class="swiper-wrapper">
	            <div class="swiper-slide slide_1">
	            	<div class="img_logo animated bounceInDown"><img src="${base}/resources/greetingCard/template/newMember/img/img_logo.png" /></div>
	            	<div class="img_text animated bounceInDown"><img src="${base}/resources/greetingCard/template/newMember/img/img_text_welcome.png" /></div>
	            	<div class="img_main animated zoomIn"><img src="${base}/resources/greetingCard/template/newMember/img/img_1.png" /></div>
	            	<p class="animated bounceInLeft"><img class="animated fadeIn" src="${base}/resources/greetingCard/template/newMember/img/img_text_1.png"/></p>
	            </div>
	            <div class="swiper-slide slide_2">
	            	<div class="textarea animated">
	            		<div class="img_logo animated">
	            			<img src="${base}/resources/greetingCard/template/newMember/img/img_logo.png"/>
	            		</div>
	            		<div class="img_main animated">
	            			<img src="${base}/resources/greetingCard/template/newMember/img/img_2.png"/>
	            		</div>
	            		${cardContent}
	            	</div> 
	            </div>
	            [#assign phoneTitle="欢迎您" /]
	            [#include "/wechat/member/card/template/commonGift.ftl" /]
	        </div>
	        <!-- Add Pagination -->
	        <div class="swiper-pagination"></div>
	    </div> 
	    <div class="to_next_page animated infinite slideInUpS"></div>
	    
	    <script type="text/javascript" src="${base}/resources/greetingCard/js/swiper.jquery.min.js" ></script>
	    <!-- Initialize Swiper -->
	    <script>
		    var swiper = new Swiper('.swiper-container', {
		        pagination: '.swiper-pagination',
		        direction: 'vertical',
		        loop: true,  
		        paginationClickable: true, 
		        onSlideChangeEnd: function(swiper){  
			        if(swiper.activeIndex == 1||swiper.activeIndex == 4){
			       		$(".slide_1 .img_logo,.slide_1 .img_text").show().addClass("bounceInDown");
			       		$(".slide_1 .img_main").show().addClass("zoomIn");
			        	$(".slide_1 p").show().addClass("bounceInLeft");
			        	$(".slide_1 p img").show().addClass("fadeIn");
			        	$(".slide_2 .img_logo").hide().removeClass("fadeInLeftBig");
			        	$(".slide_2 .img_main").hide().removeClass("fadeInRightBig");
			        	$(".slide_2 .textarea").hide().removeClass("bounceInUp");
			        	$(".showBoxPage .img_1").hide().removeClass("slideInDown");
			        }else if(swiper.activeIndex == 2){
			        	$(".slide_1 .img_logo,.slide_1 .img_text").hide().removeClass("bounceInDown");
			       		$(".slide_1 .img_main").hide().removeClass("zoomIn");
			        	$(".slide_1 p").hide().removeClass("bounceInLeft");
			        	$(".slide_1 p img").hide().removeClass("fadeIn");
			       		$(".slide_2 .img_logo").show().addClass("fadeInLeftBig");
			        	$(".slide_2 .img_main").show().addClass("fadeInRightBig");
			        	$(".slide_2 .textarea").show().addClass("bounceInUp");
			        	$(".showBoxPage .img_1").hide().removeClass("slideInDown");
			        }else if(swiper.activeIndex == 3||swiper.activeIndex == 0){
			        	$(".slide_1 .img_logo,.slide_1 .img_text").hide().removeClass("bounceInDown");
			       		$(".slide_1 .img_main").hide().removeClass("zoomIn");
			        	$(".slide_1 p").hide().removeClass("bounceInLeft");
			        	$(".slide_1 p img").hide().removeClass("fadeIn");
			        	$(".slide_2 .img_logo").hide().removeClass("fadeInLeftBig");
			        	$(".slide_2 .img_main").hide().removeClass("fadeInRightBig");
			        	$(".slide_2 .textarea").hide().removeClass("bounceInUp");
			       		$(".showBoxPage .img_1").show().addClass("slideInDown");
			        }
			    }
		    });
	    </script>
	</body>
</html>
