<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<title>酒店预订</title>
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">
		<script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/lifeService.css" />
		<script src="${base}/resources/wechat/plugins/jquery/jquery-1.9.1.min.js"></script>
		<script src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>
		
	    <!--日期选择-->
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/plugins/mobiscroll-Javascript/css/mobiscroll.javascript.min.css" />
		<script src="${base}/resources/wechat/plugins/mobiscroll-Javascript/js/mobiscroll.javascript.min.js"></script>
	    
	    <script type="text/javascript" src='https://webapi.amap.com/maps?v=1.4.2&key=4a856266555fc9033b00957de075564a'></script>
	    <!-- UI组件库 1.0 -->
	    <script src="https://webapi.amap.com/ui/1.0/main.js?v=1.0.11"></script>
	</head>
	<body class="has_fixed_footer">
		<div class="public_top_header">
			[#if applyId?? ]
				<a href="${base}/trip/index.jhtml" class="return_back"></a>
			[#else]
				<a href="[#if yphFlag??]${base}/index.jhtml[#else]${base}/travel_service/index.jhtml?sysModuleId=162[/#if]" class="return_back"></a>
			[/#if]
			酒店预订
		[#include "./wechat/include/head_nav.ftl" /]
		</div>
        [#include "./wechat/include/footer.ftl" /]
		<div class="hotel_index_page">
			<div class="img_banner">
				<img src="${base}/resources/wechat/img/recharge/pic_hotel.png"/>
			</div>

            <div class="lifePubTabSection">
                <div class="header">
                    <div class="item active">公务出行</div>
                    <div class="item">私人旅行</div>
                </div>
                <div class="content">
                    <div class="boxItem">
                        <div class="item select chailvItem">
                            <label>差旅申请单</label>
                            <div class="value text-ellipsis">
                                差旅申请单差旅申请单差旅申请单差旅申请单差旅申请单差旅申请单
                            </div>
                        </div>
                        <div class="hotel_order_form">
                            <div class="item_location">
                                <input  type="text" name="fromCityName" id="fromCityName" readonly="readonly" onfocus="this.blur()" value="${fromCityName}" class="input_text" onclick='selectCity(this)'/>
                            </div>
                            <div class="item_time_select">
                                <div class="time">
                                    <label>入住</label>
                                    <input type="text" name="arrivalDate" id="arrivalDate" value="${arrivalDate}" class="input_text" readonly="readonly" />
                            
                                </div>
                                <div class="time">
                                    <label>离开</label>
                                    <input type="text" name="departureDate" id="departureDate" value="${departureDate}" class="input_text" readonly="readonly" />
                            
                                </div>
                                <div class="total_time">${diffDays}晚</div>
                            </div>
                            
                            <div class="item_input">
                                <input type="text" readonly="readonly" name="" id="" placeholder="搜索地名、地标" value="${hotelDistrictName}"  onclick="searchInfo()"/>
                            </div>
                            <div class="item_input">
                                <input type="text"  name="hotelName" id="hotelName" placeholder="酒店名搜索" value="${hotelName}" />
                            </div>
                        </div>
                        <div class="item standard chailvItem">
                            <label>差旅标准<span class="icon_tips"></span></label>
                        </div>
                    </div>
                    <div class="btn_box" style="padding: 0.933333rem 0.4rem;">
                        <input type="button" name="" id="search" value="搜索" class="fli_btn_long fli_btn_primary" />
                        <input type="button" name="" id="myOrders" value="我的订单" class="fli_btn_long fli_btn_yellow"/>
                    </div>
                </div>
            </div>
		</div>
		
		<!--日期选拔-->
		<div id="demo"></div>
        [#include "./wechat/include/chailvTemplate.ftl" /]
		<script>
		try{//从cookie获取上次搜索信息
			var hotelSearchInfo=getCookie("hotelSearchInfo");
			//console.log("获取酒店cookie信息：", hotelSearchInfo);
			if(hotelSearchInfo){
				var hotelSearchInfoJson=$.parseJSON(hotelSearchInfo);
				if(hotelSearchInfoJson.fromCityName&&$("#fromCityName").val()==""){
					$("#fromCityName").val(hotelSearchInfoJson.fromCityName);
				}
				if(hotelSearchInfoJson.arrivalDate&&$("#arrivalDate").val()==""){
					var old = Date.parse(hotelSearchInfoJson.arrivalDate);//记录的时间
					if(old > new Date()){//记录的时间比当前时间晚
						$("#arrivalDate").val(hotelSearchInfoJson.arrivalDate);
					 }else{
						 $("#arrivalDate").val('${.now?string("yyyy-MM-dd")}');
					 }
				}
				if(hotelSearchInfoJson.departureDate&&$("#departureDate").val()==""){
					var old = Date.parse(hotelSearchInfoJson.departureDate);//记录的时间
					if(old > new Date()){//记录的时间比当前时间晚
						$("#departureDate").val(hotelSearchInfoJson.departureDate);
					 }else{
						 $("#departureDate").val('${.now?string("yyyy-MM-dd")}');
					 }
				}
			} else {//取不到缓存
				$("#fromCityName").val("${fromCityName!"深圳"}");
				$("#arrivalDate").val('${.now?string("yyyy-MM-dd")}');
				$("#departureDate").val('${.now?string("yyyy-MM-dd")}');
			}
		}catch(err){

		}

	//		showCityInfo() ;
			//自动定位城市
        /*    function showCityInfo() {
                AMap.service(["AMap.CitySearch"], function() {
                    var citysearch = new AMap.CitySearch();
                     citysearch.getLocalCity(function(status, result){
                         if(status === 'complete' && result.info === 'OK'){
                             if(result && result.city && result.bounds) {
                                 var cityinfo = result.city;
                                  $("#fromCityName").val(cityinfo);
							 }else{
                                 $("#fromCityName").val("深圳");
							 }
						 }
					 })
				})
			}*/
			$("#myOrders").click(function(){
				var orderListUrl = "orderList.jhtml";
				[#if applyId?? ]
				orderListUrl = orderListUrl + "?applyId=${applyId}&source=view";
				[/#if]
				location.href=orderListUrl;
			});
		   
		   	$("#search").click(function(){
				var fromCityName=$("#fromCityName").val();
				var arrivalDate=$("#arrivalDate").val();
				var departureDate=$("#departureDate").val();
				if(!fromCityName){
					layer.open({
					    content: '请选择城市'
					    ,skin: 'msg'
					    ,time: 2 //2秒后自动关闭
					});
					return false;
				}
				if(!arrivalDate){
					layer.open({
					    content: '请选择入住'
					    ,skin: 'msg'
					    ,time: 2 //2秒后自动关闭
					});
					return false;
				}
				if(!departureDate){
					layer.open({
					    content: '请选择离店日期'
					    ,skin: 'msg'
					    ,time: 2 //2秒后自动关闭
					});
					return false;
				}
				addCookie("hotelSearchInfo", '{"fromCityName":"'+fromCityName+'","arrivalDate":"'+arrivalDate+'","departureDate":"'+departureDate+'"}', {expires: 30*24 * 60 * 60});
				//console.log("保存酒店cookie信息完成");
				var hotelName=$("#hotelName").val();
				[#if applyId?? ]
					location.href="list.jhtml?applyId=${applyId}&fromCityName="+fromCityName+"&arrivalDate="+arrivalDate+"&departureDate="+departureDate+"&hotelName="+hotelName;
				[#else]
					location.href="list.jhtml?fromCityName="+fromCityName+"&arrivalDate="+arrivalDate+"&departureDate="+departureDate+"&hotelName="+hotelName;
				[/#if]
			});
			
		   	function formatDate(date) {
                var d = new Date(date),
    			month = '' + (d.getMonth() + 1),
    			day = '' + d.getDate(),
    			year = d.getFullYear();
 
  				if (month.length < 2){
  				   month = '0' + month;
  				} 
  				if (day.length < 2){
  				   day = '0' + day;
  				} 
 
  				return [year, month, day].join('-');
			}
			$(function(){
			   var arrivalDate=new Date($("#arrivalDate").val());
		       var departureDate=new Date($("#departureDate").val());
		       var b_time=arrivalDate.getTime();
		       var e_time=departureDate.getTime();
		       var c_time = e_time - b_time;
			   var day_num = c_time/(1000*60*60*24);
			   if(day_num<=0){
				   departureDate = arrivalDate.setDate(arrivalDate.getDate()+1); 
			      departureDate=formatDate(departureDate);
			      $("#departureDate").val(departureDate); 
			      $(".total_time").text("1晚");
			   }else{
			      $(".total_time").text(day_num+"晚");
			   }
			   
//			  mapService();
			})
			
			
	/*		 function mapService(){
				var map, geolocation;
				var fromCityName ;
				
			    //加载地图，调用浏览器定位服务
			    map = new AMap.Map('container', {
    				resizeEnable: true
				});
			    map.plugin('AMap.Geolocation', function() {
			        geolocation = new AMap.Geolocation({
			            enableHighAccuracy: true,//是否使用高精度定位，默认:true
			            timeout: 10000,          //超过10秒后停止定位，默认：无穷大
			            buttonOffset: new AMap.Pixel(10, 20),//定位按钮与设置的停靠位置的偏移量，默认：Pixel(10, 20)
			            zoomToAccuracy: true,      //定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：false
			            buttonPosition:'RB'
			        });
			        map.addControl(geolocation);
			        geolocation.getCurrentPosition(function (result,data) {
				    	
				    	if(result == "complete"){
				    		fromCityName = data.addressComponent.city;
				    		if(fromCityName!=null){
				    		   var lastChar=fromCityName.substr(fromCityName.length-1,1)
				    		   if(lastChar=='市'){
				    		      fromCityName=fromCityName.substr(0,fromCityName.length-1);
				    		   }
				    		   $("#fromCityName").val(fromCityName);
				    		}
				    		
					        	
				    	}
				    });
			        
			    });
			   
			}*/
			
			
			 function searchInfo(){
			    var fromCityName=$("#fromCityName").val();
			    var arrivalDate=$("#arrivalDate").val();
			    var departureDate=$("#departureDate").val();
			    [#if applyId?? ]
			    	location.href="searchInfo.jhtml?applyId=${applyId}&type=0&fromCityName="+fromCityName+"&arrivalDate="+arrivalDate+"&departureDate="+departureDate;
			 	[#else]
			 		location.href="searchInfo.jhtml?type=0&fromCityName="+fromCityName+"&arrivalDate="+arrivalDate+"&departureDate="+departureDate;
			 	[/#if]
			 }
			 
			 function selectCity(obj){
			    var arrivalDate="${arrivalDate}";
			    var departureDate="${departureDate}";
			    var fromCityName=$(obj).val();
			    var hotelName=$("#hotelName").val();
			    [#if applyId?? ]
			    	location.href="selectCity.jhtml?applyId=${applyId}&arrivalDate="+arrivalDate+"&departureDate="+departureDate+"&type=1"+"&fromCityName="+fromCityName+"&hotelName="+hotelName;
			 	[#else]
			 		location.href="selectCity.jhtml?arrivalDate="+arrivalDate+"&departureDate="+departureDate+"&type=1"+"&fromCityName="+fromCityName+"&hotelName="+hotelName;
			 	[/#if]
			 }
		</script>
		
		
		<script>
		   
	mobiscroll.range('#demo', {
        theme: 'ios',
        lang: 'zh',
        display: 'top',
        startInput: '#arrivalDate',
        endInput: '#departureDate',
        dateFormat: 'yy-mm-dd',
        fromText: "入住时间",
        toText: "离开时间",
        startYear: new Date().getFullYear(),
        min: new Date("${newArriveDate}"),
        onBeforeClose: function (event, inst) {
            var startDate = $(".mbsc-range-btn-v-start").text();
            var endDate = $(".mbsc-range-btn-v-end").text();
            if (endDate.length == 1) {
                startDate = $("#arrivalDate").val();
                endDate = $("#departureDate").val();
            }
            var arrivalDate = new Date(startDate);
            var departureDate = new Date(endDate);
            var b_time = arrivalDate.getTime();
            var e_time = departureDate.getTime();
            var c_time = e_time - b_time;
            var day_num = c_time / (1000 * 60 * 60 * 24);
            $(".total_time").text(day_num + "晚");
        },
        onClose: function (textVale, inst) { //插件效果退出时执行 inst:表示点击的状态反馈：set/cancel
            var startDate = $(".mbsc-range-btn-v-start").text();
            var endDate = $(".mbsc-range-btn-v-end").text();
            if (endDate.length == 1) {
                startDate = $("#arrivalDate").val();
                endDate = $("#departureDate").val();
            }
            var arrivalDate = new Date(startDate);
            var departureDate = new Date(endDate);
            var b_time = arrivalDate.getTime();
            var e_time = departureDate.getTime();
            var c_time = e_time - b_time;
            var day_num = c_time / (1000 * 60 * 60 * 24);
            if (day_num <= 0) {
                departureDate = arrivalDate.setDate(arrivalDate.getDate() + 1);
                departureDate = formatDate(departureDate);
                $("#departureDate").val(departureDate);
                $(".total_time").text("1晚");
            } else {
                $(".total_time").text(day_num + "晚");
            }
        }
    });
		   
		</script>
	</body>
</html>
