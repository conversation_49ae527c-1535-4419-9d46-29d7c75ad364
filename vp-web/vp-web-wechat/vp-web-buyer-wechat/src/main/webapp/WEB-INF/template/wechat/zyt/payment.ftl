<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<title>生活服务</title>
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">
		<script type="text/javascript" src="${base}/resources/wechat/js/flexible.js"></script>
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/common.css" />
		<link rel="stylesheet" type="text/css" href="${base}/resources/wechat/css/lifeService.css" />

		<script type="text/javascript" src="${base}/resources/wechat/plugins/jquery/jquery-1.12.4.min.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/jquery.validate.js"></script>
		<script type="text/javascript" src="${base}/resources/wechat/js/common.js"></script>

		<script type="text/javascript" src="${base}/resources/wechat/js/md5.js" ></script>
		<script type="text/javascript" src="${base}/resources/wechat/plugins/layer-v3.1.1/layer.js" ></script>
	</head>
	 [#assign current = "mall" /]
	<body class="has_fixed_footer">
		<div class="public_top_header">
            <span class="return_back" onclick="javascript:history.back();"></span>
			信息确认
			[#include "./wechat/include/head_nav.ftl" /]
			
		</div>
		
		<div class="creditCardPages">
			 
		 	<div class="public_form_section">
		 		<form action="#" id="createOrder" method="post">
		 		<input name="orderToken" value="${orderToken}" type="hidden">
				<input type="hidden" id="paymentMethodId" name="paymentMethodId" maxlength="200" value="1"/>  <!--支付方式-->
				<input type="hidden" id="shippingMethodId" name="shippingMethodId" maxlength="200" value="1"/>  <!--支付方式-->
			 	<input type="hidden" name="mobile" value="${info.mobile}">
				<input type="hidden" name="amount" value="${info.amount}">
				<input type="hidden" name="timestamp" value="${info.timestamp}">
				<input type="hidden" name="uniqueIdent" value="${info.uniqueIdent}">
				<input type="hidden" name="opOrderNo" value="${info.opOrderNo}">
				<input type="hidden" name="notifyUrl" value="${info.notifyUrl}">
				<input type="hidden" name="sign" value="${info.sign}">
					<div class="items">
						<div class="item" style="overflow:hidden;">
			 				<div class="img" style="width: 0.8rem;height: 0.8rem;margin-top: 0.35rem">
								<img src="${base}/resources/wechat/img/boc/boc_icon.png" />
							</div>
							<div class="info">
			 					${product.name}
			 				</div>
			 			</div>
					</div>
			 		<div class="items" style="clear:both;">
			 			<div class="item">
			 				<label>姓名</label>
			 				<div class="inputs">
			 					${name} 
			 				</div>
			 			</div>
			 			<div class="item">
			 				<label>卡号</label>
			 				<div class="inputs">
			 					${cardNo} 
			 				</div>
			 			</div>
			 			<div class="item">
			 				<label>充值金额</label>
			 				<div class="inputs">
			 					${currency(info.amount/100)}
			 				</div>
			 			</div>
			 			<div class="item">
			 				<label>服务费</label>
			 				<div class="inputs">
			 					${coinConvert((payAmount - info.amount)/100, coinShowRate!1)}
			 				</div>
			 			</div>
			 			<div class="item">
			 				<label>订单总金额</label>
			 				<div class="inputs">
			 					${currency(payAmount/100)}
			 				</div>
			 			</div>
			 		</div>
			 		
			 		<div class="items">
			 			<div class="item">
			 				<!--<label>支付密码</label>-->
			 				<div class="inputs"> 
								<!--<input type="password" id="payPassword" value="">-->
								<input type="hidden" name="payPassword" id="orderPayId" value=""/>
			 				</div>
			 			</div>
			 		</div>
			 		<div class="btn_submit_box">
				 		<div class="btn_pay_box">
				 			<input type="button" id="submit" value="立即支付" class="btn_main btn_primary form_submit" />
				 		</div>
				 	</div>
		 		</form>
		 	</div>
		</div>
		[#include "./wechat/include/set_payPasswords.ftl" /]
		<!--此处引入公共尾部代码  开始-->
		[#include "./wechat/include/footer.ftl" /]
		<!--此处引入公共尾部代码  结束-->


        <script type="text/javascript">
            $("#submit").on("click",function(){

				layer.open({
					content: "系统维护，暂不支持充值！"
					,skin: 'msg'
					,time: 2 //2秒后自动关闭
				});

[#--			[#if member.companyId.sourceFlag == null || member.companyId.sourceFlag==""]--]
[#--				[#if member.companyId.isFreePay ]--]
//                     submitOrder();
[#--				[#else]--]
//                     $("#paymentPop").show().find("#password").focus();
[#--				[/#if]--]
[#--			[#else] <!-- 独立部署平台-->--]
//                submitAgentOrder();
[#--			[/#if]--]

                return false;
            });


		//输入密码事件
		$("#password").keyup(function() {
			var l_pwd = this.value.length;
			if (l_pwd >= 1 && l_pwd <= 6 && event.keyCode != 8) {
				var _input = document.getElementById("number" + l_pwd);
				_input.value = this.value.charAt(l_pwd - 1);
				if (l_pwd == 6) { //输入完成动作
					var _pwd = this.value;
					$("#paymentPop").hide();
					this.blur();
					var device = openDevice();
					if (device == "android") {
						vpshop_android.callHideSoftInput();
					}
					$("#orderPayId").val(hex_md5(_pwd));
					submitOrder();
				}
			} else if (event.keyCode == 8) { //退格键删除
				var _input = document.getElementById("number" + (l_pwd + 1));
				_input.value = '';
			}
		});
		
		function submitOrder() {
			$.ajax({
				url : 'createOrder.jhtml',
				type : 'POST',
				data : $("#createOrder").serialize(),
				beforeSend : function() {
					$("#submit").attr("disabled", true);
					$("#submit").val("提交中...");
				},
				success : function(data) {
					if (data) {
						if (data.type == "success") {
							location.href = "paymentSuccess.jhtml";
						} else {
							layer.open({
								content : data.content,
								skin : 'msg',
								time : 2
							//2秒后自动关闭
							});
							$("#submit").val("提交");
							// layer.msg(data.content); 
						}
					}
					$("#submit").attr("disabled", false);
					$("#inputs_box input,#password").val("")
				}
			});
		}


            function toSubmitAgentOrder() {

                $.ajax({
                    url : 'createAgentOrder.jhtml',
                    type : 'POST',
                    data : $("#createOrder").serialize(),
                    beforeSend : function() {
                        $("#submit").attr("disabled", true);
                        $("#submit").val("提交中...");
                    },
                    success : function(data) {
                        if (data) {
                            if (data.type == "success") {
                                //    location.href = "paymentSuccess.jhtml";
                                //    location.href = '${base}/pays/agentCompanyUri.jhtml?orderSn='+data.content;
                                $.ajax({
                                    url:'${base}/pays/agentCompanyUri.jhtml?orderSn='+data.content,
                                    type:'POST',
                                    success:function(data){
                                        console.log(data);
                                        if(data.type == "success"){//支付成功
                                            window.location = data.content;
                                        } else {
                                            layer.open({
                                                content: data.content
                                                ,skin: 'msg'
                                                ,time: 2 //2秒后自动关闭
                                            });
                                            //$that.prop("disabled", false);//防止重复提交
                                            return false;
                                        }
                                    }
                                });
                            } else {
                                layer.open({
                                    content : data.content,
                                    skin : 'msg',
                                    time : 2
                                    //2秒后自动关闭
                                });
                                $("#submit").val("提交");
                                // layer.msg(data.content);
                            }
                        }
                        $("#submit").attr("disabled", false);
                        $("#inputs_box input,#password").val("")
                    }
                });
            }

            function submitAgentOrder() {
                //// 限额校验
                $.ajax({
                    url : 'checkRecharge.jhtml',
                    type : 'POST',
                    data : $("#createOrder").serialize(),
                    success : function(data) {
                        if (data) {
                            if (data.type == "success") {
                                toSubmitAgentOrder();
                            } else {
                                layer.open({
                                    content : data.content,
                                    skin : 'msg',
                                    time : 2
                                    //2秒后自动关闭
                                });
                                return false;
                            }
                        }
                    }
                });
            }
	</script>
	</body>
</html>
