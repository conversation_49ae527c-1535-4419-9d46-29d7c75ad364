@charset "utf-8";
.square-body {
  padding-top: 0;
  width: 100%;
  height: 100%;
}
.square-body::-webkit-scrollbar {
  display: none;
}
.gift-main {
  background-image: linear-gradient(0deg, #45caf5 0%, #2e94fe 100%), linear-gradient(#2e94fe, #2e94fe);
  background-blend-mode: normal, normal;
  background-size: 100% 6.53rem;
  padding-bottom: 1.5rem;
  background-repeat: no-repeat;
}
.gift-main .public_top_header {
  position: relative;
  border: none;
  box-shadow: none;
  color: #fff;
  font-size: 0.48rem;
  font-weight: 540;
  background: rgba(255, 0, 0, 0);
}
.gift-main .public_top_header .return_back::before {
  border-color: #fff;
}
.gift-main .public_top_header .search {
  width: 6.5rem;
  height: 0.773rem;
  line-height: 0.773rem;
  background-color: #ffffff;
  border-radius: 0.38rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: absolute;
  top: 0.2rem;
  left: 1.6rem;
}
.gift-main .public_top_header .search form {
  display: flex;
  align-items: center;
}
.gift-main .public_top_header .search input {
  width: 2.8rem;
  background-color: #ffffff;
  margin-left: 0.5rem;
}
.gift-main .public_top_header .search .btsearch {
  width: 0.5rem;
  height: 0.5rem;
  margin-top: 0.14rem;
  margin-right: 0.4rem;
}
.gift-main .public_top_header .search .btsearch.modify {
  width: 0.58rem;
  height: 0.58rem;
  margin-top: 0;
}
.square-body .white-right-cion .public_right_nav .icon_more{
  background-image: url(../img/redEnvelope/ellipsisImg.png);
}
.gift-main .public_top_header .search .active {
  color: #2d94f9;
}
.gift-main .public_top_header .search .active:after {
  border-top: 0.15rem solid #2d94f9;
}
.gift-main .public_top_header .gift-icon-display {
  display: inline-block;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 1001;
  padding-top: 0.1rem;
  padding-right: 0.45rem;
}
.gift-main .public_top_header .gift-icon-display .gift-icon-text {
  width: 0.8rem;
  font-family: NotoSansHans-Medium;
  font-size: 0.24rem;
  line-height: 0.32rem;
  letter-spacing: 0.06rem;
  color: #cdeeff;
  display: inline-block;
}
.gift-main .public_top_header .gift-icon-display .gift-icon-case {
  width: 0.59rem;
  height: 0.57rem;
}
.gift-main .public_top_header .gift-icon-display .gift-icon-right {
  width: 0.52rem;
  padding-bottom: 0.2rem;
}
.gift-choose-main {
  display: none;
}
.gift-choose-main .currentText {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 1.86666667rem;
  height: 1.86666667rem;
  line-height: 1.86666667rem;
  font-size: 1.06666667rem;
  background: rgba(0, 0, 0, 0.3);
  color: #fff;
  z-index: 999;
  text-align: center;
  border-radius: 0.26666667rem;
}
.gift-choose-main .gift-people-select {
  width: 9.36rem;
  min-height: 2.2rem;
  background-color: #ffffff;
  box-shadow: 0rem 0.16rem 0.387rem 0rem rgba(8, 5, 5, 0.09);
  border-radius: 0.16rem;
  margin: 0 auto;
  margin-top: 0.4rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 0.5rem;
  position: relative;
}
.gift-choose-main .gift-people-select .people-selected-add {
  display: flex;
  align-items: center;
  padding-right: 0.1rem;
  margin-top: 0.3rem;
}
.gift-choose-main .gift-people-select .people-selected-add img {
  width: 1.12rem;
  height: 1.12rem;
  border-radius: 50%;
}
.gift-choose-main .gift-people-select .people-selected-add span {
  font-size: 0.347rem;
  color: #7cbcff;
  margin-left: 0.32rem;
}
.gift-choose-main .gift-people-select .people-choose-title {
  position: absolute;
  top: 0.12rem;
  font-size: 0.267rem;
  color: #333333;
}
.gift-choose-main .gift-people-select .people-choose-title #selected_people_number {
  color: red;
  margin: 0 0.1rem;
}
.gift-choose-main .gift-people-select .people-choose-number {
  max-width: 6rem;
  max-height: 2.2rem;
  white-space: nowrap;
  overflow: auto;
}
.gift-choose-main .gift-people-select .people-choose-number::-webkit-scrollbar {
  display: none;
}
.gift-choose-main .gift-people-select .people-choose-number .people-choose-info {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 0.16rem;
}
.gift-choose-main .gift-people-select .people-choose-number .people-choose-info .picture {
  position: relative;
  width: 0.907rem;
  height: 0.907rem;
  background-color: #efebeb;
  border-radius: 50%;
  line-height: 0.907rem;
  text-align: center;
  vertical-align: middle;
  margin-top: 0.667rem;
}
.gift-choose-main .gift-people-select .people-choose-number .people-choose-info .picture .header {
  width: 0.88rem;
  height: 0.88rem;
  border-radius: 50%;
}
.gift-choose-main .gift-people-select .people-choose-number .people-choose-info .name {
  margin-top: 0.133rem;
  text-align: center;
  line-height: 0.302rem;
  height: 0.267rem;
  color: #333333;
  margin-bottom: 0.2rem;
}
.gift-choose-main .gift-people-select .people-choose-number .people-choose-info .name strong {
  font-size: 0.267rem;
}
.gift-choose-main .gift-people-select .people-choose-finish {
  background-color: #f5f5f5;
  border-radius: 0.08rem;
  padding: 0 0.4rem;
  height: 0.573rem;
  line-height: 0.573rem;
  font-size: 0.347rem;
  color: #2e94fe;
}
.gift-choose-main .choose-people-list {
  width: 9.36rem;
  min-height: 8rem;
  height: 14.2rem;
  background-color: #ffffff;
  box-shadow: 0rem 0.16rem 0.387rem 0rem rgba(8, 5, 5, 0.09);
  border-radius: 0.16rem;
  margin: 0.4rem auto;
  overflow: auto;
  display: block;
}
.gift-choose-main .choose-people-list .section-box {
  height: 100%;
  z-index: 99;
  overflow: auto;
}
.gift-choose-main .choose-people-list .select_city_section {
  margin: 0.2rem 0.373333rem 0;
  font-size: 0.373333rem;
  color: #333;
  background-color: #fff;
  padding: 0.4rem 0;
}
.gift-choose-main .choose-people-list .select_city_section header {
  line-height: 0.853333rem;
  background: #ebebeb;
  border-bottom: 1px solid #e1e1e1;
  font-size: 0.32rem;
  color: #999;
  padding: 0 0.48rem;
}
.gift-choose-main .choose-people-list .select_city_section header.fixed {
  position: absolute;
  top: 4.18rem;
  left: 0.32rem;
  right: 0.32rem;
  z-index: 99;
  width: 9.36rem;
  border-radius: 0.18rem 0.18rem 0 0;
  overflow: hidden;
}
.gift-choose-main .choose-people-list .select_city_section header.fixed + ul {
  padding-top: 0.853333rem;
}
.gift-choose-main .choose-people-list .select_city_section ul li {
  border-bottom: 1px solid #e9e7e78f;
  padding: 0 0.48rem;
  display: flex;
  align-items: center;
  padding: 0.25rem;
}
.gift-choose-main .choose-people-list .select_city_section ul li .state {
  width: 0.387rem;
  height: 0.387rem;
  line-height: 0.387rem;
  text-align: center;
  background: white;
  border: solid 0.027rem #c6e2ff;
  border-radius: 50%;
}
.gift-choose-main .choose-people-list .select_city_section ul li .sactive {
  background-color: #50a5fd;
  border: solid 0.027rem #c6e2ff;
  background-image: url(../img/gift/icon_state.png);
  background-repeat: no-repeat;
  background-position: center;
}
.gift-choose-main .choose-people-list .select_city_section ul li img {
  width: 0.907rem;
  height: 0.907rem;
  border-radius: 50%;
  margin-left: 0.36rem;
}
.gift-choose-main .choose-people-list .select_city_section ul li .info {
  margin-left: 0.188rem;
}
.gift-choose-main .choose-people-list .select_city_section ul li .info .line {
  display: flex;
  align-items: center;
}
.gift-choose-main .choose-people-list .select_city_section ul li .info .line .name {
  font-size: 0.347rem;
  color: #333333;
}
.gift-choose-main .choose-people-list .select_city_section ul li .info .line .day {
  background-color: #4c9cfe;
  padding: 0.04rem 0.16rem;
  border-radius: 0.053rem;
  font-size: 0.24rem;
  color: #ffffff;
  margin-left: 0.307rem;
}
.gift-choose-main .choose-people-list .select_city_section ul li .info .line .year {
  background-color: #fe8b4c;
}
.gift-choose-main .choose-people-list .select_city_section ul li .info .number {
  margin-top: 0.16rem;
  font-size: 0.293rem;
  color: #999999;
}
.gift-choose-main .choose-people-list .select_city_section .fixedRight {
  position: fixed;
  right: 0;
  top: 4.6rem;
  bottom: 0;
  width: 1.5rem;
  overflow: auto;
  z-index: 888;
  padding-top: 0.533333rem;
}
.gift-choose-main .choose-people-list .select_city_section .fixedRight div {
  position: relative;
  text-align: center;
  font-size: 0.32rem;
  color: #b1b1b1;
  height: 0.4rem;
  line-height: 0.4rem;
}
.gift-choose-main .choose-people-list .select_city_section .fixedRight div.active {
  color: #333;
}
.gift-send-people .gift-people-choose {
  width: 9.36rem;
  min-height: 2.2rem;
  background-color: #ffffff;
  box-shadow: 0rem 0.16rem 0.39rem 0rem rgba(8, 5, 5, 0.09);
  border-radius: 0.16rem;
  margin: 0 auto;
  margin-top: 0.4rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 0.5rem;
}
.gift-send-people .gift-people-choose .people-selected-add {
  display: flex;
  align-items: center;
  padding-right: 0.1rem;
}
.gift-send-people .gift-people-choose .people-selected-add img {
  width: 1.12rem;
  height: 1.12rem;
  border-radius: 50%;
}
.gift-send-people .gift-people-choose .people-selected-add span {
  font-size: 0.347rem;
  color: #7cbcff;
  margin-left: 0.32rem;
}
.gift-send-people .gift-people-choose .people-selected-section {
  max-width: 6rem;
  white-space: nowrap;
  overflow: auto;
}
.gift-send-people .gift-people-choose .people-selected-section::-webkit-scrollbar {
  display: none;
}
.gift-send-people .gift-people-choose .people-selected-section .people-choose-data {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 0.16rem;
}
.gift-send-people .gift-people-choose .people-selected-section .people-choose-data .picture {
  position: relative;
  width: 0.907rem;
  height: 0.907rem;
  background-color: #efebeb;
  border-radius: 50%;
  line-height: 0.907rem;
  text-align: center;
  vertical-align: middle;
  margin-top: 0.147rem;
}
.gift-send-people .gift-people-choose .people-selected-section .people-choose-data .picture .header {
  width: 0.88rem;
  height: 0.88rem;
  border-radius: 50%;
}
.gift-send-people .gift-people-choose .people-selected-section .people-choose-data .picture .state {
  width: 0.24rem;
  height: 0.24rem;
  line-height: 0.24rem;
  text-align: center;
  background: white;
  border: solid 0.027rem #c6e2ff;
  position: absolute;
  top: 0.627rem;
  left: 0.693rem;
  border-radius: 50%;
}
.gift-send-people .gift-people-choose .people-selected-section .people-choose-data .picture .sactive {
  background-color: #50a5fd;
  border: solid 0.027rem #c6e2ff;
  position: absolute;
  background-image: url(../img/gift/icon_state.png);
  background-repeat: no-repeat;
}
.gift-send-people .gift-people-choose .people-selected-section .people-choose-data .name {
  margin-top: 0.133rem;
  text-align: center;
  line-height: 0.302rem;
  height: 0.267rem;
  color: #333333;
}
.gift-send-people .gift-people-choose .people-selected-section .people-choose-data .name strong {
  font-size: 0.267rem;
}
.gift-send-people .gift-people-choose .people-selected-section .people-choose-data .number {
  font-size: 0.213rem;
  color: #999999;
  margin-top: 0.12rem;
  margin-bottom: 0.187rem;
  -webkit-text-size-adjust: none;
  text-align: center;
}
.gift-send-people .gift-people-choose .people-choose-arrow {
  display: flex;
  align-items: center;
}
.gift-send-people .gift-people-choose .people-choose-arrow span {
  font-size: 0.35rem;
  color: #2e94fe;
  padding-right: 0.16rem;
}
.gift-send-people .gift-people-choose .people-choose-arrow img {
  width: 0.23rem;
  height: 0.4rem;
}
.gift-send-people .gift-below-choose {
  width: 9.33rem;
  background-color: #ffffff;
  border-radius: 0.16rem;
  margin: 0 auto;
  margin-top: 20px;
  padding: 0.36rem 0.4rem;
}
.gift-send-people .gift-below-choose .title {
  font-size: 0.37rem;
  color: #333333;
}
.gift-send-people .gift-below-choose .option {
  font-size: 0.37rem;
  color: #999999;
  display: flex;
  margin-top: 0.48rem;
  margin-bottom: 0.6rem;
}
.gift-send-people .gift-below-choose .option div {
  flex-grow: 1;
  text-align: center;
}
.gift-send-people .gift-below-choose .option div img {
  width: 0.307rem;
  height: 0.333rem;
  padding-top: 0.02rem;
}
.gift-send-people .gift-below-choose .option .oactive {
  height: 0.64rem;
  line-height: 0.64rem;
  background-color: #c6e2ff;
  border-radius: 0.31rem;
  color: #50a5fd;
  font-weight: 600;
}
.gift-send-people .gift-below-choose .medal {
  display: flex;
  flex-wrap: wrap;
}
.gift-send-people .gift-below-choose .medal .medal-item {
  width: 33.33%;
  margin-bottom: 0.52rem;
  text-align: center;
}
.gift-send-people .gift-below-choose .medal .medal-item .icon-medal {
  max-width: 2.6rem;
  max-height: 2.6rem;
  margin-bottom: 0.1rem;
  object-fit: scale-down;
}
.gift-send-people .gift-below-choose .medal .medal-item div {
  font-size: 0.32rem;
  color: #999999;
}
.gift-send-people .gift-below-choose .medal .medal-item .text {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 0.6rem;
  line-height: 0.6rem;
}
.gift-send-people .gift-below-choose .medal .medal-item .text .selected-icon {
  width: 0.4rem;
  height: 0.4rem;
  display: none;
  margin-right: 0.04rem;
}
.gift-send-people .gift-below-choose .medal .medal-item .text .selected {
  display: block;
}
.gift-send-people .gift-below-choose .medal .medal-item .text .mactive {
  color: #333333;
}
.gift-send-people .gift-message-section {
  width: 9.33rem;
  border-radius: 0.16rem;
  margin: 0 auto;
  margin-top: 20px;
  position: relative;
}
.gift-send-people .gift-message-section header {
  color: #333333;
  font-size: 0.37rem;
  background-color: #ffffff;
  padding: 0.36rem 0.34rem 0 0.34rem;
  border-radius: 0.18rem 0.18rem 0 0;
}
.gift-send-people .gift-message-section form {
  border: none;
}
.gift-send-people .gift-message-section form textarea {
  padding-top: 0.24rem;
  display: block;
  margin: 0 auto;
  width: 100%;
  height: 2.45rem;
  background: #fff;
  border-radius: 0.03rem;
  color: #444444;
  font-size: 0.32rem;
  line-height: 0.45333333rem;
  resize: none;
  border: none;
  padding: 0.2rem 0.34rem;
  border-radius: 0 0 0.18rem 0.18rem;
}
.gift-send-people .gift-message-section .number {
  position: absolute;
  right: 0.48rem;
  top: 2.6rem;
  font-size: 0.34rem;
  color: #999;
}
.gift-send-people .gift-message-section .number span {
  color: #2e94fe;
}
.gift-send-people .gift-give-people {
  width: 100%;
  text-align: center;
}
.gift-send-people .gift-give-people .give {
  width: 8.37rem;
  height: 1.16rem;
  line-height: 1.16rem;
  text-align: center;
  background-color: #2e94fe;
  border-radius: 0.57rem;
  font-size: 0.37rem;
  color: #ffffff;
  margin: 0.68rem auto;
  border: 1px solid #2e94fe;
}
.gift-send-people .gift-give-people .give[disabled="disabled"] {
  opacity: 0.5;
  filter: alpha(opacity=50);
}
.gift-style-mine .gift-style-select {
  margin-top: 0.48rem;
  padding: 0 2.5rem;
  display: flex;
}
.gift-style-mine .gift-style-select .gift-style-item {
  width: 2.5rem;
  height: 0.71rem;
  line-height: 0.71rem;
  color: #bfe6ff;
  font-size: 0.37rem;
  text-align: center;
}
.gift-style-mine .gift-style-select .iactive {
  background-color: rgba(255, 255, 255, 0.54);
  color: #ffffff;
  border-radius: 0.35rem;
}
.gift-style-mine .gift-style-list {
  width: 9.33rem;
  background-color: #ffffff;
  border-radius: 0.16rem;
  margin: 0 auto;
  padding: 0.7rem 0.3rem;
  margin-top: 0.4rem;
}
.gift-style-mine .gift-style-list .gift-style-date {
  display: inline-block;
  text-align: center;
  padding: 0 0.4rem;
  height: 0.71rem;
  line-height: 0.71rem;
  background-color: rgba(236, 234, 234, 0.54);
  border-radius: 0.35rem;
  position: relative;
}
.gift-style-mine .gift-style-list .style-list-main {
  padding: 0 0.3rem;
}
.gift-style-mine .gift-style-list .style-list-main .style-list-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin: 0.7rem 0;
}
.gift-style-mine .gift-style-list .style-list-main .style-list-item .style-list-left {
  display: flex;
}
.gift-style-mine .gift-style-list .style-list-main .style-list-item .style-list-left .style-list-img {
  max-width: 2.6rem;
  max-height: 2.6rem;
  height: auto;
  width: auto;
  object-fit: scale-down;
}
.gift-style-mine .gift-style-list .style-list-main .style-list-item .style-list-left .style-list-info {
  margin-left: 0.2rem;
  padding-top: 0.2rem;
}
.gift-style-mine .gift-style-list .style-list-main .style-list-item .style-list-left .style-list-info .name {
  font-size: 0.37rem;
  color: #333333;
  padding-top: 0.1rem;
}
.gift-style-mine .gift-style-list .style-list-main .style-list-item .style-list-left .style-list-info .type {
  color: #666666;
  font-size: 0.29rem;
  margin-top: 0.1rem;
}
.gift-style-mine .gift-style-list .style-list-main .style-list-item .style-list-left .style-list-info > div:nth-child(3) {
  margin-top: 0.06rem !important;
  font-size: 0.27rem !important;
}
.gift-style-mine .gift-style-list .style-list-main .style-list-item .style-list-number {
  padding-bottom: 0.06rem;
}
.gift-style-mine .gift-style-list .style-list-main .style-list-item .style-list-number img {
  width: 0.31rem;
  height: 0.33rem;
}
.gift-style-mine .gift-style-list .style-list-main .style-list-item .style-list-number .number {
  font-size: 0.27rem;
  color: #666666;
}
.gift-preview-body {
  background-image: linear-gradient(0deg, #45caf5 0%, #2e94fe 100%), linear-gradient(#2e94fe, #2e94fe);
  background-blend-mode: normal, normal;
  background-repeat: no-repeat;
}
.red-packet-reward {
  background-image: url(../img/gift/icon_redback.png);
  background-blend-mode: normal, normal;
  background-size: 100% 6.28rem;
  padding-bottom: 1.5rem;
  background-repeat: no-repeat;
}
.red-packet-reward .public_top_header {
  position: relative;
  border: none;
  box-shadow: none;
  color: #fff;
  font-size: 0.48rem;
  font-weight: 540;
  background: rgba(255, 0, 0, 0);
}
.red-packet-reward .public_top_header .return_back::before {
  border-color: #fff;
}
.red-packet-reward .public_top_header .gift-icon-display {
  display: inline-block;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 1001;
  padding-top: 0.1rem;
  padding-right: 0.45rem;
}
.red-packet-reward .public_top_header .gift-icon-display .gift-icon-text {
  width: 0.8rem;
  font-family: NotoSansHans-Medium;
  font-size: 0.24rem;
  line-height: 0.32rem;
  letter-spacing: 0.06rem;
  color: #febac0;
  display: inline-block;
}
.red-packet-reward .public_top_header .gift-icon-display .gift-icon-case {
  width: 0.59rem;
  height: 0.57rem;
}
.red-packet-reward .public_top_header .gift-icon-display .gift-icon-right {
  width: 0.52rem;
  padding-bottom: 0.2rem;
}
.red-packet-reward .select-a-person {
  width: 9.36rem;
  margin: 0 auto;
  min-height: 1.96rem;
  background-color: #ffffff;
  box-shadow: 0rem 0.16rem 0.387rem 0rem rgba(8, 5, 5, 0.09);
  border-radius: 0.16rem;
  display: flex;
  justify-content: space-between;
  justify-content: center;
}
.red-packet-reward .select-a-person .person-info {
  display: flex;
  justify-content: space-between;
}
.red-packet-reward .select-a-person .person-info img {
  width: 1.147rem;
  height: 1.147rem;
  border-radius: 50%;
}
.red-packet-reward .select-a-person .person-info .name {
  font-size: 0.373rem;
  color: #333333;
}
.gift-result-preview {
  padding-bottom: 1.5rem;
}
.gift-result-preview .public_top_header {
  position: relative;
  border: none;
  box-shadow: none;
  color: #fff;
  font-size: 0.48rem;
  font-weight: 540;
  background: rgba(255, 0, 0, 0);
}
.gift-result-preview .public_top_header .return_back::before {
  border-color: #fff;
}
.gift-result-preview .public_top_header .gift-icon-display {
  display: inline-block;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 1001;
  padding-top: 0.1rem;
  padding-right: 0.45rem;
}
.gift-result-preview .public_top_header .gift-icon-display .gift-icon-text {
  width: 0.8rem;
  font-family: NotoSansHans-Medium;
  font-size: 0.24rem;
  line-height: 0.32rem;
  letter-spacing: 0.06rem;
  color: #cdeeff;
  display: inline-block;
}
.gift-result-preview .public_top_header .gift-icon-display .gift-icon-case {
  width: 0.59rem;
  height: 0.57rem;
}
.gift-result-preview .public_top_header .gift-icon-display .gift-icon-right {
  width: 0.52rem;
  padding-bottom: 0.2rem;
}
.gift-result-preview .gift-preview-style {
  width: 8rem;
  height: 10rem;
  margin: 0.75rem auto;
  padding: 0.3rem 0.4rem;
  background-image: url(../img/gift/icon_preview.png);
  background-size: 100%;
  background-repeat: no-repeat;
}
.gift-result-preview .gift-preview-style .gift-preview-info {
  width: 6.95rem;
  height: 9.08rem;
  background-color: #ffffff;
  box-shadow: 0rem 0.16rem 0.39rem 0rem rgba(8, 5, 5, 0.09);
  border-radius: 0.16rem;
  text-align: center;
}
.gift-result-preview .gift-preview-style .gift-preview-info .preview-info-img {
  margin: 1rem 0;
  max-width: 4.65rem;
  max-height: 4.65rem;
  object-fit: scale-down;
}
.gift-result-preview .gift-preview-style .gift-preview-info .style-list-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding: 0 0.44rem;
}
.gift-result-preview .gift-preview-style .gift-preview-info .style-list-item .style-list-info .name {
  font-size: 0.37rem;
  color: #333333;
  text-align: left;
  margin-bottom: 0.173rem;
}
.gift-result-preview .gift-preview-style .gift-preview-info .style-list-item .style-list-info .type {
  color: #666666;
  font-size: 0.29rem;
  margin-bottom: 0.16rem;
  text-align: left;
}
.gift-result-preview .gift-preview-style .gift-preview-info .style-list-item .style-list-info > div:nth-child(3) {
  font-size: 0.27rem !important;
}
.gift-result-preview .gift-preview-style .gift-preview-info .style-list-item .style-list-number {
  padding-bottom: 0.06rem;
}
.gift-result-preview .gift-preview-style .gift-preview-info .style-list-item .style-list-number img {
  width: 0.31rem;
  height: 0.33rem;
}
.gift-result-preview .gift-preview-style .gift-preview-info .style-list-item .style-list-number .number {
  font-size: 0.27rem;
  color: #666666;
}
.gift-result-preview .gift-preview-message {
  width: 8.81rem;
  min-height: 3.15rem;
  background-color: #ffffff;
  box-shadow: 0rem 0.16rem 0.39rem 0rem rgba(56, 170, 250, 0.09);
  border-radius: 0.16rem;
  position: relative;
  margin: 0 auto;
  padding-bottom: 0.4rem;
}
.gift-result-preview .gift-preview-message .gift-preview-top {
  position: relative;
  height: 0.8rem;
  line-height: 0.8rem;
  margin-bottom: 0.4rem;
}
.gift-result-preview .gift-preview-message .gift-preview-top .portrait {
  width: 1.4rem;
  height: 1.4rem;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #efebeb;
  border-radius: 50%;
  position: absolute;
  top: -0.5rem;
  left: 0.8rem;
}
.gift-result-preview .gift-preview-message .gift-preview-top .portrait img {
  width: 1.2rem;
  height: 1.2rem;
  border-radius: 50%;
}
.gift-result-preview .gift-preview-message .gift-preview-top .name {
  font-size: 0.37rem;
  color: #333333;
  margin-left: 2.4rem;
}
.gift-result-preview .gift-preview-message .gift-preview-top .score {
  font-size: 0.32rem;
  color: #fb5b51;
  margin-left: 0.3rem;
}
.gift-result-preview .gift-preview-message .gift-preview-msg {
  width: 8.48rem;
  min-height: 1.6rem;
  background-color: #f5f5f5;
  margin: 0 auto;
  padding: 0.2rem 1rem;
  font-size: 0.32rem;
  color: #666666;
  line-height: 0.4rem;
  border-radius: 0.16rem;
  word-break: break-all;
}
.community-main-index .community-search {
  background: #fff;
  padding-top: 0.2rem;
  position: fixed;
  width: 100%;
  left: 0;
  top: 0;
  z-index: 1000;
}
.community-main-index .community-search .title {
  display: flex;
  height: 1rem;
  line-height: 1rem;
  padding-left: 0.8rem;
  align-items: center;
  padding-bottom: 0.2rem;
}
.community-main-index .community-search .title .back {
  font-size: 0.48rem;
  color: #333333;
  font-weight: 700;
  text-align: center;
}
.community-main-index .community-search .title .back::before {
  content: " ";
  position: absolute;
  display: inline-block;
  height: 0.3rem;
  width: 0.3rem;
  border-width: 0.05rem 0.05rem 0 0;
  border-color: #333333;
  border-style: solid;
  -webkit-transform: matrix(-0.71, -0.71, 0.71, -0.71, 0, 0);
  transform: matrix(-0.71, -0.71, 0.71, -0.71, 0, 0);
  position: relative;
  left: -0.1rem;
}
.community-main-index .community-search .title .search {
  width: 6.587rem;
  height: 0.773rem;
  line-height: 0.773rem;
  margin-left: 0.5rem;
  background-color: #f5f5f5;
  border-radius: 0.38rem;
  display: flex;
  justify-content: space-between;
}
.community-main-index .community-search .title .search input {
  width: 5rem;
  margin-left: 0.6rem;
  background-color: #f5f5f5;
  font-size: 0.347rem;
}
.community-main-index .community-search .title .search .btsearch {
  width: 0.5rem;
  height: 0.5rem;
  margin-top: 0.14rem;
  margin-right: 0.4rem;
}
.community-main-index .community-banner {
  padding-top: 1.5rem;
  width: 100%;
  text-align: center;
}
.community-main-index .community-banner img {
  width: 9.36rem;
  height: 4.267rem;
  background-image: linear-gradient(0deg, #278bff 0%, #7fa9fe 100%), linear-gradient(#278bff, #278bff);
  border-radius: 0.16rem;
  margin: 0 auto;
}
.community-main-index .community-plate {
  width: 100%;
  background-color: #ffffff;
  margin-top: 0.2rem;
  margin: 0.2rem auto 0;
  white-space: nowrap;
  overflow-y: auto;
  text-align: center;
  padding-top: 0.06rem;
}
.community-main-index .community-plate a {
  font-size: 0.373rem;
  color: #999999;
  text-align: center;
  padding: 0 0.2rem;
  display: inline-block;
  white-space: nowrap;
  height: 1.067rem;
  line-height: 1.067rem;
}
.community-main-index .community-plate .selected {
  font-weight: 600;
  color: #333333;
  position: relative;
}
.community-main-index .community-plate .selected::after {
  content: " ";
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 0rem;
  width: 0.72rem;
  height: 0.107rem;
  background-image: linear-gradient(90deg, #2e94fe 0%, #7fa9fe 100%), linear-gradient(#2e94fe, #2e94fe);
}
.community-publish {
  position: fixed;
  width: 100%;
  left: 0;
  bottom: 0;
  z-index: 1000;
  height: 1.333rem;
  background-color: #ffffff;
  padding: 0 1.3rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.community-publish .selected {
  color: #2e94fe !important;
}
.community-publish .topic {
  text-align: center;
}
.community-publish .topic img {
  width: 0.573rem;
  height: 0.453rem;
}
.community-publish .topic div {
  padding: 0 0.5rem;
  font-size: 0.24rem;
  color: #999999;
}
.community-publish .publish img {
  width: 1.307rem;
  height: 1.307rem;
  background-image: linear-gradient(90deg, #2e94fe 0%, #7fa9fe 100%), linear-gradient(#ffffff, #ffffff);
  background-blend-mode: normal, 
                normal;
  box-shadow: 0rem 0.067rem 0.32rem 0rem rgba(97, 161, 254, 0.75);
  border-radius: 50%;
  margin-top: -0.36rem;
}
.community-publish .mine {
  text-align: center;
}
.community-publish .mine img {
  width: 0.44rem;
  height: 0.493rem;
}
.community-publish .mine div {
  padding: 0 0.5rem;
  font-size: 0.24rem;
  color: #999999;
}
.like-magnify {
  animation: move-magnify 1s ease 0.1s 1 alternate backwards;
}
@keyframes move-magnify {
  from {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
    -webkit-transform: scale(1);
    transform: scale(1);
  }
  50% {
    -webkit-transform: translate(0, -0.2rem) scale(2);
    transform: translate(0, -0.2rem) scale(2);
  }
  to {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}
@-webkit-keyframes move-magnify {
  from {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
    -webkit-transform: scale(1);
    transform: scale(1);
  }
  50% {
    -webkit-transform: translate(0, -0.2rem) scale(2);
    transform: translate(0, -0.2rem) scale(2);
  }
  to {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}
.community-list-pulic {
  width: 9.36rem;
  margin: 0 auto;
  overflow: auto;
  padding-bottom: 1.8rem;
}
.community-list-pulic .item {
  width: 100%;
  background-color: #fff;
  padding: 0.4rem;
  display: flex;
  justify-content: space-between;
  margin-top: 0.2rem;
  border-radius: 0.16rem;
}
.community-list-pulic .item .picture {
  width: 1.107rem;
  height: 1.107rem;
  border: solid 0.04rem #c6e2ff;
  position: relative;
  background-color: #efebeb;
  border-radius: 50%;
}
.community-list-pulic .item .picture .header {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.community-list-pulic .item .picture .sex {
  width: 0.307rem;
  height: 0.307rem;
  background-color: #50a5fd;
  border: solid 0.027rem #c6e2ff;
  position: absolute;
  top: 0.76rem;
  right: 0rem;
  border-radius: 50%;
  object-fit: scale-down;
}
.community-list-pulic .item .content {
  width: 7.12rem;
}
.community-list-pulic .item .content .section {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.community-list-pulic .item .content .section .management {
  font-size: 0.347rem;
  height: 0.302rem;
  line-height: 0.302rem;
  color: #999999;
}
.community-list-pulic .item .content .section .label span {
  padding: 0.12rem 0.2rem;
  border-radius: 0.053rem;
  color: #ffffff;
  font-size: 0.267rem;
}
.community-list-pulic .item .content .section .label .cream {
  background-color: #ff8164;
}
.community-list-pulic .item .content .section .label .top {
  background-color: #7fa9fe;
}
.community-list-pulic .item .content .type {
  color: #333333;
  font-weight: 700;
  margin: 0.12rem 0;
}
.community-list-pulic.community-list-pulic-modify .item .content  .text span img{
  transform: translateY(0.12rem);
}
.community-list-pulic .item .content .text {
  font-size: 0.32rem;
  line-height: 0.54rem;
  color: #666666;
  min-height: 1rem;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  word-break: break-all;
}
.community-list-pulic .item .content .text span {
  word-break: break-all;
  display: block;
  max-height: 3rem;
}
.community-list-pulic .item .content .text p {
  line-height: 0.6rem !important;
}
.community-list-pulic .item .content .text .image {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
.community-list-pulic .item .content .text .image img {
  width: 2rem;
  height: 3rem;
  object-fit: cover;
  margin: 0.2rem 0.1rem;
  flex-grow: 1;
}
.community-list-pulic .item .content .text .info {
  word-break: break-all;
  height: 2.16rem;
  overflow: hidden;
  text-overflow: ellipsis;
}
.community-list-pulic .item .content .text .info-all {
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
}
.community-list-pulic .item .content .text .all {
  color: #2e94fe;
}
.community-list-pulic .item .content .state {
  display: flex;
  justify-content: space-between;
  padding: 0.2rem 0.1rem;
}
.community-list-pulic .item .content .state .time {
  font-size: 0.32rem;
  color: #c0bfbf;
}
.community-list-pulic .item .content .state .include {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.community-list-pulic .item .content .state .include span {
  font-size: 0.32rem;
  color: #c0bfbf;
  min-width: 0.5rem;
  text-align: end;
}
.community-list-pulic .item .content .state .include .good {
  display: flex;
  align-items: flex-end;
}
.community-list-pulic .item .content .state .include .good .like {
  width: 0.4rem;
  height: 0.427rem;
  margin-left: 0.1rem;
}
.community-list-pulic .item .content .state .include .good .selected {
  color: #f76268;
}
.community-list-pulic .item .content .state .include .message {
  display: flex;
  align-items: flex-end;
  margin-left: 0.5rem;
}
.community-list-pulic .item .content .state .include .message img {
  width: 0.413rem;
  height: 0.427rem;
  margin-left: 0.1rem;
}
.community-list-pulic .item .content .msg {
  padding: 0.3rem 0.24rem;
  width: 100%;
  background-color: #f5f5f5;
}
.community-list-pulic .item .content .msg .user {
  font-size: 0.32rem;
  line-height: 0.48rem;
}
.community-list-pulic .item .content .msg .user .name {
  color: #50a5fd;
}
.community-list-pulic .item .content .msg .user .content {
  color: #333333;
}
.topic-details-main {
  padding-bottom: 1.5rem;
  padding-top: 0.8rem;
}
.topic-details-main .public_top_header {
  border: none;
  box-shadow: none;
  color: #333333;
  background: #fff;
  font-size: 0.48rem;
  font-weight: 540;
  z-index: 1002;
}
.topic-details-main .public_top_header .return_back::before {
  border-color: #2e94fe;
}
.topic-details-main .topic-details-info {
  width: 9.36rem;
  margin: 0.453rem auto;
  overflow: auto;
}
.topic-details-main .topic-details-info .details {
  width: 100%;
  background-color: #fff;
  padding: 0.4rem;
  display: flex;
  justify-content: space-between;
  margin-top: 0.2rem;
  border-radius: 0.16rem;
}
.topic-details-main .topic-details-info .details .content {
  width: 100%;
}
.topic-details-main .topic-details-info .details .content .section {
  display: flex;
  position: relative;
}
.topic-details-main .topic-details-info .details .content .section .picture {
  width: 1.107rem;
  height: 1.107rem;
  border: solid 0.04rem #c6e2ff;
  position: relative;
  background-color: #efebeb;
  border-radius: 50%;
}
.topic-details-main .topic-details-info .details .content .section .picture .header {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.topic-details-main .topic-details-info .details .content .section .picture .sex {
  width: 0.307rem;
  height: 0.307rem;
  background-color: #50a5fd;
  border: solid 0.027rem #c6e2ff;
  position: absolute;
  top: 0.76rem;
  right: 0rem;
  border-radius: 50%;
  object-fit: scale-down;
}
.topic-details-main .topic-details-info .details .content .section .management {
  font-size: 0.347rem;
  height: 0.302rem;
  color: #999999;
  margin-left: 0.2rem;
}
.topic-details-main .topic-details-info .details .content .section .label {
  position: absolute;
  right: 0;
}
.topic-details-main .topic-details-info .details .content .section .label span {
  padding: 0.12rem 0.2rem;
  border-radius: 0.053rem;
  color: #ffffff;
  font-size: 0.267rem;
}
.topic-details-main .topic-details-info .details .content .section .label .cream {
  background-color: #ff8164;
}
.topic-details-main .topic-details-info .details .content .section .label .top {
  background-color: #7fa9fe;
}
.topic-details-main .topic-details-info .details .content .type {
  margin: 0.12rem 0.1rem 0.12rem 0.2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.topic-details-main .topic-details-info .details .content .type > span:nth-child(1) {
  color: #333333;
  font-weight: 700;
}
.topic-details-main .topic-details-info .details .content .type > span:nth-child(2) {
  font-size: 0.32rem;
  color: #c0bfbf;
}
.topic-details-main .topic-details-info .details .content .text {
  font-size: 0.32rem;
  line-height: 0.54rem;
  color: #666666;
  margin-left: 0.2rem;
  margin-right: 0.1rem;
}
.topic-details-main .topic-details-info .details .content .text .medal {
  width: 100%;
  text-align: center;
}
.topic-details-main .topic-details-info .details .content .text .info {
  word-break: break-all;
  height: 2.16rem;
  overflow: hidden;
  text-overflow: ellipsis;
}
.topic-details-main .topic-details-info .details .content .text .info-all {
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
}
.topic-details-main .topic-details-info .details .content .text .info-all p {
  line-height: 0.6rem !important;
}
.topic-details-main .topic-details-info .details .content .text .all {
  color: #2e94fe;
}
.topic-details-main .topic-details-info .details .content .text .image {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
.topic-details-main .topic-details-info .details .content .text .image img {
  width: 2rem;
  height: 3rem;
  object-fit: cover;
  margin: 0.2rem 0.1rem;
  flex-grow: 1;
}
.topic-details-main .topic-details-info .details .content .discuss {
  margin-top: 0.6rem;
  padding-left: 0.2rem;
  border-bottom: 0.027rem solid #f5f5f5;
}
.topic-details-main .topic-details-info .details .content .discuss div {
  display: inline-block;
  padding-bottom: 0.3rem;
  margin-right: 0.6rem;
}
.topic-details-main .topic-details-info .details .content .discuss div > span:nth-child(1) {
  font-size: 0.32rem;
  color: #333333;
}
.topic-details-main .topic-details-info .details .content .discuss div > span:nth-child(2) {
  font-size: 0.32rem;
  color: #c0bfbf;
  margin-left: 0.2rem;
}
.topic-details-main .topic-details-info .details .content .discuss .selected {
  position: relative;
}
.topic-details-main .topic-details-info .details .content .discuss .selected::after {
  content: " ";
  position: absolute;
  height: 0.027rem;
  background-color: #2e94fe;
  bottom: -0.01rem;
  left: 0rem;
  width: 100%;
}
.topic-details-main .topic-details-info .details .content .switchs {
  text-align: right;
  font-size: 0.34rem;
  padding-top: 0.2rem;
}
.topic-details-main .topic-details-info .details .content .switchs .default {
  color: #999;
  padding-right: 0.2rem;
  border-right: 0.02rem solid #999;
}
.topic-details-main .topic-details-info .details .content .switchs .latest {
  color: #999;
  padding-left: 0.2rem;
}
.topic-details-main .topic-details-info .details .content .switchs .switchs-active {
  color: #666;
}
.topic-details-main .topic-details-info .details .content .item .message {
  display: flex;
  margin-top: 0.28rem;
  margin-left: 0.2rem;
}
.topic-details-main .topic-details-info .details .content .item .message .mpicture {
  width: 0.76rem;
  height: 0.76rem;
  border: 0.02rem solid #c6e2ff;
  position: relative;
  background-color: #efebeb;
  border-radius: 50%;
}
.topic-details-main .topic-details-info .details .content .item .message .mpicture .mheader {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.topic-details-main .topic-details-info .details .content .item .message .mpicture .msex {
  width: 0.213rem;
  height: 0.213rem;
  background-color: #50a5fd;
  border: solid 0.027rem #c6e2ff;
  position: absolute;
  top: 0.46rem;
  right: 0rem;
  border-radius: 50%;
  object-fit: scale-down;
}
.topic-details-main .topic-details-info .details .content .item .message .mtop {
  padding-left: 0.24rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-grow: 1;
}
.topic-details-main .topic-details-info .details .content .item .message .mtop .muser {
  font-size: 0.32rem;
  height: 0.48rem;
  line-height: 0.48rem;
  color: #50a5fd;
}
.topic-details-main .topic-details-info .details .content .item .message .mtop .mtime {
  font-size: 0.267rem;
  line-height: 0.48rem;
  height: 0.48rem;
  color: #999999;
}
.topic-details-main .topic-details-info .details .content .item .message .mtop > div:nth-child(2) {
  display: flex;
  align-items: center;
}
.topic-details-main .topic-details-info .details .content .item .message .mtop .mup {
  font-size: 0.267rem;
  line-height: 0.48rem;
  height: 0.48rem;
  color: #7fa9fe;
}
.topic-details-main .topic-details-info .details .content .item .message .mtop .mremove {
  width: 0.307rem;
  height: 0.333rem;
  margin-left: 0.375rem;
  margin-top: -0.08rem;
}
.topic-details-main .topic-details-info .details .content .item .mtext {
  font-size: 0.32rem;
  line-height: 0.48rem;
  color: #666666;
  margin-left: 1.2rem;
  margin-top: 0.1rem;
}
.topic-details-main .topic-details-bottom {
  position: fixed;
  width: 100%;
  left: 0;
  bottom: 0;
  z-index: 1000;
  height: 1.333rem;
  background-color: #ffffff;
  padding: 0 0.8rem 0 0.6rem;
  display: flex;
  align-items: center;
}
.topic-details-main .topic-details-bottom .good {
  width: 0.4rem;
  height: 0.427rem;
  margin-left: 0.8rem;
}
.topic-details-main .topic-details-bottom .collect {
  width: 0.467rem;
  height: 0.467rem;
  margin-left: 0.587rem;
}
.topic-details-main .topic-details-bottom .details-write {
  width: 6.32rem;
  height: 0.8rem;
  background-color: #f5f5f5;
  border-radius: 0.393rem;
  line-height: 0.773rem;
  margin-left: 0.5rem;
  display: flex;
  align-items: center;
}
.topic-details-main .topic-details-bottom .details-write input {
  width: 4rem;
  padding-left: 0.2rem;
  background-color: #f5f5f5;
  font-size: 0.32rem;
}
.topic-details-main .topic-details-bottom .details-write input::-ms-input-placeholder {
  color: #999999;
}
.topic-details-main .topic-details-bottom .details-write input::-webkit-input-placeholder {
  color: #999999;
}
.topic-details-main .topic-details-bottom .details-write img {
  width: 0.4rem;
  height: 0.387rem;
  margin-left: 0.48rem;
}
.enterprise-notice-main {
  padding-bottom: 1.5rem;
}
.enterprise-notice-main .public_top_header {
  position: relative;
  border: none;
  box-shadow: none;
  color: #333333;
  background: #fff;
  font-size: 0.48rem;
  font-weight: 540;
}
.enterprise-notice-main .public_top_header .return_back::before {
  border-color: #2e94fe;
}
.enterprise-notice-main .notice-search {
  margin: 0.4rem auto;
  width: 8.707rem;
  height: 0.867rem;
  background-color: #ededed;
  border-radius: 0.427rem;
  line-height: 0.773rem;
  margin-left: 0.5rem;
  display: flex;
  justify-content: space-between;
}
.enterprise-notice-main .notice-search input {
  width: 6.587rem;
  margin-left: 1.06rem;
  background-color: #ededed;
  font-size: 0.32rem;
  text-align: center;
}
.enterprise-notice-main .notice-search input::-ms-input-placeholder {
  color: #999999;
}
.enterprise-notice-main .notice-search input::-webkit-input-placeholder {
  color: #999999;
}
.enterprise-notice-main .notice-search .btsearch {
  width: 0.56rem;
  height: 0.56rem;
  margin-top: 0.14rem;
  margin-right: 0.5rem;
}
.enterprise-notice-main .notice-message .item {
  margin: 0 auto 0.2rem;
  width: 9.36rem;
  background-color: #ffffff;
  border-radius: 0.16rem;
  padding: 0.387rem 0.6rem 0.6rem;
}
.enterprise-notice-main .notice-message .item .top {
  display: flex;
  align-items: center;
  position: relative;
}
.enterprise-notice-main .notice-message .item .top .notice-pic {
  width: 0.853rem;
  height: 0.853rem;
  line-height: 0.853rem;
  text-align: center;
  background: #1fe557;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.enterprise-notice-main .notice-message .item .top .notice-pic img {
  width: 0.56rem;
  height: 0.56rem;
  object-fit: scale-down;
}
.enterprise-notice-main .notice-message .item .top strong {
  font-size: 0.373rem;
  color: #333333;
  margin-left: 0.267rem;
}
.enterprise-notice-main .notice-message .item .top span {
  font-size: 0.32rem;
  color: #999999;
  position: absolute;
  right: 0;
}
.enterprise-notice-main .notice-message .item .info {
  font-size: 0.32rem;
  line-height: 0.48rem;
  color: #666666;
  word-break: break-all;
  margin-top: 0.293rem;
}
.enterprise-square-main {
  padding-bottom: 1.5rem;
}
.enterprise-square-main .public_top_header {
  position: relative;
  border: none;
  box-shadow: none;
  color: #333333;
  background: #fff;
  font-size: 0.48rem;
  font-weight: 540;
}
.enterprise-square-main .public_top_header .return_back::before {
  border-color: #2e94fe;
}
.enterprise-square-main .enterprise-square-module {
  background: #fff;
  padding: 0.16rem 0.2rem 0.32rem;
  display: flex;
}
.enterprise-square-main .enterprise-square-module .module {
  text-align: center;
  flex-grow: 1;
}
.enterprise-square-main .enterprise-square-module .module img {
  width: 0.853rem;
  height: 0.853rem;
  border-radius: 0.16rem;
  background: white;
}
.enterprise-square-main .enterprise-square-module .module div {
  font-size: 0.32rem;
  color: #666666;
  margin-top: 0.267rem;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.enterprise-square-main .enterprise-square-dynamic {
  padding: 0.4rem 0.373rem 0.18rem 0.52rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.enterprise-square-main .enterprise-square-dynamic .dynamic {
  font-size: 0.32rem;
  color: #333333;
}
.enterprise-square-main .enterprise-square-dynamic .all {
  width: 2.387rem;
  height: 0.56rem;
  text-align: center;
  background-color: #ffffff;
  border-radius: 0.273rem;
  font-size: 0.32rem;
  color: #333333;
  position: relative;
  padding-right: 0.2rem;
  display: flex;
  align-items: center;
  padding-left: 0.4rem;
}
.enterprise-square-main .enterprise-square-dynamic .all:after {
  content: "";
  position: absolute;
  top: 0.205rem;
  right: 0.36rem;
  width: 0;
  height: 0;
  border-top: 0.18rem solid #adadad;
  border-bottom: 0 solid transparent;
  border-left: 0.12rem solid transparent;
  border-right: 0.12rem solid transparent;
}
.enterprise-square-main .enterprise-square-list {
  padding: 0 0.32rem;
}
.enterprise-square-main .enterprise-square-list .item {
  width: 100%;
  margin-bottom: 0.32rem;
  background-color: #ffffff;
  border-radius: 0.16rem;
  padding: 0.36rem 0;
}
.enterprise-square-main .enterprise-square-list .item .top {
  display: flex;
  justify-content: space-between;
  padding: 0 0.4rem 0 0.3rem;
}
.enterprise-square-main .enterprise-square-list .item .top .start {
  display: flex;
}
.enterprise-square-main .enterprise-square-list .item .top .start .portrait .picture {
  width: 0.907rem;
  height: 0.907rem;
  border: solid 0.04rem #c6e2ff;
  position: relative;
  background-color: #efebeb;
  border-radius: 50%;
  margin-right: 0.32rem;
}
.enterprise-square-main .enterprise-square-list .item .top .start .portrait .picture .header {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.enterprise-square-main .enterprise-square-list .item .top .start .portrait .picture .sex {
  width: 0.24rem;
  height: 0.24rem;
  background-color: #50a5fd;
  border: solid 0.027rem #c6e2ff;
  position: absolute;
  top: 0.56rem;
  left: 0.68rem;
  border-radius: 50%;
  object-fit: scale-down;
}
.enterprise-square-main .enterprise-square-list .item .top .start .info {
  margin-top: 0.06rem;
}
.enterprise-square-main .enterprise-square-list .item .top .start .info > div:nth-child(1) {
  display: flex;
  align-items: center;
}
.enterprise-square-main .enterprise-square-list .item .top .start .info > div:nth-child(1) .name {
  font-size: 0.32rem;
  color: #333333;
  margin-right: 0.167rem;
}
.enterprise-square-main .enterprise-square-list .item .top .start .info > div:nth-child(1) .orange {
  background-color: #ffc61a;
  padding: 0.02rem 0.04rem;
  border-radius: 0.053rem;
  font-size: 0.213rem;
  color: #ffffff;
}
.enterprise-square-main .enterprise-square-list .item .redEnvelopeReward{
  height: 2.2133rem;
  margin:0.2667rem 0.56rem;
  border-radius: 0.1333rem;
  background: url("./../img/redEnvelope/hongbaodbj.png") center center/cover no-repeat;
  padding:0.5867rem 0.667rem 0.2933rem 2.2933rem;
  position: relative;
  overflow: hidden;
}
.enterprise-square-main .enterprise-square-list .item .redEnvelopeReward .bg2{
  position: absolute;
  top: -0.34335rem;
  left: -1.4rem;
  width: 2.9rem;
  height: 2.9rem;
}
.enterprise-square-main .enterprise-square-list .item .redEnvelopeReward .bg1{
  position: absolute;
  top: 50%;
  z-index: 1;
  margin-top: -0.8rem;
  left: 0.533rem;
  width: 1.6rem;
  height: 1.6rem;
}
.enterprise-square-main .enterprise-square-list .item .compeletRedEnvelopeReward{
  -webkit-opacity: 0.5;
  -moz-opacity: 0.5;
  -khtml-opacity: 0.5;
  filter:alpha(opacity=50);
  opacity: 0.5;
}
.layui-m-layer-layer_redEnvelopeReward{
  width: 100%;
  height: 9.6rem;
  background: rgb(255,74,74);
  background-image: linear-gradient(to bottom, rgb(255,145,107) 0%,rgb(255,74,74) 100%)!important;
  position: relative;
  border-radius: 0.2667rem!important;
}
.layui-m-layer-layer_redEnvelopeReward .RedEnvelopeMessage{
  margin-top: 0.64rem;
  font-size: 0.32rem;
}
.layui-m-layer-layer_redEnvelopeReward .receiveRedEnvelope{
  width: 2.0267rem;
  height: 2.0267rem;
  line-height: 2.0267rem;
  background: rgb(255,227,140);
  border-radius: 50%;
  text-align: center;
  font-size: 0.48rem;
  color: rgb(175,102,54);
  position: absolute;
  bottom: 0.9867rem;
  left: 50%;
  margin-left: -1.01335rem;
}
.layui-m-layer-layer_redEnvelopeReward .seeResult{
  position: absolute;
  bottom: 0.82667rem;
  left: 50%;
  margin-left: -1.2rem;
  color: rgb(254,255,189);
  font-size: 0.32rem;
  width: 2.4rem;
  text-align: center;
}
.anim-rotate {
  /*margin-left: -100rpx;*/
  transform-style: preserve-3d;
  -webkit-animation:rotateBY 2s linear infinite;
  -o-animation:rotateBY 2s linear infinite;
  animation: rotateBY 2s linear infinite;
}

@-o-keyframes rotateBY{
  0%{
    transform: rotateY(0deg);
  }
  25%{
    transform: rotateY(90deg);
  }
  50%{
    transform: rotateY(180deg);
  }
  75%{
    transform: rotateY(270deg);
  }
  100%{
    transform: rotateY(360deg);
  }
}
@-moz-keyframes rotateBY{
  0%{
    transform: rotateY(0deg);
  }
  25%{
    transform: rotateY(90deg);
  }
  50%{
    transform: rotateY(180deg);
  }
  75%{
    transform: rotateY(270deg);
  }
  100%{
    transform: rotateY(360deg);
  }
}
@keyframes rotateBY{
  0%{
    transform: rotateY(0deg);
  }
  25%{
    transform: rotateY(90deg);
  }
  50%{
    transform: rotateY(180deg);
  }
  75%{
    transform: rotateY(270deg);
  }
  100%{
    transform: rotateY(360deg);
  }
}
.layui-m-layer-layer_redEnvelopeReward .layui-m-layercont{
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 1.70667rem 1.6533rem 0.9867rem;
  color: #feffbd;
  font-size: 0.4267rem;
  text-align: center;
}
.layui-m-layer-layer_redEnvelopeReward .layer_redEnvelopeReward_content{

}
.enterprise-square-main .enterprise-square-list .item .redEnvelopeReward .message{
  color: rgb(255,255,255);
  font-size: 0.32rem;
  margin: 0;
}
.enterprise-square-main .enterprise-square-list .item .redEnvelopeReward .claimStatus{
  position: absolute;
  right:  0.667rem;
  bottom: 0.2933rem;
  color: rgb(255,198,74);
  font-size: 0.267rem;
  width: 1.4667rem;
  height: 0.48rem;
  line-height: 0.48rem;
  border-radius: 0.1333rem;
  background: rgb(218,65,55);
  text-align: center;
}
.enterprise-square-main .enterprise-square-list .item .top .start .info > div:nth-child(1) .blue {
  background-color: #3aa8fd;
  padding: 0.02rem 0.04rem;
  border-radius: 0.053rem;
  font-size: 0.213rem;
  color: #ffffff;
}
.enterprise-square-main .enterprise-square-list .item .top .start .info .number {
  font-size: 0.267rem;
  margin-top: 0.04rem;
  color: #999999;
}
.enterprise-square-main .enterprise-square-list .item .top .end {
  display: flex;
  align-items: center;
}
.enterprise-square-main .enterprise-square-list .item .top .end span {
  font-size: 0.32rem;
  color: #50a5fd;
}
.enterprise-square-main .enterprise-square-list .item .top .end .orange {
  color: #fca361;
}
.enterprise-square-main .enterprise-square-list .item .top .end img {
  max-width: 0.387rem;
  max-height: 0.387rem;
  margin-left: 0.1rem;
  object-fit: scale-down;
}
.enterprise-square-main .enterprise-square-list .item .message {
  display: flex;
  margin-left: 0.547rem;
  margin-right: 0.453rem;
  margin-bottom: 0.12rem;
}
.enterprise-square-main .enterprise-square-list .item .message .blessing {
  max-width: 1.44rem;
  max-height: 1.44rem;
  border-radius: 0.053rem;
  margin-right: 0.3rem;
  object-fit: scale-down;
  margin-top: 0.24rem;
}
.enterprise-square-main .enterprise-square-list .item .message .medal {
  max-width: 2rem;
  max-height: 2rem;
  margin-right: 0.16rem;
  object-fit: scale-down;
  padding-top: 0.14rem;
}
.enterprise-square-main .enterprise-square-list .item .message .msg {
  font-size: 0.32rem;
  line-height: 0.48rem;
  color: #333333;
  padding-top: 0.24rem;
}
.enterprise-square-main .enterprise-square-list .item .message .msg span {
  color: #50a5fd;
}
.enterprise-square-main .enterprise-square-list .item .message .msg div {
  word-break: break-all;
}
.enterprise-square-main .enterprise-square-list .item .state {
  display: flex;
  justify-content: space-between;
  margin-left: 0.56rem;
  margin-right: 0.52rem;
}
.enterprise-square-main .enterprise-square-list .item .state .privacy {
  display: flex;
  align-items: center;
}
.enterprise-square-main .enterprise-square-list .item .state .privacy .time {
  font-size: 0.32rem;
  color: #c0bfbf;
}
.enterprise-square-main .enterprise-square-list .item .state .privacy .private {
  width: 0.32rem;
  height: 0.32rem;
  text-align: center;
  background-color: #fe728f;
  font-size: 0.267rem;
  line-height: 0.32rem;
  color: #ffffff;
  border-radius: 50%;
  margin-left: 0.213rem;
  margin-right: 0.12rem;
}
.enterprise-square-main .enterprise-square-list .item .state .privacy .visible {
  font-size: 0.267rem;
  color: #999999;
}
.enterprise-square-main .enterprise-square-list .item .state .include {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.enterprise-square-main .enterprise-square-list .item .state .include span {
  font-size: 0.32rem;
  color: #c0bfbf;
  min-width: 0.5rem;
  text-align: end;
}
.enterprise-square-main .enterprise-square-list .item .state .include .good {
  display: flex;
  align-items: flex-end;
}
.enterprise-square-main .enterprise-square-list .item .state .include .good .like {
  width: 0.4rem;
  height: 0.427rem;
  margin-left: 0.1rem;
}
.enterprise-square-main .enterprise-square-list .item .state .include .good .selected {
  color: #f76268;
}
.enterprise-square-main .enterprise-square-list .item .state .include .info {
  display: flex;
  align-items: flex-end;
  margin-left: 0.5rem;
}
.enterprise-square-main .enterprise-square-list .item .state .include .info img {
  width: 0.413rem;
  height: 0.427rem;
  margin-left: 0.1rem;
}
.enterprise-square-main .enterprise-square-list .item .reply {
  margin: 0.24rem auto;
  width: 8.453rem;
  background-color: #f5f5f5;
  padding: 0.16rem 0;
}
.enterprise-square-main .enterprise-square-list .item .reply .mreply {
  display: flex;
  padding: 0 0.28rem;
}
.enterprise-square-main .enterprise-square-list .item .reply .mreply img {
  width: 0.4rem;
  height: 0.427rem;
  margin-right: 0.2rem;
}
.enterprise-square-main .enterprise-square-list .item .reply .mreply span {
  font-size: 0.32rem;
  color: #50a5fd;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.enterprise-square-main .enterprise-square-list .item .reply .msg {
  font-size: 0.32rem;
  color: #333333;
  padding: 0rem 0.28rem 0.08rem;
}
.enterprise-square-main .enterprise-square-list .item .reply .msg:last-child {
  padding-bottom: 0;
}
.enterprise-square-main .enterprise-square-list .item .reply .msg:nth-of-type(2) {
  padding-top: 0.08rem !important;
  padding-bottom: 0.08rem !important;
}
.enterprise-square-main .enterprise-square-list .item .reply .msg .blue {
  color: #50a5fd;
}
.community-mine-main {
  width: 100%;
  height: 3.347rem;
  background-image: linear-gradient(0deg, #45caf5 0%, #2e94fe 100%), linear-gradient(#ffffff, #ffffff);
  background-blend-mode: normal, normal;
}
.community-mine-main .public_top_header {
  position: relative;
  border: none;
  box-shadow: none;
  color: #fff;
  font-size: 0.48rem;
  font-weight: 540;
  background: rgba(255, 0, 0, 0);
}
.community-mine-main .public_top_header .return_back::before {
  border-color: #fff;
}
.community-mine-main .community-mine-info {
  width: 9.36rem;
  min-height: 1.973rem;
  background-color: #ffffff;
  border-radius: 0.16rem;
  margin: 1rem auto 0.2rem;
  padding-left: 0.453rem;
  padding-top: 0.44rem;
  display: flex;
}
.community-mine-main .community-mine-info .picture {
  width: 1.107rem;
  height: 1.107rem;
  border: solid 0.04rem #c6e2ff;
  position: relative;
  background-color: #efebeb;
  border-radius: 50%;
  margin-right: 0.2rem;
}
.community-mine-main .community-mine-info .picture .header {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.community-mine-main .community-mine-info .picture .sex {
  width: 0.307rem;
  height: 0.307rem;
  background-color: #50a5fd;
  border: solid 0.027rem #c6e2ff;
  position: absolute;
  top: 0.76rem;
  right: 0rem;
  border-radius: 50%;
  object-fit: scale-down;
}
.community-mine-main .community-mine-info .info .name {
  font-size: 0.373rem;
  font-weight: 600;
  color: #333333;
}
.community-mine-main .community-mine-info .info .company {
  font-size: 0.32rem;
  color: #999999;
  margin-top: 0.16rem;
}
.community-mine-main .community-mine-item {
  width: 9.36rem;
  height: 1.8rem;
  background-color: #ffffff;
  border-radius: 0.893rem;
  margin: 0 auto;
  display: flex;
  align-items: center;
}
.community-mine-main .community-mine-item .mitem {
  flex-grow: 1;
  text-align: center;
}
.community-mine-main .community-mine-item .mitem .mselected {
  color: #2e94fe !important;
}
.community-mine-main .community-mine-item .mitem div {
  font-size: 0.32rem;
  color: #999999;
}
.community-mine-main .community-mine-item > div:nth-child(1) img {
  width: 0.44rem;
  height: 0.453rem;
}
.community-mine-main .community-mine-item > div:nth-child(2) img {
  width: 0.413rem;
  height: 0.427rem;
}
.community-mine-main .community-mine-item > div:nth-child(3) img {
  width: 0.4rem;
  height: 0.427rem;
}
.community-mine-main .community-mine-item > div:nth-child(4) img {
  width: 0.467rem;
  height: 0.467rem;
}
a,
a:link,
a:visited,
a:hover,
a:active {
  text-decoration: none;
  color: inherit;
}
.release-topic-main {
  padding-bottom: 1.5rem;
}
.release-topic-main .public_top_header {
  position: relative;
  border: none;
  box-shadow: none;
  color: #333333;
  background: #fff;
  font-size: 0.48rem;
  font-weight: 540;
}
.release-topic-main .public_top_header .return_back::before {
  border-color: #2e94fe;
}
.release-topic-main .release-topic-entry {
  width: 9.36rem;
  min-height: 7.227rem;
  background-color: #ffffff;
  border-radius: 0.16rem;
  margin: 0.2rem auto 0.24rem;
  padding: 0.347rem 0.453rem;
}
.release-topic-main .release-topic-entry .title {
  border-bottom: 0.013rem solid #ebebeb;
  padding: 0.387rem 0.08rem;
}
.release-topic-main .release-topic-entry .title input {
  width: 100%;
  font-size: 0.373rem;
}
.release-topic-main .release-topic-entry .title input::-ms-input-placeholder {
  color: #999999;
}
.release-topic-main .release-topic-entry .title input::-webkit-input-placeholder {
  color: #999999;
}
.release-topic-main .release-topic-entry .number {
  float: right;
  font-size: 0.34rem;
  color: #999;
  margin-top: 0.06rem;
}
.release-topic-main .release-topic-entry .number span {
  color: #2e94fe;
}
.release-topic-main .release-topic-entry textarea {
  padding-top: 0.5rem;
  display: block;
  margin: 0 auto;
  width: 100%;
  height: 4.6rem;
  background: #fff;
  color: #444444;
  font-size: 0.373rem;
  line-height: 0.5rem;
  resize: none;
  border: none;
  margin-bottom: 0.2rem;
}
.release-topic-main .release-topic-entry textarea::-ms-input-placeholder {
  color: #c6c5c5;
}
.release-topic-main .release-topic-entry textarea::-webkit-input-placeholder {
  color: #c6c5c5;
}
.release-topic-main .release-topic-entry .add {
  position: relative;
  display: flex;
}
.release-topic-main .release-topic-entry .add .image {
  width: 0.56rem;
  height: 0.52rem;
  overflow: hidden;
  margin-left: 0.128rem;
}
.release-topic-main .release-topic-entry .add .image input {
  position: relative;
  z-index: 1;
  opacity: 0;
}
.release-topic-main .release-topic-entry .add .image img {
  width: 0.56rem;
  height: 0.52rem;
  position: absolute;
  top: 0rem;
  z-index: 0;
}
.release-topic-main .release-topic-entry .add .pic {
  width: 0.56rem;
  height: 0.56rem;
  margin-left: 0.68rem;
}
.release-topic-main .release-topic-entry .picture-section {
  display: flex;
  flex-wrap: wrap;
}
.release-topic-main .release-topic-entry .picture-section .picture-area {
  position: relative;
  width: 1.5rem;
  height: 1.8rem;
  margin: 0.6rem 0.3rem 0;
}
.release-topic-main .release-topic-entry .picture-section .picture-area .picture-close {
  position: absolute;
  height: 0.4rem;
  line-height: 0.4rem;
  text-align: center;
  width: 0.4rem;
  color: white;
  background: #bbb;
  font-weight: bold;
  top: -0.2rem;
  border-radius: 50%;
  right: -0.12rem;
}
.release-topic-main .release-topic-entry .picture-section .picture-area img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.release-topic-main .release-topic-options {
  padding: 0 0.453rem;
  width: 9.36rem;
  /* min-height: 2.36rem; */
  background-color: #ffffff;
  border-radius: 0.16rem;
  margin: 0 auto;
  /* padding-bottom: 0.48rem; */
  padding: 0.3rem 0.453rem;
}
.release-topic-main .release-topic-options .module {
  display: flex;
  justify-content: space-between;
}
.release-topic-main .release-topic-options .module > div:nth-child(1) {
  font-size: 0.373rem;
  color: #333333;
  font-weight: 600;
}
.release-topic-main .release-topic-options .module > div:nth-child(2) {
  padding-right: 0.64rem;
  position: relative;
}
.release-topic-main .release-topic-options .module > div:nth-child(2)::after {
  content: " ";
  position: absolute;
  display: inline-block;
  height: 0.2rem;
  width: 0.2rem;
  border-width: 0 0 0.04rem 0.04rem;
  border-color: #e2e2e2;
  border-style: solid;
  -webkit-transform: matrix(-0.71, -0.71, 0.71, -0.71, 0, 0);
  transform: matrix(-0.71, -0.71, 0.71, -0.71, 0, 0);
  right: 0.2rem;
  top: 0.14rem;
}
.release-topic-main .release-topic-options .module .module-select {
  font-size: 0.373rem;
  color: #999999;
  width: 6.4rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: right;
}
.release-topic-main .release-topic-options .module .member {
  font-size: 0.373rem;
  color: #2e94fe;
  width: 6.4rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: right;
}
.release-topic-main .release-topic-button {
  width: 8.64rem;
  height: 1.2rem;
  line-height: 1.2rem;
  background-color: #2e94fe;
  border-radius: 0.593rem;
  margin: 2rem auto;
  font-size: 0.4rem;
  color: #ffffff;
  text-align: center;
}
.personal-home-page {
  padding-bottom: 1.5rem;
}
.personal-home-page .public_top_header {
  position: relative;
  border: none;
  box-shadow: none;
  color: #fff;
  font-size: 0.48rem;
  font-weight: 540;
  background: rgba(255, 0, 0, 0);
}
.personal-home-page .public_top_header .return_back::before {
  border-color: #fff;
}
.personal-home-page .public_top_header .search {
  width: 6.5rem;
  height: 0.773rem;
  line-height: 0.773rem;
  background-color: #ffffff;
  border-radius: 0.38rem;
  display: flex;
  justify-content: space-between;
  position: absolute;
  top: 0.2rem;
  left: 1.6rem;
}
.personal-home-page .public_top_header .search input {
  width: 2.8rem;
  background-color: #ffffff;
  margin-left: 0.5rem;
}
.personal-home-page .public_top_header .search .btsearch {
  width: 0.5rem;
  height: 0.5rem;
  margin-top: 0.14rem;
  margin-right: 0.4rem;
}
.personal-home-page .public_top_header .search .active {
  color: #2d94f9;
}
.personal-home-page .public_top_header .search .active:after {
  border-top: 0.15rem solid #2d94f9;
}
.personal-home-page .public_top_header .gift-icon-display {
  display: inline-block;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 1001;
  padding-top: 0.1rem;
  padding-right: 0.45rem;
}
.personal-home-page .public_top_header .gift-icon-display .gift-icon-text {
  width: 0.8rem;
  font-family: NotoSansHans-Medium;
  font-size: 0.24rem;
  line-height: 0.32rem;
  letter-spacing: 0.06rem;
  color: #cdeeff;
  display: inline-block;
}
.personal-home-page .public_top_header .gift-icon-display .gift-icon-case {
  width: 0.59rem;
  height: 0.57rem;
}
.personal-home-page .public_top_header .gift-icon-display .gift-icon-right {
  width: 0.52rem;
  padding-bottom: 0.2rem;
}
.personal-home-page .personal-home-info {
  width: 9.36rem;
  min-height: 4.653rem;
  background-color: #ffffff;
  border-radius: 0.16rem;
  margin: 1.4rem auto 0.26rem;
  position: relative;
  text-align: center;
}
.personal-home-page .personal-home-info .picture {
  width: 1.587rem;
  height: 1.587rem;
  border: 0.01rem solid #c6e2ff;
  position: absolute;
  background-color: #efebeb;
  border-radius: 50%;
  margin: 0 auto;
  top: -0.8rem;
  left: 50%;
  transform: translate(-50%);
}
.personal-home-page .personal-home-info .picture .header {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.personal-home-page .personal-home-info .picture .sex {
  width: 0.44rem;
  height: 0.44rem;
  background-color: #50a5fd;
  border: solid 0.027rem #c6e2ff;
  position: absolute;
  top: 1rem;
  right: 0rem;
  border-radius: 50%;
}
.personal-home-page .personal-home-info .name {
  font-size: 0.48rem;
  color: #333333;
  text-align: center;
  padding-top: 1rem;
  font-weight: 600;
  margin-bottom: 0.46rem;
}
.personal-home-page .personal-home-info .universal {
  font-size: 0.347rem;
  color: #999999;
  margin-bottom: 0.2rem;
  text-align: left;
  padding-left: 0.88rem;
}
.personal-home-page .personal-home-date {
  width: 9.36rem;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
}
.personal-home-page .personal-home-date .date {
  padding: 0 0.28rem;
  width: 4.533rem;
  min-height: 2.2rem;
  background-color: #ffffff;
  border-radius: 0.16rem;
}
.personal-home-page .personal-home-date .date .tag {
  width: 1.813rem;
  height: 0.6rem;
  line-height: 0.6rem;
  border-radius: 0rem 0rem 0.16rem 0.16rem;
  margin-bottom: 0.28rem;
  font-size: 0.32rem;
  color: #ffffff;
  text-align: center;
}
.personal-home-page .personal-home-date .date .birthday {
  background-color: #ffc000;
}
.personal-home-page .personal-home-date .date .anniversary {
  background-color: #3098fd;
}
.personal-home-page .personal-home-date .date .time {
  font-size: 0.347rem;
  color: #333333;
  font-weight: 600;
  margin-bottom: 0.12rem;
  padding-left: 0.267rem;
}
.personal-home-page .personal-home-date .date .state {
  font-size: 0.267rem;
  color: #666666;
  padding-left: 0.267rem;
}
.personal-home-page .personal-home-bottom {
  background-color: #ffffff;
  position: fixed;
  width: 100%;
  min-height: 2.28rem;
  left: 0;
  bottom: 0;
  z-index: 1000;
  padding: 0 0.64rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.personal-home-page .personal-home-bottom .redpacket {
  width: 4.267rem;
  height: 1.147rem;
  line-height: 1.147rem;
  text-align: center;
  background-image: linear-gradient(0deg, #fd967a 0%, #f63c45 100%), linear-gradient(#f7484c, #f7484c);
  background-blend-mode: normal, 
                normal;
  border-radius: 0.16rem;
  font-size: 0.32rem;
  color: #ffffff;
}
.personal-home-page .personal-home-bottom .redpacket img {
  width: 0.587rem;
  height: 0.56rem;
  margin-bottom: -0.14rem;
}
.personal-home-page .personal-home-bottom .gift {
  width: 4.267rem;
  height: 1.147rem;
  line-height: 1.147rem;
  text-align: center;
  background-image: linear-gradient(0deg, #3eb9f8 0%, #2e94fe 100%), linear-gradient(#36a7fb, #36a7fb);
  background-blend-mode: normal, 
                normal;
  border-radius: 0.16rem;
  font-size: 0.32rem;
  color: #ffffff;
}
.personal-home-page .personal-home-bottom .gift img {
  width: 0.533rem;
  height: 0.533rem;
  margin-bottom: -0.14rem;
}
.modal {
  display: none;
  position: fixed;
  z-index: 1100;
  padding: 1.2rem 0;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: #000000;
  background-color: rgba(0, 0, 0, 0.9);
}
@-webkit-keyframes zoom {
  from {
    -webkit-transform: scale(0);
  }
  to {
    -webkit-transform: scale(1);
  }
}
@keyframes zoom {
  from {
    transform: scale(0.1);
  }
  to {
    transform: scale(1);
  }
}
.modal .modal-content {
  margin: 2rem auto;
  display: block;
  width: 80%;
  max-width: 9rem;
  -webkit-animation-name: zoom;
  -webkit-animation-duration: 0.6s;
  animation-name: zoom;
  animation-duration: 0.6s;
  object-fit: scale-down;
}
.modal .close {
  position: absolute;
  top: 0.3rem;
  right: 0.6rem;
  color: #f1f1f1;
  font-size: 1rem;
  font-weight: bold;
  transition: 0.3s;
}
.modal .close:hover,
.modal .close:focus {
  color: #bbb;
  text-decoration: none;
  cursor: pointer;
}
.msg-modal {
  display: none;
  position: fixed;
  z-index: 1001;
  padding: 1.18rem 0;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: #000000;
  background-color: rgba(0, 0, 0, 0.5);
}
.msg-modal .msg-bg {
  background: white;
  border: 0.01rem solid #e6e6e6;
}
.msg-modal .msg-bg textarea {
  padding: 0.5rem;
  display: block;
  margin: 0 auto;
  width: 100%;
  height: 4.2rem;
  background: #fff;
  color: #444444;
  font-size: 0.373rem;
  line-height: 0.5rem;
  resize: none;
  border: none;
}
.msg-modal .msg-bg .add {
  height: 1.2rem;
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 0.01rem solid #e9e9e9;
  padding: 0.32rem 0.5rem;
}
.msg-modal .msg-bg .add .left .pic {
  width: 0.56rem;
  height: 0.56rem;
  margin-right: 0.4rem;
}
.msg-modal .msg-bg .add .qqFace {
  position: absolute !important;
  width: 80% !important;
  top: 1.2rem !important;
}
.msg-modal .msg-bg .add .right {
  display: flex;
}
.msg-modal .msg-bg .add .right .cancel {
  padding: 0 0.2rem;
  background-color: white;
  border-radius: 0.1rem;
  font-size: 0.37rem;
  color: #c0bfbf;
  border: 1px solid #e6e6e6;
  margin-right: 0.4rem;
}
.msg-modal .msg-bg .add .right .published {
  padding: 0 0.2rem;
  background-color: #2e94fe;
  border-radius: 0.1rem;
  font-size: 0.37rem;
  color: #ffffff;
  border: 1px solid #2e94fe;
}
.msg-modal .msg-bg .add .right .published[disabled="disabled"] {
  opacity: 0.5;
  filter: alpha(opacity=50);
}
.choose-department-main .department-company {
  margin: 0.1rem 0 0.2rem 0;
  font-size: 0.32rem;
  color: #333;
  height: 1rem;
  line-height: 1rem;
  padding: 0 0.4rem;
  background: white;
}
.choose-department-main .department-company a {
  color: #2e94fe;
}
.choose-department-main .choose-department2 {
  font-size: 0.32rem;
  background: white;
  position: relative;
}
.choose-department-main .choose-department2 .section1 {
  height: 1.2rem;
  line-height: 1.2rem;
  padding: 0 0.4rem;
  border-bottom: 0.0012rem solid #e6e6e6;
}
.choose-department-main .choose-department2 .section1 .checkbox_section input[type=checkbox]:checked + label,
.choose-department-main .choose-department2 .section1 .checkbox_section input[type=radio]:checked + label {
  background-color: #4993fa;
}
.choose-department-main .choose-department2 .section1 .checkbox_section {
  right: 0rem;
  margin-right: 0.1rem;
  top: 0.14rem;
  display: inline-block;
}
.choose-department-main .choose-department2 .section1 .checkbox_section label {
  width: 0.48rem;
  height: 0.48rem;
  background: url(../img/square/icon_checked_w.png) center center / 76% no-repeat #fff;
  border: solid 0.027rem #e3e3e3;
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  border-radius: 100%;
  box-sizing: content-box;
}
.choose-department-main .choose-department2 .section1 .checkbox_section label::before {
  display: none;
}
.choose-department-main .choose-department2 .section1 .area {
  display: inline-flex;
  justify-content: space-between;
  align-items: center;
  width: 8.4rem;
}
.choose-department-main .choose-department2 .section1 .area div {
  display: inline-block;
  height: 0.2rem;
  width: 0.2rem;
  border-width: 0 0 0.04rem 0.04rem;
  border-color: #666;
  border-style: solid;
  margin-top: 0.2rem;
  -webkit-transform: translateY(-50%) matrix(-0.71, -0.71, 0.71, -0.71, 0, 0);
  transform: translateY(-50%) matrix(-0.71, -0.71, 0.71, -0.71, 0, 0);
}
.choose-department-main .choose-department2 .section1 .department {
  color: #333;
  position: relative;
  width: 8.4rem;
  display: inline-block;
}
.choose-department-main .choose-department2 .section1 .department::after {
  content: " ";
  position: absolute;
  display: inline-block;
  height: 0.2rem;
  width: 0.2rem;
  border-width: 0 0 0.04rem 0.04rem;
  border-color: #666;
  border-style: solid;
  -webkit-transform: translateY(-50%) matrix(-0.71, -0.71, 0.71, -0.71, 0, 0);
  transform: translateY(-50%) matrix(-0.71, -0.71, 0.71, -0.71, 0, 0);
  right: 0.6rem;
  top: 50%;
}
@keyframes slide-down {
  0% {
    transform: scale(1, 0);
  }
  100% {
    transform: scale(1, 1);
  }
}
@-webkit-keyframes slide-down {
  0% {
    -webkit-transform: scale(1, 0);
  }
  100% {
    -webkit-transform: scale(1, 1);
  }
}
@keyframes slide-up {
  0% {
    transform: scale(1, 1);
  }
  100% {
    transform: scale(1, 0);
  }
}
@-webkit-keyframes slide-up {
  0% {
    -webkit-transform: scale(1, 1);
  }
  100% {
    -webkit-transform: scale(1, 0);
  }
}
.choose-department-main .choose-department2 .show-up {
  animation: slide-up 0.3s ease-in;
  -webkit-animation: slide-up 0.3s ease-in;
}
.choose-department-main .choose-department2 .show-down {
  animation: slide-down 0.3s ease-in;
  -webkit-animation: slide-down 0.3s ease-in;
}
.choose-department-main .choose-department2 .section2 {
  display: none;
  transition: all 0.5s;
  padding-left: 1rem;
  transform-origin: 50% 0;
  animation: slide-down 0.3s ease-in;
  -webkit-animation: slide-down 0.3s ease-in;
}
.choose-department-main .choose-department2 .section2 .item {
  height: 1.2rem;
  line-height: 1.2rem;
  border-bottom: 0.0012rem solid #e6e6e6;
}
.choose-department-main .choose-department2 .section2 .item .checkbox_section input[type=checkbox]:checked + label,
.choose-department-main .choose-department2 .section2 .item .checkbox_section input[type=radio]:checked + label {
  background-color: #4993fa;
}
.choose-department-main .choose-department2 .section2 .item .checkbox_section {
  right: 0rem;
  margin-right: 0.1rem;
  top: 0.14rem;
  display: inline-block;
}
.choose-department-main .choose-department2 .section2 .item .checkbox_section label {
  width: 0.48rem;
  height: 0.48rem;
  background: url(../img/square/icon_checked_w.png) center center / 76% no-repeat #fff;
  border: solid 0.027rem #e3e3e3;
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  border-radius: 100%;
  box-sizing: content-box;
}
.choose-department-main .choose-department2 .section2 .item .checkbox_section label::before {
  display: none;
}
.choose-department-main .choose-department2 .section2 .item .area {
  display: inline-flex;
  justify-content: space-between;
  align-items: center;
  width: 7.8rem;
}
.choose-department-main .choose-department2 .section2 .item .area div {
  display: inline-block;
  height: 0.2rem;
  width: 0.2rem;
  border-width: 0 0 0.04rem 0.04rem;
  border-color: #666;
  border-style: solid;
  margin-top: 0.2rem;
  -webkit-transform: translateY(-50%) matrix(-0.71, -0.71, 0.71, -0.71, 0, 0);
  transform: translateY(-50%) matrix(-0.71, -0.71, 0.71, -0.71, 0, 0);
}
.choose-department-main .choose-department2 .section2 .item .department {
  color: #333;
  position: relative;
  width: 7.4rem;
  display: inline-block;
}
.choose-department-main .choose-department2 .section2 .item .department::after {
  content: " ";
  position: absolute;
  display: inline-block;
  height: 0.2rem;
  width: 0.2rem;
  border-width: 0 0 0.04rem 0.04rem;
  border-color: #666;
  border-style: solid;
  -webkit-transform: translateY(-50%) matrix(-0.71, -0.71, 0.71, -0.71, 0, 0);
  transform: translateY(-50%) matrix(-0.71, -0.71, 0.71, -0.71, 0, 0);
  right: 0.6rem;
  top: 50%;
}
.choose-department-main .choose-department {
  height: 1.2rem;
  line-height: 1.2rem;
  font-size: 0.32rem;
  background: white;
  padding: 0 0.4rem;
  position: relative;
  border-bottom: 0.0012rem solid #e6e6e6;
}
.choose-department-main .choose-department .checkbox_section input[type=checkbox]:checked + label,
.choose-department-main .choose-department .checkbox_section input[type=radio]:checked + label {
  background-color: #4993fa;
}
.choose-department-main .choose-department .checkbox_section {
  right: 0rem;
  margin-right: 0.1rem;
  top: 0.14rem;
  display: inline-block;
}
.choose-department-main .choose-department .checkbox_section label {
  width: 0.48rem;
  height: 0.48rem;
  background: url(../img/square/icon_checked_w.png) center center / 76% no-repeat #fff;
  border: solid 0.027rem #e3e3e3;
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  border-radius: 100%;
  box-sizing: content-box;
}
.choose-department-main .choose-department .checkbox_section label::before {
  display: none;
}
.choose-department-main .choose-department .area {
  display: inline-flex;
  justify-content: space-between;
  align-items: center;
  width: 90%;
}
.choose-department-main .choose-department .area div {
  display: inline-block;
  height: 0.2rem;
  width: 0.2rem;
  border-width: 0 0 0.04rem 0.04rem;
  border-color: #666;
  border-style: solid;
  margin-top: 0.2rem;
  -webkit-transform: translateY(-50%) matrix(-0.71, -0.71, 0.71, -0.71, 0, 0);
  transform: translateY(-50%) matrix(-0.71, -0.71, 0.71, -0.71, 0, 0);
}
.choose-department-main .choose-department .department {
  color: #333;
  position: relative;
  width: 8.4rem;
  display: inline-block;
}
.choose-department-main .choose-department .department::after {
  content: " ";
  position: absolute;
  display: inline-block;
  height: 0.2rem;
  width: 0.2rem;
  border-width: 0 0 0.04rem 0.04rem;
  border-color: #666;
  border-style: solid;
  -webkit-transform: translateY(-50%) matrix(-0.71, -0.71, 0.71, -0.71, 0, 0);
  transform: translateY(-50%) matrix(-0.71, -0.71, 0.71, -0.71, 0, 0);
  right: 0.6rem;
  top: 50%;
}
.choose-department-main .fixed-bottom {
  position: fixed;
  bottom: 0;
  width: 100%;
}
.choose-department-main .fixed-bottom > div:nth-child(1) {
  height: 0.8rem;
  line-height: 0.8rem;
  font-size: 0.24rem;
  color: #333;
  padding: 0 0.4rem;
}
.choose-department-main .fixed-bottom > div:nth-child(2) {
  height: 1.2rem;
  line-height: 1.2rem;
  font-size: 0.32rem;
  background: #fff;
  padding: 0 0.4rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.choose-department-main .fixed-bottom > div:nth-child(2) .scroll::-webkit-scrollbar {
  display: none;
}
.choose-department-main .fixed-bottom > div:nth-child(2) .option {
  height: 0.6rem;
  line-height: 0.6rem;
  max-width: 7.4rem;
  overflow-x: scroll;
  overflow-y: hidden;
  white-space: nowrap;
}
.choose-department-main .fixed-bottom > div:nth-child(2) .option span {
  color: #333;
  font-size: 0.32rem;
  height: 0.4rem;
  line-height: 0.4rem;
  background-color: #f2f2f2;
  padding: 0.12rem;
  margin-right: 0.2rem;
  border-radius: 0.04rem;
}
.choose-department-main .fixed-bottom > div:nth-child(2) .sure {
  padding: 0 0.2rem;
  background-color: #2e94fe;
  border-radius: 0.1rem;
  font-size: 0.32rem;
  color: #ffffff;
  height: 0.6rem;
  line-height: 0.6rem;
  border: 1px solid #2e94fe;
}
.choose-department-main .fixed-bottom > div:nth-child(2) .sure[disabled="disabled"] {
  opacity: 0.5;
  filter: alpha(opacity=50);
}
.qqFace {
  position: relative;
  width: 100%;
  background: #fff;
  max-height: 4rem;
  overflow-y: auto;
  z-index: 999;
}
.qqFace table {
  width: 100%;
}
.qqFace table td {
  padding: 0.1rem;
  text-align: center;
}
.qqFace table td img {
  width: 0.6rem;
  height: auto;
}
