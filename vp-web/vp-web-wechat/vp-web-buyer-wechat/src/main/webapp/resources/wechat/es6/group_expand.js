var app = new Vue({
    el: '#tuanSavePage',
    data: {
        item: {
            id: dataItemId,
            img: dataItemImg,
            title: dataItemTitle,
            price: dataItemPrice,
            oldPrice: dataItemOldPrice,
        }
    },
    methods: {
        savePictrue: function () {
            html2canvas(document.querySelector("#saveAsPictrue"),{useCORS: true}).then(function (canvas) {
                //document.body.appendChild(canvas)
                var context = canvas.getContext('2d');
                // 【重要】关闭抗锯齿
                context.mozImageSmoothingEnabled = false;
                context.webkitImageSmoothingEnabled = false;
                context.msImageSmoothingEnabled = false;
                context.imageSmoothingEnabled = false;
                // var imgToSave = Canvas2Image.convertToPNG(canvas, canvas.width, canvas.height)
                // console.log("haha",imgToSave)


                var imgToSave = Canvas2Image.convertToPNG(canvas, canvas.width, canvas.height).getAttribute('src')
                console.log(imgToSave)
                $("#base64").val(imgToSave)
                $("#savePosterForm").submit()
                //app.saveFile(imgToSave);
            });
        },
        // saveFile:function(data, filename){
        //     var save_link = document.createElementNS('http://www.w3.org/1999/xhtml', 'a');
        //     save_link.href = data;
        //     save_link.download = filename;

        //     var event = document.createEvent('MouseEvents');
        //     event.initMouseEvent('click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
        //     save_link.dispatchEvent(event);
        // },
        saveFile: function (Url) {
           /*  var blob = new Blob([''], { type: 'application/octet-stream' });
            var url = URL.createObjectURL(blob);
            var a = document.createElement('a');
            a.href = Url;
            a.download = Url.replace(/(.*\/)*([^.]+.*)/ig, "$2").split("?")[0];
            var e = document.createEvent('MouseEvents');
            e.initMouseEvent('click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
            a.dispatchEvent(e);
            URL.revokeObjectURL(url); */
        },
    }
})