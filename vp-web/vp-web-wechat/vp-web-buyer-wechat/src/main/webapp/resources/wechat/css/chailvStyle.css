@charset "utf-8";
.public_right_nav ul li {
  display: block;
  padding: 0.13rem 0;
  color: #fff;
  font-size: 0.32rem;
  text-align: center;
}
.public_right_nav ul li.active {
  color: #4993fa;
}
.add_btn {
  display: inline-block;
  min-width: 1.5467rem;
  height: 0.56rem;
  line-height: 0.56rem;
  text-align: center;
  border-radius: 0.1333rem;
  padding: 0 0.2667rem;
  background: #4993fa;
  font-size: 0.3467rem;
  color: #fff;
}
.add_btn.del {
  background-color: #fc1b3b;
}
.button_box {
  text-align: center;
  padding: 0.2667rem 0 0.5333rem;
}
.button_box .btn_theme_empty {
  display: inline-block;
  width: 3.7333rem;
  height: 0.9067rem;
  border: 1px solid #4993fa;
  background: #fff;
  text-align: center;
  line-height: 0.88rem;
  font-size: 0.3733rem;
  color: #4993fa;
  border-radius: 0.1333rem;
  margin: 0 0.4rem;
}
.button_box .btn_theme_empty.active {
  background: #4993fa;
  color: #fff;
}
.chailvPage .xingcheng_box {
  background: #fff;
  margin: 0.1333rem 0;
}
.chailvPage .xingcheng_box header {
  line-height: 0.6667rem;
  background: #e9f0f9;
  padding: 0.1067rem 0.3333rem 0.1067rem 0;
  overflow: hidden;
}
.chailvPage .xingcheng_box header .split {
  display: inline-block;
  vertical-align: middle;
  width: 0.1067rem;
  height: 0.32rem;
  background: #4993fa;
  margin: -0.04rem 0.24rem 0 0.4rem;
}
.chailvPage .xingcheng_box header label {
  display: block;
  width: 1.6267rem;
  height: 0.6667rem;
  font-size: 0.3467rem;
  color: #fff;
  text-align: center;
  border-top-right-radius: 0.3333rem;
  border-bottom-right-radius: 0.3333rem;
  background: #589bf9;
  background: -webkit-linear-gradient(left, #589bf9, #87b6f7);
  /* Safari 5.1 - 6.0 */
  background: -o-linear-gradient(left, #589bf9, #87b6f7);
  /* Opera 11.1 - 12.0 */
  background: -moz-linear-gradient(left, #589bf9, #87b6f7);
  /* Firefox 3.6 - 15 */
  background: linear-gradient(left, #589bf9, #87b6f7);
  /* 标准的语法 */
  float: left;
}
.chailvPage .xingcheng_box header .add_btn {
  float: right;
  margin: 0.0533rem 0;
}
.chailvPage .table_box {
  background: #fff;
  margin: 0.1333rem 0;
}
.chailvPage .table_box header {
  font-size: 0.3467rem;
  color: #333;
  line-height: 0.6133rem;
  padding: 0.1333rem 0.3333rem;
  background: #e9f0f9;
}
.chailvPage .table_box header .split {
  display: inline-block;
  vertical-align: middle;
  width: 0.1067rem;
  height: 0.32rem;
  background: #4993fa;
  margin-top: -0.04rem;
  margin-right: 0.24rem;
}
.chailvPage .table_box header .add_btn {
  float: right;
}
.chailvPage .table_box .content {
  padding: 0.2667rem 0.3333rem;
}
.chailvPage .table_box .content .text_empty_section {
  margin: 1.3333rem 0;
}
.chailvPage .table_box .table_label {
  padding: 0.16rem 0;
}
.chailvPage .table_box .table_label label {
  display: block;
  width: 2.1067rem;
  line-height: 0.4267rem;
  font-size: 0.32rem;
  color: #fff;
  background: #79b1ff;
  padding: 0 0.0667rem;
  border-radius: 0.0667rem;
  text-align: center;
}
.chailvPage .table_box .table_label label.color_y {
  background: #faa63a;
}
.chailvPage .table_box .table_label .no {
  float: right;
  color: #999;
  font-size: 0.2933rem;
  margin-top: 0.1rem;
}
.chailvPage .table_box .table_label .no span {
  color: #333;
}
.chailvPage .table_box .table {
  border: 1px solid #e1e1e1;
  margin-bottom: 0.4267rem;
}
.chailvPage .table_box .table .line {
  width: 100%;
  padding: 0.5333rem 0;
  overflow: hidden;
}
.chailvPage .table_box .table .line + .line {
  border-top: 1px solid #e1e1e1;
}
.chailvPage .table_box .table .line > div {
  float: left;
  line-height: 0.5333rem;
}
.chailvPage .table_box .table .line > div:nth-child(1) {
  width: 2.1333rem;
  text-align: center;
  font-size: 0.3467rem;
  color: #333;
  font-weight: 800;
}
.chailvPage .table_box .table .line > div:nth-child(1) span {
  margin-right: 0.08rem;
}
.chailvPage .table_box .table .line > div:nth-child(2) {
  width: 3.0667rem;
  text-align: left;
  font-size: 0.3467rem;
  color: #4993fa;
  font-weight: 800;
}
.chailvPage .table_box .table .line > div:nth-child(3) {
  width: 3.3333rem;
  text-align: center;
  font-size: 0.2933rem;
  color: #4993fa;
}
.chailvPage .header_page {
  width: 100%;
  height: 2.5067rem;
  margin-bottom: -0.6667rem;
  padding-top: 0.6667rem;
  padding-left: 0.4rem;
  background: #4a94fa;
  background: -webkit-linear-gradient(left, #6cc9ff, #4a94fa);
  /* Safari 5.1 - 6.0 */
  background: -o-linear-gradient(left, #6cc9ff, #4a94fa);
  /* Opera 11.1 - 12.0 */
  background: -moz-linear-gradient(left, #6cc9ff, #4a94fa);
  /* Firefox 3.6 - 15 */
  background: linear-gradient(left, #6cc9ff, #4a94fa);
  /* 标准的语法 */
}
.chailvPage .header_page h1 {
  color: #fff;
  font-size: 0.6133rem;
}
.chailvPage .header_page h1 span {
  color: #fff;
  font-size: 0.2933rem;
  font-weight: lighter;
  margin-left: 0.32rem;
}
.chailvPage .margin_round_section {
  margin: 0.2667rem;
  background: #fff;
  border-radius: 0.05rem;
}
.chailvPage .margin_round_section header {
  position: relative;
  padding: 0.24rem 0.4rem;
  font-size: 0.3467rem;
  line-height: 0.8rem;
  overflow: hidden;
}
.chailvPage .margin_round_section header + .form_section {
  border-top: 1px solid #e1e1e1;
}
.chailvPage .margin_round_section header label {
  display: block;
  width: 1.6267rem;
  height: 0.6667rem;
  line-height: 0.6667rem;
  font-size: 0.3467rem;
  color: #fff;
  text-align: center;
  border-top-right-radius: 0.3333rem;
  border-bottom-right-radius: 0.3333rem;
  background: #589bf9;
  background: -webkit-linear-gradient(left, #589bf9, #87b6f7);
  /* Safari 5.1 - 6.0 */
  background: -o-linear-gradient(left, #589bf9, #87b6f7);
  /* Opera 11.1 - 12.0 */
  background: -moz-linear-gradient(left, #589bf9, #87b6f7);
  /* Firefox 3.6 - 15 */
  background: linear-gradient(left, #589bf9, #87b6f7);
  /* 标准的语法 */
  float: left;
  margin-left: -0.4rem;
}
.chailvPage .margin_round_section header h2 {
  font-size: 0.3467rem;
  font-weight: lighter;
  margin-left: 0.32rem;
  float: left;
}
.chailvPage .margin_round_section header .arrow {
  position: absolute;
  margin-top: 0.16rem;
  margin-right: 0.2rem;
  float: right;
  z-index: 99;
  display: inline-block;
  height: 0.32rem;
  width: 0.32rem;
  border-width: 0.04rem 0.04rem 0 0;
  border-color: #999;
  border-style: solid;
  -webkit-transform: matrix(-0.71, 0.71, -0.71, -0.71, 0, 0);
  transform: matrix(-0.71, 0.71, -0.71, -0.71, 0, 0);
  position: relative;
  top: -0.04rem;
}
.chailvPage .margin_round_section header .arrow.active {
  margin-top: 0.36rem;
  -webkit-transform: matrix(0.71, -0.71, 0.71, 0.71, 0, 0);
  transform: matrix(0.71, -0.71, 0.71, 0.71, 0, 0);
}
.chailvPage .margin_round_section .table_box header {
  border-top: 1px solid #e1e1e1;
  border-bottom: none;
  background: transparent;
  margin-bottom: -0.6rem;
}
.chailvPage .steps_section {
  padding: 0.4rem;
  border-top: 1px solid #e1e1e1;
}
.chailvPage .steps_section .line:after {
  content: " ";
  display: block;
  font-size: 0;
  height: 0;
  clear: both;
  visibility: hidden;
}
.chailvPage .steps_section .line:last-child span.num:before {
  height: 0;
}
.chailvPage .steps_section .line + .line {
  margin-top: 0.4rem;
}
.chailvPage .steps_section .line span {
  display: block;
  float: left;
  font-size: 0.32rem;
  color: #666;
  text-align: center;
  line-height: 0.6133rem;
}
.chailvPage .steps_section .line span.num {
  position: relative;
  width: 0.6133rem;
  height: 0.6133rem;
  font-size: 0.3467rem;
  color: #fff;
  background: #4a94fa;
  border-radius: 100%;
}
.chailvPage .steps_section .line span.num:before {
  content: " ";
  width: 1px;
  height: 70px;
  border-left: 1px dashed #4a94fa;
  position: absolute;
  left: 50%;
  top: 0.6133rem;
}
.chailvPage .steps_section .line span.num + span {
  width: 1.7067rem;
  text-align: center;
}
.chailvPage .steps_section .line span.num + span + span {
  width: 2.24rem;
  text-align: left;
}
.chailvPage .steps_section .line span.reason {
  color: #4993fa;
}
.chailvPage .steps_section .line span:last-child {
  float: right;
}
.chailvPage.has_fixed_footer {
  padding-bottom: 2rem;
}
.chailvPage .fixed_bottom_option_line {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  background: #fff;
  box-shadow: 0 0 0.2667rem rgba(0, 0, 0, 0.15);
  z-index: 100;
}
.chailvPage .fixed_bottom_option_line a {
  display: block;
  width: 50%;
  float: left;
  text-align: center;
  height: 1.3333rem;
  line-height: 1.3333rem;
  font-size: 0.32rem;
  color: #4993fa;
}
.chailvPage .fixed_bottom_option_line a:only-child {
  margin: 0.2rem auto;
  height: 0.9333rem;
  line-height: 0.9333rem;
  background: #4993fa;
  border-radius: 0.1067rem;
  color: #fff;
  float: none;
  width: 90%;
}
.chailvPage .fixed_bottom_option_line .submit {
  position: absolute;
  left: 50%;
  top: -50%;
  width: 1.76rem;
  height: 1.76rem;
  margin-left: -0.88rem;
  text-align: center;
  border: 0.08rem solid #fff;
  font-size: 0.3467rem;
  color: #fff;
  border-radius: 100%;
  box-shadow: 0 0 0.2667rem rgba(0, 0, 0, 0.15);
  background: #4993fa;
  background: -webkit-linear-gradient(left, #4993fa, #88b6f7);
  /* Safari 5.1 - 6.0 */
  background: -o-linear-gradient(left, #4993fa, #88b6f7);
  /* Opera 11.1 - 12.0 */
  background: -moz-linear-gradient(left, #4993fa, #88b6f7);
  /* Firefox 3.6 - 15 */
  background: linear-gradient(left, #4993fa, #88b6f7);
  /* 标准的语法 */
}
.chailvPage .chailv_items.active {
  border: 1px solid #4993fa;
}
.chailvPage .chailv_items header {
  border-bottom: 1px solid #e1e1e1;
  font-weight: 800;
  font-size: 0.2933rem;
}
.chailvPage .chailv_items header .status {
  float: right;
  font-size: 0.32rem;
  color: #4993fa;
}
.chailvPage .chailv_items header .status.red {
  color: #fa495a;
}
.chailvPage .chailv_items .content {
  position: relative;
  padding: 0.1333rem 0.4rem 0.4rem;
}
.chailvPage .chailv_items .content > a {
  position: relative;
  float: right;
  font-size: 0.32rem;
  color: #4993fa;
  font-weight: 800;
  z-index: 99;
}
.chailvPage .chailv_items .content h2 {
  font-size: 0.3467rem;
  color: #333;
  line-height: 0.7467rem;
  max-width: 80%;
}
.chailvPage .chailv_items .content .line {
  font-size: 0.2933rem;
  color: #666;
  line-height: 0.5333rem;
  overflow: hidden;
}
.chailvPage .chailv_items .content .line label {
  width: 1.5rem;
  float: left;
  text-align: right;
}
.chailvPage .chailv_items .content .line p {
  margin-left: 1.5rem;
}
.chailvPage .chailv_items .content .line p span {
  color: #4993fa;
}
.chailvPage .chailv_items .content .option {
  position: absolute;
  right: 0.2667rem;
  bottom: 0.52rem;
}
.chailvPage .chailv_items .content .option a {
  display: inline-block;
  min-width: 1.3333rem;
  height: 0.5333rem;
  line-height: 0.5333rem;
  text-align: center;
  border-radius: 0.05rem;
  padding: 0 0.2667rem;
  background: #4993fa;
  font-size: 0.2933rem;
  color: #f4f5fa;
}
.chailvPage .chailv_items .content .option a.btn_edit {
  background-color: #fab949;
}
.chailvPage .chailv_items .content .option a.btn_del {
  background-color: #fa495a;
}
.switch_text {
  position: relative;
  display: inline-block;
  width: auto;
  background: #fff;
  padding: 0 0.03rem;
  border-radius: 0.4rem;
  border: 1px solid #4993fa;
  cursor: pointer;
  -webkit-transition: ease 0.5s;
  transition: ease 0.5s;
  z-index: 1;
  margin-top: 0.24rem;
}
.switch_text .num_circle {
  position: absolute;
  right: -0.1333rem;
  top: -0.1333rem;
  z-index: 99;
}
.switch_text .text {
  position: relative;
  display: block;
  height: 0.72rem;
  line-height: 0.72rem;
  font-size: 0.32rem;
  color: #4993fa;
  float: left;
  padding: 0 0.3rem;
  z-index: 5;
}
.switch_text .text.text_1 {
  color: #fff;
}
.switch_text .sButton {
  position: absolute;
  left: 0;
  top: 0;
  width: 50%;
  height: 100%;
  background: #4993fa;
  border-radius: 0.4rem;
  -webkit-transition: ease 0.5s;
  -webkit-transition: 0.5s ease;
  transition: 0.5s ease;
  z-index: 0;
}
.switch_text.off .text {
  color: #f5e5e5;
}
.switch_text.off .text.text_1 {
  color: #4993fa;
}
.switch_text.off .text.text_2 {
  color: #fff;
}
.switch_text.off .sButton {
  right: 0;
  left: auto;
}
.layui-m-layercont .simple_line {
  padding: 0.6667rem 0;
  font-size: 0.4rem;
}
.text_empty_section {
  color: #aaa;
  font-size: 0.4rem;
  margin-top: 50%;
  text-align: center;
}
.chailvPage .public_right_nav .icon_more {
  font-size: 0.32rem;
  width: 1rem;
  height: 1rem;
  color: #4993fa;
  text-align: center;
  margin-top: 0.15rem;
  background: url(../img/recharge/icon_filter_active.png) top center no-repeat;
  background-size: 0.48rem auto;
  padding-top: 0.2rem;
}
.chailvPage .public_right_nav ul {
  right: 0;
}
.chailvPage .public_right_nav ul:before {
  right: 0.3rem;
}
.chailvPage .public_right_nav ul a {
  text-align: center;
}
.select_radio_page .swiper-container {
  position: fixed;
  top: 1.17333rem;
  left: 0;
  width: 100%;
  background: #fff;
  line-height: 1.2rem;
}
.select_radio_page .swiper-container .swiper-slide {
  position: relative;
  width: auto;
  font-size: 0.3467rem;
  color: #4a94fa;
  padding: 0 0.4rem;
}
.select_radio_page .swiper-container .swiper-slide:last-child {
  color: #999;
}
.select_radio_page .swiper-container .swiper-slide + .swiper-slide {
  padding-left: 0.6667rem;
}
.select_radio_page .swiper-container .swiper-slide + .swiper-slide:before {
  content: " ";
  display: inline-block;
  height: 0.24rem;
  width: 0.24rem;
  border-width: 0.04rem 0.04rem 0 0;
  border-color: #ddd;
  border-style: solid;
  -webkit-transform: matrix(0.71, 0.71, -0.71, 0.71, -2, 0);
  transform: matrix(0.71, 0.71, -0.71, 0.71, -4, 0);
  position: relative;
  top: -0.04rem;
  position: absolute;
  left: 0;
  margin-top: 0.5rem;
}
.select_radio_page ul {
  margin-top: 1.4rem;
  padding: 0 0.32rem;
  background: #fff;
}
.select_radio_page ul li {
  padding: 0.5rem 0;
  line-height: 0.4rem;
}
.select_radio_page ul li + li {
  border-top: 1px solid #e1e1e1;
}
.select_radio_page ul li .fli_checkbox_blue {
  display: block;
  float: left;
}
.select_radio_page ul li .fli_checkbox_blue label:before {
  width: 97vw;
}
.select_radio_page ul li p {
  display: block;
  margin-left: 0.8rem;
}
.select_radio_page ul li span {
  color: #999;
}
.select_radio_page ul li.has_children {
  position: relative;
}
.select_radio_page ul li.has_children:before {
  content: " ";
  display: inline-block;
  height: 0.32rem;
  width: 0.32rem;
  border-width: 0.04rem 0.04rem 0 0;
  border-color: #ddd;
  border-style: solid;
  -webkit-transform: matrix(0.71, 0.71, -0.71, 0.71, -2, 0);
  transform: matrix(0.71, 0.71, -0.71, 0.71, -4, 0);
  position: relative;
  top: -0.04rem;
  position: absolute;
  right: 0.2667rem;
  margin-top: 0.54rem;
}
.icon_fly {
  display: inline-block;
  vertical-align: middle;
  margin-top: -0.0533rem;
  width: 0.36rem;
  height: 0.2933rem;
  background: url(../img/img_2019/icon_fly.png) center center no-repeat;
  background-size: contain;
}
.icon_hotel {
  display: inline-block;
  vertical-align: middle;
  margin-top: -0.0533rem;
  width: 0.3733rem;
  height: 0.32rem;
  background: url(../img/img_2019/icon_hotel.png) center center no-repeat;
  background-size: contain;
}
.icon_train {
  display: inline-block;
  vertical-align: middle;
  margin-top: -0.0533rem;
  width: 0.3067rem;
  height: 0.3733rem;
  background: url(../img/img_2019/icon_train.png) center center no-repeat;
  background-size: contain;
}
.icon_didi {
  display: inline-block;
  vertical-align: middle;
  margin-top: -0.0533rem;
  width: 0.36rem;
  height: 0.2667rem;
  background: url(../img/img_2019/icon_didi.png) center center no-repeat;
  background-size: contain;
}
.icon_butie {
  display: inline-block;
  vertical-align: middle;
  margin-top: -0.0533rem;
  width: 0.36rem;
  height: 0.2933rem;
  background: url(../img/img_2019/icon_butie.png) center center no-repeat;
  background-size: contain;
}
.icon_ship {
  display: inline-block;
  vertical-align: middle;
  margin-top: -0.0533rem;
  width: 0.4rem;
  height: 0.4rem;
  background: url(../img/img_2019/icon_ship.png) center center no-repeat;
  background-size: contain;
}
.icon_public {
  display: inline-block;
  vertical-align: middle;
  margin-top: -0.0533rem;
  width: 0.4rem;
  height: 0.4rem;
  background: url(../img/img_2019/icon_public.png) center center no-repeat;
  background-size: contain;
}
.icon_other {
  display: inline-block;
  vertical-align: middle;
  margin-top: -0.0533rem;
  width: 0.4rem;
  height: 0.4rem;
  background: url(../img/img_2019/icon_other.png) center center no-repeat;
  background-size: contain;
}
.icon_del {
  display: inline-block;
  vertical-align: middle;
  margin-top: -0.0533rem;
  width: 0.5333rem;
  height: 0.5333rem;
  background: url(../img/img_2019/icon_del.png) center center no-repeat;
  background-size: contain;
}
.bank_list li {
  position: relative;
  background: #fff;
  margin: 0.2rem 0;
  padding: 0.2667rem 0.32rem;
}
.bank_list li .fli_checkbox_blue {
  display: inline-block;
  vertical-align: middle;
}
.bank_list li .info {
  display: inline-block;
  vertical-align: middle;
  margin-left: 0.4rem;
}
.bank_list li .info p {
  font-size: 0.32rem;
  color: #999;
  line-height: 0.5333rem;
}
.bank_list li .info p.title {
  font-size: 0.3467rem;
  color: #333;
}
.bank_list li .icon_del {
  position: absolute;
  right: 0.4rem;
  top: 50%;
  margin-top: -0.2667rem;
}
.fliSkinToDark .public_right_nav ul li.active {
  color: #4da990;
}
.fliSkinToDark .add_btn {
  color: #fff;
  background: #4da990;
}
.fliSkinToDark .add_btn.del {
  background-color: #fc1b3b;
}
.fliSkinToDark .button_box .btn_theme_empty {
  border-color: #4da990;
  color: #4da990;
}
.fliSkinToDark .button_box .btn_theme_empty.active {
  background: #4da990;
  color: #fff;
}
.fliSkinToDark .chailvPage .xingcheng_box header {
  background-color: rgba(77, 169, 144, 0.2);
}
.fliSkinToDark .chailvPage .xingcheng_box header .split {
  background: #4da990;
}
.fliSkinToDark .chailvPage .xingcheng_box header label {
  background: #4da990;
  background: -webkit-linear-gradient(left, #4da990, rgba(77, 169, 144, 0.5));
  background: -o-linear-gradient(left, #4da990, rgba(77, 169, 144, 0.5));
  background: -moz-linear-gradient(left, #4da990, rgba(77, 169, 144, 0.5));
  background: linear-gradient(left, #4da990, rgba(77, 169, 144, 0.5));
}
.fliSkinToDark .chailvPage .table_box header .split {
  background: #4da990;
}
.fliSkinToDark .chailvPage .table_box .table_label label {
  background: rgba(77, 169, 144, 0.5);
}
.fliSkinToDark .chailvPage .table_box .table .line > div:nth-child(2) {
  color: #4da990;
}
.fliSkinToDark .chailvPage .table_box .table .line > div:nth-child(3) {
  color: #4da990;
}
.fliSkinToDark .chailvPage .header_page {
  background: #4da990;
  background: -webkit-linear-gradient(left, rgba(77, 169, 144, 0.5), #4da990);
  /* Safari 5.1 - 6.0 */
  background: -o-linear-gradient(left, rgba(77, 169, 144, 0.5), #4da990);
  /* Opera 11.1 - 12.0 */
  background: -moz-linear-gradient(left, rgba(77, 169, 144, 0.5), #4da990);
  /* Firefox 3.6 - 15 */
  background: linear-gradient(left, rgba(77, 169, 144, 0.5), #4da990);
  /* 标准的语法 */
}
.fliSkinToDark .chailvPage .margin_round_section header label {
  background: #4da990;
  background: -webkit-linear-gradient(left, #4da990, rgba(77, 169, 144, 0.5));
  background: -o-linear-gradient(left, #4da990, rgba(77, 169, 144, 0.5));
  background: -moz-linear-gradient(left, #4da990, rgba(77, 169, 144, 0.5));
  background: linear-gradient(left, #4da990, rgba(77, 169, 144, 0.5));
}
.fliSkinToDark .chailvPage .steps_section .line span.num {
  background: #4da990;
}
.fliSkinToDark .chailvPage .steps_section .line span.num:before {
  border-left-color: #4da990;
}
.fliSkinToDark .chailvPage .steps_section .line span.reason {
  color: #4da990;
}
.fliSkinToDark .chailvPage .fixed_bottom_option_line a {
  color: #4da990;
}
.fliSkinToDark .chailvPage .fixed_bottom_option_line a:only-child {
  background: #4da990;
  color: #fff;
}
.fliSkinToDark .chailvPage .fixed_bottom_option_line .submit {
  background: #4da990;
  background: -webkit-linear-gradient(left, #4da990, rgba(77, 169, 144, 0.5));
  background: -o-linear-gradient(left, #4da990, rgba(77, 169, 144, 0.5));
  background: -moz-linear-gradient(left, #4da990, rgba(77, 169, 144, 0.5));
  background: linear-gradient(left, #4da990, rgba(77, 169, 144, 0.5));
}
.fliSkinToDark .chailvPage .chailv_items.active {
  border: 1px solid #4da990;
}
.fliSkinToDark .chailvPage .chailv_items header .status {
  color: #4da990;
}
.fliSkinToDark .chailvPage .chailv_items header .status.red {
  color: #fa495a;
}
.fliSkinToDark .chailvPage .chailv_items .content > a {
  color: #4da990;
}
.fliSkinToDark .chailvPage .chailv_items .content .line p span {
  color: #4da990;
}
.fliSkinToDark .chailvPage .chailv_items .content .option a {
  background: #4da990;
}
.fliSkinToDark .chailvPage .chailv_items .content .option a.btn_edit {
  background-color: #fab949;
}
.fliSkinToDark .chailvPage .chailv_items .content .option a.btn_del {
  background-color: #fa495a;
}
.fliSkinToDark .switch_text {
  border: 1px solid #4da990;
}
.fliSkinToDark .switch_text .text {
  color: #4da990;
}
.fliSkinToDark .switch_text .text.text_1 {
  color: #fff;
}
.fliSkinToDark .switch_text .sButton {
  background: #4da990;
}
.fliSkinToDark .switch_text.off .text.text_1 {
  color: #4da990;
}
.fliSkinToDark .chailvPage .public_right_nav .icon_more {
  color: #fff;
  background-image: url(../img/colorfulSkin/dark/recharge/icon_filter_active.png);
}
.fliSkinToDark .select_radio_page .swiper-container .swiper-slide {
  color: #4da990;
}
.fliSkinToDark .icon_del {
  background-image: url(../img/colorfulSkin/dark/img_2019/icon_del.png);
}
