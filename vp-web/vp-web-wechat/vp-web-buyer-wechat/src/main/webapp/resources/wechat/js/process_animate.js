function processAnimate(_option) {
	var _time = _option.time || 0;
	var _start_time = (_option.start_time && _option.start_time >= 0) ? _option.start_time : 0;
	var callback = _option.callback;
	
	/* 总时间
	 * _time(min)
	 * time_total (s)
	 */
	var time_total = _time * 60;
	
	//当前角度
	var angle = 0;
	
	//当前分钟
	var _min = 0;
	
	//当前秒
	var _sec = 0;
	
	//角度180度零界点标志
	var flag = true;
	
	// 速度
	var v = time_total / 360 * 1000;
	
	// 计时
	var _curr_time = 0;
	
	var _countTimeId = null;
	
	var _countRotateId = null;
	
	//已过去的时间显示格式化
	var _showTime = function(_t){
		_min = Math.floor(_t / 60);
		_sec = Math.floor(_t % 60);
		
		if(_min < 10) {
			_min = "0" + _min;
		}
		if(_sec < 10) {
			_sec = "0" + _sec;
		}
		$(".time").html(_min + ":" + _sec);
	}
	
	var _showProcess = function(_angle){
		$('.circle').find('.icon_circle').css({
			transform: "rotate(" + _angle + "deg)"
		});
		if(_angle <= 180) {
			$('.circle').find('.right').css({
				transform: "rotate(" + _angle + "deg)"
			});
		} else {
			if(flag) {
				$('.circle').find('.right').css({
					"transform": "rotate(0)",
					"border-color": "#f9d425"
				});
				flag = false;
			}
			$('.circle').find('.left').css({
				transform: "rotate(" + (_angle - 180) + "deg)"
			});
		}
	}
	
	//计时函数
	var _countTime = function() {
		if(time_total-- > 0) {
			_curr_time++;
			_showTime(_curr_time);
		} else {
			clearInterval(_countTimeId);
			hasCallBack(callback);
		}
	}

	var _countRotate = function () {
		angle += 1;
		if(angle > 360) {
			clearInterval(_countRotateId);
			return;
		}
		_showProcess(angle);
	}
	
	var _setStartStatus = function(){
		if(_start_time >= time_total){
			hasCallBack(callback);
		}else{
			angle = (_start_time / time_total) * 360;
			time_total = time_total - _start_time;
			_curr_time = _start_time;
			
			_showTime(_curr_time);
			_showProcess(angle);
			
			// 计时器
			_countTimeId = setInterval(_countTime, 1000);
	
			// 圆角计时器
			_countRotateId = setInterval(_countRotate, v);
		}
	}
	
	/**
	 * 重置参数
	 */
	var reset_args = function(){
		time_total = _time * 60;
		
		_start_time = 0;
		
		angle = 0;
		
		//当前分钟
		_min = 0;
		
		//当前秒
		_sec = 0;
		
		//角度180度零界点标志
		flag = true;
		
		$('.circle').find('.right, .icon_circle').css({
			"transform": "rotate(0)",
			"border-color": "#dfdfdf"
		});
		$(".time").html("00:00");
	}
	
	this.start = function(){
		clearInterval(_countTimeId);
		clearInterval(_countRotateId);
		
		_setStartStatus();
	}
	
	this.end = function(){
		reset_args();
		clearInterval(_countTimeId);
		clearInterval(_countRotateId);
		
		hasCallBack(callback);
	}
	
	
}

function hasCallBack(callback){
	if(callback && callback instanceof Function){
		callback();
	}
}
