.loginPage {
  padding-top: 0;
  background: #ffffff;
  background-size: 100% 5rem;
}
.van-nav-bar {
  height: 1.18rem;
  line-height: 1.18rem;
  background-color: transparent;
}
.van-hairline--bottom::after {
  border-bottom-width: 0;
}
.van-nav-bar__title {
  color: rgb(51,51,51);
  font-size: 0.48rem;
  font-weight: bold;
  letter-spacing: 0.1rem;
}

.loginPage .content{
padding: 0 0.74666667rem ;
}
.loginPage .content .van-cell{
  margin-bottom: 0.26667rem;
  border-radius: 0.16rem;
  padding: 0.29333rem 0.66667rem;
  background: #f3f3f3;
  position: relative;
}
.PhoneCodeDialog .content .van-cell .van-cell__value,
.PhoneCodeDialog .content .van-cell .van-cell__value .van-field__body,
.PhoneCodeDialog .content .van-cell .van-cell__value .van-field__body .van-field__control{
  background: #f3f3f3;
}
.borderR{
  border-radius: 0.16rem;
}
.loginPage .content .titleName{
  font-size: 0.42666667rem;
  color: rgb(51,51,51);
  height: 3.73333333rem;
  line-height:3.73333333rem;
  text-align: center;
  font-weight: bold;
  width: 100%;
}
.loginPage .content .openCondition{
  color: rgb(51,51,51);
  font-size: 0.32rem;
  line-height: 0.48rem;
  margin: 0.77333333rem 0 0.906666667rem ;
}
.loginPage .content .openCondition .opentip{
  width:0.3733rem;
  height:0.3733rem;
  text-align: center;
  line-height: 0.3733rem;
  margin:0.05333rem 0.1066rem 0 0;
  border: 1px solid #333;
  border-radius: 0.1866rem;
}
.loginPage .content .openCondition p{
  display: inline-block;
}
.loginPage .btnoption{
  margin-top: 4.93333333rem;
}
.loginPage .van-nav-bar .van-icon{
  color: rgb(51,51,51);
  font-weight: bold;
}
.corporate-code-open.loginPage .van-nav-bar .van-icon{
  font-size: 0.55rem;
}
.loginPage .i-text {
  padding: 0 0.16rem;
  text-align: center;
  height: 0.88rem;
  line-height: 0.88rem;
  display: block;
  min-width: 1.6rem;
  border-radius: 0.16rem;
  background: #4993fa;;
  color:#FFFFFF;;
  font-size: 0.32rem;
  position: absolute;
  right: -0.5rem;
  top: 50%;
  transform: translateY(-50%);
}
.openCard .moblieLable{
  display: flex;
  align-items: center;
  color: #999999;
}
.openCard  .changeMoblie{
  color: rgb(20,143,240);
  font-size: 0.3466667rem;
}
.openCard .openAfter{
  color: rgb(20,143,240);
  font-size: 0.32rem;
  line-height: 0.48rem;
  margin: 0.58666667rem 0 0.93333333rem 0.2933333rem;
}
.openCard .openBefore {
  color: rgb(102, 102, 102);
  font-size: 0.32rem;
  line-height: 0.48rem;
}
.openCard .openBefore .isAgree{
  margin-right: 0.2133333rem;
}
.openCard .openBefore .clauseName{
  color: rgb(20,143,240);
}
.van-dialog.PhoneCodeDialog .van-dialog__header{
  color: rgb(51,51,51);
  font-size: 0.48rem;
  font-weight: bold;
}
.van-dialog.PhoneCodeDialog .van-dialog__content .content{
  height: 2.5rem;
  overflow: auto;
  padding: 0.2rem 0.74666667rem;
}
.van-dialog.PhoneCodeDialog .van-dialog__content .content .retransmissionCode{
  background: #f3f3f3;
}
.van-dialog.PhoneCodeDialog .van-dialog__content .content .retransmissionCode .i-text{
  background: #148ff0;
}
.van-dialog.PhoneCodeDialog .van-dialog__content .content .retransmissionCode .van-cell{
  margin-bottom: 0;
}
.van-dialog.PhoneCodeDialog .van-dialog__content .contentDoing{
  height: 3.2rem;
  position: relative;
}
.van-dialog.PhoneCodeDialog .van-dialog__content .contentDoing img{
  position: absolute;
  top: 0.6rem;
  left: 50%;
  transform: translateX(-50%);
  height: 2.66667rem;
}
.van-dialog.PhoneCodeDialog .van-dialog__content .content{
  font-size: 0.37333333rem;
  color: rgb(51,51,51);
}
.van-dialog.PhoneCodeDialog .van-dialog__content .content p{
  /*height: 1.066666667rem;*/
  line-height: 0.6rem;
  color: rgb(102,102,102);
  font-size: 0.32rem;
  width: 100%;
  text-align: center;
}

.van-dialog.PhoneCodeDialog .van-dialog__content .contentDoing p{
margin-bottom: 0.5333333333rem;
}
.van-dialog.PhoneCodeDialog .dialog__header{
  text-align: left;
}
.van-dialog.PhoneCodeDialog .van-dialog__footer{
  margin: 0.266666667rem 0 0.587rem 0;
  text-align: center;
  padding: 0.01rem 1.1733333rem;
}
.van-dialog.PhoneCodeDialog .van-dialog__footer .van-dialog__confirm{
  color: rgb(255, 255, 255);
  background: rgb(20, 143, 240);
  border-color: rgb(20, 143, 240);

  /*margin: 0 0.5rem;*/
  /*width: calc(100% - 1rem);*/
  border-radius: 0.4rem;
  /*width: 2.48rem!important;*/

  height: 0.8rem;
  line-height: 0.8rem;
  font-size: 0.32rem;
}
.van-dialog.PhoneCodeDialog .van-dialog__footer .van-dialog__cancel{
  height: 0.8rem;
  line-height: 0.8rem;
  font-size: 0.32rem;
  border-radius: 0.4rem;
  border: 0.0266667rem solid rgb(153,153,153);
  color: rgb(51,51,51);
  margin-right: 0.533333rem;
  /*width: 2.48rem;*/
}
.van-dialog.PhoneCodeDialog .van-dialog__footer:after {
  border-top-width: 0;
}
.openCard .btnoption{
margin-top: 0.66666667rem;
}
.steps{

}
.steps .item{
  background: #fff;
  text-align: center;
  width: 34%;
  height: 1.12rem;
  line-height: 1.12rem;;
  position: relative;
  font-size: 0.32rem;
  padding-left: 0.56rem;
  border:1px solid #cccccc;
  border-left: none;
  border-right: none;
}
.steps .item:first-of-type{
  width: 28%;
  padding-left: 0;
}
.steps .item:last-of-type{
  width: 38%;
}
.steps .item::before{
  content: "";
  height: 0.733rem;
  width: 0.733rem;
  border-width: 1px 1px 0 0;
  border-color: #ccc;
  background: #fff;
  position: absolute;
  top: 0.16rem;
  right: -0.3733rem;
  z-index: 60;
  border-style: solid;
  transform: rotate(45deg);
}
.steps .item.active{
  background: rgb(20, 143, 240);
  color: #fff;
  border-color:  rgb(20, 143, 240);
}
.steps .item.active::before{
  background: rgb(20, 143, 240);
  border-color: rgb(20, 143, 240);
}
.steps .item:last-of-type::before,.steps .item:last-of-type.active::before{
  border: none;
  width: 0;
  height: 0;
}
.steps .item span{
  position: relative;
  z-index: 99;
  white-space: nowrap;
}
.corporate-code-open.loginPage .content{
  padding: 0;
}
.corporate-code-open.loginPage .content .content-main{
    padding: 1rem 0.74666667rem 0.32rem;
}
.corporate-code-open.loginPage .content .content-main .btnoption{
    margin-top: 1.56rem;
}
.corporate-code-open.loginPage .content .content-main .msgtitle{
    line-height: 0.533333rem;
    font-size: 0.32rem;
    margin: 0.4rem 0 0.16rem;
}
.corporate-code-open.loginPage .content .content-main  .msg-content{
    font-size: 0.293333rem;
    line-height: 0.53333rem;
    color: #999999;
}
.corporate-code-open.loginPage .van-steps .van-steps__items .van-step__title{
  font-size: 0.3733rem;
}
.corporate-code-open.loginPage .van-steps{
padding: 0.32rem 0.74666667rem 0;
}
.corporate-code-open.loginPage .van-steps .van-step--horizontal .van-step__circle-container{
  top: 0.88rem;
}
.corporate-code-open.loginPage .van-steps .van-step--horizontal .van-step__line{
  top: 0.88rem;
}
.corporate-code-open .van-dialog.PhoneCodeDialog .van-dialog__content .content {
  height: auto;
  min-height: 2rem;
}
.corporate-code-open  .kaitong-img{
  text-align: center;
}
.corporate-code-open  .kaitong-img img{
  height: 2.98rem;
}
.corporate-code-open .van-steps .van-icon-checked::before{
  font-size: 0.55rem;
}
.corporate-code-open .infomation p{
  font-size: .3467rem;
  height: 0.8267rem;
}
.corporate-code-open .infomation p label{
  display: inline-block;
  width: 1.8rem;
  text-align: justify;
  text-align-last: justify;
  text-align-all: justify;
  margin-right: 0.05rem;
  word-break: break-all;
  height: 0.8267rem;
  line-height: .8267rem;
  white-space: normal;
  overflow: hidden;
}
.corporate-code-open .infomation p label span{
  display: inline-block;
  width: 100%;
}
.corporate-code-open .infomation{
  position: relative;
  text-align: center;
}
.corporate-code-open .info-content{
  display: inline-block;
  margin-top: 0.32rem;
}
.corporate-code-open .info-content p .value{
  margin: 0 0.32rem;
}
.corporate-code-open .info-content p .edit {
  font-size: 0.55rem;
}
.corporate-code-open .van-dialog.PhoneCodeDialog .van-dialog__content .content{
  padding: 0.2rem 0.5666667rem;
}
.corporate-code-open .van-nav-bar{
  z-index: 3;
  background: #fff;
}
.corporate-code-open .van-steps  .van-step__circle{
  width: 0.32rem;
  height: 0.32rem;
}
.corporate-code-open .line-breaks{
  word-break: break-all;
}
.paymentPage .order_created {
  color: #444;
  /*padding: 0  0.74666667rem;*/
  border-radius: 0.16rem;
  padding: 0.32rem 0.42rem;
  background: #ffffff;
}
.paymentPage  .payment-content{
  /*border-top: 1px dashed #ccc;*/
  padding: 0.32rem 0.42rem;
  background: #fff;
  margin-top: 0.32rem;
  border-radius: 0.16rem;
}
.paymentPage .order_created .ybtAmount {
  display: none;
  text-align: center;
  color: #ff0030;
  font-size: 0.48rem;
}
.paymentPage .order_created .time_left {
  font-family: SourceHanSansSC-Medium;
  font-size: .3733rem;
  font-stretch: normal;
  letter-spacing: 0;
  color: #333;
  text-align: justify;
  margin-left: 0.24rem;
}
.paymentPage .order_created .img_suc {
  display: block;
  width: 2.13333333rem;
  height: auto;
}
.paymentPage .order_created > p {
  width: 6.66666667rem;
  font-size: 0.34rem;
  line-height: 0.48rem;
  margin: 0 auto;
}
.paymentPage .order_created   b {
  font-size: 0.42rem;
  color: #4993fa;
}
.paymentPage .order_created .orderCancelSpan {
  display: inline-block;
  vertical-align: middle;
  margin-top: -0.05rem;
  color: red;
}
.paymentPage .order_created .order_info {
  position: relative;
  margin-top: 0.34666667rem;
  padding: 0.26666667rem 0;
}
.paymentPage .order_created .order_info:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1px solid #e5e5e5;
  color: #e5e5e5;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
.paymentPage .order_created .order_info p {
  line-height: 0.61333333rem;
  max-height: 3.06666667rem;
  overflow: hidden;
}
.paymentPage .order_created .order_info p span + span {
  margin-left: 0.3rem;
}
  .sales-price{
  font-family: SourceHanSansSC-Bold;
  font-size: 0.32rem!important;
  font-weight: bold;
  font-stretch: normal;
  letter-spacing: 0.0133rem;
  color: #ff2c25;
}
.sales-price span{
  font-size: 0.64rem;
}
 .info-footer{
  background: #fff;
  position: fixed;
  bottom: 0;
  width: 100%;
  padding: 0.22rem 0.32rem;
  box-sizing: border-box;
}
 .info-footer .total span{
  font-family: SourceHanSansSC-Medium;
  font-size: 0.32rem;
  font-weight: bold;
  font-stretch: normal;
  letter-spacing: 0;
  color: #ff2c25;
}
 .info-footer .total .money-total span{
  font-family: SourceHanSansSC-Bold;
  font-size: 0.64rem;
  font-weight: bold;
}
.info-footer  .total{
  text-align: center;
}
 .info-footer  .total p{
  font-family: SourceHanSansSC-Medium;
  font-size: 0.32rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0px;
  color: #999999;
}
 .info-footer .btn_submit_long{
  width: 2.667rem;
  height: 0.9067rem;
  line-height: 0.9067rem;
  border-radius: 0.12rem;
  margin: 0;
  font-family: SourceHanSansSC-Medium;
  font-size: 0.3733rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0.013rem;
  color: #ffffff;
}
 .payment-content .title{
   font-family: SourceHanSansSC-Medium;
   font-size: .3733rem;
   font-weight: 700;
   font-stretch: normal;
   letter-spacing: 0;
   color: #333;
   display: -webkit-box;
   -webkit-box-orient: vertical;
   overflow: hidden;
   margin-bottom: 0.12rem;
   padding-top: 0.12rem;
 }
.payment-content .title span{
  font-size: 0.32rem;
  margin-left: 0.32rem;
  font-weight: normal;
}
 .payment-content .item label{
   white-space: nowrap;
   font-size: .3733rem;
   color: #333;
 }
.paymentPage .payment-content .qbItem .van-cell{
  margin-bottom: 0;
  padding: 0.12rem 0.32rem;
  border: 1px solid #CCCCCC;
  font-size: 0.3733rem;
  color: #333;
  background: #fff;
  width: 50%;
}
.payment-content .tip{
  font-size: 0.29rem;
  margin: 0.24rem 0;
}
.payment-content .tip img{
  height: 0.48rem;
  margin-right: 0.08rem;
}
.loginPage.paymentPage .content .van-radio-group .van-cell{
  background: transparent;
  border-bottom: 1px solid #CCCCCC!important;

}
.loginPage.paymentPage [class*=van-hairline]::after{
  border: none;
}
.loginPage.paymentPage .van-cell::after{
  border: none;
}
.loginPage.paymentPage .content .van-radio-group{
  padding: 0 0.32rem;
}
.loginPage.paymentPage .content .van-radio-group .van-cell{
  border-radius: 0;
  padding: 0.29333rem 0;
  font-size: 0.3733rem;
  color: #333;
}
.loginPage.paymentPage .content .van-radio-group .van-cell .balance-value{
  margin-right: 0.32rem;
  color: #333;
  font-size: 0.32rem;
}
.loginPage.paymentPage .content  .margin-top_big{
  margin-top: 0.32rem;
}
.loginPage.paymentPage{
  background: #f2f2f2;
  padding-bottom: 1.6rem;
    /*padding-top: 1.18rem;*/
}
.loginPage.paymentPage .van-nav-bar{
  background: #ffffff;
}
.loginPage.paymentPage .content{
  padding: 0.32rem;
}
.payment-content .tip.blue-tip{
  color: #73a8ff;
}
.loginPage.paymentPage .order_info .item{
  margin: 0.24rem 0;
  font-size: 0.3733rem;
  color: #333;
}
.loginPage.paymentPage .order_info .item .order-money{
  font-size: 0.29rem;
  margin-left: 0.12rem;
}
.loginPage.paymentPage .order_info .item label{
  font-family: SourceHanSansSC-Medium;
  font-size: .3733rem;
  font-weight: 700;
  font-stretch: normal;
  letter-spacing: 0;
  color: #333;
  white-space: nowrap;
}
.tridPay .tripartitePaymentList {
  font-size: 0.32rem;
  padding: 0.24rem 0.52rem 0.12rem;
}
.tridPay .tripartitePaymentList>div{
    white-space: nowrap;
}
.tridPay .tripartitePaymentList img{
  margin-right: 0.24rem;
  height: 0.52rem;
}
.paymentPage  .yhImg{
    margin-right: 0.12rem;
    height: 0.62rem;
}

.paymentPage .public_top_header{
    background: #ffffff;
    font-family: SourceHanSansSC-Medium;
    font-size: 0.48rem;
    font-weight: bold;
    font-stretch: normal;
    letter-spacing: 0.0133rem;
    color: #333333;
}

.paymentPage .public_top_header  .return_back:before{
    border-color: #000000;
    border-width: 0.05rem 0.05rem 0 0;
}
.loginPage.paymentPage.havePadding{
  padding-top: 1.18rem;
}
.paymentPage  .content  .payment-content .van-cell{
  padding: 0.29333rem 0.32rem;
  background: #fff;
}
.loginPage.paymentPage .content .payment-content  .van-cell .balance-value{
  margin-right: 0.32rem;
  color: #333;
  font-size: 0.32rem;
}
.layui-m-layer-fliplus_layer_skin_wallet .layui-m-layercont{
  padding: 0.42rem 0.8rem;
}
.layui-m-layer-fliplus_layer_skin_wallet .layui-m-layercont .opt{
  margin: 0.12rem;
}
.layui-m-layer-fliplus_layer_skin_wallet .layui-m-layercont .opt button{
  width: 2.533rem;
  height: 0.8rem;
  border-radius: 0.4rem;
  border-color: #999999;
  background: #fff;
  border: 1px solid;
  line-height: 0.8rem;
/* box-sizing: border-box; */
  text-align: center;
}
.layui-m-layer-fliplus_layer_skin_wallet .layui-m-layercont  .err-msg{
  margin: 0.32rem 0;
}
.layui-m-layer-fliplus_layer_skin_wallet .layui-m-layercont   .error-tip img{
  width: 1.42rem;
}
.layui-m-layer-fliplus_layer_skin_wallet .layui-m-layercont   .error-tip  p{
  font-size: 0.52rem;
  font-weight: bold;
}
.layui-m-layer-fliplus_layer_skin_wallet .layui-m-layercont .solve{
  display: none;
}
.layui-m-layer-fliplus_layer_skin_wallet .layui-m-layercont .solve img{
  width: 100%;
}
.layui-m-layer-fliplus_layer_skin_wallet .layui-m-layercont .solve p{
  position: relative;
  margin-bottom: 0.24rem;
  text-align: center;
}
.layui-m-layer-fliplus_layer_skin_wallet .layui-m-layercont .solve p:before{
  content: "";
  width: 46%;
  height: 0;
  border-top: 1px dashed #999999;
  position: absolute;
  top: 50%;
  left: -0.8rem;
}
.layui-m-layer-fliplus_layer_skin_wallet .layui-m-layercont .solve p:after{
  content: "";
  width: 49%;
  height: 0;
  border-top: 1px dashed #999;
  position: absolute;
  top: 50%;
  right: -0.8rem;
}