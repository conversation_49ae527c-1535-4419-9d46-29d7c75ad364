@charset "utf-8";
body {
  padding-bottom: 1.30666667rem;
}
.productDetailsPage .to_cart_buy a.flexbox{
  display: flex;
}
.productDetailsPage .to_cart_buy a.byNow.flexbox p{
  line-height: initial;
}
.fixedShare {
  position: fixed;
  right: 0;
  top: 24%;
  width: 1.6133rem;
  height: 1.0933rem;
  background: url(../img/tuan/link_share.png) center center no-repeat;
  background-size: contain;
  z-index: 100000;
}
.tuanAward {
  display: inline-block;
  vertical-align: middle;
  height: 0.6933rem;
  line-height: 0.6933rem;
  background: #ffead4;
  font-size: 0.2667rem;
  color: #666;
  padding: 0 0.24rem;
  border-radius: 0.6933rem;
  margin-left: 0.16rem;
}
.tuanAward span {
  color: #fc0022;
  margin-left: 0.1067rem;
}
.tuanListSection {
  margin: 0.32rem 0;
  background: #fff;
}
.tuanListSection header {
  font-size: 0.32rem;
}
.tuanListSection header span {
  float: right;
  margin-right: 0.32rem;
}
.tuanListSection .swiper-container {
  height: 2.8rem;
  width: 100%;
}
.tuanListSection .swiper-container .swiper-wrapper{
  width: 100%;
}
.tuanListSection .swiper-slide {
  padding: 0.16rem 0.6133rem 0.16rem 0.4533rem;
  width: 100%;
  box-sizing: border-box;
}
.tuanListSection .swiper-slide .imgs {
  display: inline-block;
  vertical-align: middle;
}
.tuanListSection .swiper-slide .imgs .img {
  display: inline-block;
  vertical-align: middle;
  width: 0.8267rem;
  height: 0.8267rem;
  overflow: hidden;
  border-radius: 100%;
}
.tuanListSection .swiper-slide .imgs .img img {
  display: block;
  width: 100%;
  height: 100%;
}
.tuanListSection .swiper-slide .imgs .img + .img {
  margin-left: -0.5rem;
}
.tuanListSection .swiper-slide .info {
  display: inline-block;
  vertical-align: middle;
  margin-left: 0.16rem;
}
.tuanListSection .swiper-slide .info p {
  font-size: 0.24rem;
  color: #999;
}
.tuanListSection .swiper-slide .info p:first-child {
  font-size: 0.32rem;
  color: #333;
}
.tuanListSection .swiper-slide .info p:first-child span {
  color: #df5429;
}
.tuanListSection .swiper-slide > span {
  display: block;
  float: right;
  width: 1.76rem;
  height: 0.7467rem;
  line-height: 0.7467rem;
  text-align: center;
  background-color: #eb522b;
  font-size: 0.32rem;
  color: #fff;
  border-radius: 0.7467rem;
}
.tuanRuleSection {
  text-align: left;
}
.popuo-tuanRuleSection .layui-layer-btn{
  text-align: center;
}
.popuo-tuanRuleSection .layui-layer-btn{
  border-top: 1px solid #F5F5F5;
}
.popuo-tuanRuleSection .layui-layer-btn a{
  width: 2.48rem;
  height: 0.8rem;
  background-color: #578ffe;
  border-radius: 30px;
  line-height: 0.8rem;
  font-family: PingFang-SC-Bold;
  font-size: 0.32rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0.0267rem;
  color: #ffffff;
  border: none;
}
.tuanRuleSection.modify{
  padding: 0.32rem 0.52rem;
  text-align: justify;
}
.tuanRuleSection header {
  font-size: 0.3733rem;
  color: #eb522c;
  text-align: center;
  line-height: 0.6667rem;
}
.tuanRuleSection h2 {
  font-size: 0.3467rem;
  color: #333;
  line-height: 0.5333rem;
}
.tuanRuleSection p {
  font-size: 0.2667rem;
  color: #333;
  line-height: 0.5333rem;
}
.tuanRuleSection p + h2 {
  margin-top: 0.16rem;
}
.detailPage .top {
  position: relative;
}
.detailPage .top img {
  display: block;
  width: 100%;
  height: 10rem;
  object-fit: contain;
}
.no_ticket {
  display: block;
  width: 9.4rem;
  height: 1.88rem;
  background: url(../img/no_ticket.png) center center no-repeat;
  background-size: contain;
  margin: 0 auto 0.2667rem;
}
.detailPage .top .shopTag {
  position: absolute;
  top: 0;
  left: 0.69333333rem;
  z-index: 9999;
  width: 1.33333333rem;
  height: 1.70666667rem;
  line-height: 0.5rem;
  padding: 0.0667rem 0.2133rem;
  text-align: center;
  color: #ffffff;
  font-size: 0.37333333rem;
  background: url(../img/pic_tag.png) center center / contain no-repeat;
}
.detailPage .top .swiper-pagination-bullet {
  background: rgba(0, 0, 0, 0.7);
  width: 0.24rem;
  height: 0.24rem;
  margin: 0.1067rem;
}
.detailPage .top .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background: #416ffb;
}
.detailPage .top .swiper-container .swiper-slide.default img {
  display: block;
  width: 2rem;
  height: auto;
  margin: 2rem auto;
}
.detailPage .switch_section {
  display: none;
}
.detailPage .switch_section:first-child {
  display: block;
}
.detail_header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 1.18666667rem;
  background: #f0f0f0;
  z-index: 99999;
}
.detail_header:after {
  content: " ";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 1px;
  border-bottom: 1px solid #d6d6d6;
  color: #d6d6d6;
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
.detail_header .return_back:before {
  top: 0.26rem;
  left: 0.38rem;
}
.detail_header .links {
  text-align: center;
}
.detail_header .links a {
  display: inline-block;
  font-size: 0.48rem;
  line-height: 1.17333333rem;
  padding: 0rem 0.42666667rem;
  color: #444;
  vertical-align: text-top;
}
.detail_header .links a.active {
  /*color: #4993fa;*/
  /*border: none;*/
  /*font-family: SourceHanSansSC-Bold;*/
  /*font-size: 0.4267rem;*/
  font-weight: bold;
  /*color: #333333;*/
}
.productDetailsPage.product-content-page .tab_theme_section .item.active a{
  /*font-weight: bold;*/
  /*border: initial;*/
  /*font-family: SourceHanSansSC-Bold;*/
  /*font-size: 0.4rem;*/
}
.cakeDetailPage .width100,
.cakeDetailPage .detailPage .popbg .selectArgsBox .to_cart_buy.width100{
  width: 100%;
  padding-right: 0!important;
}
.cakeDetailPage .detailPage .popbg .selectArgsBox .to_cart_buy.width100 .btn_exchange_Redeem_now{
  width: 3rem;
}
.cakeDetailPage .detail_header .links a.active{
  color: #444;
  font-weight: bold;
}
.cakeDetailPage .redeem_now_long_btn{
    width: calc(100% - 0.64rem)!important;
    margin: 0!important;
}
.baseInfo {
  position: relative;
  background: #fff;
  border-top: 1px solid #e5e5e5;
  border-bottom: 1px solid #e5e5e5;
}
.baseInfo .price_feedback {
  position: absolute;
  right: 0.266666rem;
  bottom: 0.266666rem;
  text-align: right;
  max-width: 40%;
}
.baseInfo .price_feedback a {
  display: inline-block;
  width: 2rem;
  height: 0.613333rem;
  border: 1px solid #ee0a3b;
  color: #ee0a3b;
  font-size: 0.32rem;
  text-align: center;
  line-height: 0.613333rem;
  border-radius: 0.053333rem;
}
.baseInfo .price_feedback a.disabled {
  color: #999;
  border: 1px solid #999;
}
.baseInfo .price_feedback p {
  display: block;
  font-size: 0.293333rem;
  color: #999;
  padding: 0.133333rem 0;
}
[data-dpr="1"] .baseInfo {
  border-top: 1px solid #efefef;
  border-bottom: 1px solid #efefef;
}
[data-dpr="1"] .baseInfo .price_feedback a {
  line-height: 0.586666rem;
  border-radius: 0.066666rem;
}
.baseInfo .title {
  padding: 0.266666rem;
}
.baseInfo .title p {
  font-size: 0.4rem;
  /*height: 1.06666667rem;*/
  line-height: 0.53333333rem;
  /*max-height: 1.8rem;*/
  overflow: hidden;
  height: auto;
  color: #444;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  word-break: break-all;
  /* overflow: hidden; */
}
.baseInfo .title .small {
  font-size: 0.32rem;
  line-height: 0.50666667rem;
  color: #ff022f;
  max-height: 1.06666667rem;
  max-width: 100%;
  overflow: hidden;
}
.baseInfo .price_section {
  position: relative;
  height: 1.306666rem;
  padding: 0 0.266666rem;
  margin-top: 0.4rem;
  margin-bottom: -0.2rem;
}
.baseInfo .price_section .shop {
  font-size: 0.4rem;
  color: #ff022f;
}
.baseInfo .price_section .shop span {
  font-size: 0.613333rem;
}
.baseInfo .price_section .market {
  font-size: 0.4rem;
  color: #999;
  text-decoration: line-through;
}
.baseInfo .price_section .save {
  position: absolute;
  right: 0.6rem;
  top: -0.32rem;
  text-align: center;
  color: #4993fa;
}
.baseInfo .price_section .save .icon_save {
  display: block;
  width: 1.06666667rem;
  height: 1.06666667rem;
  background: url(../img/pic_save.png) center center / contain no-repeat;
}
.baseInfo .price_section .save .text_save {
  display: block;
  font-size: 0.32rem;
  line-height: 0.48rem;
  margin-left: -0.10666667rem;
}
.baseInfo .price_section.on_sale_status {
  background: #ff022f;
  padding-top: 0.133333rem;
  margin-top: 0;
  margin-bottom: 0;
}
.baseInfo .price_section.on_sale_status .shop,
.baseInfo .price_section.on_sale_status .market {
  color: #fff;
}
.baseInfo .price_section.on_sale_status .label_sale {
  position: absolute;
  right: 0.293333rem;
  top: 0;
  display: block;
  width: 1.09333333rem;
  height: 0.82666667rem;
  line-height: 0.93333333rem;
  background: #fdebc1;
  color: #ff022f;
  font-size: 0.42666667rem;
  text-align: center;
  border: 1px solid #fdebc1;
}
.baseInfo .price_section.on_sale_status .label_sale:after {
  content: " ";
  position: absolute;
  right: 0;
  bottom: -0.42666667rem;
  width: 0;
  height: 0;
  border-left: 0.5467rem solid transparent;
  border-right: 0.5467rem solid transparent;
  border-bottom: 0.2133rem solid transparent;
  border-top: 0.2133rem solid #fdebc1;
}
.baseInfo .price_section.on_sale_status .count_down {
  position: absolute;
  right: 1.46666667rem;
  top: 0;
  width: 2rem;
  font-size: 0.32rem;
  text-align: center;
  color: #fff;
}
.baseInfo .price_section.on_sale_status .count_down p {
  line-height: 0.66666667rem;
}
.baseInfo .price_section.on_sale_status .count_down span {
  display: inline-block;
  vertical-align: middle;
  width: 0.48rem;
  height: 0.48rem;
  line-height: 0.48rem;
  background: #fff;
  color: #444;
}
.sale_section {
  position: relative;
  padding: 0.266666rem 0;
  background: #fdebc1;
  overflow: hidden;
}
.sale_section .label {
  display: inline-block;
  width: 1.653333rem;
  text-align: center;
  color: #ff022f;
  font-size: 0.373333rem;
  line-height: 0.666666rem;
  vertical-align: middle;
}
.sale_section .content {
  display: inline-block;
  vertical-align: middle;
}
.sale_section .content p {
  font-size: 0.293333rem;
  color: #033333;
  line-height: 0.373333rem;
}
.sale_section .content p span {
  display: inline-block;
  height: 0.373333rem;
  background: #ff022f;
  color: #fff;
  padding: 0 0.106666rem;
  margin-right: 0.08rem;
  border-radius: 0.053333rem;
}
.sale_section .content p a {
  color: #033333;
  margin-left: 0.133333rem;
}
.sale_section .content p + p {
  margin-top: 0.053333rem;
}
.select_link {
  position: relative;
  display: block;
  height: 1.33333333rem;
  line-height: 1.30666667rem;
  font-size: 0.4rem;
  padding: 0rem 0.4rem;
  color: #444;
  background-color: #fff;
}
.select_link .attrs {
  float: right;
  margin-right: 0.4rem;
  max-width: 70%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
section.addr {
  position: relative;
  padding: 0.45333333rem 0.4rem;
  background: #fff;
  border-top: 1px solid #e5e5e5;
}
section.addr .fli_link_line:after {
  top: 0.133333rem;
  right: 0.066666rem;
}
[data-dpr="1"] section.addr {
  border-top: none;
}
[data-dpr="1"] section.addr:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1px solid #e5e5e5;
  color: #e5e5e5;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
section.addr .item {
  font-size: 0.4rem;
  position: relative;
  overflow: hidden;
}
section.addr .item + .item {
  margin-top: 0.4rem;
}
section.addr .item .label {
  float: left;
  color: #444;
  margin-right: 0.13333333rem;
}
section.addr .item .value {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 70%;
  float: left;
  color: #999;
}
section.addr .item .area_tips {
  font-size: 0.32rem;
  color: #fd1b3b;
}
section.addr .item.error_line {
  margin-top: 0.1rem;
  padding-left: 1rem;
  margin-bottom: -0.1rem;
}
.view_section .tab_theme_section {
  margin-top: 0;
}
.evaluate_section {
  position: relative;
  margin-top: 0.26666667rem;
  background: #fff;
}
.evaluate_section:after {
  content: " ";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 1px;
  border-bottom: 1px solid #e5e5e5;
  color: #e5e5e5;
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
.evaluate_section .header {
  position: relative;
  height: 1.33333333rem;
  line-height: 1.30666667rem;
  font-size: 0.4rem;
  color: #444;
  padding: 0rem 0.4rem;
}
.evaluate_section .header:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1px solid #e5e5e5;
  color: #e5e5e5;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
.evaluate_section .header:after {
  content: " ";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 1px;
  border-bottom: 1px solid #e5e5e5;
  color: #e5e5e5;
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
.evaluate_section .header > span {
  color: #204f9f;
}
.evaluate_section .header .right span {
  margin-top: -1px;
  color: #fd203e;
}
.evaluate_section .btn_link {
  display: block;
  height: 1.2rem;
  line-height: 1.2rem;
  margin-top: 0.42666667rem;
  color: #4993fa;
  text-align: center;
}
.evaluate_section .drag_handle {
  display: block;
  text-align: center;
  font-size: 0.34rem;
  color: #444;
  padding: 0.4rem;
  background: #f0f0f0;
}
.switch_section:first-child .detailContent {
  position: relative;
  padding-top: 1.8rem;
}
.switch_section:first-child .detailContent .tab_theme_section {
  position: absolute;
  left: 0;
  top: 0;
}
.switch_section:first-child .detailContent .tab_theme_section.fixed {
  position: fixed;
  top: 0.9rem;
}
.productDetailsPage.product-content-page .switch_section:first-child .detailContent{
  padding-top: 0;
}
.productDetailsPage.product-content-page .switch_section:first-child .detailContent .tab_theme_section.fixed{
  padding-top: 0.24rem;
}
.detailContent {
  padding-top: 1.3rem;
}
.detailContent .tab_theme_section {
  position: fixed;
  top: 0.8rem;
  left: 0;
  width: 100%;
  padding-top: 0.05rem;
  padding-bottom: 0.09rem;
  z-index: 9999;
  background: #f2f2f2;
  border-bottom: none;
  margin: 0.2rem 0;
}
.detailContent .tab_theme_section .item {
  background: #fff;
}
.detailContentSection .args {
  padding: 0.32rem;
  background: #fff;
  font-size: 0.32rem;
}
.detailContentSection .args table {
  width: 100%;
  border-collapse: collapse;
}
.detailContentSection .args table td,
.detailContentSection .args table th {
  padding: 0.1rem 0.2rem;
  border: 1px solid #eee;
}
.detailContentSection .args table td:first-child,
.detailContentSection .args table th:first-child {
  width: 30%;
  text-align: right;
  background: #f5fafe;
}
.detailContentSection .args table td:first-child + td,
.detailContentSection .args table th:first-child + th {
  background: #fff;
}
.detailContentSection .args table td:first-child[colspan="2"],
.detailContentSection .args table th:first-child[colspan="2"] {
  text-align: center;
  font-weight: 600;
  background: #eef7fe;
  padding: 0.2rem;
}
.detailContentSection .args .item {
  position: relative;
  font-size: 0.32rem;
  line-height: 0.4rem;
  padding: 0.4rem;
  overflow: hidden;
}
.detailContentSection .args .item:after {
  content: " ";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 1px;
  border-bottom: 1px solid #e5e5e5;
  color: #e5e5e5;
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
.detailContentSection .args .item .label {
  color: #999;
  float: left;
  width: 1.5rem;
  padding: 0 0.06rem;
}
.detailContentSection .args .item .value {
  color: #666;
  margin-left: 1.86rem;
}
.evaluate_list .evaluate_empty {
  padding: 0.34666667rem 0.4rem;
}
.evaluate_list .item {
  padding: 0.34666667rem 0.4rem;
  background: #fff;
  margin-bottom: 0.13333333rem;
}
.evaluate_list .item .user_info {
  height: 0.88rem;
  line-height: 0.88rem;
  overflow: hidden;
  font-size: 0.4rem;
  margin-bottom: 0.37333333rem;
}
.evaluate_list .item .user_info .img {
  width: 0.88rem;
  height: 0.88rem;
  border-radius: 100%;
  overflow: hidden;
  float: left;
}
.evaluate_list .item .user_info .img img {
  height: 0.88rem;
}
.evaluate_list .item .user_info .name {
  color: #444444;
  margin-left: 0.13333333rem;
  margin-right: 0.96rem;
  float: left;
}
.evaluate_list .item .user_info .time {
  color: #204f9f;
  float: left;
}
.evaluate_list .item .content {
  color: #444444;
  font-size: 0.34rem;
  line-height: 0.45333333rem;
  max-height: 4.53333333rem;
}
.evaluate_list .item .appendex {
  overflow: hidden;
}
.evaluate_list .item .appendex > span {
  display: block;
  float: left;
  color: #999;
  font-size: 0.32rem;
  line-height: 0.53333333rem;
  margin-top: 0.13333333rem;
}
.evaluate_list .item .appendex .starsSection {
  float: right;
  margin-top: 0.13333333rem;
}
.img_list {
  font-size: 0.32rem;
  background: #fff;
  padding-top: 1px;
  overflow: hidden;
}
.img_list .detailDescSec {
  display: inline-block;
  transform-origin: 0 0;
  -ms-transform-origin: 0 0;
  -webkit-transform-origin: 0 0;
}
.img_list .detailDescSec .intro_detail {
  padding-top: 0.2rem;
  width: 100%;
}
.img_list .detailDescSec .intro_detail .detail_sec {
  margin-bottom: 0.4rem;
}
.img_list .detailDescSec .intro_detail .detail_sec header {
  height: 0.7rem;
  padding: 0 0.29rem;
  position: relative;
  overflow: hidden;
}
.img_list .detailDescSec .intro_detail .detail_sec header label {
  line-height: 0.7rem;
  color: #969696;
  margin: 0 auto;
  width: 2.8rem;
  text-align: center;
  font-size: 0.32rem;
  font-weight: 600;
  position: absolute;
  top: 0;
  left: 50%;
  transform: translate(-50%, 0);
  z-index: 10;
  background-color: #fff;
}
.img_list .detailDescSec .intro_detail .detail_sec header::before {
  content: "";
  width: 88%;
  height: 0;
  border: 0.01rem solid #efefef;
  display: block;
  left: 6%;
  top: 50%;
  position: absolute;
  z-index: -10;
}
.img_list .detailDescSec .intro_detail .detail_sec .txt {
  height: auto;
  width: 100%;
  overflow-x: hidden;
}
.img_list .detailDescSec .intro_detail .detail_sec .txt p {
  text-align: justify;
  word-break: break-all;
  margin-top: 0.1rem;
  color: #8e8e8e;
  font-size: 0.3rem;
  line-height: 0.46rem;
  padding: 0 0.29rem;
}
.img_list .detailDescSec .intro_detail .detail_sec .txt img {
  margin-top: 0.2rem;
  width: 100%;
  display: block;
}
.img_list .detailDescSec .intro_detail .detail_sec .img-preview {
  padding-top: 0.2rem;
  width: 100%;
  overflow: hidden;
}
.img_list .detailDescSec .intro_detail .detail_sec .img-preview li {
  width: 100%;
  padding: 0.1rem 0;
  height: auto;
  margin-top: 0.1rem;
}
.img_list .detailDescSec .intro_detail .detail_sec .img-preview li a {
  display: block;
  width: 100%;
  height: auto;
}
.img_list .detailDescSec .intro_detail .detail_sec .img-preview li a img {
  width: 100%;
  height: auto;
}
.img_list .detailDescSec .bigPc {
  width: 88%;
  margin: 0 auto;
}
.img_list .detailDescSec .bigPc img {
  width: 100%;
  display: block;
  height: auto;
}
.img_list img {
  display: block;
  width: 100%;
  max-width: 100%;
  height: auto;
}
.starsSection {
  overflow: hidden;
}
.starsSection span {
  display: block;
  float: left;
  width: 0.4rem;
  height: 0.37333333rem;
  margin-right: 0.18666667rem;
  background: url(../img/icon_star.png) center center / contain no-repeat;
}
.starsSection span.active {
  background: url(../img/icon_star_active.png) center center / contain no-repeat;
}
.fixed_bottom,.fixed_bottom1 {
  position: fixed;
  width: 100%;
  height: 1.30666667rem;
  left: 0;
  bottom: 0;
  z-index: 99999;
  background: #fff;
}
.fixed_bottom:before,.fixed_bottom1:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1px solid #e5e5e5;
  color: #e5e5e5;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
.fixed_bottom .attentionItem,.fixed_bottom1 .attentionItem {
  display: inline-block;
  width: 1.7rem;
  text-align: center;
}
.fixed_bottom .attentionItem .icon_attention,.fixed_bottom1 .attentionItem .icon_attention {
  display: block;
  margin: 0 auto;
  width: 0.72rem;
  height: 0.64rem;
  background: url(../img/icon_attention.png) center center / contain no-repeat;
}
.fixed_bottom .attentionItem .text,.fixed_bottom1 .attentionItem .text {
  font-size: 0.266666rem;
  color: #333333;
  line-height: 0.533333rem;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.fixed_bottom .attentionItem.active .icon_attention,.fixed_bottom1 .attentionItem.active .icon_attention  {
  background: url(../img/icon_attention_active.png) center center / contain no-repeat;
}
.fixed_bottom .cart_num,.fixed_bottom1 .cart_num {
  position: relative;
  display: inline-block;
  vertical-align: super;
  margin-left: 0.4rem;
  top: 0.14rem;
}
.fixed_bottom .cart_num .icon_cart,.fixed_bottom1 .cart_num .icon_cart {
  width: 0.72rem;
  height: 0.64rem;
  background: url(../img/icon_cart_detail.png) center center / contain no-repeat;
}
.cakeDetailPage .fixed_bottom .btnbox,.cakeDetailPage .fixed_bottom1 .btnbox{
  width: calc(100% - 6.48rem - 0.16rem);
  text-align: center;
}
 .fixed_bottom .cart_num .text,.fixed_bottom1 .cart_num .text {
  font-size: 0.266666rem;
  color: #333333;
  line-height: 0.533333rem;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 1rem;
}
.cakeDetailPage .fixed_bottom .cart_num .text,.fixed_bottom1 .cart_num .text {
  font-size: 0.266666rem;
  color: #333333;
  line-height: 0.533333rem;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 1.2rem;
  text-align: left;
}
.fixed_bottom .btn_index_long,.fixed_bottom1 .btn_index_long {
  width: 5.6rem;
  height: 1.06666667rem;
  line-height: 1.06666667rem;
  margin: 0.13333333rem 0.4rem;
  float: right;
}
.fixed_bottom .btn_index_long.bg_h,.fixed_bottom1 .btn_index_long.bg_h {
  background: #fd8a3d;
}
.fixed_bottom .btn_index_long[disabled="disabled"],.fixed_bottom1 .btn_index_long[disabled="disabled"] {
  opacity: 0.5;
}
.to_cart_buy {
  position: absolute;
  right: 0;
  bottom: 0;
  overflow: hidden;
}
.to_cart_buy .btn_index_long[disabled="disabled"] {
  opacity: 0.5;
}
.to_cart_buy a {
  display: block;
  float: left;
  width: 3.2rem;
  height: 1.30666667rem;
  line-height: 1.30666667rem;
  color: #fff;
  font-size: 0.5rem;
  text-align: center;
}
.to_cart_buy a.btn_exchange {
  background: #ff4343;
  width: 6.4rem;
}
.product-content-page .to_cart_buy a.btn_exchange{
  width: 2.67rem;
  height: 0.9067rem;
  line-height: 0.9067rem;
  border-radius: 0.133rem;
  font-family: SourceHanSansSC-Medium;
  font-size: 0.3733rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0.013rem;
  color: #ffffff;
}
.to_cart_buy a.btn_exchange.disabled {
  opacity: 0.5;
}
.cakeDetailPage .to_cart_buy a.toCart,.cakeDetailPage .to_cart_buy a.byNow {
  height: 0.88rem;
  line-height: 0.88rem;
  width: 3rem;
  border-radius: 0.16rem;
  background: rgb(51,51,51);
  color: rgb(255,255,255);
  font-size: 0.3733rem;
  margin-right: 0.24rem;
}
.cakeDetailPage .to_cart_buy a.byNow{
  background: rgb(55,154,255);
}
.cakeDetailPage .to_cart_buy{
  bottom: 0.2133rem;
 padding-right: 0.16rem;
}
.to_cart_buy a.toCart {
  background: #fd8a3d;
  /*background: -webkit-linear-gradient(left, #fcc127, #fd8a3d);*/
  /*!* Safari 5.1 - 6.0 *!*/
  /*background: -o-linear-gradient(left, #fcc127, #fd8a3d);*/
  /*!* Opera 11.1 - 12.0 *!*/
  /*background: -moz-linear-gradient(left, #fcc127, #fd8a3d);*/
  /*!* Firefox 3.6 - 15 *!*/
  /*background: linear-gradient(left, #fcc127, #fd8a3d);*/
  /*!* 标准的语法（必须放在最后） *!*/
}
.to_cart_buy a.toCart[disabled="disabled"] {
  opacity: 0.5;
}
.to_cart_buy a.byNow {
  background: #1875f9;
  /*background: -webkit-linear-gradient(left, #416efb, #519cff);*/
  /*!* Safari 5.1 - 6.0 *!*/
  /*background: -o-linear-gradient(left, #416efb, #519cff);*/
  /*!* Opera 11.1 - 12.0 *!*/
  /*background: -moz-linear-gradient(left, #416efb, #519cff);*/
  /*!* Firefox 3.6 - 15 *!*/
  /*background: linear-gradient(left, #416efb, #519cff);*/
  /*!* 标准的语法（必须放在最后） *!*/
}
.to_cart_buy a.byNow.tuan_single {
  background: #fd6a56;
}
.to_cart_buy a.byNow.tuan_tuan {
  background: #fe3c22;
}
.to_cart_buy a.byNow.tuan_single,
.to_cart_buy a.byNow.tuan_tuan {
  padding: 0.2667rem 0;
}
.cakeDetailPage .to_cart_buy a.byNow.tuan_tuan,
.cakeDetailPage .to_cart_buy a.byNow.tuan_single{
  padding-top: 0.1rem;
}

.to_cart_buy a.byNow.tuan_single p,
.to_cart_buy a.byNow.tuan_tuan p {
  font-size: 0.32rem;
  color: #fff;
  line-height: 0.4rem;
}
.to_cart_buy a.byNow.tuan_single[disabled="disabled"],
.to_cart_buy a.byNow.tuan_tuan[disabled="disabled"] {
  opacity: 0.6;
}
.to_cart_buy a.byNow[disabled="disabled"] {
  opacity: 0.5;
}
.selectArgsBox {
  position: absolute;
  bottom: -100%;
  left: 0;
  width: 100%;
  height: 70%;
  background: #FFFFFF;
}
.selectArgsBox .to_cart_buy {
  display: -webkit-flex;
  display: flex;
  justify-content: space-around;
  position: absolute;
  left: 0;
  bottom: 0.32rem;
  width: 100%;
 align-items: center;
}
.selectArgsBox .to_cart_buy a {
  height: 1.02rem;
  line-height: 1.02rem;
  border-radius: 1.02rem;
  width: 40%;
  font-size: 0.4rem;
}
.selectArgsBox .to_cart_buy a:only-child {
  width: 100%;
}
.selectArgsBox .to_cart_buy a.btn_index_long {
  width: 100%;
}
.selectArgsBox .to_cart_buy a.btn_index_long.makerSuretoBuy{
    width: calc(100% - 0.64rem);
    height: 0.88rem;
    line-height: 0.88rem;
    margin-bottom: 0.21335rem;
    border-radius: 0.16rem;
}
.cakeDetailPage .detailPage .popbg .selectArgsBox .to_cart_buy{
  width: auto;
  left: initial;
  height: 100%;
  padding-right: 0.16rem;
  bottom: 0;
}
.detailPage .popbg .selectArgsBox .to_cart_buy.makerSure{
  width: 100%;
  height: auto;
    padding-right: 0;
}
.detailPage .popbg .selectArgsBox .cart_num{
  /*margin-left: 1.04rem;*/
}
.selectArgsBox .selectArgs {
  padding-left: 0.37333333rem;
  margin-bottom: 1.33333333rem;
}
.selectArgsBox .selectArgs .icon_close {
  position: absolute;
  right: 0.4rem;
  top: 0.26666667rem;
  width: 0.82666667rem;
  height: 0.82666667rem;
  background: url(../img/icon_close_circle.png) center center no-repeat;
  background-size: 90%;
  z-index: 10000;
}
.selectArgsBox .selectArgs > header {
  position: relative;
  height: 2.2rem;
}
.selectArgsBox .selectArgs > header .img {
  position: absolute;
  top: -0.82666667rem;
  left: 0;
  display: block;
  width: 2.93333333rem;
  height: 2.93333333rem;
  background: #f0f0f0;
  padding: 0.13333333rem;
  overflow: hidden;
}
.selectArgsBox .selectArgs > header .img img {
  width: 2.66666667rem;
  height: 2.66666667rem;
}
.selectArgsBox .selectArgs > header .intro {
  position: absolute;
  left: 3.2rem;
  bottom: 0;
  color: #444444;
  min-height: 1rem;
}
.selectArgsBox .selectArgs > header .intro .line1 {
  color: #fd1b3b;
  font-size: 0.48rem;
}
.selectArgsBox .selectArgs > header .intro .line2 {
  color: #999;
  font-size: 0.32rem;
  line-height: 0.50666667rem;
  max-height: 1.01333333rem;
  overflow: hidden;
}
.selectArgsBox .selectArgs .content_section {
  max-height: 6.66666667rem;
  padding-top: 0.26666667rem;
  overflow: auto;
}
.selectArgsBox .selectArgs section {
  padding-top: 0.26666667rem;
  padding-right: 0.4rem;
}
.selectArgsBox .selectArgs section header {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #444444;
}
.selectArgsBox .selectArgs section.select_item ul {
  padding-top: 0.26666667rem;
  overflow: hidden;
}
.selectArgsBox .selectArgs section.select_item ul li {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  float: left;
  min-width: 1.86666667rem;
  max-width: 100%;
  height: 0.8rem;
  line-height: 0.8rem;
  text-align: center;
  -webkit-border-radius: 0.13333333rem;
  -moz-border-radius: 0.13333333rem;
  -ms-border-radius: 0.13333333rem;
  -o-border-radius: 0.13333333rem;
  border-radius: 0.13333333rem;
  font-size: 0.34rem;
  color: #444;
  margin-right: 0.24rem;
  margin-bottom: 0.24rem;
  padding-left: 0.13333333rem;
  padding-right: 0.13333333rem;
  border-width: 0.03rem;
  border-style: solid;
  border-color: #f0f0f0;
}
.selectArgsBox .selectArgs section.select_item ul li.active,
.selectArgsBox .selectArgs section.select_item ul li:active {
  border-color: #4993fa;
  color: #4993fa;
}
.selectArgsBox .selectArgs section.select_item ul li.locked {
  background: #f5f5f5;
}
.selectArgsBox .selectArgs section.select_item ul li.locked.active,
.selectArgsBox .selectArgs section.select_item ul li.locked:active {
  border-color: #f0f0f0;
  color: #444;
}
.selectArgsBox .selectArgs section.times {
  color: #444444;
  height: 1.6rem;
  line-height: 0.8rem;
}
.selectArgsBox .selectArgs section.times .limit {
  color: #fd1b3b;
  font-size: 0.32rem;
  float: right;
  margin-right: 0.1rem;
}
.selectArgsBox .selectArgs section.times .line1 {
  margin-bottom: 0.4rem;
  overflow: hidden;
}
.selectArgsBox .selectArgs section.times .line1 .num_count_box {
  float: right;
}
.selectArgsBox .btn-100-all {
  position: fixed;
  bottom: 0;
  left: 0;
  display: block;
  width: 100%;
  height: 1.06666667rem;
  line-height: 1.06666667rem;
  text-align: center;
  background: #4993fa;
  color: #ffffff;
  font-size: 0.42rem;
  border: none;
}
/*京东样式添加*/
.img_list table {
  width: 100%;
}
.img_list table:first-child {
  margin-top: 0.2rem;
}
.content_tpl {
  width: 100%;
}
.formwork {
  overflow: hidden;
  width: 100%;
  padding: 0.13333333rem 0;
  border-bottom: 0.01333333rem dashed #e6e6e6;
  line-height: 0.5rem;
  text-align: left;
  font-family: Arial, Helvetica, sans-serif;
}
.formwork_title1,
.formwork_titlecenter,
.formwork_titleleft,
.formwork_titleleft2 {
  line-height: 0.6rem;
}
.formwork p {
  margin: 0;
  padding: 0;
}
.formwork_img {
  width: 100%;
  margin: 0 auto;
  text-align: center;
}
.formwork_titleleft {
  font-weight: 700;
}
.formwork_titlecenter {
  text-align: center;
  font-weight: 700;
}
.formwork_text {
  width: 100%;
  padding: 0.4rem 0.2rem;
  text-indent: 2em;
  max-height: 100000000rem;
}
.formwork_imgleft,
.formwork_imgleft1,
.formwork_tpl_imgleft,
.formwork_tpl_imgright,
.formwork_tpl_left,
.formwork_tpl_left1 {
  float: left;
}
.formwork_imgleft {
  width: 26.66%;
  padding: 0.13333333rem 0.26666667rem 0.13333333rem 0;
}
.formwork_imgright {
  float: right;
  width: 26.66%;
  padding: 0.13333333rem 0 0.13333333rem 0.26666667rem;
}
.formwork_imgleft1 {
  width: 48%;
  padding: 0.13333333rem 0.26666667rem 0.13333333rem 0;
}
.formwork_imgright1 {
  float: right;
  width: 48%;
  padding: 0.13333333rem 0 0.13333333rem 0.26666667rem;
}
.formwork_tpl_left {
  width: 50%;
}
.formwork_tpl_imgleft {
  width: 20%;
  padding: 0.13333333rem 0.26666667rem 0.13333333rem 0;
}
.formwork_tpl_imgright {
  width: 20%;
  padding: 0.13333333rem 0.26666667rem 0.13333333rem 0.13333333rem;
}
.formwork_tpl_left1 {
  width: 50%;
  text-align: center;
}
.formwork_tpl_img {
  width: 100%;
  padding: 0.26666667rem;
}
.formwork_title1 {
  text-align: center;
  font-weight: 700;
}
.formwork_bt_dz,
.formwork_bt_it,
.formwork_bt_rb,
.formwork_bt_top {
  display: none;
  width: 100%;
  height: 0.57333333rem;
  /* background-image: url(../img/jd_content_header.png);
  background-repeat: no-repeat; */
}
.formwork_bt_dz span,
.formwork_bt_it span,
.formwork_bt_rb span,
.formwork_bt_top span {
  float: left;
  padding-left: 0.13333333rem;
  font-family: "microsoft yahei";
}
.formwork_bt_dz span.s2,
.formwork_bt_it span.s2,
.formwork_bt_rb span.s2,
.formwork_bt_top span.s2 {
  padding-left: 0.05333333rem;
  line-height: 0.7rem;
  font-size: 0.28rem;
}
.formwork_bt {
  overflow: hidden;
  width: 100%;
  padding: 0.13333333rem 0;
  line-height: 0.5rem;
  text-align: left;
  font-family: Arial, Helvetica, sans-serif;
}
#J-detail-banner,
.quality-life li,
.service-pic {
  text-align: center;
}
.formwork_bt_dz {
  background-position: 0 0;
}
.formwork_bt_dz span {
  color: #FFF;
  padding-top: 0.18666667rem;
}
.formwork_bt_dz span.s2 {
  padding-top: 0.24rem;
}
.formwork_bt_rb {
  background-position: 0 -1.2rem;
}
.formwork_bt_rb span {
  font-size: 0.36rem;
  color: #C90014;
  padding-left: 0.03rem;
  padding-top: 0.16rem;
}
.formwork_bt_rb span.s2 {
  color: #666;
  padding-left: 0.13333333rem;
  padding-top: 0.21333333rem;
}
.formwork_bt_it {
  background-position: 0 -0.6rem;
}
.formwork_bt_it span {
  color: #000;
  padding-top: 0.10666667rem;
}
.formwork_bt_it span.s2 {
  padding-top: 0.13333333rem;
}
.formwork_bt_top {
  background-position: 0 -1.8rem;
}
.formwork_bt_top span {
  color: #000;
  padding-top: 0.13333333rem;
}
.formwork_bt_top span.s2 {
  padding-top: 0.18666667rem;
}
.goods-introduction-xingyun {
  font-family: Arial;
  color: #666;
  font-size: 0.32rem;
}
.goods-introduction-xingyun .title {
  height: 0.64rem;
  line-height: 0.64rem;
  border-bottom: 1px solid #308de5;
  font-family: microsoft yahei;
}
.goods-introduction-xingyun .title .chinese {
  display: inline-block;
  font-size: 0.3733rem;
  color: #fff;
  background: #308de5;
  padding: 0 0.2133rem;
}
.goods-introduction-xingyun .title .english {
  margin-left: 0.2667rem;
  font-size: 0.32rem;
  color: #308de5;
}
.goods-introduction-xingyun .after-sale-content {
  padding: 0.5333rem 0.32rem 0.4rem;
}
.goods-introduction-xingyun .after-sale-content img {
  border: 0;
  vertical-align: top;
  width: 100%;
}
.goods-introduction-xingyun .after-sale-content strong span span {
  font-size: 0.36rem;
}
.goods-introduction-xingyun .goods-parameter {
  margin-bottom: 0.5333rem;
}
.goods-introduction-xingyun .goods-parameter .parameter-content {
  padding: 0 0.32rem;
}
.goods-introduction-xingyun .goods-parameter .parameter-content .classify {
  margin-top: 0.3467rem;
  margin-bottom: 0.1333rem;
  font-size: 0.36rem;
  height: 0.5867rem;
  line-height: 0.5867rem;
  font-family: microsoft yahei;
  color: #666;
  font-weight: 600;
}
.goods-introduction-xingyun .goods-parameter .parameter-content li {
  display: block;
  width: 49%;
  color: #999;
  font-size: 0.32rem;
  line-height: 0.64rem;
  text-align: left;
  margin-right: 1%;
  float: left;
}
.goods-introduction-xingyun .goods-parameter .parameter-content .logistics-pic {
  margin-top: 0.4667rem;
  margin-left: 0.6667rem;
  text-align: left;
  overflow: hidden;
}
.goods-introduction-xingyun .goods-parameter .parameter-content .logistics-pic img {
  border: 0;
  vertical-align: top;
  width: 100%;
}
.goods-attributes-container .parameter-content {
  padding: 0.32rem;
  line-height: 0.6rem;
}
.goods-attributes-container .parameter-content .classify {
  font-size: 0.32rem;
  font-weight: 600;
  color: #444;
}
.goods-attributes-container .parameter-content ul {
  margin-bottom: 0.16rem;
}
.goods-attributes-container .after-sale-service {
  padding: 0.32rem;
}
.common-problem {
  padding: 0.32rem;
  line-height: 0.6rem;
}
.common-problem .title {
  height: 0.64rem;
  line-height: 0.64rem;
  border-bottom: 1px solid #308de5;
  font-family: microsoft yahei;
}
.common-problem .title .chinese {
  display: inline-block;
  font-size: 0.3733rem;
  color: #fff;
  background: #308de5;
  padding: 0 0.2133rem;
}
.common-problem .title .english {
  margin-left: 0.2667rem;
  font-size: 0.32rem;
  color: #308de5;
}
.common-problem .single-problem {
  margin-bottom: 0.15rem;
}
.common-problem .single-problem .problem-title {
  color: #444;
  line-height: 0.6rem;
}
.fliSkinToDark .detail_header {
  background-color: #3C4451;
}
.fliSkinToDark .detail_header .links a {
  color: #fff;
}
.fliSkinToDark .detail_header .links a.active {
  color: #4da990;
}
.fliSkinToDark .to_cart_buy a.byNow {
  background: #4da990;
}
.fliSkinToDark .to_cart_buy a.byNow.tuan_single {
  background: #3C4451;
}
.fliSkinToDark .to_cart_buy a.toCart {
  background: #3C4451;
}
.fliSkinToDark .evaluate_section .header > span {
  color: #4da990;
}
.fliSkinToDark .evaluate_section .btn_link {
  color: #4da990;
}
.fliSkinToDark .detailPage .top .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background: #4da990;
}
.fliSkinToDark .detailPage .top .shopTag {
  background-image: url(../img/colorfulSkin/dark/pic_tag.png);
}
.cakeDetailPage .brandItem{
  position: relative;
  height: 1.33333333rem;
  line-height: 1.3333rem;
  font-size: 0.4rem;
  padding: 0rem 0.4rem;
  color: #444;
  background-color: #fff;
  margin-top: 0.267rem;
}
.cakeDetailPage .brandItem>div{
  height: 100%;
}
.cakeDetailPage .brandItem>div img{
  width: 0.8567rem;
  height: 0.8567rem;
  border: 0.0267rem solid rgb(245,245,245);
  display: block;
  margin-right: 0.2933rem;
}
.cakeDetailPage .brandItem>div span{
  color: rgb(51,51,51);
  font-size: 0.3733rem;
}
.cakeDetailPage .brandItem p{
  margin-right: 0.4rem;
  color: rgb(153,153,153);
  font-size: 0.32rem;
  text-align: right;
}
.cakeDetailPage .baseInfo .price_feedback{
display: none;
}
.cakeDetailPage .productDistribution{

color: #fff;
font-size: 0.266666667rem;

/*margin: 0 0.0533333rem;*/
position: absolute;
right: 0.266666rem;
top: 0.4rem;
}
.cakeDetailPage .productDistribution span{
  background: rgb(255,114,73);
  border-radius: 0.26666665rem;
  padding: 0.05rem 0.16rem ;
}
.cakeDetailPage .productDistribution span:not(:first-child){
  margin-left: 0.1rem;
}
.cakeDetailPage .baseInfo .price_section{
height: auto;
}
.cakeDetailPage .baseInfo .title p.productName{
height: auto;
font-weight: bold;
margin: 0.24rem 0;
}
.cakeDetailPage .baseInfo .title .small{
color: rgb(153,153,153);
font-size: 0.32rem;
max-height: inherit;
}
.cakeDetailPage .select_link .labelName{
font-weight: bold;
}
.cakeDetailPage section.addr .item .label{
font-weight: bold;
}
.cakeDetailPage section.addr .item .value.scopeDelivery{
  font-size: 0.32rem;
  padding-top: 0.053rem;
  float: right;
  margin-right: 0.4rem;
}
.cakeDetailPage section.addr .item .value.cityAdress{
background: url("./../img/cake/index/dingwei_icon.png") no-repeat left center;
background-size: 0.266667rem auto;
padding-left: 0.4rem;
margin-left:0.08rem ;
font-size: 0.32rem;
color: rgb(102,102,102);
  max-width: calc(100% - 1.5rem);
}
.cakeDetailPage section.addr .item .value.cityAdress.delivery{
  margin-top: 0.1rem;
}
.cakeDetailPage section.addr .item .value.cityAdress.nodelivery{
  margin-top: -0.02rem;
}
.cakeDetailPage section.addr .express{
  font-size: 0.32rem;
  color: #999;
}
.cakeDetailPage .select_link .attrs.selectDetail{
color: rgb(153,153,153);
font-size: 0.32rem;
}
.cakeDetailPage .nonDistributable{
position: absolute;
top: 0.44rem;
color: rgb(255,114,73);
font-size: 0.32rem;
left: 1rem;
}
.cakeDetailPage .addr .cityAdressBox{
padding-bottom: 0.4rem;
}
.cakeDetailPage section.addr .item.express{
margin-top: 0;
}
.cakeDetailPage .detailContentSection{
background: #ffffff;
padding: 0.3733rem;
}
.cakeDetailPage .detailContentSection .introduction .imgList{
width: 100%;
}
.cakeDetailPage .detailContentSection .introduction .imgList img{
  width: 100%;
}
.cakeDetailPage .detailContentSection .detailDescSec{
width: 100%;
}
.cakeDetailPage .detailContentSection .detailDescSec .introduction .feature li{
height: auto;
clear: both;
overflow: hidden;
padding: 0.32rem 0;
border-top: 0.05rem solid #f2f2f2;
}
.cakeDetailPage .detailContentSection .detailDescSec .introduction .feature li i{
display: block;
width: 0.64rem;
height: 0.64rem;
float: left;
margin-right: 0.4rem;
}
.cakeDetailPage .detailContentSection .detailDescSec .introduction .feature li>div{
float: left;
width: calc(100% - 0.72rem - 0.32rem);
}
.cakeDetailPage .detailContentSection .detailDescSec .introduction .feature li>div h3{
font-weight: normal;
color: #524946;
display: inline-block;
}
.cakeDetailPage .detailContentSection .detailDescSec .introduction .feature li>div p{
color: #6d6d6d;
}
.cakeDetailPage .star{
display: inline-block;
vertical-align: text-top;
height: 0.32rem;
width: 1.6rem;
background: url("./../img/cake/other/stock-stars.png") 0 bottom repeat-x;
-webkit-background-size: auto 200%;
-moz-background-size: auto 200%;
-ms-background-size: auto 200%;
-o-background-size: auto 200%;
background-size: auto 200%;
}
.cakeDetailPage .star p{
height: 0.32rem;
background: #f96366 url("./../img/cake/other/stock-stars.png") repeat-x;
width: 0%;
-webkit-background-size: auto 200%;
-moz-background-size: auto 200%;
-ms-background-size: auto 200%;
-o-background-size: auto 200%;
background-size: auto 200%;
}
.cakeDetailPage .detailContentSection .detailDescSec .introduction .feature li i.material{
background: url("./../img/cake/other/yuanliao_iocn.png") no-repeat center center ;
background-size: contain;
}
.cakeDetailPage .detailContentSection .detailDescSec .introduction .feature li i.taste{
background: url("./../img/cake/other/kogan_iocn.png") no-repeat center center ;
background-size: contain;
}
.cakeDetailPage .detailContentSection .detailDescSec .introduction .feature li i.sweetness{
background: url("./../img/cake/other/tiandu_iocn.png") no-repeat center center ;
background-size: contain;
}
.cakeDetailPage .detailContentSection .detailDescSec .introduction .feature li i.storage{
background: url("./../img/cake/other/chunchutianjian_iocn.png") no-repeat center center ;
background-size: contain;
}
.cakeDetailPage .detailContentSection .detailDescSec .introduction .feature li i.package{
background: url("./../img/cake/other/baozhuang_iocn.png") no-repeat center center ;
background-size: contain;
}
.productDetailsPage .reminder-tip.jd-product{
  background: #fff;
  border-top: 1px solid #efefef;
  padding: 0.45333333rem 0.4rem;
  color: #aaaaaa;
  font-size: 0.3467rem;
  line-height: 0.5867rem;
}
.productDetailsPage .reminder-tip.jd-product .label{
  margin-right: 0.12rem;
}
.productDetailsPage .reminder-tip.jd-product span{
  color: #f6a63e;
}
.weixinProductDetailsPage .selectArgsBox{
  height: auto;
  min-height: 8.2rem;
  border-radius: 0.4rem 0.4rem 0 0;
}
/*.weixinProductDetailsPage .selectArgsBox .selectArgs > header .img {*/
  /*top: 0.26666667rem;*/
/*}*/
.weixinProductDetailsPage .selectArgsBox .selectArgs > header .intro {
  padding-top: 0.32rem;
  top: 0;
  bottom: initial;
  width: calc(100% - 3.2rem);
  font-weight: bold;
}
.weixinProductDetailsPage .selectArgsBox .selectArgs section.times{
  height: initial;
}
.weixinProductDetailsPage .selectArgsBox .selectArgs section.times .line1{
  margin-bottom: 0;
}
.weixinProductDetailsPage .selectArgsBox .selectArgs > header .intro .line2{
  color: #444444;
  height: 1.6rem;
  font-size: 0.42rem;
  margin-top: 0.68rem;
}
.weixinProductDetailsPage .selectArgsBox .selectArgs > header .intro .line2 .limit{
  color: #fd1b3b;
  font-size: 0.32rem;
  float: right;
  margin-right: 0.1rem;
}
.weixinProductDetailsPage .selectArgsBox .selectArgs{
  padding: 0 0.37333333rem;
}
.weixinProductDetailsPage #pop_select .selectArgsBox .selectArgs{
  margin-bottom: 2.3rem;
}
.weixinProductDetailsPage .selectArgsBox .selectArgs .icon_close{
  right: 0.37333333rem;
}
.weixinProductDetailsPage .selectArgsBox .selectArgs section.times{
  padding-top: 0;
  padding-right: 0;
}
.weixinProductDetailsPage .selectArgsBox .selectArgs > header .intro .line3{
  color: #666666;
  font-size: 0.32rem;
  margin-top: 0.58rem;
}
.weixinProductDetailsPage  p.coupon {
  display: inline-block;
  border: 1px solid #fdeded;
  background: #fdeded;
  height: 0.48rem;
  line-height: 0.48rem;
  padding: 0 0.16rem;
  color: #da7c8c;
  font-size: 0.2667rem;
  /*margin-top: 0.16rem;*/
  border-radius: 0.12rem;
  font-weight: normal;
}
.weixinProductDetailsPage .price_section p.coupon{
  margin-left: 0.32rem;
  position: relative;
  top: -0.08rem;
}
.weixinProductDetailsPage #buy_select .selectArgsBox .selectArgs {
  padding-bottom: 1.3rem;
}
.weixinProductDetailsPage #buy_select  .weixinCouponTip{
  font-size: 0.3467rem;
  color: #ea3a52;
}
.weixinProductDetailsPage #buy_select  .weixinCouponTip img{
  width: 0.42rem;
  margin-right: 0.12rem;
}
.weixinProductDetailsPage .selectArgsBox .selectArgs .itemList{
  padding-top: 0.32rem;
  font-size: 0.38rem;
}
.weixinProductDetailsPage .selectArgsBox .selectArgs .itemList .item{
  border-top: 1px solid #f5f5f5;
  height:1.4rem;
}
.weixinProductDetailsPage .selectArgsBox .selectArgs .itemList .item:last-child{
  /*margin-bottom: 1.2rem;*/
}
.weixinProductDetailsPage .selectArgsBox .selectArgs .itemList .item .address{
  position: relative;
}
.weixinProductDetailsPage .selectArgsBox .selectArgs .itemList .item .address:after{
  content: "";
  display: inline-block;
  height: 0.266666rem;
  width: 0.266666rem;
  border-width: 0.04rem 0.04rem 0 0;
  border-color: #cccc;
  border-style: solid;
  -webkit-transform: matrix(0.71, 0.71, -0.71, 0.71, -2, 0);
  transform: matrix(0.71, 0.71, -0.71, 0.71, -4, 0);
  position: absolute;
  right: 0;
  top: 0.3rem;
}
.weixinProductDetailsPage .selectArgsBox .selectArgs .itemList .item .noAddress.address:after{
  top: 0.1rem;
}
.weixinProductDetailsPage .selectArgsBox .selectArgs .itemList .item .address p:last-child{
  padding-left: 0.42rem;
  padding-right: 0.36rem;
  box-sizing: border-box;
  text-align: left;
}
.weixinProductDetailsPage .selectArgsBox .selectArgs .itemList .item .address p{
  position: relative;
}
.weixinProductDetailsPage .selectArgsBox .selectArgs .itemList .item .address .area_tips{
  position: absolute;
  left: 0.42rem;
  bottom: -0.32rem;
  color: #fd1b3b;
  display: none;
  width: auto;
  max-width: 80%;
}
.weixinProductDetailsPage .selectArgsBox .selectArgs .itemList .item .address.noAddress p:last-child{
  text-align: right;
}
.weixinProductDetailsPage .selectArgsBox .selectArgs .itemList .item .address.weixinCouponAddress{
  height: 1.7rem;
}
.weixinProductDetailsPage .selectArgsBox .selectArgs .itemList .item .address.weixinCouponAddress p{
  margin-top: -0.32rem;
}
.weixinProductDetailsPage .selectArgsBox .selectArgs .itemList .item .address.weixinCouponAddress:after{
  top: 0.42rem;
}
.weixinProductDetailsPage .selectArgsBox .selectArgs .itemList .item p:last-child{
  text-align: right;
  font-size: 0.3467rem;
  color: #888;
  padding-right: 0.36rem;
}
.weixinProductDetailsPage .selectArgsBox .selectArgs .itemList .item .noAddress{
  color: red;
}
.weixinProductDetailsPage .selectArgsBox .selectArgs .itemList .wechatlogo{
  width: 0.48rem;
  height: 0.48rem;
  margin-right: 0.12rem;
}
.weixinProductDetailsPage .selectArgsBox .to_cart_buy a.btn_index_long.payImmediately{
  width: calc(100% -  0.746rem);
  background-color: #ea3a52;
  margin-bottom: 0.32rem;
}
.weixinProductDetailsPage .to_cart_buy a.byNow.tuan_tuan{
  background-color: #ea3a52;
  border-radius: 0.65rem;
  height: 1.02rem;
  margin-right: 0.6133rem;
}
.weixinProductDetailsPage #pop_select .to_cart_buy a.byNow.tuan_tuan{
  margin-right: 0;
}
.weixinProductDetailsPage .to_cart_buy{
  bottom: 0.143rem;
}
.weixinProductDetailsPage .to_cart_buy a.byNow.tuan_single{
  background-color: #e9aea9;
  border-radius: 0.65rem;
  margin-right: 0.32rem;
  height: 1.02rem;
}
.weixinProductDetailsPage .selectArgsBox .to_cart_buy a.btn_index_long.payImmediately.repeat{
  pointer-events:none;
  opacity: 0.5;
}
.weixinProductDetailsPage .selectArgsBox .selectArgs > header .img{
  border-radius: 0.16rem;
}
.weixinProductDetailsPage .selectArgsBox .selectArgs section.times .line1.buyNum{
  font-size: 0.38rem;
}
.weixinProductDetailsPage .tuanListSection .swiper-slide > span{
  background-color: #ea3a52;
}
.weixinProductDetailsPage .selectArgsBox .to_cart_buy{
  bottom: 0.832rem;
}
.weixinProductDetailsPage .detail_header .links a{
  color: #999999;
}
.weixinProductDetailsPage .detail_header .links a.active,
.weixinProductDetailsPage .tab_theme_section .item.active a {
  color: #444444;
  font-weight: bold;
}
.weixinProductDetailsPage .tab_theme_section .item.active a{
  border-bottom: none;
}
.weixinProductDetailsPage .detailPage .top .swiper-pagination-bullet.swiper-pagination-bullet-active{
  background-color: #ea3a52;
}
.weixinProductDetailsPage .selectArgsBox .selectArgs .itemList .item .label{
  white-space: nowrap;
  width: 1.52rem;
}
.weixinProductDetailsPage .selectArgsBox .selectArgs .itemList .item .address{
  width: calc(100% - 1.6rem);
}
.weixinProductDetailsPage .selectArgsBox .selectArgs .itemList .item .address p{
  width: 100%;
}
.weixinProductDetailsPage .selectArgsBox .selectArgs .itemList .item .address p span{
  display: inline-block;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
 text-overflow: ellipsis;
}


.weixinProductDetailsPage em{
  font-size: 0.52rem!important;
  font-style:normal;
}
.weixinProductDetailsPage  span.price_decimals{
  font-size: 0.32rem!important;
}
.weixinProductDetailsPage  #buyPrice{
  font-size: 0.32rem;
}
.weixinProductDetailsPage .tuanRuleSection{
  max-height: 10rem;
  overflow: auto;
  text-align: justify;
  word-break: break-all;
}
.weixinProductDetailsPage .layui-m-layer-activity-rules .layui-m-layercont{
  padding: 0.32rem 0.367rem;
}
.weixinProductDetailsPage .layui-m-layer-activity-rules .tuanRuleSection header{
  text-align: left;
  margin-top: 0.32rem;
  color: initial;
}
.weixinProductDetailsPage .layui-m-layer-activity-rules .tuanRuleSection h2{
  font-weight: normal;
}
.weixinProductDetailsPage  .selectArgsBox .selectArgs section.select_item ul li.active,
.weixinProductDetailsPage  .selectArgsBox .selectArgs section.select_item ul li:active {
  border-color: #ea3a52;;
  color: #ea3a52;;
}
.weixinProductDetailsPage .to_cart_buy.normal_buy{
  width: 100%;
  padding: 0 0.32rem;
  box-sizing: border-box;
}
.weixinProductDetailsPage .to_cart_buy.normal_buy .long_btn_byNow{
  width: 100%;
  height: 1.02rem;
  line-height: 1.02rem;
  border-radius: 1.02rem;
}
.weixinProductDetailsPage .fixed_bottom.iphone_bottom{
  height: 1.6rem;
}
.weixinProductDetailsPage .fixed_bottom.iphone_bottom .to_cart_buy{
  bottom: 0.3rem;
}
.product-content-page.productDetailsPage .swiper-pagination-page{
  position: absolute;
  right: 0.62rem;
  bottom: 0.32rem;
  z-index: 1;
  width: 1.32rem;
  height: 0.6133rem;
  line-height: 0.6133rem;
  background-color: #676363;
  border-radius: 0.3067rem;
  opacity: 0.5;
  text-align: center;
  color: #fff;
  font-size: 0.32rem;
}
.product-content-page.productDetailsPage .swiper-pagination-page span{
    display: inline-block;
    float: left;
    width: 60%;
    margin-right: 0;
    background-color: #0f1010;
    border-radius: 0.3067rem 0 0.5rem 0.3067rem;
    opacity: 0.9;
}
.product-content-page.productDetailsPage   .sales-price{
  font-family: SourceHanSansSC-Bold;
  font-size: 0.32rem!important;
  font-weight: bold;
  font-stretch: normal;
  letter-spacing: 0.0133rem;
  color: #ff2c25;
}
.product-content-page.productDetailsPage  .sales-price span{
  font-size: 0.64rem;
}
.product-content-page.productDetailsPage  .baseInfo  .group_count_down_content{
  font-size: 0.32rem;
  color: #ff2c25;
}
.product-content-page.productDetailsPage  .baseInfo  .group_count_down_content .label_sale{
  background: #f9d4d2;
  height: 0.52rem;
  line-height: 0.52rem;
  padding: 0 0.12rem;
}
.product-content-page.productDetailsPage  .baseInfo .price_section .market{
  display: inline-block;
  font-family: SourceHanSansSC-Medium;
  font-size: 0.32rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0.0133rem;
  color: #ff2c25;
  text-decoration:inherit;
  position: relative;
}
.product-content-page.productDetailsPage  .baseInfo .price_section .market:after{
  content: "";
  width: 100%;
  height: 0.02rem;
  background-color: #ff2c25;
  position: absolute;
  left: 0;
  top: 0.2rem;
  /* transform: translateY(-50%); */
  transform: rotate(-4deg);
}
.product-content-page.productDetailsPage  .baseInfo  .title{
  font-family: SourceHanSansSC-Medium;
  font-size: 0.4267rem;
  font-weight: bold;
  font-stretch: normal;
  letter-spacing: 0;
  color: #333333;
}
.product-content-page .goods_list_section ul li a p.title{
  height: auto;
  font-family: SourceHanSansSC-Medium;
/* font-size: 0.3733rem; */
  font-weight: bold;
  font-stretch: normal;
  letter-spacing: 0;
  color: #333333;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 0.16rem;
  word-break: break-all;
  padding: 0 0.32rem;
}
.product-content-page  .goods_list_section ul li p.price{
  padding: 0 0.32rem;
}
.product-content-page .goods_list_section ul.item{
  width: 50%;
  float: left;
}
.product-content-page .goods_list_section{
  padding-top: 0.18rem;
}
.product-content-page .goods_list_section ul.item:first-of-type{
  padding-right: 0.12rem;
}
.product-content-page .goods_list_section ul.item:last-of-type{
  padding-left: 0.12rem;
}
.product-content-page .goods_list_section ul.item li{
  width: 100%;
  padding: 0 0 0.2rem;
  border-radius: 0.16rem;
  margin:0 0 0.2rem;
  height: auto;
}
.product-content-page .goods_list_section ul li a .img{
  height: 3.4933333rem;
  margin-bottom: 0.32rem;
}
.product-content-page .goods_list_section ul li a .img img{
  width: 100%;
  height: 100%;
  border-radius: 0.16rem 0.16rem 0 0;
}
.product-content-page.productDetailsPage .specifications-content{
  background: #ffffff;
}
.product-content-page.productDetailsPage .specifications-content .choose-ge .label{
  float: left;
  color: #444;
  margin-right: 0.13333333rem;
  font-family: SourceHanSansSC-Medium;
  font-size: 0.3733rem;
  font-stretch: normal;
  letter-spacing: 0;
  color: #333333;
}
.product-content-page.productDetailsPage .specifications-content  .label{
  color: #333333;
  font-size: 0.3733rem;
  font-weight: bold;
}
.product-content-page.productDetailsPage .specifications-content section.addr .item  .value {
  font-family: SourceHanSansSC-Medium;
  font-size: 0.3733rem;
  font-weight: normal;
  letter-spacing: 0px;
  color: #666666;
}
.product-content-page.productDetailsPage .specifications-content .choose-ge .attrs{
  float:left ;
  font-family: SourceHanSansSC-Medium;
  font-size: 0.3733rem;
  font-weight: normal;
  letter-spacing: 0px;
  color: #666666;
}
.product-content-page.productDetailsPage .specifications-content .select_link,.product-content-page.productDetailsPage .specifications-content .addr{
  background: transparent;
}

.product-content-page.productDetailsPage .card-rd{
  border-radius: 0.2133rem;
  margin: 0.32rem 0.32rem 0;
}
.product-content-page.productDetailsPage .specifications-content .addr{
  padding-top: 0;
  width: 100%;
}
.product-content-page.productDetailsPage .specifications-content .addr .item{
  width: 100%;
  display: flex;
  align-items: center;
}
.product-content-page.productDetailsPage .specifications-content .addr .item.content_line{
  height: 0.52rem;
  line-height: 0.52rem;
}
.product-content-page.productDetailsPage .specifications-content .addr .item.content_line .value{
  line-height: 0.52rem;
  display: flex;
  align-items: center;
}
.product-content-page.productDetailsPage .specifications-content .addr .item.content_line .label{
  line-height: 0.52rem;
  display: flex;
  align-items: center;
}
.product-content-page.productDetailsPage .specifications-content .addr:before{
  border-top: none;
  display: none;
}
.product-content-page.productDetailsPage .to_cart_buy a.byPurchase,
.product-content-page.productDetailsPage .to_cart_buy a.toCart{
  width: 2.67rem;
  height: 0.9067rem;
  line-height: 0.9067rem;
  background-color: #ffa132;
  border-radius: 0.133rem;
  font-family: SourceHanSansSC-Medium;
  font-size: 0.3733rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0.013rem;
  color: #ffffff;
  margin-right: 0.133rem;
}
.product-content-page.productDetailsPage  .fixed_bottom:before{
  border-top: none;
}
.product-content-page.productDetailsPage .fixed_bottom{
  height: 1.52rem;
}
.product-content-page.productDetailsPage  .to_cart_buy{
  bottom: 0.42rem;
  right: 0.3467rem;
}
.product-content-page.productDetailsPage .to_cart_buy a.byNow{
  width: 2.67rem;
  height: 0.9067rem;
  line-height: 0.9067rem;
  /*background-color: #ffa132;*/
  border-radius: 0.133rem;
  font-family: SourceHanSansSC-Medium;
  font-size: 0.3733rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0.013rem;
  color: #ffffff;
}
.product-content-page.productDetailsPage .selectArgsBox{
  border-radius: 0.32rem 0.32rem 0 0;
}
.product-content-page.productDetailsPage .selectArgs{
  padding-top: 0.7467rem;
}
.product-content-page.productDetailsPage .selectArgsBox .selectArgs > header{
  height: 3.25rem;
}
.product-content-page.productDetailsPage .selectArgsBox .selectArgs > header .img{
  background-color: #f5f5f5;
  box-sizing: border-box;
  padding: 0.013333333rem;
  position: inherit;
  top: 0;
}
.product-content-page.productDetailsPage .selectArgsBox .selectArgs > header .img img{
  width: 100%;
  height: 100%;
}
.product-content-page.productDetailsPage  .selectArgsBox .selectArgs > header .intro{
  top: 0.32rem;
}
.product-content-page.productDetailsPage  .selectArgsBox .selectArgs > header .intro .market {
  display: inline-block;
  font-family: SourceHanSansSC-Medium;
  font-size: 0.32rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0.0133rem;
  color: #ff2c25;
  text-decoration: inherit;
  position: relative;
}
.product-content-page.productDetailsPage  .selectArgsBox .selectArgs > header .intro .market:after{
  content: "";
  width: 100%;
  height: 0.02rem;
  background-color: #ff2c25;
  position: absolute;
  left: 0;
  top: 0.2rem;
  transform: rotate(-4deg);
}
.product-content-page.productDetailsPage .selectArgsBox .selectArgs section.select_item ul li{
  background-color: #f5f5f5;
  border-radius: 0.36rem;
  padding: 0 0.42rem;
  font-family: SourceHanSansSC-Medium;
  font-size: 0.3733rem;
  font-weight: 500;
  font-stretch: normal;
  letter-spacing: 0;
  color: #333333;
  border: solid 1px #f5f5f5;
}
.product-content-page.productDetailsPage .selectArgsBox .selectArgs section.select_item ul li.active{
  border-color: #1875f9;
  background-color: #ddebff;
  color: #1875f9;
}
.product-content-page.productDetailsPage .baseInfo .price_section{
  height: auto;
}
.product-content-page.productDetailsPage .baseInfo .price_section .content{
  margin: 0.12rem 0;
}
.product-content-page.productDetailsPage .baseInfo .price_section .content a{
  background-color: #fcc8c8;
  border-radius: 0.0533rem;
  display: inline-block;
  font-family: SourceHanSansSC-Regular;
  font-size: 0.24rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0.013rem;
  color: #f12038;
  padding: 0.05rem 0.12rem;
}
.product-content-page.productDetailsPage .tuanListSection .select_link{
  background: transparent;
}
.product-content-page.productDetailsPage .to_cart_buy a.byNow.tuan_single{
  margin-right: 0.1333rem;
  background: #fd6a56;
  height: 1rem;
  line-height: 1rem;
}
.product-content-page.productDetailsPage .to_cart_buy a.byNow.tuan_tuan{
  margin-right: 0.1333rem;
  background: #fe3c22;
  height: 1rem;
  line-height: 1rem;
}
.product-content-page.productDetailsPage .selectArgsBox .to_cart_buy a{
  width:40%;
}
.product-content-page.productDetailsPage .selectArgsBox .to_cart_buy a.btn_index_long{
  width: 70%;
}
.product-content-page.productDetailsPage .selectArgsBox .selectArgs .times{
  /*margin-top: 1.52rem;*/
}
.productDetailsPage.product-content-page .evaluate_section{
  margin-bottom: 0.32rem;
}
.productDetailsPage.product-content-page .switch_section:first-child .detailContent{
  background: #fff;
  border-radius: 0.23rem 0.23rem 0 0;
  /*margin-top: 0.32rem;*/
}
.productDetailsPage.product-content-page .switch_section:first-child .detailContent .tab_theme_section:before,
.productDetailsPage.product-content-page .switch_section:first-child .detailContent .tab_theme_section:after{
  display: none;
}
.productDetailsPage.product-content-page .detailContent .tab_theme_section{
  background: transparent;
  position: initial;
  margin: 0;
}
.productDetailsPage.product-content-page .detailContent .detailContentSection{
  background: #f2f2f2;
}
.productDetailsPage.product-content-page .detailPage  .shopTag{
  height: 0.76rem;
  line-height: 0.76rem;
  background-color: #1875f9;
  border-radius: 0 0.2633rem;
  font-family: SourceHanSansSC-Medium;
  font-size: 0.4rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0;
  color: #ffffff;
  display: inline-block;
  padding: 0 0.2rem;
  position: absolute;
  right: 0;
  top: 0;
}
.productDetailsPage.product-content-page.hide-header-page .detail_header{
  display: none;
}
.productDetailsPage.product-content-page.hide-header-page{
  padding-top: 0;
}
.productDetailsPage.product-content-page .option-btn{
  padding: 0 0.6933rem 0.32rem;
}
.productDetailsPage.product-content-page .option-btn img{
  height: 0.62rem;
  margin-right: 0.1rem;
}
.productDetailsPage.product-content-page .option-btn li{
  font-family: SourceHanSansSC-Regular;
  font-size:0.32rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0.0133rem;
  color: #999999;
}
.product-content-page .goods_header{
  display: none;
}
.product-content-page .goods_header img.ic{
height: 0.92rem;
}
.product-content-page.hide-header-page .goods_header {
  position: fixed;
  z-index: 99999;
  width: calc(100% - 1.5rem);
  display: flex;
  padding: 0.12rem 0.32rem;
}
.product-content-page .public_right_nav{
  position: fixed;
  z-index: 99999;
}
.product-content-page.hide-header-page .detail_header .public_right_nav{
  /*display: none;*/
}
.product-content-page.hide-header-page  .public_right_nav .icon_more{
  background-image: url(./../img/icons/top-more.png);
  width: 0.92rem;
  height: 0.92rem;
  margin-top: 0.12rem;
}
.success_tip_content{
  text-align: center;
  padding: 0.52rem 0.32rem;
  font-size: 0.3733rem;
  color: #333;
}
.success_tip_content .tip1{
  font-size: 0.42rem;
  font-weight: bold;
  margin: 0.52rem 0 0.24rem;
}
.success_tip_content .tip2{
  margin: 0 0 0.52rem;
}
 .success_tip_content_btn input{
  height: 0.8rem;
  line-height: 0.8rem;
  font-size: 0.32rem;
  border-radius: 0.4rem;
  border: 0.0266667rem solid rgb(153, 153, 153);
  color: rgb(51, 51, 51);
  margin-right: 0.533333rem;
  background: #fff;
  padding: 0 0.24rem;
}
.success_tip_content_btn #seePurchase{
  color: rgb(255, 255, 255);
  background: rgb(20, 143, 240);
  border-color: rgb(20, 143, 240);
  margin-right: 0;
}
/*.weixinProductDetailsPage .selectArgsBox .selectArgs > header{*/
  /*height: 3.2rem;*/
/*}*/