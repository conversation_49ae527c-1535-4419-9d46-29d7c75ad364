var shop = {
    base: "/vp-web-buyer-wechat",
    locale: "zh_CN"
};

//创建图形验证码
function createcode(callbackfn) {

    $.ajax({
        url: shop.base+"/login/getPic.jhtml",
        type: 'POST',
        dataType: 'json',
        cache: false,
        success: function (data) {
            if (data != null) {
                $("#imgscode").imgcode(
                    {
                        frontimg: 'data:image/png;base64,' + data.slidingImage,
                        backimg: 'data:image/png;base64,' + data.backImage,
                        yHeight: data.yHeight,
                        backImgWidth:data.oriImageWidth, //背景图的宽
                        frontimgWidth:data.templateWidth, //抠图的宽
                        refreshcallback: function () {
                            // 刷新验证码
                            createcode(callbackfn);
                        },
                        callback: function (msg) {
                            var $this = this;
                            $.ajax({
                                url: shop.base+"/login/checkcapcode.jhtml",
                                type: 'POST',
                                data: {
                                    xpos: Math.round(msg.xpos),
                                    capcode: data.capcode,
                                },
                                dataType: 'json',
                                cache: false,
                                success: function (data) {
                                    if (data.code == 1) {
                                        $this.getsuccess();
                                        var cl1 =  setTimeout(function () {
                                            layer.closeAll();
                                            if(callbackfn){
                                                callbackfn()
                                            }
                                            clearTimeout(cl1);
                                        }, 500);
                                    } else if (data.code == 3) {
                                        $this.getfail("验证码过期，请刷新");
                                        var cl2 = setTimeout(function () {
                                            createcode(callbackfn);
                                            clearTimeout(cl2);
                                        }, 800);

                                    } else {
                                        $this.getfail("验证不通过");
                                        var cl3 = setTimeout(function () {
                                            createcode(callbackfn);
                                            clearTimeout(cl3);
                                        }, 800);

                                    }
                                }
                            })
                        }
                    }
                )

            }
        }
    });
}
