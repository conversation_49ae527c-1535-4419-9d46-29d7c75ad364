.cakeListPage .headerTop{
  width: 100%;
  height: 3.0133333rem;
  background:#ffffff;
  position: fixed;
  z-index: 9;
}
.cakeListPage .fixed_tab_page{
  padding-top: 3.133333rem;
}
.cakeListPage .public_top_header,.cakeListPage .sortSection.fixed{
  background: transparent;
  box-shadow: none;
}
.cakeListPage .sortSection.fixed{
  top: 1.86rem;
}
body.cakeListPage.product_list_page .public_top_header .search_section .input{
  background-color: #FFFFFF;
  border: none;
  height: 0.88rem;
  box-sizing: border-box;
}
body.cakeListPage{
  padding-top: 0;
}
.cakeListPage .sortSection.fixed:before{
  height: 0;
}
.cakeListPage .headerTop .address{
  font-size: 0.32rem;
  color: rgb(51,51,51);
  font-family: "PingFang-SC-Bold";
  font-weight: bold;
}
.cakeListPage .headerTop .address .addressDetail{
  margin: 0 0.16rem;
  overflow: hidden;
  height: 1.18rem;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 70%;
}
.cakeListPage .public_top_header  .address{
  width: 100%;
  font-size: 0.32rem;
  overflow: hidden;
  height: 100%;
}
.cakeListPage .cart_num .icon_cart{
  background: url("./../img/cake/index/gouwuche_icon.png") center center no-repeat;
  background-size: contain;
}
.cakeListPage .public_top_header .search_section{
  width: calc(100% - 2.32rem);
  border: solid 1px #dddddd;
  margin-top: -0.32rem;
  margin-left: 0.92rem;
  margin-right: 0.16rem;
  border-radius: 0.44rem;
}
.cakeListPage  .return_back{
  top: 0.88rem;
}
.cakeListPage .btn_search{
top: 1rem;
}
.cakeListPage .public_top_header .cart_num{
  top: 1.1rem;
}
.cakeListPage .headerTop .address img.adressIcon{
  width: 0.34666667rem;
  margin-left: 0.42rem;
}
.cakeListPage .headerTop .address img.selectIcon{
  width: 0.16rem;
}
.cakeListPage .sortSection li.active a{
  border-bottom: none;
  color: #4993fa;
}
#search_condition_brand .content .item:nth-of-type(2n){
  margin-left: 0.24rem;
}
#search_condition_brand .content .item {
  background: #f7f7f7;
  height: 1.67rem;
  width: calc(50% - 0.12rem);
  max-width: calc(50% - 0.12rem);
  display: flex;
  float: left;
  padding: 0 0.2267rem;
  border-color: #f7f7f7;
  margin-bottom: 0.24rem;
  border-radius: 0.29335rem;
}
#search_condition_brand .content .item.active{
  background: #edf4ff;
}
#search_condition_brand .content .item.active p{
  color: #1c77f9;
}
#search_condition_brand .content .item p{
  white-space: nowrap; /* 确保文本在一行内显示 */
  overflow: hidden; /* 隐藏溢出的内容 */
  text-overflow: ellipsis; /* 使用省略号表示文本溢出 */
  color: #666666;
  font-size: 0.32rem;
}
#search_condition_brand .content .item img{
  width: 1.2266667rem;
  height: 1.2266667rem;
  margin-right: 0.32rem;
}
#search_condition_brand .search_condition_filter_section .scroll_section{
  padding: 0.32rem 0.24rem;
}