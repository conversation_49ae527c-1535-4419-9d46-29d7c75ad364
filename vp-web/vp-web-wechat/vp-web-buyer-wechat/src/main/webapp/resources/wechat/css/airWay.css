@charset "utf-8";
.airwayIndexPage {
  background: #fff;
}
.airwayIndexPage .fli_btn_long {
  display: block;
  width: 100%;
  height: 1.173333rem;
  line-height: 1.173333rem;
  text-align: center;
  border-radius: 0.08rem;
  font-size: 0.426666rem;
  color: #fff;
  margin: 0.266666rem 0;
}
.airwayIndexPage .fli_btn_long.fli_btn_primary {
  background: #4993fa;
}
.airwayIndexPage .fli_btn_long.fli_btn_yellow {
  background: #fac441;
}
.airwayIndexPage .toptag {
  width: 100%;
  height: auto;
}
.airwayIndexPage .toptag img {
  width: 100%;
  height: auto;
  display: block;
}
.airwayIndexPage #airWayForm {
  width: 100%;
}
.airwayIndexPage #airWayForm .bgbox {
  height: 4rem;
  position: relative;
  background: #f0f0f0;
}
.airwayIndexPage #airWayForm .bgbox .inputBox {
  position: absolute;
  top: -0.8rem;
  width: 100%;
}
.airwayIndexPage #airWayForm .bgbox .inputBox .place_form_to {
  position: relative;
  height: 1.733333rem;
  margin: 0.266666rem 0.4rem;
  padding: 0.426666rem 0.32rem;
  background: #fff;
  line-height: 0.906666rem;
  text-align: center;
  z-index: 99;
  border-radius: 0.11rem;
  box-shadow: 0 0.01rem 0.01rem 0.001rem #ccc;
}
.airwayIndexPage #airWayForm .bgbox .inputBox .place_form_to .from,
.airwayIndexPage #airWayForm .bgbox .inputBox .place_form_to .to {
  width: 3.5rem;
  line-height: 0.906666rem;
  font-size: 0.4rem;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.airwayIndexPage #airWayForm .bgbox .inputBox .place_form_to .from {
  float: left;
  text-align: left;
}
.airwayIndexPage #airWayForm .bgbox .inputBox .place_form_to .to {
  float: right;
  text-align: right;
}
.airwayIndexPage #airWayForm .bgbox .inputBox .place_form_to .icon_change {
  display: inline-block;
  width: 0.906666rem;
  height: 0.906666rem;
  background: url(../img/recharge/icon_change.png) center center / contain no-repeat;
}
.airwayIndexPage #airWayForm .bgbox .inputBox .select_time,
.airwayIndexPage #airWayForm .bgbox .inputBox .select_num {
  position: relative;
  height: 1.333333rem;
  margin: 0.266666rem 0.4rem;
  padding: 0.4rem 0.32rem;
  background: #fff;
  line-height: 0.533333rem;
  z-index: 99;
  border-radius: 0.11rem;
  box-shadow: 0 0.01rem 0.01rem 0.001rem #ccc;
}
.airwayIndexPage #airWayForm .bgbox .inputBox .select_time input,
.airwayIndexPage #airWayForm .bgbox .inputBox .select_num input {
  color: #999;
  font-size: 0.426666rem;
  width: 4rem;
}
.airwayIndexPage #airWayForm .bgbox .inputBox .select_time label,
.airwayIndexPage #airWayForm .bgbox .inputBox .select_num label {
  font-size: 0.426666rem;
  color: #444;
}
.airwayIndexPage #airWayForm .bgbox .inputBox .select_time select,
.airwayIndexPage #airWayForm .bgbox .inputBox .select_num select {
  border: none;
  width: 1rem;
  font-size: 0.426666rem;
  color: #999;
  background-color: #fff;
}
.airwayIndexPage #airWayForm .commitBox {
  display: block;
  background-color: #fff;
  padding: 0.8rem 0.4rem;
}
.airwayIndexPage #airWayForm .commitBox .notice {
  color: #4993fa;
  font-size: 0.34rem;
  padding-left: 0.2rem;
  line-height: 0.5rem;
}
.airWaySelectCitypage {
  position: relative;
}
.airWaySelectCitypage .fixed_header {
  position: fixed;
  top: 1.17333333rem;
  left: 0;
  width: 100%;
  background: #f2f2f2;
  z-index: 100;
}
.airWaySelectCitypage .fixed_header .input_section {
  background: #4993fa;
  padding: 0.1rem 0.4rem;
  overflow: hidden;
}
.airWaySelectCitypage .fixed_header .input_section .input_text {
  height: 1.1rem;
  font-size: 0.32rem;
  float: left;
  display: block;
  border-radius: 0.08rem;
  padding-left: 0.3rem;
  width: 88%;
}
.airWaySelectCitypage .fixed_header .input_section .text_concel {
  width: 10%;
  float: right;
  font-size: 0.373333rem;
  color: #fff;
  line-height: 1.173333rem;
  text-align: center;
}
.airWaySelectCitypage .fixed_header .search_suggest_list {
  position: fixed;
  top: 2.66rem;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 999;
  display: none;
  overflow-y: scroll;
}
.airWaySelectCitypage .fixed_header .search_suggest_list ul {
  background: #fff;
}
.airWaySelectCitypage .fixed_header .search_suggest_list ul li {
  line-height: 1rem;
  padding: 0 0.4rem;
  font-size: 0.32rem;
  border-bottom: 1px solid #e1e1e1;
}
.airWaySelectCitypage .fixed_header .tabsection {
  background-color: #fff;
  overflow: hidden;
}
.airWaySelectCitypage .fixed_header .tabsection .item {
  line-height: 1rem;
  text-align: center;
  width: 50%;
  box-sizing: border-box;
  font-size: 0.4rem;
  float: left;
  border-bottom: 1px solid transparent;
}
.airWaySelectCitypage .fixed_header .tabsection .item.active {
  border-bottom: 1px solid #4993fa;
}
.airWaySelectCitypage #citys {
  margin-top: 3.5rem;
}
.airWaySelectCitypage #citys .swiper-slide {
  padding: 0.4rem;
}
.airWaySelectCitypage #citys .swiper-slide .hot {
  overflow: hidden;
}
.airWaySelectCitypage #citys .swiper-slide .hot li {
  display: block;
  width: 32%;
  height: 0.8rem;
  line-height: 0.8rem;
  background: #fff;
  border-radius: 0.053333rem;
  color: #333;
  font-size: 0.32rem;
  text-align: center;
  float: left;
  margin-bottom: 0.24rem;
}
.airWaySelectCitypage #citys .swiper-slide .hot li:nth-child(3n + 2) {
  margin-left: 2%;
  margin-right: 2%;
}
.airWayResultsPage {
  padding-top: 1.2rem;
}
.airWayResultsPage .public_top_header .from,
.airWayResultsPage .public_top_header .to {
  display: inline-block;
  width: 3.33rem;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  vertical-align: middle;
}
.airWayResultsPage .fly_search_detail_page {
  padding-top: 1.306666rem;
}
.airWayResultsPage .dateBox header {
  position: fixed;
  left: 0;
  top: 1.173333rem;
  width: 100%;
  background: #4993fa;
  padding: 0.16rem 0.4rem;
  z-index: 10;
  display: flex;
  justify-content: space-between;
}
.airWayResultsPage .dateBox header a {
  color: #fff;
  font-size: 0.32rem;
  line-height: 0.986666rem;
  flex: 2;
  vertical-align: middle;
}
.airWayResultsPage .dateBox header a.prev {
  padding-left: 0.466666rem;
  text-align: left;
  background: url(../img/recharge/icon_left.png) left center no-repeat;
  background-size: 0.226666rem 0.426666rem;
}
.airWayResultsPage .dateBox header a.next {
  padding-right: 0.466666rem;
  text-align: right;
  background: url(../img/recharge/icon_right.png) right center no-repeat;
  background-size: 0.226666rem 0.426666rem;
}
.airWayResultsPage .dateBox header .input_text {
  flex: 6;
  height: 0.986666rem;
  font-size: 0.32rem;
  color: #999;
  padding-left: 1.466666rem;
  background: url(../img/recharge/icon_calender.png) 0.666666rem center no-repeat #fff;
  background-size: 0.48rem 0.48rem;
  border: none;
  border-radius: 0.08rem;
  margin: 0 auto;
}
.airWayResultsPage .loadingBox {
  padding-top: 1.29rem;
}
.airWayResultsPage .loadingBox .img_loading {
  margin: 3rem auto;
  display: block;
  width: 2.5rem;
  height: 2.5rem;
  background: url(../img/airway/loading.gif) center center / contain no-repeat;
}
.airWayResultsPage .result {
  margin-top: 1.29rem;
  padding: 0.1rem 0.3rem 1.6rem;
  position: relative;
}
.airWayResultsPage .result .item {
  padding: 0.266666rem 0.2rem 0.53333332rem;
  background: #fff;
  margin-top: 0.2rem;
  overflow: hidden;
  border-radius: 0.08rem;
  position: relative;
}
.airWayResultsPage .result .item .left .line1 .from,
.airWayResultsPage .result .item .left .line1 .to {
  text-align: center;
  display: inline-block;
  vertical-align: middle;
}
.airWayResultsPage .result .item .left .line1 .from p,
.airWayResultsPage .result .item .left .line1 .to p {
  color: #333;
  font-size: 0.3rem;
  width: 1.8rem;
  max-height: 1.6rem;
  text-align: center;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.airWayResultsPage .result .item .left .line1 .from p:first-child,
.airWayResultsPage .result .item .left .line1 .to p:first-child {
  font-size: 0.4rem;
  font-weight: 600;
}
.airWayResultsPage .result .item .left .line1 .from p:last-child,
.airWayResultsPage .result .item .left .line1 .to p:last-child {
  margin-top: 0.2rem;
}
.airWayResultsPage .result .item .left .line1 .arrow {
  display: inline-block;
  vertical-align: middle;
  text-align: center;
  width: 3.2rem;
  position: relative;
}
.airWayResultsPage .result .item .left .line1 .arrow .time {
  font-size: 0.32rem;
  color: #999;
  position: relative;
  width: 100%;
}
.airWayResultsPage .result .item .left .line1 .arrow .time .acrossDay {
  position: absolute;
  color: #4993fa;
  right: -0.2rem;
  top: -0.2rem;
}
.airWayResultsPage .result .item .left .line1 .arrow .arriveBybox {
  background: url(../img/recharge/train_arrow.png) center center / 100% 0.18rem no-repeat;
  min-height: 0.333333rem;
}
.airWayResultsPage .result .item .left .line1 .arrow .arriveBybox span {
  padding: 0 0.1rem;
  font-size: 0.34rem;
  color: #4993fa;
  background: #fff;
}
.airWayResultsPage .result .item .left .line1 .arrow .arriveBy {
  text-align: center;
  font-size: 0.29rem;
  width: 100%;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.airWayResultsPage .result .item .left .line1 .arrow .arriveBy label {
  color: #999;
}
.airWayResultsPage .result .item .left .line1 .arrow .arriveBy span {
  color: #444;
}
.airWayResultsPage .result .item .right p:nth-child(1) {
  font-size: 0.32rem;
  color: #666;
  line-height: 0.506666rem;
}
.airWayResultsPage .result .item .right p:nth-child(1) .price {
  color: #ff4343;
  font-size: 0.35rem;
}
.airWayResultsPage .result .item .right p:nth-child(1) .price span {
  font-size: 0.426666rem;
}
.airWayResultsPage .result .item .right .free {
  text-align: right;
  font: normal normal 0.25001rem/0.493333rem "Microsoft Yahei";
  color: #4993fa;
}
.airWayResultsPage .result .item .right .tax {
  font-size: 0.3rem;
  color: #888;
  text-align: right;
}
.airWayResultsPage .result .item .airway {
  position: absolute;
  bottom: 0.190066rem;
}
.airWayResultsPage .result .item .airway span {
  color: #999;
  margin-left: 0.2rem;
  font-size: 0.28rem;
}
.airWayResultsPage .result .item .airway label {
  color: #999;
  font-size: 0.28rem;
}
.airWayResultsPage .result .item .airway .share {
  color: #4993fa;
}
.airWayResultsPage .result .filterBox {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(120, 120, 120, 0.6);
  z-index: 1001;
  display: none;
}
.airWayResultsPage .result .filterBox .contentBox {
  position: absolute;
  bottom: 1.42rem;
  background-color: #fff;
  width: 100%;
  z-index: 1002;
}
.airWayResultsPage .result .filterBox .contentBox .header {
  padding: 0 0.5rem;
  border-bottom: 0.001rem solid #e7e7e7;
  background-color: #ffffff;
  height: 1.2rem;
  display: flex;
  justify-content: space-between;
}
.airWayResultsPage .result .filterBox .contentBox .header span {
  line-height: 1.2rem;
  font-size: 0.373333rem;
  text-align: center;
}
.airWayResultsPage .result .filterBox .contentBox .header .cancel {
  color: #5c5c5c;
}
.airWayResultsPage .result .filterBox .contentBox .header .reset {
  display: inline-block;
  border: 0.001rem solid #d4d2d2;
  line-height: 0.8rem;
  height: 0.8rem;
  width: 2rem;
  padding: 0 0.2rem;
  text-align: center;
  vertical-align: middle;
  margin-top: 0.2rem;
  color: #999;
}
.airWayResultsPage .result .filterBox .contentBox .header .submit {
  color: #f10214;
}
.airWayResultsPage .result .filterBox .contentBox .arrivesoon {
  height: 1rem;
  background-color: #fff;
  line-height: 1rem;
  padding-right: 0.5rem;
  border-bottom: 0.001rem solid #f5f4f4;
  font-size: 0.32rem;
}
.airWayResultsPage .result .filterBox .contentBox .arrivesoon label {
  display: inline-block;
  width: 3.14rem;
  text-align: center;
}
.airWayResultsPage .result .filterBox .contentBox .arrivesoon .Switch {
  height: 0.6rem;
  width: 1.4rem;
  float: right;
  margin-top: 0.2rem;
  background-color: #ececec;
}
.airWayResultsPage .result .filterBox .contentBox .arrivesoon .Switch.On {
  background-color: #4993fa;
}
.airWayResultsPage .result .filterBox .contentBox .arrivesoon .Switch.On .SwitchButton {
  left: 0.82rem;
}
.airWayResultsPage .result .filterBox .contentBox .arrivesoon .Switch .SwitchLine:before {
  position: absolute;
  content: "OFF";
  color: #999;
  left: 0.76rem;
  top: -0.2rem;
  font-size: 0.2rem;
}
.airWayResultsPage .result .filterBox .contentBox .arrivesoon .Switch .SwitchLine:after {
  position: absolute;
  content: "ON";
  color: #fff;
  left: 0.1rem;
  top: -0.2rem;
  font-size: 0.2rem;
}
.airWayResultsPage .result .filterBox .contentBox .arrivesoon .Switch .SwitchButton {
  width: 0.558rem;
  height: 0.55rem;
  left: 1px;
  top: 1px;
  border: none;
}
.airWayResultsPage .result .filterBox .contentBox .listscontainer {
  display: flex;
  height: 6rem;
}
.airWayResultsPage .result .filterBox .contentBox .listscontainer .tabs {
  flex: 0.3;
  background-color: #f7f7f7;
  border-right: 0.01rem solid #e1e1e1;
}
.airWayResultsPage .result .filterBox .contentBox .listscontainer .tabs .tab {
  text-align: center;
  background-color: #ececec;
  height: 1rem;
  line-height: 1rem;
  font-size: 0.32rem;
}
.airWayResultsPage .result .filterBox .contentBox .listscontainer .tabs .tab.active {
  background-color: #fff;
}
.airWayResultsPage .result .filterBox .contentBox .listscontainer .sections {
  flex: 0.7rem;
}
.airWayResultsPage .result .filterBox .contentBox .listscontainer .sections .sec {
  padding: 0 0.266666rem 0.16rem;
  overflow-y: scroll;
  height: 6rem;
  -webkit-overflow-scrolling: touch;
  width: 100%;
}
.airWayResultsPage .result .filterBox .contentBox .listscontainer .sections .sec li {
  padding: 0 0.213333rem;
  line-height: 1.173333rem;
  position: relative;
  overflow: hidden;
}
.airWayResultsPage .result .filterBox .contentBox .listscontainer .sections .sec li span {
  float: left;
  color: #333;
  font-size: 0.37rem;
}
.airWayResultsPage .result .filterBox .contentBox .listscontainer .sections .sec li .fli_checkbox_blue {
  position: absolute;
  right: 0.68rem;
  top: 26%;
}
.airWayResultsPage .result footer {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  padding: 0.18rem 0 0.1rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: box;
  display: flex;
  -webkit-justify-content: space-around;
  justify-content: space-around;
  -webkit-align-items: flex-end;
  align-items: flex-end;
  background: #f8f8f8;
  z-index: 100000;
}
.airWayResultsPage .result footer a span {
  display: block;
  width: 0.64rem;
  height: 0.64rem;
  margin: 0 auto;
}
.airWayResultsPage .result footer a .icon_filter {
  background: url(../img/recharge/icon_filter.png) center center / contain no-repeat;
}
.airWayResultsPage .result footer a .icon_time {
  background: url(../img/recharge/icon_time.png) center center / contain no-repeat;
}
.airWayResultsPage .result footer a .icon_money {
  background: url(../img/recharge/icon_dollar.png) center center / contain no-repeat;
}
.airWayResultsPage .result footer a p {
  font-size: 0.32rem;
  color: #999;
  line-height: 0.533333rem;
}
.airWayResultsPage .result footer a.active p {
  color: #4993fa;
}
.airWayResultsPage .result footer a.active .icon_filter {
  background: url(../img/recharge/icon_filter_active.png) center center / contain no-repeat;
}
.airWayResultsPage .result footer a.active .icon_time {
  background: url(../img/recharge/icon_time_active.png) center center / contain no-repeat;
}
.airWayResultsPage .result footer a.active .icon_money {
  background: url(../img/recharge/icon_dollar_active.png) center center / contain no-repeat;
}
.airWayItemInfopage {
  position: relative;
  min-height: auto;
}
.airWayItemInfopage .topban {
  position: absolute;
  top: 1.173333rem;
  left: 0;
  height: 4rem;
  width: 100%;
  background-color: #4993fa;
}
.airWayItemInfopage .InfoBox {
  position: relative;
  padding: 0.3333rem 0.2667rem;
}
.airWayItemInfopage .InfoBox .lists {
  width: 100%;
  padding: 0.356666rem 0;
  box-shadow: 0 0.1rem 0.15rem #e5e5e5;
  background-color: #fff;
  border-radius: 0.1rem;
}
.airWayItemInfopage .InfoBox .lists .item {
  width: 100%;
  padding: 0.2rem 0.4rem 0;
}
.airWayItemInfopage .InfoBox .lists .item:first-child {
  background: url(../img/airway/topbg.png) center / cover no-repeat;
}
.airWayItemInfopage .InfoBox .lists .item .date {
  color: #4993fa;
  font-size: 0.35rem;
  line-height: 0.5rem;
}
.airWayItemInfopage .InfoBox .lists .item .date span {
  margin-right: 0.2rem;
}
.airWayItemInfopage .InfoBox .lists .item .line {
  display: flex;
  justify-content: space-between;
}
.airWayItemInfopage .InfoBox .lists .item .line .from,
.airWayItemInfopage .InfoBox .lists .item .line .to {
  width: 3rem;
}
.airWayItemInfopage .InfoBox .lists .item .line .from p:first-child,
.airWayItemInfopage .InfoBox .lists .item .line .to p:first-child {
  font-size: 0.583333rem;
  color: #4a93fb;
  font-weight: 600;
}
.airWayItemInfopage .InfoBox .lists .item .line .from p:nth-child(2),
.airWayItemInfopage .InfoBox .lists .item .line .to p:nth-child(2) {
  line-height: 0.5rem;
  color: #666;
  font-size: 0.326666rem;
}
.airWayItemInfopage .InfoBox .lists .item .line .from {
  text-align: left;
}
.airWayItemInfopage .InfoBox .lists .item .line .to {
  text-align: right;
}
.airWayItemInfopage .InfoBox .lists .item .line .arrow {
  text-align: center;
  width: 3.6rem;
}
.airWayItemInfopage .InfoBox .lists .item .line .arrow p:first-child {
  font-size: 0.293333rem;
  color: #999;
  position: relative;
}
.airWayItemInfopage .InfoBox .lists .item .line .arrow p:first-child .plusflag {
  position: absolute;
  top: -0.2rem;
  right: -0.2rem;
  color: #4993fa;
  font-size: 0.313333rem;
}
.airWayItemInfopage .InfoBox .lists .item .line .arrow p:nth-child(2) {
  height: 0.35rem;
  background: url(../img/recharge/train_arrow.png) center center / 100% auto no-repeat;
}
.airWayItemInfopage .InfoBox .lists .item .line .arrow p:nth-child(3) {
  font-size: 0.293333rem;
  color: #626262;
}
.airWayItemInfopage .InfoBox .lists .fact {
  font-size: 0.293333rem;
  color: #626262;
  text-align: center;
}
.airWayItemInfopage .InfoBox .lists .acrossCity {
  text-align: center;
  font-size: 0.36rem;
  color: #4993fa;
  margin: 0.12rem 0;
}
.airWayItemInfopage .InfoBox .lists .acrossCity span {
  margin: 0 0.12rem;
}
.airWayItemInfopage .InfoBox .lists .notice {
  font-size: 0.34rem;
  line-height: 0.5rem;
  padding: 0.2rem 0.4rem 0;
  border-top: 0.001rem solid #f1f1f1;
}
.airWayItemInfopage .InfoBox .lists .notice label {
  color: #ff4343;
}
.airWayItemInfopage .InfoBox .lists .notice span {
  color: #9d9d9d;
}
.airWayItemInfopage .InfoBox .fly_list {
  width: 100%;
}
.airWayItemInfopage .InfoBox .fly_list li {
  background-color: #fff;
  border-radius: 0.1rem;
  padding: 0.2rem 0.34rem;
  overflow: hidden;
  margin: 0.206666rem 0;
}
.airWayItemInfopage .InfoBox .fly_list li .section1 {
  height: 2.2rem;
  float: left;
}
.airWayItemInfopage .InfoBox .fly_list li .section1 p:first-child {
  line-height: 0.6rem;
}
.airWayItemInfopage .InfoBox .fly_list li .section1 p:first-child .agent {
  color: #222;
  font-size: 0.38rem;
  font-weight: 800;
  vertical-align: middle;
}
.airWayItemInfopage .InfoBox .fly_list li .section1 p:first-child .type {
  color: #999;
  font-size: 0.3rem;
  vertical-align: middle;
  margin-left: 0.2rem;
}
.airWayItemInfopage .InfoBox .fly_list li .section1 p:first-child label {
  font-size: 0.326666rem;
  color: #333;
  line-height: 0.7rem;
  font-weight: 600;
}
.airWayItemInfopage .InfoBox .fly_list li .section1 p:nth-child(2) .price {
  color: #ff4343;
  font-size: 0.38rem;
  line-height: 0.77rem;
  margin-right: 0.2rem;
  font-weight: 900;
}
.airWayItemInfopage .InfoBox .fly_list li .section1 p:nth-child(2) .price span {
  font-size: 0.48rem;
}
.airWayItemInfopage .InfoBox .fly_list li .section1 p:nth-child(2) label {
  font-size: 0.326666rem;
  color: #333;
  line-height: 0.7rem;
  font-weight: 600;
}
.airWayItemInfopage .InfoBox .fly_list li .section1 p:nth-child(3) span {
  color: #999;
  line-height: 0.8rem;
  font-size: 0.3rem;
}
.airWayItemInfopage .InfoBox .fly_list li .section1 p:nth-child(3) span.icon_im {
  display: inline-block;
  vertical-align: middle;
  width: 0.36rem;
  height: 0.36rem;
  background: url(../img/recharge/icon_im.png) no-repeat;
  background-position: center center;
  background-size: contain;
}
.airWayItemInfopage .InfoBox .fly_list li .section1 p:nth-child(3) span.tgq {
  color: #4993fa;
}
.airWayItemInfopage .InfoBox .fly_list li .section2 {
  float: right;
  padding-top: 0.2rem;
  height: 2.2rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.airWayItemInfopage .InfoBox .fly_list li .section2 button {
  border: none;
  width: 1.52rem;
  height: 0.88rem;
  line-height: 0.88rem;
  background: #4993fa;
  border-radius: 0.053333rem;
  text-align: center;
  color: #fff;
  font-size: 0.393333rem;
}
.airWayItemInfopage .InfoBox .fly_list li .section2 button:focus {
  outline: none;
}
.airWayItemInfopage .InfoBox .fly_list li .section2 p {
  font: normal normal 0.3001rem "Microsoft Yahei";
  text-align: right;
  color: #666;
  line-height: 0.8rem;
}
.airWayCompleteInfo {
  position: relative;
  min-height: auto;
}
.airWayCompleteInfo .topban {
  position: absolute;
  top: 1.173333rem;
  left: 0;
  height: 4rem;
  width: 100%;
  background-color: #4993fa;
}
.airWayCompleteInfo .InfoBox {
  position: relative;
  padding: 0.3333rem 0.2667rem;
}
.airWayCompleteInfo .InfoBox .lists {
  width: 100%;
  padding: 0.356666rem 0;
  box-shadow: 0 0.1rem 0.15rem #e5e5e5;
  background-color: #fff;
  border-radius: 0.1rem;
}
.airWayCompleteInfo .InfoBox .lists .item {
  width: 100%;
  padding: 0.2rem 0.4rem 0;
}
.airWayCompleteInfo .InfoBox .lists .item:first-child {
  background: url(../img/airway/topbg.png) center / cover no-repeat;
}
.airWayCompleteInfo .InfoBox .lists .item .date {
  color: #4993fa;
  font-size: 0.35rem;
  line-height: 0.5rem;
}
.airWayCompleteInfo .InfoBox .lists .item .date span {
  margin-right: 0.2rem;
}
.airWayCompleteInfo .InfoBox .lists .item .line {
  display: flex;
  justify-content: space-between;
}
.airWayCompleteInfo .InfoBox .lists .item .line .from,
.airWayCompleteInfo .InfoBox .lists .item .line .to {
  width: 3rem;
}
.airWayCompleteInfo .InfoBox .lists .item .line .from p:first-child,
.airWayCompleteInfo .InfoBox .lists .item .line .to p:first-child {
  font-size: 0.583333rem;
  color: #4a93fb;
  font-weight: 600;
}
.airWayCompleteInfo .InfoBox .lists .item .line .from p:nth-child(2),
.airWayCompleteInfo .InfoBox .lists .item .line .to p:nth-child(2) {
  line-height: 0.5rem;
  color: #666;
  font-size: 0.326666rem;
}
.airWayCompleteInfo .InfoBox .lists .item .line .from {
  text-align: left;
}
.airWayCompleteInfo .InfoBox .lists .item .line .to {
  text-align: right;
}
.airWayCompleteInfo .InfoBox .lists .item .line .arrow {
  text-align: center;
  width: 3.6rem;
}
.airWayCompleteInfo .InfoBox .lists .item .line .arrow p:first-child {
  font-size: 0.293333rem;
  color: #999;
  position: relative;
}
.airWayCompleteInfo .InfoBox .lists .item .line .arrow p:first-child .plusflag {
  position: absolute;
  top: -0.2rem;
  right: -0.2rem;
  color: #4993fa;
  font-size: 0.313333rem;
}
.airWayCompleteInfo .InfoBox .lists .item .line .arrow p:nth-child(2) {
  height: 0.35rem;
  background: url(../img/recharge/train_arrow.png) center center / 100% auto no-repeat;
}
.airWayCompleteInfo .InfoBox .lists .item .line .arrow p:nth-child(3) {
  font-size: 0.293333rem;
  color: #626262;
}
.airWayCompleteInfo .InfoBox .lists .fact {
  font-size: 0.293333rem;
  color: #626262;
  text-align: center;
}
.airWayCompleteInfo .InfoBox .lists .acrossCity {
  text-align: center;
  font-size: 0.36rem;
  color: #4993fa;
  margin: 0.12rem 0;
}
.airWayCompleteInfo .InfoBox .lists .acrossCity span {
  margin: 0 0.12rem;
}
.airWayCompleteInfo .InfoBox .lists .notice {
  font-size: 0.34rem;
  line-height: 0.5rem;
  padding: 0.2rem 0.4rem 0;
  border-top: 0.001rem solid #f1f1f1;
}
.airWayCompleteInfo .InfoBox .lists .notice label {
  color: #ff4343;
}
.airWayCompleteInfo .InfoBox .lists .notice span {
  color: #9d9d9d;
}
.airWayCompleteInfo .InfoBox .airplane_common {
  width: 100%;
  padding: 0.356666rem 0;
}
.airWayCompleteInfo .InfoBox .airplane_common .section_1 {
  background: #fff;
  border-radius: 0.1rem;
}
.airWayCompleteInfo .InfoBox .airplane_common .section_1 .line1 {
  padding: 0.266666rem 0.4rem;
  line-height: 0.533333rem;
  overflow: hidden;
}
.airWayCompleteInfo .InfoBox .airplane_common .section_1 .line1 header {
  font-size: 0.373333rem;
  color: #333;
  float: left;
  font-weight: 800;
}
.airWayCompleteInfo .InfoBox .airplane_common .section_1 .line1 .item {
  font-size: 0.32rem;
  color: #666;
  margin-left: 0.5rem;
  float: left;
}
.airWayCompleteInfo .InfoBox .airplane_common .section_1 .line1 .item span {
  color: #ff4343;
  margin-left: 0.16rem;
}
.airWayCompleteInfo .InfoBox .airplane_common .section_1 .line2 {
  line-height: 0.68rem;
  padding: 0 0.4rem;
  font-size: 0.32rem;
  color: #4993fa;
}
.airWayCompleteInfo .InfoBox .airplane_common .section_2 {
  position: relative;
  padding: 0.266666rem 0.4rem;
  margin: 0.133333rem 0 0;
  background: #fff;
  margin: 0.133333rem auto;
  border-radius: 0.1rem;
}
.airWayCompleteInfo .InfoBox .airplane_common .section_2 header {
  font-size: 0.373333rem;
  color: #333;
  font-weight: 800;
}
.airWayCompleteInfo .InfoBox .airplane_common .section_2 header #inventory {
  font-weight: normal;
  margin-left: 0.2rem;
}
.airWayCompleteInfo .InfoBox .airplane_common .section_2 .icon_add {
  position: absolute;
  top: 0.168rem;
  right: 0.360666rem;
  display: inline-block;
  vertical-align: middle;
  width: 0.613333rem;
  height: 0.613333rem;
  background: url(../img/recharge/icon_plus.png) center center / contain no-repeat;
}
.airWayCompleteInfo .InfoBox .airplane_common .section_3 {
  padding: 0.266666rem 0.4rem;
  background-color: #fff;
  border-radius: 0.1rem;
}
.airWayCompleteInfo .InfoBox .airplane_common .section_3 .infoitems {
  width: 96%;
  position: relative;
  border-bottom: 0.01rem dashed #f1f1f1;
}
.airWayCompleteInfo .InfoBox .airplane_common .section_3 .infoitems .item {
  min-width: 50%;
  float: left;
  font-size: 0.353333rem;
  line-height: 0.6rem;
}
.airWayCompleteInfo .InfoBox .airplane_common .section_3 .infoitems .item:first-child {
  color: #4993fa;
  font-weight: 800;
}
.airWayCompleteInfo .InfoBox .airplane_common .section_3 .infoitems .item label {
  font-size: 0.323333rem;
  color: #333;
  font-weight: 800;
  line-height: 0.6rem;
}
.airWayCompleteInfo .InfoBox .airplane_common .section_3 .infoitems .item span {
  font-size: 0.3rem;
  line-height: 0.6rem;
}
.airWayCompleteInfo .InfoBox .airplane_common .section_3 .infoitems .icon_del {
  position: absolute;
  right: -0.4rem;
  top: 50%;
  width: 0.613333rem;
  height: 0.613333rem;
  transform: translate(0, -50%);
  background: url(../img/recharge/icon_minus.png) center center / contain no-repeat;
}
.airWayCompleteInfo .InfoBox .airplane_common .section_3 .infoitems::after {
  content: "";
  display: block;
  visibility: hidden;
  clear: both;
}
.airWayCompleteInfo .InfoBox .airplane_common .relationship {
  margin: 0.1333333rem 0;
  background-color: #fff;
  border-radius: 0.1rem;
}
.airWayCompleteInfo .InfoBox .airplane_common .relationship .name,
.airWayCompleteInfo .InfoBox .airplane_common .relationship .tel,
.airWayCompleteInfo .InfoBox .airplane_common .relationship .mail {
  padding: 0 0.4rem;
  border-bottom: 0.01rem solid #efefef;
}
.airWayCompleteInfo .InfoBox .airplane_common .relationship .name label,
.airWayCompleteInfo .InfoBox .airplane_common .relationship .tel label,
.airWayCompleteInfo .InfoBox .airplane_common .relationship .mail label {
  font-size: 0.373333rem;
  vertical-align: top;
  display: inline-block;
  height: 1rem;
  line-height: 1rem;
  width: 1.5rem;
  margin-right: 0.1rem;
  font-weight: 800;
}
.airWayCompleteInfo .InfoBox .airplane_common .relationship .name input,
.airWayCompleteInfo .InfoBox .airplane_common .relationship .tel input,
.airWayCompleteInfo .InfoBox .airplane_common .relationship .mail input {
  vertical-align: top;
  display: inline-block;
  height: 1rem;
  line-height: 1rem;
  width: 6rem;
  font-size: 0.37rem;
  background-color: transparent;
}
.airWayCompleteInfo .InfoBox .airplane_common .relationship .mail {
  border-bottom: none;
}
.airWayCompleteInfo .InfoBox .airplane_common .relationship .tel span {
  display: inline-block;
  height: 0.8rem;
  line-height: 0.8rem;
  font-size: 0.37rem;
  margin-top: 0.1rem;
  border: 0.02rem solid #e1e1e1;
  border-radius: 0.05rem;
  width: 1rem;
  text-align: center;
}
.airWayCompleteInfo .InfoBox .airplane_common .relationship .tel input {
  width: 5.5rem;
}
.airWayCompleteInfo .InfoBox .airplane_common .price_total {
  padding: 0.4rem;
  margin: 0.133333rem 0;
  background: #fff;
  border-radius: 0.1rem;
}
.airWayCompleteInfo .InfoBox .airplane_common .price_total .item {
  font-size: 0.373333rem;
  color: #999;
  line-height: 0.7rem;
}
.airWayCompleteInfo .InfoBox .airplane_common .price_total .item .right {
  float: right;
  color: #ff4343;
}
.airWayCompleteInfo .InfoBox .airplane_common .agreenment_section {
  padding: 0.3rem 0.4rem;
  background: #fff;
  border-radius: 0.1rem;
}
.airWayCompleteInfo .InfoBox .airplane_common .agreenment_section .fli_checkbox_blue {
  float: left;
  margin-top: 0.18rem;
}
.airWayCompleteInfo .InfoBox .airplane_common .agreenment_section .fli_checkbox_blue input[type="checkbox"] {
  visibility: hidden;
}
.airWayCompleteInfo .InfoBox .airplane_common .agreenment_section .fli_checkbox_blue label {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  width: 0.45333333rem;
  height: 0.45333333rem;
  border: 0.028rem;
  border-style: solid;
  border-color: #ddd;
  border-radius: 50%;
}
.airWayCompleteInfo .InfoBox .airplane_common .agreenment_section .fli_checkbox_blue label::before {
  content: " ";
  position: absolute;
  top: -0.26rem;
  bottom: -0.26rem;
  left: -0.26rem;
  right: -0.26rem;
}
.airWayCompleteInfo .InfoBox .airplane_common .agreenment_section p {
  margin-left: 0.8rem;
  font-size: 0.266666rem;
  color: #333;
  line-height: 0.4rem;
}
.airWayCompleteInfo .fixed_bottom_settlement {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 1.333333rem;
  background: #fff;
  z-index: 9999;
  overflow: hidden;
}
.airWayCompleteInfo .fixed_bottom_settlement .btn_submit {
  float: right;
  height: 100%;
  width: 2.88rem;
  color: #fff;
  background: #416efb;
  font-size: 0.426666rem;
  text-align: center;
  border: none;
  background: -webkit-linear-gradient(left, #416efb, #519cff);
  background: -o-linear-gradient(left, #416efb, #519cff);
  background: -moz-linear-gradient(left, #416efb, #519cff);
  background: linear-gradient(left, #416efb, #519cff);
}
.airWayCompleteInfo .fixed_bottom_settlement .payment {
  padding: 0.133333rem;
  float: right;
}
.airWayCompleteInfo .fixed_bottom_settlement .payment p {
  text-align: right;
  font-size: 0.426666rem;
  color: #333;
  line-height: 0.533333rem;
}
.airWayCompleteInfo .fixed_bottom_settlement .payment p span {
  color: #ff022f;
}
.airWayCompleteInfo .fixed_bottom_settlement .payment p.text_gray {
  color: #a2a7bd;
  font-size: 0.32rem;
}
.airWayCompleteInfo .areaCode {
  z-index: 10000;
  position: fixed;
  top: 0;
  bottom: 0;
  width: 100%;
  -webkit-transform: translate3d(100%, 0, 0);
  -moz-transform: translate3d(100%, 0, 0);
  transform: translate3d(100%, 0, 0);
  -webkit-transition: transform 250ms ease-in-out;
  -webkit-transition: -webkit-transform 250ms ease-in-out;
  transition: -webkit-transform 250ms ease-in-out;
  -o-transition: -o-transform 250ms ease-in-out;
  -moz-transition: transform 250ms ease-in-out, -moz-transform 250ms ease-in-out;
  transition: transform 250ms ease-in-out;
  transition: transform 250ms ease-in-out, -webkit-transform 250ms ease-in-out, -moz-transform 250ms ease-in-out, -o-transform 250ms ease-in-out;
}
.airWayCompleteInfo .areaCode.entry {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}
.airWayCompleteInfo .areaCode .topfixed {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000000;
  width: 100%;
}
.airWayCompleteInfo .areaCode .topfixed .input_section {
  background: #4993fa;
  padding: 0.1rem 0.4rem;
  overflow: hidden;
}
.airWayCompleteInfo .areaCode .topfixed .input_section .input_text {
  height: 1.1rem;
  font-size: 0.32rem;
  float: left;
  display: block;
  border-radius: 0.08rem;
  padding-left: 0.3rem;
  width: 88%;
}
.airWayCompleteInfo .areaCode .topfixed .input_section .text_concel {
  width: 10%;
  float: right;
  font-size: 0.373333rem;
  color: #fff;
  line-height: 1.173333rem;
  text-align: center;
}
.airWayCompleteInfo .areaCode .topfixed .search_suggest_list {
  position: fixed;
  top: 2.51rem;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 999999;
  display: none;
  overflow-y: scroll;
}
.airWayCompleteInfo .areaCode .topfixed .search_suggest_list ul {
  background: #fff;
}
.airWayCompleteInfo .areaCode .topfixed .search_suggest_list ul li {
  line-height: 1rem;
  padding: 0 0.4rem;
  font-size: 0.32rem;
  border-bottom: 1px solid #e1e1e1;
}
.airWayCompleteInfo .areaCode .topfixed .search_suggest_list ul li span {
  font-size: 0.3rem;
}
.airWayCompleteInfo .areaCode .currentText {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 1.86666667rem;
  height: 1.86666667rem;
  line-height: 1.86666667rem;
  font-size: 1.06666667rem;
  background: rgba(0, 0, 0, 0.3);
  color: #fff;
  z-index: 999;
  text-align: center;
  border-radius: 0.26666667rem;
}
.airWayCompleteInfo .areaCode .index-bar {
  position: absolute;
  width: 1rem;
  height: 100%;
  z-index: 1;
  right: 0;
  top: 2.5rem;
  padding-top: 0.3rem;
  font-size: 0.32rem;
  text-align: center;
  background-color: #fff;
  color: #4993fa;
  box-sizing: border-box;
}
.airWayCompleteInfo .areaCode .index-bar .ilist {
  display: flex;
  flex-direction: column;
  height: 13rem;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.4);
}
.airWayCompleteInfo .areaCode .index-bar .ilist li {
  position: relative;
  flex: 1;
  right: 0;
  height: 0.48rem;
  line-height: 0.48rem;
}
.airWayCompleteInfo .areaCode .index-bar .ilist li:active {
  background-color: rgba(0, 0, 0, 0.1);
}
.airWayCompleteInfo .areaCode .country-wrapper {
  position: relative;
  -webkit-overflow-scrolling: touch;
}
.airWayCompleteInfo .areaCode .country-wrapper .inner-wrapper {
  position: fixed;
  top: 2.5rem;
  bottom: 0;
  width: 100%;
  overflow-y: auto;
}
.airWayCompleteInfo .areaCode .country-wrapper .inner-wrapper .country-box {
  line-height: 0.6rem;
  color: #333;
}
.airWayCompleteInfo .areaCode .country-wrapper .inner-wrapper .country-box .ctitle {
  background-color: #fbfbfb;
  border-top: 1px solid #e1e5e9;
  border-bottom: 1px solid #e1e5e9;
  padding-left: 0.36rem;
  font-size: 0.38rem;
  height: 1rem;
  line-height: 1rem;
}
.airWayCompleteInfo .areaCode .country-wrapper .inner-wrapper .country-box .clist li {
  background-color: #fff;
  padding: 0 1.2rem 0 0.36rem;
  height: 0.8rem;
  line-height: 0.8rem;
}
.airWayCompleteInfo .areaCode .country-wrapper .inner-wrapper .country-box .clist li span {
  font-size: 0.3rem;
}
.airWayCompleteInfo .areaCode .country-wrapper .inner-wrapper .bottomline {
  height: 0.8rem;
  text-align: center;
  color: #999;
  font-size: 0.3rem;
  line-height: 0.8rem;
  background-color: #fff;
}
.airWayAffrimOrderPage .topban {
  position: absolute;
  top: 1.173333rem;
  left: 0;
  height: 4rem;
  width: 100%;
  background-color: #4993fa;
}
.airWayAffrimOrderPage .InfoBox {
  position: relative;
  padding: 0.3333rem 0.2667rem;
}
.airWayAffrimOrderPage .InfoBox .lists {
  width: 100%;
  padding: 0.356666rem 0;
  box-shadow: 0 0.1rem 0.15rem #e5e5e5;
  background-color: #fff;
  border-radius: 0.1rem;
}
.airWayAffrimOrderPage .InfoBox .lists .item {
  width: 100%;
  padding: 0.2rem 0.4rem 0;
}
.airWayAffrimOrderPage .InfoBox .lists .item:first-child {
  background: url(../img/airway/topbg.png) center / cover no-repeat;
}
.airWayAffrimOrderPage .InfoBox .lists .item .date {
  color: #4993fa;
  font-size: 0.35rem;
  line-height: 0.5rem;
}
.airWayAffrimOrderPage .InfoBox .lists .item .date span {
  margin-right: 0.2rem;
}
.airWayAffrimOrderPage .InfoBox .lists .item .line {
  display: flex;
  justify-content: space-between;
}
.airWayAffrimOrderPage .InfoBox .lists .item .line .from,
.airWayAffrimOrderPage .InfoBox .lists .item .line .to {
  width: 3rem;
}
.airWayAffrimOrderPage .InfoBox .lists .item .line .from p:first-child,
.airWayAffrimOrderPage .InfoBox .lists .item .line .to p:first-child {
  font-size: 0.583333rem;
  color: #4a93fb;
  font-weight: 600;
}
.airWayAffrimOrderPage .InfoBox .lists .item .line .from p:nth-child(2),
.airWayAffrimOrderPage .InfoBox .lists .item .line .to p:nth-child(2) {
  line-height: 0.5rem;
  color: #666;
  font-size: 0.326666rem;
}
.airWayAffrimOrderPage .InfoBox .lists .item .line .from {
  text-align: left;
}
.airWayAffrimOrderPage .InfoBox .lists .item .line .to {
  text-align: right;
}
.airWayAffrimOrderPage .InfoBox .lists .item .line .arrow {
  text-align: center;
  width: 3.6rem;
}
.airWayAffrimOrderPage .InfoBox .lists .item .line .arrow p:first-child {
  font-size: 0.293333rem;
  color: #999;
  position: relative;
}
.airWayAffrimOrderPage .InfoBox .lists .item .line .arrow p:first-child .plusflag {
  position: absolute;
  top: -0.2rem;
  right: -0.2rem;
  color: #4993fa;
  font-size: 0.313333rem;
}
.airWayAffrimOrderPage .InfoBox .lists .item .line .arrow p:nth-child(2) {
  height: 0.35rem;
  background: url(../img/recharge/train_arrow.png) center center / 100% auto no-repeat;
}
.airWayAffrimOrderPage .InfoBox .lists .item .line .arrow p:nth-child(3) {
  font-size: 0.293333rem;
  color: #626262;
}
.airWayAffrimOrderPage .InfoBox .lists .fact {
  font-size: 0.293333rem;
  color: #626262;
  text-align: center;
}
.airWayAffrimOrderPage .InfoBox .lists .acrossCity {
  text-align: center;
  font-size: 0.36rem;
  color: #4993fa;
  margin: 0.12rem 0;
}
.airWayAffrimOrderPage .InfoBox .lists .acrossCity span {
  margin: 0 0.12rem;
}
.airWayAffrimOrderPage .InfoBox .lists .notice {
  font-size: 0.34rem;
  line-height: 0.5rem;
  padding: 0.2rem 0.4rem 0;
  border-top: 0.001rem solid #f1f1f1;
}
.airWayAffrimOrderPage .InfoBox .lists .notice label {
  color: #ff4343;
}
.airWayAffrimOrderPage .InfoBox .lists .notice span {
  color: #9d9d9d;
}
.airWayAffrimOrderPage .InfoBox .passengerInfo {
  width: 100%;
  background-color: #fff;
  margin: 0.206666rem 0;
  padding: 0.2rem 0.3rem;
}
.airWayAffrimOrderPage .InfoBox .passengerInfo .section1 {
  display: flex;
}
.airWayAffrimOrderPage .InfoBox .passengerInfo .section1 .label {
  flex: 0.15;
  font-size: 0.37rem;
  color: #333;
  display: inline-block;
}
.airWayAffrimOrderPage .InfoBox .passengerInfo .section1 .infobox {
  flex: 0.85;
}
.airWayAffrimOrderPage .InfoBox .passengerInfo .section1 .infobox .info {
  overflow: hidden;
  margin-bottom: 0.1rem;
}
.airWayAffrimOrderPage .InfoBox .passengerInfo .section1 .infobox .info .num,
.airWayAffrimOrderPage .InfoBox .passengerInfo .section1 .infobox .info .passport,
.airWayAffrimOrderPage .InfoBox .passengerInfo .section1 .infobox .info .name {
  display: inline-block;
  font-size: 0.32rem;
  vertical-align: top;
}
.airWayAffrimOrderPage .InfoBox .passengerInfo .section1 .infobox .info .passport {
  margin-left: 0.4rem;
}
.airWayAffrimOrderPage .InfoBox .passengerInfo .section2 {
  display: flex;
  padding-top: 0.2rem;
  position: relative;
}
.airWayAffrimOrderPage .InfoBox .passengerInfo .section2 .label {
  flex: 0.15;
  font-size: 0.37rem;
  color: #333;
  display: inline-block;
  line-height: 0.49rem;
}
.airWayAffrimOrderPage .InfoBox .passengerInfo .section2 .infobox {
  flex: 0.85;
}
.airWayAffrimOrderPage .InfoBox .passengerInfo .section2 .infobox .info {
  overflow: hidden;
}
.airWayAffrimOrderPage .InfoBox .passengerInfo .section2 .infobox .info li {
  width: 50%;
  text-align: left;
  float: left;
  font-size: 0.32rem;
  line-height: 0.49rem;
  height: 100%;
  margin-bottom: 0.1rem;
}
.airWayAffrimOrderPage .InfoBox .passengerInfo .section2:after {
  content: "";
  position: absolute;
  top: 0;
  width: 98%;
  left: 1%;
  height: 0;
  border-top: 0.01rem solid #f1f1f1;
}
.airWayAffrimOrderPage .InfoBox .favourable_section {
  position: relative;
  background: #fff;
  margin-top: 0.266666rem;
  color: #444;
}
.airWayAffrimOrderPage .InfoBox .favourable_section .lines_Section_all {
  position: relative;
  padding: 0.06rem 0.38rem;
}
.airWayAffrimOrderPage .InfoBox .favourable_section .lines_Section_all a {
  display: block;
  font-size: 0.4rem;
  line-height: 1.1rem;
  color: #444;
}
.airWayAffrimOrderPage .InfoBox .favourable_section .lines_Section_all a span.like_btn {
  display: inline-block;
  vertical-align: middle;
  height: 0.58666667rem;
  line-height: 0.58666667rem;
  color: #fff;
  background: #fd1b3b;
  padding: 0rem 0.21333333rem;
  border-radius: 0.08rem;
  font-size: 0.32rem;
  margin-left: 0.26666667rem;
  margin-top: -0.05333333rem;
}
.airWayAffrimOrderPage .InfoBox .favourable_section .lines_Section_all a .right_text {
  float: right;
  padding-right: 0.32rem;
  color: #fd1b3b;
}
.airWayAffrimOrderPage .InfoBox .favourable_section .lines_Section_all a.fli_link_line:after {
  right: 0;
  top: 0.4rem;
}
.airWayAffrimOrderPage .InfoBox .favourable_section .lines_Section_all:nth-child(2):before {
  content: "";
  display: block;
  border-top: 0.01rem solid #f1f1f1;
  width: 98%;
  height: 0;
  margin: 0 auto;
}
.airWayAffrimOrderPage .InfoBox .amount_section {
  position: relative;
  background: #fff;
  margin-top: 0.266666rem;
  padding: 0.4rem;
}
.airWayAffrimOrderPage .InfoBox .amount_section p {
  display: block;
  line-height: 0.64rem;
  font-size: 0.34rem;
  color: #444;
}
.airWayAffrimOrderPage .InfoBox .amount_section p span {
  float: right;
  color: #ff3900;
}
.airWayAffrimOrderPage .InfoBox .amount_section .icon-ali-jifen {
  vertical-align: baseline;
}
.airWayOrderInfoPage .infoTitle {
  margin-top: 0.24rem;
  font-size: 0.373333rem;
  padding: 0 0.4rem;
  background-color: #fff;
}
.airWayOrderInfoPage .infoTitle .no_order {
  line-height: 0.933333rem;
}
.airWayOrderInfoPage .infoTitle .no_order .status {
  float: right;
  color: #e21b38;
}
.airWayOrderInfoPage .infoTitle .tripinfo {
  line-height: 0.633333rem;
  border-top: 1px solid #f1f1f1;
  font-size: 0.3rem;
  color: #4993fa;
}
.airWayOrderInfoPage .infoTitle .tripinfo .trip {
  margin-left: 0.2rem;
}
.airWayOrderInfoPage .topban {
  position: absolute;
  top: 1.173333rem;
  left: 0;
  height: 4rem;
  width: 100%;
  background-color: #4993fa;
}
.airWayOrderInfoPage .InfoBox {
  position: relative;
  padding: 0.3333rem 0.2667rem;
}
.airWayOrderInfoPage .InfoBox .lists {
  width: 100%;
  padding: 0.356666rem 0;
  box-shadow: 0 0.1rem 0.15rem #e5e5e5;
  background-color: #fff;
  border-radius: 0.1rem;
}
.airWayOrderInfoPage .InfoBox .lists .item {
  width: 100%;
  padding: 0.2rem 0.4rem 0;
}
.airWayOrderInfoPage .InfoBox .lists .item:first-child {
  background: url(../img/airway/topbg.png) center / cover no-repeat;
}
.airWayOrderInfoPage .InfoBox .lists .item .date {
  color: #4993fa;
  font-size: 0.35rem;
  line-height: 0.5rem;
}
.airWayOrderInfoPage .InfoBox .lists .item .date span {
  margin-right: 0.2rem;
}
.airWayOrderInfoPage .InfoBox .lists .item .line {
  display: flex;
  justify-content: space-between;
}
.airWayOrderInfoPage .InfoBox .lists .item .line .from,
.airWayOrderInfoPage .InfoBox .lists .item .line .to {
  width: 3rem;
}
.airWayOrderInfoPage .InfoBox .lists .item .line .from p:first-child,
.airWayOrderInfoPage .InfoBox .lists .item .line .to p:first-child {
  font-size: 0.583333rem;
  color: #4a93fb;
  font-weight: 600;
}
.airWayOrderInfoPage .InfoBox .lists .item .line .from p:nth-child(2),
.airWayOrderInfoPage .InfoBox .lists .item .line .to p:nth-child(2) {
  line-height: 0.5rem;
  color: #666;
  font-size: 0.326666rem;
}
.airWayOrderInfoPage .InfoBox .lists .item .line .from {
  text-align: left;
}
.airWayOrderInfoPage .InfoBox .lists .item .line .to {
  text-align: right;
}
.airWayOrderInfoPage .InfoBox .lists .item .line .arrow {
  text-align: center;
  width: 3.6rem;
}
.airWayOrderInfoPage .InfoBox .lists .item .line .arrow p:first-child {
  font-size: 0.293333rem;
  color: #999;
  position: relative;
}
.airWayOrderInfoPage .InfoBox .lists .item .line .arrow p:first-child .plusflag {
  position: absolute;
  top: -0.2rem;
  right: -0.2rem;
  color: #4993fa;
  font-size: 0.313333rem;
}
.airWayOrderInfoPage .InfoBox .lists .item .line .arrow p:nth-child(2) {
  height: 0.35rem;
  background: url(../img/recharge/train_arrow.png) center center / 100% auto no-repeat;
}
.airWayOrderInfoPage .InfoBox .lists .item .line .arrow p:nth-child(3) {
  font-size: 0.293333rem;
  color: #626262;
}
.airWayOrderInfoPage .InfoBox .lists .fact {
  font-size: 0.293333rem;
  color: #626262;
  text-align: center;
}
.airWayOrderInfoPage .InfoBox .lists .acrossCity {
  text-align: center;
  font-size: 0.36rem;
  color: #4993fa;
  margin: 0.12rem 0;
}
.airWayOrderInfoPage .InfoBox .lists .acrossCity span {
  margin: 0 0.12rem;
}
.airWayOrderInfoPage .InfoBox .lists .notice {
  font-size: 0.34rem;
  line-height: 0.5rem;
  padding: 0.2rem 0.4rem 0;
  border-top: 0.001rem solid #f1f1f1;
}
.airWayOrderInfoPage .InfoBox .lists .notice label {
  color: #ff4343;
}
.airWayOrderInfoPage .InfoBox .lists .notice span {
  color: #9d9d9d;
}
.airWayOrderInfoPage .InfoBox .passengerInfo {
  width: 100%;
  background-color: #fff;
  margin: 0.206666rem 0;
  padding: 0.2rem 0.3rem;
}
.airWayOrderInfoPage .InfoBox .passengerInfo .section0 {
  font-size: 0.35rem;
  color: #4993fa;
  line-height: 0.8rem;
  height: 0.8rem;
}
.airWayOrderInfoPage .InfoBox .passengerInfo .section1 {
  display: flex;
  padding-top: 0.2rem;
  position: relative;
}
.airWayOrderInfoPage .InfoBox .passengerInfo .section1 .label {
  flex: 0.15;
  font-size: 0.37rem;
  color: #333;
  display: inline-block;
}
.airWayOrderInfoPage .InfoBox .passengerInfo .section1 .infobox {
  flex: 0.85;
}
.airWayOrderInfoPage .InfoBox .passengerInfo .section1 .infobox .info {
  overflow: hidden;
  margin-bottom: 0.1rem;
}
.airWayOrderInfoPage .InfoBox .passengerInfo .section1 .infobox .info .num,
.airWayOrderInfoPage .InfoBox .passengerInfo .section1 .infobox .info .passport,
.airWayOrderInfoPage .InfoBox .passengerInfo .section1 .infobox .info .name {
  display: inline-block;
  font-size: 0.32rem;
  vertical-align: top;
}
.airWayOrderInfoPage .InfoBox .passengerInfo .section1 .infobox .info .passport {
  margin-left: 0.4rem;
}
.airWayOrderInfoPage .InfoBox .passengerInfo .section1 .icon_Info {
  width: 0.6rem;
  height: 0.6rem;
  right: 0.1rem;
  position: absolute;
  background: url(../img/airway/passengerInfo.png) center center / contain no-repeat;
}
.airWayOrderInfoPage .InfoBox .passengerInfo .section1:after {
  content: "";
  position: absolute;
  top: 0;
  width: 98%;
  left: 1%;
  height: 0;
  border-top: 0.01rem solid #f1f1f1;
}
.airWayOrderInfoPage .InfoBox .passengerInfo .section2 {
  display: flex;
  position: relative;
  padding-top: 0.2rem;
}
.airWayOrderInfoPage .InfoBox .passengerInfo .section2 .label {
  flex: 0.15;
  font-size: 0.37rem;
  color: #333;
  display: inline-block;
  line-height: 0.49rem;
}
.airWayOrderInfoPage .InfoBox .passengerInfo .section2 .infobox {
  flex: 0.85;
}
.airWayOrderInfoPage .InfoBox .passengerInfo .section2 .infobox .info {
  overflow: hidden;
}
.airWayOrderInfoPage .InfoBox .passengerInfo .section2 .infobox .info li {
  width: 50%;
  text-align: left;
  float: left;
  font-size: 0.32rem;
  line-height: 0.49rem;
  height: 100%;
  margin-bottom: 0.1rem;
}
.airWayOrderInfoPage .InfoBox .passengerInfo .section2:after {
  content: "";
  position: absolute;
  top: 0;
  width: 98%;
  left: 1%;
  height: 0;
  border-top: 0.01rem solid #f1f1f1;
}
.airWayOrderInfoPage .InfoBox .payby {
  position: relative;
  background: #fff;
  margin-top: 0.266666rem;
  color: #444;
}
.airWayOrderInfoPage .InfoBox .payby header {
  background: #f9f9f9;
  padding: 0.18rem 0.38rem;
  font-size: 0.4rem;
}
.airWayOrderInfoPage .InfoBox .payby header span:first-child {
  border-left-width: 0.1rem;
  border-left-style: solid;
  border-left-color: #4993fa;
  padding-left: 0.35rem;
  height: 0.64rem;
  line-height: 0.64rem;
}
.airWayOrderInfoPage .InfoBox .payby .content {
  padding: 0.3rem;
}
.airWayOrderInfoPage .InfoBox .payby .content p {
  display: block;
  line-height: 0.64rem;
  font-size: 0.34rem;
  color: #444;
}
.airWayOrderInfoPage .InfoBox .payby .content p span {
  float: right;
  color: #ff3900;
}
.airWayOrderInfoPage .passengerInfoPAGE {
  z-index: 10000;
  position: fixed;
  top: 0;
  bottom: 0;
  width: 100%;
  -webkit-transform: translate3d(100%, 0, 0);
  -moz-transform: translate3d(100%, 0, 0);
  transform: translate3d(100%, 0, 0);
  -webkit-transition: transform 250ms ease-in-out;
  -webkit-transition: -webkit-transform 250ms ease-in-out;
  transition: -webkit-transform 250ms ease-in-out;
  -o-transition: -o-transform 250ms ease-in-out;
  -moz-transition: transform 250ms ease-in-out, -moz-transform 250ms ease-in-out;
  transition: transform 250ms ease-in-out;
  transition: transform 250ms ease-in-out, -webkit-transform 250ms ease-in-out, -moz-transform 250ms ease-in-out, -o-transform 250ms ease-in-out;
  background-color: #f2f2f2;
}
.airWayOrderInfoPage .passengerInfoPAGE.entry {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}
.airWayOrderInfoPage .passengerInfoPAGE .contentBox {
  margin-top: 1.137777rem;
  overflow-y: scroll;
  height: 100%;
  position: relative;
}
.airWayOrderInfoPage .passengerInfoPAGE .contentBox .topban {
  position: absolute;
  left: 0;
  top: 0;
  height: 4rem;
  width: 100%;
  background-color: #4993fa;
}
.airWayOrderInfoPage .passengerInfoPAGE .contentBox .plistsbox {
  position: relative;
  padding: 0.3333rem 0.2667rem;
}
.airWayOrderInfoPage .passengerInfoPAGE .contentBox .plistsbox .plists {
  padding-bottom: 2rem;
}
.airWayOrderInfoPage .passengerInfoPAGE .contentBox .plistsbox .plists .pitem {
  background-color: #fff;
  border-radius: 0.1rem;
  padding: 0.2rem 0.3rem;
  font-size: 0.37rem;
}
.airWayOrderInfoPage .passengerInfoPAGE .contentBox .plistsbox .plists .pitem:not(:last-child) {
  margin-bottom: 0.1rem;
}
.airWayOrderInfoPage .passengerInfoPAGE .contentBox .plistsbox .plists .pitem .ptitle {
  line-height: 0.6rem;
  margin-bottom: 0.1rem;
}
.airWayOrderInfoPage .passengerInfoPAGE .contentBox .plistsbox .plists .pitem .ptitle .pname {
  color: #333;
  font-weight: 800;
  font-size: 0.4rem;
}
.airWayOrderInfoPage .passengerInfoPAGE .contentBox .plistsbox .plists .pitem .ptitle .ptype {
  color: #4993fa;
  margin-left: 0.3rem;
  font-size: 0.37rem;
}
.airWayOrderInfoPage .passengerInfoPAGE .contentBox .plistsbox .plists .pitem .linebox::after {
  content: "";
  display: block;
  clear: both;
  height: 0;
  visibility: hidden;
}
.airWayOrderInfoPage .passengerInfoPAGE .contentBox .plistsbox .plists .pitem .linebox .pline {
  height: 0.8rem;
  min-width: 50%;
  float: left;
  line-height: 0.8rem;
  min-height: 0.8rem;
}
.airWayOrderInfoPage .passengerInfoPAGE .contentBox .plistsbox .plists .pitem .linebox .pline label {
  display: inline-block;
  min-width: 1.4rem;
  color: #999;
}
.airWayOrderInfoPage .passengerInfoPAGE .contentBox .plistsbox .plists .pitem .linebox .pline span {
  color: #333;
}
.airWayOrderInfoPage .passengerInfoPAGE .contentBox .plistsbox .plists .pitem .plinelast {
  overflow: hidden;
}
.airWayOrderInfoPage .passengerInfoPAGE .contentBox .plistsbox .plists .pitem .plinelast .numtag {
  float: left;
  width: 18%;
  color: #999;
  line-height: 0.8rem;
}
.airWayOrderInfoPage .passengerInfoPAGE .contentBox .plistsbox .plists .pitem .plinelast .numsbox {
  float: left;
  width: 82%;
}
.airWayOrderInfoPage .passengerInfoPAGE .contentBox .plistsbox .plists .pitem .plinelast .numsbox span {
  color: #4993fa;
  margin-right: 0.3rem;
  line-height: 0.8rem;
  vertical-align: top;
}
.airWayAddPassengerPage {
  position: relative;
  min-height: auto;
  padding-bottom: 2rem;
}
.airWayAddPassengerPage .passengerInfoInputs .itembox {
  background-color: #fff;
  padding: 0.2rem 0.4rem;
}
.airWayAddPassengerPage .passengerInfoInputs .itembox .itemline {
  height: 1.1rem;
  line-height: 1.1rem;
  font-size: 0.37rem;
}
.airWayAddPassengerPage .passengerInfoInputs .itembox .itemline label {
  color: #333;
  display: inline-block;
  width: 2.4rem;
}
.airWayAddPassengerPage .passengerInfoInputs .itembox .itemline .radiobox {
  display: inline-block;
  width: 1.5rem;
  height: 0.8rem;
  border: 0.01rem solid #ccc;
  border-radius: 0.09rem;
  vertical-align: middle;
  line-height: 0.8rem;
  text-align: right;
  color: #ccc;
  margin-right: 0.19rem;
}
.airWayAddPassengerPage .passengerInfoInputs .itembox .itemline .menradio {
  background: url(../img/airway/Men.png) left center / 50% no-repeat;
}
.airWayAddPassengerPage .passengerInfoInputs .itembox .itemline .menradio.active {
  background: url(../img/airway/Men_active.png) left center / 50% no-repeat;
  color: #4993fa;
  border-color: #4993fa;
}
.airWayAddPassengerPage .passengerInfoInputs .itembox .itemline .womenradio {
  background: url(../img/airway/women.png) left center / 50% no-repeat;
}
.airWayAddPassengerPage .passengerInfoInputs .itembox .itemline .womenradio.active {
  background: url(../img/airway/women_active.png) left center / 50% no-repeat;
  color: #4993fa;
  border-color: #4993fa;
}
.airWayAddPassengerPage .passengerInfoInputs .itembox .itemline:not(:first-child) {
  border-top: 0.01rem solid #f1f1f1;
}
.airWayAddPassengerPage .nationandArea {
  z-index: 10000;
  position: fixed;
  top: 0;
  bottom: 0;
  width: 100%;
  -webkit-transform: translate3d(100%, 0, 0);
  -moz-transform: translate3d(100%, 0, 0);
  transform: translate3d(100%, 0, 0);
  -webkit-transition: transform 250ms ease-in-out;
  -webkit-transition: -webkit-transform 250ms ease-in-out;
  transition: -webkit-transform 250ms ease-in-out;
  -o-transition: -o-transform 250ms ease-in-out;
  -moz-transition: transform 250ms ease-in-out, -moz-transform 250ms ease-in-out;
  transition: transform 250ms ease-in-out;
  transition: transform 250ms ease-in-out, -webkit-transform 250ms ease-in-out, -moz-transform 250ms ease-in-out, -o-transform 250ms ease-in-out;
}
.airWayAddPassengerPage .nationandArea.entry {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}
.airWayAddPassengerPage .topfixed {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000000;
  width: 100%;
}
.airWayAddPassengerPage .topfixed .input_section {
  background: #4993fa;
  padding: 0.1rem 0.4rem;
  overflow: hidden;
}
.airWayAddPassengerPage .nationandArea  .topfixed .input_section{
  margin-top: 1.18rem;
}
.airWayAddPassengerPage .topfixed .input_section .input_text {
  height: 1.1rem;
  font-size: 0.32rem;
  float: left;
  display: block;
  border-radius: 0.08rem;
  padding-left: 0.3rem;
  width: 88%;
}
.airWayAddPassengerPage .topfixed .input_section .text_concel {
  width: 10%;
  float: right;
  font-size: 0.373333rem;
  color: #fff;
  line-height: 1.173333rem;
  text-align: center;
}
.airWayAddPassengerPage .topfixed .search_suggest_list {
  position: fixed;
  top: 2.51rem;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 999999;
  display: none;
  overflow-y: scroll;
}
.airWayAddPassengerPage .topfixed .search_suggest_list ul {
  background: #fff;
}
.airWayAddPassengerPage .topfixed .search_suggest_list ul li {
  line-height: 1rem;
  padding: 0 0.4rem;
  font-size: 0.32rem;
  border-bottom: 1px solid #e1e1e1;
}
.airWayAddPassengerPage .topfixed .search_suggest_list ul li span {
  font-size: 0.3rem;
}
.airWayAddPassengerPage .country-wrapper {
  position: relative;
  -webkit-overflow-scrolling: touch;
  position: fixed;
  top: 2.5rem;
  bottom: 0;
  width: 100%;
  overflow-y: auto;
}
.airWayAddPassengerPage .country-wrapper .clist li {
  background-color: #fff;
  padding: 0 1.2rem 0 0.36rem;
  height: 0.8rem;
  line-height: 0.8rem;
}
.airWayAddPassengerPage .country-wrapper .clist li span {
  font-size: 0.3rem;
}
.airWayAddPassengerPage .country-wrapper .clist .bottomline {
  height: 0.8rem;
  text-align: center;
  color: #999;
  font-size: 0.3rem;
  line-height: 0.8rem;
  background-color: #fff;
}
.fliSkinToDark .airWayResultsPage .loadingBox .img_loading {
  width: 1.3333rem;
  height: 1.3333rem;
  background-image: url(../img/colorfulSkin/dark/loading.gif);
}
.fliSkinToDark .airWayResultsPage .dateBox header {
  background: #4da990;
}
.fliSkinToDark .airWayResultsPage .result .item .left .line1 .arrow .arriveBybox {
  background-image: url(../img/colorfulSkin/dark/recharge/train_arrow.png);
}
.fliSkinToDark .airWayResultsPage .result .item .left .line1 .arrow .arriveBybox span {
  color: #4da990;
}
.fliSkinToDark .airWayResultsPage .result .item .right .free {
  color: #4da990;
}
.fliSkinToDark .airWayResultsPage .result .item .airway .share {
  color: #4da990;
}
.fliSkinToDark .airWayResultsPage .result .filterBox .contentBox .arrivesoon .Switch.On {
  background-color: #4da990;
}
.fliSkinToDark .airWayResultsPage .result footer a.active p {
  color: #4da990;
}
.fliSkinToDark .airWayResultsPage .result footer a.active .icon_filter {
  background-image: url(../img/colorfulSkin/dark/recharge/icon_filter_theme.png);
}
.fliSkinToDark .airWayResultsPage .result footer a.active .icon_time {
  background-image: url(../img/colorfulSkin/dark/recharge/icon_time_active.png);
}
.fliSkinToDark .airWayResultsPage .result footer a.active .icon_money {
  background-image: url(../img/colorfulSkin/dark/recharge/icon_dollar_active.png);
}
.fliSkinToDark .airwayIndexPage .fli_btn_long.fli_btn_primary {
  background: #4da990;
}
.fliSkinToDark .airwayIndexPage .toptag {
  height: 2rem;
  background: #3C4451;
  border-bottom-left-radius: 0.24rem;
  border-bottom-right-radius: 0.24rem;
}
.fliSkinToDark .airwayIndexPage .toptag img {
  display: none;
}
.fliSkinToDark .airwayIndexPage #airWayForm .commitBox .notice {
  color: #4da990;
}
.fliSkinToDark .airWaySelectCitypage .fixed_header .input_section {
  background: #4da990;
}
.fliSkinToDark .airWaySelectCitypage .fixed_header .tabsection .item.active {
  border-bottom-color: #4da990;
}
.fliSkinToDark .airWayItemInfopage .topban {
  height: 2rem;
  background: #4da990;
  margin-bottom: -1rem;
}
.fliSkinToDark .airWayItemInfopage .InfoBox .lists .item:first-child {
  background-image: url(../img/colorfulSkin/dark/airway/topbg.png);
}
.fliSkinToDark .airWayItemInfopage .InfoBox .lists .item .date {
  color: #4da990;
}
.fliSkinToDark .airWayItemInfopage .InfoBox .lists .item .line .from p:first-child,
.fliSkinToDark .airWayItemInfopage .InfoBox .lists .item .line .to p:first-child {
  color: #4da990;
}
.fliSkinToDark .airWayItemInfopage .InfoBox .lists .item .line .arrow p:first-child .plusflag {
  color: #4da990;
}
.fliSkinToDark .airWayItemInfopage .InfoBox .lists .item .line .arrow p:nth-child(2) {
  background-image: url(../img/colorfulSkin/dark/airway/recharge/train_arrow.png);
}
.fliSkinToDark .airWayItemInfopage .InfoBox .lists .acrossCity {
  color: #4da990;
}
.fliSkinToDark .airWayItemInfopage .InfoBox .fly_list li .section1 p:nth-child(3) span.tgq {
  color: #4da990;
}
.fliSkinToDark .airWayItemInfopage .InfoBox .fly_list li .section2 button {
  background: #4da990;
}
.fliSkinToDark .airWayCompleteInfo .topban {
  height: 2rem;
  background-color: #4da990;
  margin-bottom: -1rem;
}
.fliSkinToDark .airWayCompleteInfo .InfoBox .lists .item:first-child {
  background-image: url(../img/colorfulSkin/dark//airway/topbg.png);
}
.fliSkinToDark .airWayCompleteInfo .InfoBox .lists .item .date {
  color: #4da990;
}
.fliSkinToDark .airWayCompleteInfo .InfoBox .lists .item .line .from p:first-child,
.fliSkinToDark .airWayCompleteInfo .InfoBox .lists .item .line .to p:first-child {
  color: #4da990;
}
.fliSkinToDark .airWayCompleteInfo .InfoBox .lists .item .line .arrow p:first-child .plusflag {
  color: #4da990;
}
.fliSkinToDark .airWayCompleteInfo .InfoBox .lists .item .line .arrow p:nth-child(2) {
  background-image: url(../img/colorfulSkin/dark/recharge/train_arrow.png);
}
.fliSkinToDark .airWayCompleteInfo .InfoBox .lists .acrossCity {
  color: #4da990;
}
.fliSkinToDark .airWayCompleteInfo .InfoBox .airplane_common .section_1 .line2 {
  color: #4da990;
}
.fliSkinToDark .airWayCompleteInfo .InfoBox .airplane_common .section_2 .icon_add {
  background-image: url(../img/colorfulSkin/dark/recharge/icon_plus.png);
}
.fliSkinToDark .airWayCompleteInfo .InfoBox .airplane_common .section_3 .infoitems .item:first-child {
  color: #4da990;
}
.fliSkinToDark .airWayCompleteInfo .InfoBox .airplane_common .section_3 .infoitems .icon_del {
  background-image: url(../img/colorfulSkin/dark/recharge/icon_minus.png);
}
.fliSkinToDark .airWayCompleteInfo .fixed_bottom_settlement .btn_submit {
  background: #4da990;
  background: -webkit-linear-gradient(left, #4da990, #10b465);
  background: -o-linear-gradient(left, #4da990, #10b465);
  background: -moz-linear-gradient(left, #4da990, #10b465);
  background: linear-gradient(left, #4da990, #10b465);
}
.fliSkinToDark .airWayCompleteInfo .areaCode .topfixed .input_section {
  background: #4da990;
}
.fliSkinToDark .airWayCompleteInfo .areaCode .index-bar {
  color: #4da990;
}
.fliSkinToDark .airWayAffrimOrderPage .topban {
  height: 2rem;
  background-color: #4da990;
  margin-bottom: -1rem;
}
.fliSkinToDark .airWayAffrimOrderPage .InfoBox .lists .item:first-child {
  background-image: url(../img/colorfulSkin/dark/airway/topbg.png);
}
.fliSkinToDark .airWayAffrimOrderPage .InfoBox .lists .item .date {
  color: #4da990;
}
.fliSkinToDark .airWayAffrimOrderPage .InfoBox .lists .item .line .from p:first-child,
.fliSkinToDark .airWayAffrimOrderPage .InfoBox .lists .item .line .to p:first-child {
  color: #4da990;
}
.fliSkinToDark .airWayAffrimOrderPage .InfoBox .lists .item .line .arrow p:first-child .plusflag {
  color: #4da990;
}
.fliSkinToDark .airWayAffrimOrderPage .InfoBox .lists .item .line .arrow p:nth-child(2) {
  background-image: url(../img/colorfulSkin/dark/recharge/train_arrow.png);
}
.fliSkinToDark .airWayAffrimOrderPage .InfoBox .lists .acrossCity {
  color: #4da990;
}
.fliSkinToDark .airWayOrderInfoPage .infoTitle .tripinfo {
  color: #4da990;
}
.fliSkinToDark .airWayOrderInfoPage .topban {
  height: 2rem;
  background-color: #4da990;
}
.fliSkinToDark .airWayOrderInfoPage .topban + .InfoBox {
  margin-top: -1rem;
}
.fliSkinToDark .airWayOrderInfoPage .InfoBox .lists .item:first-child {
  background-image: url(../img/colorfulSkin/dark/airway/topbg.png);
}
.fliSkinToDark .airWayOrderInfoPage .InfoBox .lists .item .date {
  color: #4da990;
}
.fliSkinToDark .airWayOrderInfoPage .InfoBox .lists .item .line .from p:first-child,
.fliSkinToDark .airWayOrderInfoPage .InfoBox .lists .item .line .to p:first-child {
  color: #4da990;
}
.fliSkinToDark .airWayOrderInfoPage .InfoBox .lists .item .line .arrow p:first-child .plusflag {
  color: #4da990;
}
.fliSkinToDark .airWayOrderInfoPage .InfoBox .lists .item .line .arrow p:nth-child(2) {
  background-image: url(../img/colorfulSkin/dark/recharge/train_arrow.png);
}
.fliSkinToDark .airWayOrderInfoPage .InfoBox .lists .acrossCity {
  color: #4da990;
}
.fliSkinToDark .airWayOrderInfoPage .InfoBox .passengerInfo .section0 {
  color: #4da990;
}
.fliSkinToDark .airWayOrderInfoPage .InfoBox .passengerInfo .section1 .icon_Info {
  background-image: url(../img/colorfulSkin/dark/airway/passengerInfo.png);
}
.fliSkinToDark .airWayOrderInfoPage .InfoBox .payby header span:first-child {
  border-left-color: #4da990;
}
.fliSkinToDark .airWayOrderInfoPage .passengerInfoPAGE .contentBox .topban {
  height: 2rem;
  background-color: #4da990;
  margin-bottom: -1rem;
}
.fliSkinToDark .airWayOrderInfoPage .passengerInfoPAGE .contentBox .plistsbox .plists .pitem .ptitle .ptype {
  color: #4da990;
}
.fliSkinToDark .airWayOrderInfoPage .passengerInfoPAGE .contentBox .plistsbox .plists .pitem .plinelast .numsbox span {
  color: #4da990;
}
.fliSkinToDark .airWayAddPassengerPage .passengerInfoInputs .itembox .itemline .menradio.active {
  background-image: url(../img/colorfulSkin/dark/airway/Men_active.png);
  color: #4da990;
  border-color: #4da990;
}
.fliSkinToDark .airWayAddPassengerPage .passengerInfoInputs .itembox .itemline .womenradio.active {
  background-image: url(../img/colorfulSkin/dark/airway/women_active.png);
  color: #4da990;
  border-color: #4da990;
}
.fliSkinToDark .airWayAddPassengerPage .topfixed .input_section {
  background: #4da990;
}
