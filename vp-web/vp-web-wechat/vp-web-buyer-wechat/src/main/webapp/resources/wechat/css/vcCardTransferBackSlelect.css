@charset "utf-8";
body.vcCardTransferBackSlelectPage{
  padding-top: 0;
}
.vcCardTransferBackSlelectPage .cardList ul,.purchasePage .purchaserList ul{
    margin-top: 0.3467rem;
}
.vcCardTransferBackSlelectPage .cardList ul li{
    position: relative;
    width: calc(100% - 0.5866rem);
    margin: 0 0.2933rem 0.1867rem 0.2933rem;
    background: -webkit-linear-gradient(to bottom,rgb(255,255,255),rgb(212,230,249));
    background: linear-gradient(to bottom,rgb(255,255,255),rgb(212,230,249));
    padding: 0.4267rem 0.64rem 0.2rem 0.7467rem;
    color: rgb(51,51,51);
    font-size: 0.3733rem;
    border-radius: 0.16rem;
}

.purchasePage .purchaserList ul li{
    width: calc(100% - 0.5866rem);
    margin: 0 0.2933rem 0.1333rem 0.2933rem;
    background: #ffffff;
    color: rgb(51,51,51);
    font-size: 0.3733rem;
    padding: 0.4533rem 0.5067rem;
}
.purchasePage .purchaserList ul li img{
    width: 0.72rem;
    margin-right: 0.4533rem;
}
.purchasePage .purchaserList ul li p.setGray{
    color: rgb(153,153,153);
}
.vcCardTransferBackSlelectPage .addBank{
    margin: 0.8rem 0.2933rem 0;
    width: calc(100% - 0.5866rem);
    border-radius: 0.16rem;
}
.vcCardTransferBackSlelectPage .cardList ul li .labelText{
    display: inline-block;
    width: 1.6rem;
    text-align: justify;
    text-align-last: justify;
    text-align-all: justify;
    word-break: break-all;
    margin-right: 0.05rem;
    height: 0.8267rem;
    line-height: 0.8267rem;
    white-space: normal;
    overflow: hidden;
}
.vcCardTransferBackSlelectPage .cardList ul li .labelText span{
    display: inline-block;
    width: 100%;
}
.vcCardTransferBackSlelectPage .cardList ul li .brankName{
    color: rgb(51,51,51);
    font-size: 0.3733rem;
    font-weight: bold;
}
.vcCardTransferBackSlelectPage .cardList ul li .edit{
    color: rgb(145,198,255);
    font-size: 0.32rem;
}
.vcCardTransferBackSlelectPage .cardList ul li p:nth-child(2){
    margin-top: 0.12rem;
}
.vcCardTransferBackSlelectPage .cardList ul li img{
    width: 0.4267rem;
}
.vcCardTransferBackSlelectPage .editBtn{
  position: absolute;
  bottom: 0;
  right: 0;
}
.vcCardTransferBackSlelectPage .van-nav-bar .van-icon{
    color: rgb(51,51,51);
    font-weight: bold;
}
.vcCardTransferBackSlelectPage .van-nav-bar__title {
    color: rgb(51,51,51);
    font-size: 0.48rem;
    font-weight: bold;
    /*letter-spacing: 0.1rem;*/
}
.vcCardTransferBankeditPage{
    background: #ffffff;
}
.vcCardTransferBankeditPage .public_form_section{
    margin-top: 0.32rem;
}
.vcCardTransferBankeditPage .public_top_header .return_back:before {
    border-color: rgb(51,51,51);
    height: 0.26rem;
    width: 0.26rem;
    border-width: 0.05rem 0.05rem 0 0;
}
.vcCardTransferBankeditPage .public_form_section  .item{
    border-top: 1px solid #e8e8e8;
}
.vcCardTransferBankeditPage .public_form_section  .item.lastItem{
    border-bottom: 1px solid #e8e8e8;
}
.vcCardTransferBankeditPage .public_form_section .item label{
    width: 1.6rem;
    text-align: justify;
    text-align-last: justify;
    text-align-all: justify;
    word-break: break-all;
}
.vcCardTransferBankeditPage  .layui-m-anim-scale .layui-m-layercont{
    padding: 0.12rem 0.8rem 0.8rem;
}
.vcCardTransferBankeditPage  .layui-m-anim-scale h3{
    color: #333;
    font-size: .48rem;
    font-weight: 700;
}
.vcCardTransferBankeditPage  .layui-m-anim-scale .layui-m-layercont .unbound_agreement_box .content{
    line-height: .6rem;
    color: #666;
    font-size: .32rem;
    width: 100%;
    text-align: center;
}
.vcCardTransferBankeditPage  .layui-m-anim-scale .layui-m-layercont .unbound_agreement_box .optBtn{
    width: 100%;
    height: 1.173rem;
    margin-top: 0.32rem;
}
.vcCardTransferBankeditPage  .layui-m-anim-scale .layui-m-layercont .unbound_agreement_box .optBtn .cancle,.vcCardTransferBankeditPage  .layui-m-anim-scale .layui-m-layercont .unbound_agreement_box .optBtn .confirm{
    display: inline-block;
    height: 0.8rem;
    line-height: .8rem;
    font-size: .32rem;
    border-radius: 0.4rem;
    border: 0.0266667rem solid #999;
    color: #333;
    width: 2.773rem;
    margin-right: 0.533333rem;
    background: #ffffff;
}
.vcCardTransferBankeditPage  .layui-m-anim-scale .layui-m-layercont .unbound_agreement_box .optBtn .confirm{
    color: #fff;
    background: #148ff0;
    border-color: #148ff0;
}
.vcCardTransferBankeditPage .public_form_section .item label.fieldError{
    width: 100%;
    text-align: left;
    text-align-last: left;
    text-align-all: left;
}
.vcCardTransferBankeditPage .public_form_section .item .inputs{
    margin-left: 2rem;
}
.vcCardTransferBankeditPage .fli_link_line:after{
    right: 0;
}
.vcCardTransferBankeditPage .public_top_header {
    background: #ffffff;
    box-shadow: none;
    color: rgb(51,51,51);
    font-size: 0.48rem;
    font-weight: bold;
}
.vcCardTransferBankeditPage .creditCardPages{
    padding: 0 0.48rem 0;
}
.vcCardTransferBankeditPage .public_top_header #unbound{
    position: absolute;
    right: 0.32rem;
    font-weight: normal;
    font-size: 0.373333rem;
    color: #4993FA;
}
.vcCardTransferBankeditPage .public_form_section .btn_submit_box{
    padding: 0;
}
.vcCardTransferBankeditPage .public_form_section .btn_cornor_long{
    width: 100%;
    border-radius: 0.16rem;
    height: 1.1733rem;
    font-size: 0.3733rem;
}