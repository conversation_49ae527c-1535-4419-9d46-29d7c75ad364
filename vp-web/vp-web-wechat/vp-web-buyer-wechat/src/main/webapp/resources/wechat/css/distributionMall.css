.distributionMallPage{
  padding-top: 0;
  background: #f5f5f5;

}
.van-nav-bar {
  height: 1.18rem;
  line-height: 1.18rem;
  background-color: #ffffff;
  z-index: 10;
}
.van-hairline--bottom::after {
  border-bottom-width: 0;
}
.van-nav-bar__title {
  color: rgb(51,51,51);
  font-size: 0.48rem;
  font-weight: bold;
  letter-spacing: 0.1rem;
}
.distributionMallPage p{
  font-size: 0.37333333rem;
}
.distributionMallPage h2{
  font-size: 0.37333333rem;
}
.distributionMallPage .title{
  color: rgb(51,51,51);
  margin-top: 1.28rem;
}
.distributionMallPage .content{
  padding: 0 0.773333333rem ;
}
.textAlign_right{
  text-align: right;
}
.van-hairline--bottom::after {
  border-bottom-width: 0;
}
.van-nav-bar__title {
  color: rgb(51,51,51);
  font-size: 0.48rem;
  font-weight: bold;
  letter-spacing: 0.1rem;
}
.van-icon::before {
  font-size: 0.6rem;
  color: rgb(51,51,51);
}
.distributionMallPage .swiper-container#swiperbanner {
  position: relative;
  z-index: 9;
  margin: 0.32rem 0;
}
.distributionMallPage .swiper-container#swiperbanner a {
  display: block;
}
.distributionMallPage .swiper-container#swiperbanner a img {
  display: block;
  width: 9.3867rem;
  height: 2.8rem;
  margin: 0 auto;
  border-radius: 0.16rem;
}
.distributionMallPage .data-item{
  color: rgb(51,51,51);
  font-family: SourceHanSansSC-Bold;
  margin: 0.32rem;
  padding: 0.10667rem 0.32rem   0.16rem;
  background: #ffffff;
  border-radius: 0.16rem;
}
.distributionMallPage .data-item header{
  font-size: 0.3733rem;
  position: relative;

  font-weight: bold;
  padding:  0.48rem 0 0.48rem 0.4rem;
}
.distributionMallPage .data-item header:after{
  content: '';
  width: 0.2133rem;
  height: 0.46rem;
  border-radius: 0.2rem;
  background: #fec4a3;
  position: absolute;
  left: 0;
  top: 50%;
  margin-top: -0.23rem;
}
.distributionMallPage .data-item .card-item{
  background: #fdf4ef;
  border-radius: 0.16rem;
  width: calc(50% - 0.16rem);
  float: left;
  margin-bottom: 0.32rem;
  padding: 0.48rem 0.32rem 0.32rem 0.48rem;
  min-height: 2.1rem;
}
.distributionMallPage .data-item .card-item:nth-of-type(2n){
  margin-left: 0.32rem;
}
.distributionMallPage .data-item .card-item p:first-child{
  font-size: 0.48rem;
  font-weight: bold;
  margin-bottom: 0.2667rem;
}
.distributionMallPage .data-item .card-item p:first-child span{
  font-size: 0.32rem;
  margin-right: 0.03rem;
}
.distributionMallPage .data-item .card-item p:last-child{
  font-size: 0.32rem;
  color: rgb(102,102,102);
}
.distributionMallPage .data-item .card-item p:last-child .row-icon::before{
  color: #c9c9c9;
  font-size: 0.42rem;
}
.distributionMallPage .data-item .tool-item {
  width: 1.6rem;
}
.distributionMallPage .data-item .tool-item img{
  width: 1.067rem;
  height: 1.067rem;
}
.distributionMallPage .data-item .tool-item p{
  font-family: SourceHanSansSC-Regular;
  font-size: 0.32rem;
  color: rgb(102,102,102);
  margin : 0.2667rem 0 0.32rem;
}
.sharePage .content{
  letter-spacing: 0.02rem;
  margin: 0.32rem 0.32rem 0;
  padding: 0.533rem;
  background: #ffffff;
  border-radius: 0.16rem;
}
.sharePage .content .swiper-slide{
  display: block;
}
.sharePage .content .zhImg{
  width: 100%;
  height: 100%;
}
.sharePage .content .product-img{
  width: 100%;
}
.sharePage .content .describe{
  color: rgb(51,51,51);
  font-size: 0.3733rem;
  font-family: 'SourceHanSansSC-Medium';
  margin-top: 0.3733rem;
  font-weight: bold;
}
.sharePage .content .describe .content-left{
  margin-right: 0.32rem;
  position: relative;
}
.sharePage .content .describe .content-left p:first-child{
  /*padding: 0.12rem 0.12rem 0 0;*/
}
.sharePage .content .describe .content-left p:last-child{
  margin-top: 0.24rem;
  position: absolute;
  bottom: 0.19rem;
  white-space: nowrap;
}
.sharePage .content .describe .content-left p:last-child .price{
  color: rgb(254,42,47);
}
.sharePage .content .describe .content-left p:last-child .price span{
  font-size: 0.8rem;
}
.sharePage .content .describe .content-right .code-img{
  width: 2.3467rem;
}
.sharePage .content .describe .content-right span{
  color: rgb(153,153,153);
  font-size: 0.2667rem;
  display: block;
  width: 100%;
  text-align: center;
  font-weight: normal;
  white-space: nowrap;
}
.sharePage .content .tip{
  width: 100%;
  text-align: center;
  color: rgb(182,182,182);
  font-family: SourceHanSansSC-Medium;
  font-size: 0.3733rem;
}
.sharePage .content .share-content{
  padding-bottom: 0.5333rem;
  border-radius: 0.16rem;
}
.sharePage .share-btn{
  height: 1.2rem;
  margin: 0.5867rem 0.32rem;
  display: block;
  width: calc(100% - 0.64rem);
  border: none;
  background: rgb(254,42,47);
  color: #ffffff;
  border-radius: 0.6rem;
}
.promotionOrderPage .header-nav{
  padding: 0.48rem 0.5867rem;
  background: #F2F2F2;
}
.promotionOrderPage .header-nav li{
  color: rgb(102,102,102);
  font-size: 0.4267rem;
  font-family: SourceHanSansSC-Medium;
}
.promotionOrderPage .header-nav li.active{
  color: rgb(254,42,47);
  font-family: SourceHanSansSC-Bold;
  font-weight: bold;
}
.promotionOrderPage .data_list{
  padding: 0 0.32rem;

}
.promotionOrderPage .data_list .content-top{
  font-size: 0.32rem;
  font-family: SourceHanSansSC-Medium;
  color: rgb(0,0,0);
  padding-bottom: 0.48rem;
}
.promotionOrderPage .data_list .content-top .unpaid{
  color: rgb(86,146,255);
}
.promotionOrderPage .data_list .content-top .paid{
  color: rgb(255,146,86);
}
.promotionOrderPage .data_list .content-top .completed{
  color: rgb(255,146,86);
}
.promotionOrderPage .data_list .content-top .invalid{
  color: rgb(170,165,163);
}
.promotionOrderPage .data_list .recodList li.recodListItem{
  background: #ffffff;
  border-radius: 0.16rem;
  margin-bottom: 0.32rem;
  padding: 0.48rem 0.5867rem;
}
.promotionOrderPage .data_list .recodList li img{
  width: 2.3733rem;
  height: 2.3733rem;
  margin-right: 0.267rem;
}
.promotionOrderPage .data_list .recodList .content-btow>div{
  position: relative;
  width: 100%;
}
.promotionOrderPage .data_list .recodList .content-btow ul li p:first-child span{
  font-size: 0.267rem;
  text-align: center;
}
.promotionOrderPage .data_list .recodList .content-btow ul li p.yj{
  color: rgb(254,42,47);
}
.promotionOrderPage .data_list .recodList .content-btow ul li p:first-child{
  font-size: 0.42rem;
  text-align: center;
}
.promotionOrderPage .data_list .recodList .content-btow ul li p:last-child{
  font-size: 0.32rem;
  color: rgb(153,153,153);
  text-align: center;
}
.promotionOrderPage .data_list .recodList .content-btow .product-name{
  font-size: 0.3733rem;
  font-family: SourceHanSansSC-Medium;
  text-align: justify;
  word-break: break-all;

}
.wk3{
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  line-height: 0.48rem;
  height: 1.44rem;
  /*text-align: justify;*/
}
.promotionOrderPage .data_list .recodList .content-btow .data-exhibition{
  position: absolute;
  bottom: 0;
  width: 100%;
}
.promotionOrderPage .data_list .recodList .content-btow .data-exhibition li p:last-child{
  margin-top: 0.16rem;
}