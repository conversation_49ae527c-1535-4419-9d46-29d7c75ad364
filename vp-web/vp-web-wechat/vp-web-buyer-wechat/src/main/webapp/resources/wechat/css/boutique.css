@charset "utf-8";
.boutique-shop-page .public_top_header .search_section{
  margin-right: 0.32rem;
  margin-left: 3.0867rem;
  border-radius: 0.44rem;
}
.boutique-shop-page .banner_section .swiper-container-horizontal>.swiper-pagination{
  text-align: center;
}
.boutique-shop-page .public_top_header .shop-logo{
  height: 1.0133rem;
  width: auto;
  position: absolute;
  top: 0.08335rem;
  left: 0.6933rem;
}

.boutique-shop-page .jdindexPage .banner_section{
  width: 100%;
  height: 5.26rem;
  box-sizing: border-box;
  padding: 1.5rem 0.32rem 0.32rem;
}
.boutique-shop-page .public_top_header{
  box-shadow:none;
  margin-top: 0;
}
.boutique-shop-page .public_top_header.transparent{
  margin-top: 0.1rem;
}
.boutique-shop-page .jdindexPage .headerContent{
  background: #faeef0;
  background: -webkit-linear-gradient(to bottom,#fde9ed,#f6f3f4);
  background: linear-gradient(to bottom,#fde9ed,#f6f3f4);
}
.jdindexPage.modify .btn_search{
  line-height: 0.88rem;
}
.jdindexPage .banner_section .swiper-container {
  height: 3.44rem;
  border-radius: 0.13rem;
}
.jdindexPage .banner_section img {
  display: block;
  width: 100%;
  height:3.44rem;
}
.jdindexPage .banner_section .swiper-container a.swiper-slide {
  display: block;
  width: 100%;
  height: 3.44rem;
  overflow: hidden;
}
.jdindexPage .shop_nav_section {
  background: #fff;
  border-radius: 0.13rem;
  margin: 0 0.32rem 0.32rem;
}
.jdindexPage .advertising-space{
  margin: 0 0.32rem 0.32rem;
}
.jdindexPage .selected-goods-swipe .van-swipe-item{
  height: 2.6367rem;
  background: #ffffff;
}
.jdindexPage .selected-goods-swipe:first-child{
  margin-bottom: 0.1936rem;
}
.jdindexPage .selected-goods-swipe{
  margin-left: 0.09rem;
  border-radius: 0.16rem;
}
.advertising-space .time-limit-swipe{
  margin-right: 0.09rem;
  border-radius: 0.16rem;
}
.advertising-space .time-limit-swipe .van-swipe-item{
  height:5.467rem;
  /*background: #fdeef1;*/
  /*background: -webkit-linear-gradient(to bottom,#fce2e6,#ffffff);*/
  /*background: linear-gradient(to bottom,#fce2e6,#ffffff);*/
}
.selected-goods-swipe .goods-item {
 height: 100%;
}
.selected-goods-swipe .goods-item .title-tag{
  height: 100%;
  padding: 0.533rem 0 0 0.533rem;
}
.selected-goods-swipe .goods-item .title-tag p:first-child{
  font-family: "PingFangSC-Semibold";
  font-size: 0.37rem;
  color: rgb(34,34,34);
  font-weight: bold;
  margin-bottom: 0.05rem;
}
.selected-goods-swipe .goods-item .title-tag p:last-child{
  font-family: "PingFangSC-Semibold";
  font-size: 0.32rem;
  color: rgb(250,180,118);
}
.time-limit-swipe .count-down-box{
  font-size: 0.2933rem;
  padding: 0.2933rem 0 0 0.2933rem;
}
.time-limit-swipe .goods-item{
  width: 100%;
  height: 100%;
  position: relative;
}
.time-limit-swipe .van-swipe__indicator{
  background-color: #999;
}
.time-limit-swipe .goods-item img{
  width: 100%;
  height: 100%;
}
.count-down-box .colon {
  display: block;
  color: #222222;
  font-weight: bolder;
  margin: 0 0.03rem;
  float: left;
  line-height: 0.4rem;
}
.count-down-box .block {
  float: left;
  display: block;
  width: 0.4rem;
  height: 0.4rem;
  text-align: center;
  line-height: 0.4rem;
  font-size: 0.267rem;
  background-color: #222222;
  color: #ffffff;
  border-radius: 50%;
}
.count-down-box .tag{
  color: rgb(34,34,34);
  font-weight: bold;
  font-size: 0.32rem;
  width: 1.35rem;
  text-align: center;
  height: 0.5867rem;
  line-height: 0.5867rem;
  background: #fff;
  border-radius: 0.2933rem;
}
.count-down-box .tag span{
  color: rgb(253,53,85);
}
.count-down-box .time-label{
  color: rgb(102,102,102);
  font-size: 0.24rem;
  margin: 0 0.1rem 0 0.08rem;
  font-weight: bold;
  white-space: nowrap;
}
.selected-goods-swipe .goods-item .title-tag.hot-title-tag p:last-child{
  color: rgb(250,118,143);
}
.selected-goods-swipe .goods-item img{
  width:100%;
  height: 100%;
}
.jdindexPage .shop_nav_section.circle_top_style {
  position: relative;
}
.jdindexPage .shop_nav_section.circle_top_style:before {
  content: "";
  position: absolute;
  left: 0;
  top: -0.38rem;
  width: 100%;
  height: 0.4rem;
  background: url(../img/jd/cate_circle_top.png) center center / contain no-repeat;
  z-index: 888888;
}
.jdindexPage .shop_nav_section ul.shop_nav_section_ul {
  padding-top: 0.42rem;
  padding-bottom: 0.22rem;
  overflow-x: scroll;
  white-space: nowrap;
  -webkit-overflow-scrolling: touch;
}
.jdindexPage .shop_nav_section ul.shop_nav_section_ul::-webkit-scrollbar{
  display: none;
  height: 0;
}
.jdindexPage .shop_nav_section ul li {
  width: 20%;
  display: inline-block;
}
.jdindexPage .shop_nav_section {
  position: relative;
  padding-bottom: 0.12rem;
}
.jdindexPage .shop_nav_section .scroll-bar-lists{
  position: absolute;
  left: 50%;
  bottom: 0.16rem;
  transform: translateX(-50%);
  height: 0.06rem;
  border-radius: 0.12rem;
}
.jdindexPage .shop_nav_section .scroll-bar-lists p.item{
  display: inline-block;
  background: #9c9c9c;
  width: 0.16rem;
  height: 0.06rem;
}
.jdindexPage .shop_nav_section .scroll-bar-lists p.bar{
  display: inline-block;
  width: 0.64rem;
  background: #fd3555;
  height: 0.06rem;
  position: absolute;
  left: 0rem;
  bottom: 0;
}
.jdindexPage .shop_nav_section ul li a .img {
  width: 1.32rem;
  height: 1.32rem;
  margin: 0 auto;
}
.jdindexPage .shop_nav_section ul li a .img img {
  display: block;
  width: 100%;
  height: 100%;
}
.jdindexPage .shop_nav_section ul li a .text {
  font-size: 0.32rem;
  height: 0.73333333rem;
  line-height: 0.6rem;
  text-align: center;
  color: #444444;
}
.jdindexPage  .boutique-goods-lists .headNav{
  background: #ffffff;
  height: 1.3067rem;
  border-radius: 0.16rem;
  margin: 0 0.32rem 0.32rem;
  padding: 0 0.56rem 0 0.267rem;
}
.jdindexPage  .boutique-goods-lists .headNav img{
  height: 0.72rem;
}
.jdindexPage  .boutique-goods-lists .hot .item{
  background: #ffffff;
  position: relative;
  border-radius: 0.16rem;
  height: 3rem;
  align-items: center;
  padding: 0.24rem 0.32rem 0.24rem 0.16rem;
  margin-bottom: 0.32rem;
}
.jdindexPage  .boutique-goods-lists .hot .item .img-box{
  width: 2.8rem;
  height: 2.8rem;
  position: relative;
}
.jdindexPage  .boutique-goods-lists .hot .item .product-img{
  max-height: 2.8rem;
  max-width: 2.8rem;
  min-width: 2.6rem;
  min-height: 2.6rem;
}
.jdindexPage  .boutique-goods-lists .hot .item .product-introduce{
  height: 100%;
  padding: 0.16rem 0 0.16rem 0.3733rem;
  position: relative;
}
.jdindexPage  .boutique-goods-lists .hot .item .product-introduce .title{
  color: rgb(34,34,34);
  font-size: 0.3467rem;
  line-height: 0.48rem;
  font-weight: 400;
  max-height: 1.44rem;
  /*min-height: 0.96rem;*/
  overflow: hidden;
}
/*.jdindexPage  .boutique-goods-lists .hot .item .product-introduce .tagList span,*/
/*.jdindexPage  .boutique-goods-lists ul.best li .des p:nth-child(2) span{*/
  /*font-size: 0.2933rem;*/
  /*font-weight: bold;*/
  /*padding: 0.08rem 0.12rem;*/
  /*margin-top: 0.267rem;*/
  /*display: inline-block;*/
/*}*/
.jdindexPage  .boutique-goods-lists .hot  .item .product-introduce .price{
  position: absolute;
  bottom: 0.16rem;
}
.jdindexPage  .boutique-goods-lists .hot .item .product-introduce .price .current-price{
  color: rgb(253,53,85);
  font-size: 0.24rem;
  font-weight: bold;
}
.jdindexPage  .boutique-goods-lists .hot .item .product-introduce .price .current-price span{
  font-size: 0.3467rem;
}
.jdindexPage  .boutique-goods-lists .hot .item .product-introduce .price .original-price{
  color: rgb(153,153,153);
  font-size: 0.24rem;
  font-weight: bold;
  margin-left: 0.24rem;
  text-decoration: line-through;
}

/*.jdindexPage  .boutique-goods-lists .hot .item .product-introduce .tagList .tag1,*/
/*.jdindexPage  .boutique-goods-lists ul.best li .des p:nth-child(2) .tag1{*/
  /*background: rgb(253,53,85);*/
  /*color: #ffffff;*/
/*}*/
/*.jdindexPage  .boutique-goods-lists .hot .item .product-introduce .tagList span:last-child,*/
/*.jdindexPage  .boutique-goods-lists ul.best li .des p:nth-child(2) span:last-child{*/
  /*background: rgb(253,232,236);*/
  /*color: rgb(253,53,85);*/
/*}*/
.jdindexPage  .boutique-goods-lists ul.hot{
  margin: 0 0.32rem 0.32rem;
}
.jdindexPage  .boutique-goods-lists ul.best{
  width: 100%;
  padding: 0 0.32rem;
}
.jdindexPage  .boutique-goods-lists ul.best li{
  width: calc(50% - 0.16rem);
  float: left;
  margin-bottom: 0.32rem;
  background: #fff;
  border-radius: 0.16rem;
  box-sizing: border-box;
  padding: 0 0.2rem;
}
.jdindexPage  .boutique-goods-lists ul.best li .imgBox{
  width: 100%;
  height: 4rem;
  position: relative;
}
.jdindexPage  .boutique-goods-lists ul.best li .imgBox img{
  height: 3.4401rem;
  max-width: calc(100% - 0.7466rem);
}
.jdindexPage  .boutique-goods-lists ul.best li .des{
  width: 100%;
  padding: 0 0.24rem;
}
.jdindexPage  .boutique-goods-lists ul.best li .des p:first-child{
  height: 1.2rem;
  line-height: .4rem;
  font-size: .32rem;
  overflow: hidden;
  color: rgb(34,34,34);
  font-weight: 400;
  margin-bottom: 0.12rem;
  text-align: justify;
  word-break: break-all;
}
/*.jdindexPage  .boutique-goods-lists ul.best li .des p:nth-child(2)  {*/
  /*margin-bottom: 0.267rem;*/
/*}*/
.jdindexPage  .boutique-goods-lists ul.best li .des p:last-child{
  color: #ff5726;
  font-size: .266667rem;
  margin-bottom: 0.37333333rem;
}
.jdindexPage  .boutique-goods-lists ul.best li .des p:last-child span:first-child span{
 font-size: 0.3467rem;
  font-weight: 700;
}
.jdindexPage  .boutique-goods-lists ul.best li .des p:last-child span.sale{
  color: rgb(153,153,153);
  font-size: 0.24rem;
  font-weight: bold;
  margin-left: 0.24rem;
  text-decoration: line-through;
}
.jdindexPage  .boutique-goods-lists ul.best li:nth-child(2n){
  margin-left: 0.32rem;
}
.jdindexPage  .boutique-goods-lists .hot .item img.tag{
  position: absolute;
  left: 0.4533rem;
  top: 0;
  height: 0.8533rem;
  width: 0.64rem;
  z-index: 9;
}
.jdindexPage  .boutique-goods-lists .headNav a{
  color: #999999;
  font-size: 0.32rem;
}
.boutique-shop-page .festival_index_layer {
  margin-top: 0;
}

.boutique-shop-page .festival_index_layer img {
  display: block;
  width: 100%;
  height: auto;
}
.boutique-shop-page .festival_index_layer .header {
  /*margin: 0 0.32rem 0.32rem;*/
  background-color: #fff;
  border-radius:0 0.16rem 0.16rem 0;
}
.boutique-shop-page .festival_index_layer .header .title {
  display: inline-block;
  height: 1.2rem;
  border-bottom-right-radius: 0.32rem;
  background-color: #fde7eb;
  padding-right: 0.2rem;
}
.boutique-shop-page .festival_index_layer .header .title span {
  display: block;
  background-color: #fd3555;
  height: 1.2rem;
  border-bottom-right-radius: 0.32rem;
  font-size: 0.4rem;
  font-weight: bolder;
  text-align: center;
  min-width: 2.5067rem;
  padding: 0 0.32rem;
  line-height: 1.2rem;
  color: #fff;
}
.boutique-shop-page .festival_index_layer .header .right {
  font-size: 0.2933rem;
  color: #999;
  font-weight: normal;
  float: right;
  padding: 0.4rem 0.32rem 0 0;
}
.boutique-shop-page .festival_index_layer .layer_header {
  display: block;
  width: calc(100% - 0.64rem);
  height: auto;
  margin: 0 auto 0.24rem;
}






















