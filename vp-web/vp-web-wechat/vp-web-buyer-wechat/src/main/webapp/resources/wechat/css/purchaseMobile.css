.loginPage {
  padding-top: 0;
  background: #F2F2F2;
  background-size: 100% 5rem;
}
.van-nav-bar {
  height: 1.18rem;
  line-height: 1.18rem;
  background-color: #ffffff;
}
.van-hairline--bottom::after {
  border-bottom-width: 0;
}
.van-nav-bar__title {
  color: rgb(51,51,51);
  font-size: 0.48rem;
  font-weight: bold;
  letter-spacing: 0.1rem;
}

.loginPage .content{
  margin: 0.32rem;
  background: #ffffff;
  border-radius: 0.2267rem;
  padding-bottom: 0.5466rem;
}
.loginPage .content .title{
  font-family: SourceHanSansSC-Bold;
  font-size: 0.48rem;
  font-stretch: normal;
  letter-spacing: 0;
  color: #000000;
  font-weight: bold;
  padding: 0.5466rem 0 0.3733rem 0.4533rem;
}
.createdPurchaseContent {
  padding: 0.32rem 0.42rem;
  background: #ffffff;
  filter: blur(0);
}
.createdPurchaseContent  .title{
  text-align: center;
  font-family: SourceHanSansSC-Medium;
  font-size: 0.42rem;
  font-weight: bold;
  font-stretch: normal;
  letter-spacing: 0;
  color: #333333;
  margin: 0.12rem 0;
}
 .createdPurchaseContent .van-field__control{
text-align: right;
}
.createdPurchaseContent textarea.van-field__control {
  text-align: left;
}
.createdPurchaseContent .opt {
  margin: 0.32rem 0;
}
.createdPurchaseContent .opt .van-button{
  border-radius: 0.16rem;
  height: 1.08rem;
}
.getCodeBtn{
  height: 0.72rem;
  width: 2.2rem;
  display: block;
  color: #ffffff;
  background: #1875f9;
  line-height: 0.72rem;
  text-align: center;
  border-radius: 0.12rem;
  font-size: 0.32rem;
}
.createdPurchaseContent .van-field__error-message{
  display: none;
}
.purchaseCommonForm,
.purchaseCommonForm #app{
  height: 100%;
}
.purchaseCommonForm  .van-form{
  margin: 0 0.24rem;
}
.purchaseCommonForm  .van-form .van-cell{
  border-bottom: 1px solid #F5F5F5;
}
.purchaseCommonForm .van-cell__title,
.createdPurchaseContent .van-cell__title{
  font-family: SourceHanSansSC-Medium;
  font-size: 0.3733rem;
  font-weight: bold;
  font-stretch: normal;
  letter-spacing: 0;
  color: #333333;
}
.purchaseCommonForm  .van-form .van-cell .van-field__error-message{
  display: none;
}
.createdPurchaseContent .tip{
  font-family: SourceHanSansSC-Medium;
  font-size: 0.32rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 1px;
  color: #999999;
  margin: 0.12rem 0 0.52rem;
}
.loginPage .btnoption{
  background: #ffffff;
  position: fixed;
  width: 100%;
  bottom: 0;
  padding: 0.4rem 0.5733rem;
  box-sizing: border-box;
}
.loginPage .btnoption .primaryBtn{
  border-radius: 0.16rem;
  height: 1.08rem;
}
.purchaseList .item{
  background: #ffffff;
  margin: 0.32rem;
  border-radius: .16rem;
  padding: 0.3467rem 0.4533rem;
}
.purchaseList .item .header{
  font-family: SourceHanSansSC-Medium;
  font-size: 0.3733rem;
  font-weight: bold;
  font-stretch: normal;
  letter-spacing: 0;
  color: #333333;
  width: calc(100% - 2.5rem);

}
.purchaseList .item .header p{
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.purchaseList .item .timeRght{
  font-family: SourceHanSansSC-Medium;
  font-size: 0.32rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0.0133rem;
  color: #999999;
  white-space: nowrap;
}
.purchaseList .item .header img{
  height: 0.42rem;
  margin-right:0.16rem ;
}
.purchaseList .item .contentItem{
  background-color: #eef9ff;
  border-radius: 0.1467rem;
  border: solid 1px #f5f5f5;
  margin: 0.24rem 0;
  font-family: SourceHanSansSC-Medium;
  font-size: 0.3733rem;
  font-weight: bold;
  font-stretch: normal;
  letter-spacing: 0;
  color: #333333;
  padding: 0.2667rem 0.4267rem;
}
.purchaseList .item  .option{
  border-top: 1px solid #f5f5f5;
  padding-top: 0.2667rem;
  margin-top: 0.2667rem;
}
.purchaseList .item  .option .van-button{
  margin-left: 0.32rem;
  height: 0.72rem;
  width: 2.16rem;
}
.purchaseList .item .contentItem .infomation{
  font-size: 0.32rem;
}
.purchaseList .item .contentItem .infomation span{
  font-size: 0.48rem;
}
.purchaseList .item .contentItem .quantity{
  font-weight: normal;
}
.purchaseList .item .contentItem .quantity span{
  font-weight: bold;
  font-size: 0.4267rem;
}
.purchaseList .item .contentItem .des{
  margin-top: 0.08rem;
}
.purchaseList .item .contentItem .des .quantityText{
  color: #999999;
  font-weight: normal;
}
.purchaseList .item .contentItem .des .tab{
  background-color: #82cc89;
  border-radius: 0.08rem;
  font-family: SourceHanSansSC-Medium;
  font-size: 0.32rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0;
  color: #ffffff;
  padding: 0.012rem 0.14rem;
}
.purchaseList .item .contentItem .des .tab.status-1{
  background-color: #ff7777;
}
.purchaseList .item .contentItem .des .tab.status1{
  background-color: #77adff;
}
.purchaseListPage .purchaseListContent{
  height: calc(100% - 3rem);
  overflow: auto;
  margin-top: 0.24rem;
}
.loginPage .sreachContent{
  background: #ffffff;
  padding: 0 0.42rem 0.24rem;
  font-weight: bold;
}
.loginPage .sreachContent .van-search{
  border: 1px solid #1875f9;
  background: #F2F2F2;
  border-radius: 0.213rem;
  width: 6rem;
  /*height: 0.9067rem;*/
  height: 1.12rem;
  box-sizing: border-box;
  padding: 0.16rem 0;
}
.loginPage .sreachContent .van-search .van-search__action{
  border-left: 1px solid #dbe4fc;
  padding-left: 0.24rem;
  height: 100%;
  line-height: initial;
  display: flex;
  align-items: center;
}
.loginPage .sreachContent .van-search .searchBtn{
  font-family: SourceHanSansSC-Bold;
  font-size: 0.4rem;
  font-stretch: normal;
  letter-spacing: 0.04rem;
  color: #1875f9;
}
.loginPage .sreachContent .van-search .van-search__content{
  background: inherit;
  border-radius: 0.213rem;
}
.purchaseList .item .tip{
  font-family: SourceHanSansSC-Medium;
  font-size: 0.32rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 1px;
  color: #999999;
}

.loginPage .van-nav-bar .van-icon{
  color: rgb(51,51,51);
  font-weight: bold;
  font-size: 0.52rem;
}
.loginPage .van-nav-bar .van-nav-bar__right .van-icon{
  font-size: 0.65rem;
}

.loginPage .sreachContent  .addInvoice{
  /*height: 0.9067rem;*/
  height: 1rem;
  font-family: SourceHanSansSC-Medium;
  font-size: 0.32rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0;
  color: #1875f9!important;
  white-space: nowrap;
}
.loginPage .sreachContent .van-search .van-icon-search{
  font-size: 0.52rem;
}
.loginPage .sreachContent  .addInvoice img{
  height: 0.56rem;
  width: auto;
}
.purchaseListPage{
  background: #ffffff;
  padding-top: 0;
  height: 100%;
}
.purchaseListPage #app{
  height: 100%;
}
.purchaseListPage  .item{
  border-bottom: 1px solid #f2f2f2;
  margin:0 0.32rem;
  padding: 0.24rem 0.42rem;
  font-size: 0.3733rem;
  color: #666666;
}
.purchaseListPage  .item.active{
  color: #333333;
  font-size: 0.42rem;
}
.purchaseListPage  .item.active p:first-of-type{
  font-weight: bold;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.purchaseListPage .title{
    padding-top: 0.42rem;
    text-align: center;
    font-family: SourceHanSansSC-Medium;
    font-size: 0.42rem;
    font-weight: bold;
    font-stretch: normal;
    letter-spacing: 0;
    color: #333333;
}
.purchaseListPage  .item p:first-of-type{
  margin-bottom: 0.16rem;
}
.purchaseListPage .option{
  position: fixed;
  bottom: 0.42rem;
  width: 100%;
  padding-top: 0.42rem;
  box-shadow: 0 -0.12rem 0.2rem 0 rgba(0, 0, 0, 0.1);
}
.purchaseListPage .option .van-button{
    width: 3.2rem;
    height: 0.72rem;
}
.purchaseListPage   .addPurchase{
  margin-right: 0.32rem;
}
.van-dialog.PhoneCodeDialog .dialog__header{
  text-align: left;
}
.van-dialog.PhoneCodeDialog .van-dialog__footer{
  margin: 0.266666667rem 0 0.587rem 0;
  text-align: center;
  padding: 0.01rem 1.1733333rem;
}
.van-dialog.PhoneCodeDialog .van-dialog__footer .van-dialog__confirm{
  color: rgb(255, 255, 255);
  background: rgb(20, 143, 240);
  border-color: rgb(20, 143, 240);

  /*margin: 0 0.5rem;*/
  /*width: calc(100% - 1rem);*/
  border-radius: 0.4rem;
  /*width: 2.48rem!important;*/

  height: 0.8rem;
  line-height: 0.8rem;
  font-size: 0.32rem;
}
.van-dialog.PhoneCodeDialog .van-dialog__footer .van-dialog__cancel{
  height: 0.8rem;
  line-height: 0.8rem;
  font-size: 0.32rem;
  border-radius: 0.4rem;
  border: 0.0266667rem solid rgb(153,153,153);
  color: rgb(51,51,51);
  margin-right: 0.533333rem;
  /*width: 2.48rem;*/
}
.van-dialog.PhoneCodeDialog .van-dialog__header{
  font-family: PingFang-SC-Bold;
  font-size: 0.48rem;
  font-weight: bold;
  font-stretch: normal;
  letter-spacing: 0.0267rem;
  color: #333333;
}
.van-dialog.PhoneCodeDialog .van-dialog__footer:after {
  border-top-width: 0;
}

input:focus {
  outline: none;
  box-shadow: none;
  /* 其他需要重写的样式 */
}
.purchaseIndexPage{
  padding-bottom: 1.8rem;
}
.contarctAndInvoicePage .content{
  background: #F2F2F2;
}
.contarctAndInvoicePage .content .item-card{
 background: #ffffff;
  margin-bottom: 0.32rem;
  border-radius: 0.16rem;
  padding-bottom: 0.28rem;
}
.contarctAndInvoicePage .content .item-card .itemContent{
  padding: 0 0.4533rem 0.12rem;
  font-family: SourceHanSansSC-Medium;
  font-size: 0.3733rem;
  font-weight: normal;
  font-stretch: normal;
  line-height: 0.56rem;
  letter-spacing: 0;
  color: #333333;
}
.contarctAndInvoicePage .content .item-card .title{
  padding: 0.5466rem 0 0.12rem 0.4533rem;
}
.contarctAndInvoicePage .content .item-card .contarct{
position: relative;
  font-family: SourceHanSansSC-Medium;
  font-size: 0.3733rem;
  font-weight: normal;
  font-stretch: normal;
  line-height: 0.52rem;
  letter-spacing: 0;
  color: #999999;
  padding: 0.12rem 0.4533rem 0.44rem;
}
.contarctAndInvoicePage .content .item-card .contarct .des{
  margin: 0.82rem 0 0;
}
.contarctAndInvoicePage .content .item-card .contarct .state{
  position: absolute;
  right: 0.4533rem;
  bottom: 0.38rem;
  font-family: SourceHanSansSC-Medium;
  font-size: 0.32rem;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0.02rem;
  color: #333333;
}
.contarctAndInvoicePage .content .item-card .contarct .state img{
  height: 0.7467rem;
  margin-bottom: 0.24rem;
}
.contarctAndInvoicePage .content .item-card .option{
  border-top: 1px solid #f5f5f5;
  padding-top: 0.28rem;
  width: calc(100% - 0.48rem);
  margin: 0 auto;
}
.contarctAndInvoicePage .content .item-card .option .van-button{
  margin-right: 0.32rem;
    width: 2.16rem;
    height: 0.72rem;
}
.applyPurchasePage .tip{
  font-family: SourceHanSansSC-Medium;
  font-size: 0.32rem;
  font-weight: bold;
  font-stretch: normal;
  letter-spacing: 0;
  color: #999;
  margin-top: 0.32rem;
  line-height: 0.48rem;
}
.applyPurchasePage .van-switch{
  margin: 0 0.12rem;
}
.applyPurchasePage.purchaseCommonForm .van-cell__value{
  font-size: 0.3733rem;
}