var app = new Vue({
    el: '#coinRecordPage',
    data: {
        minDate: new Date(2020, 0, 1),
        maxDate: new Date(2025, 10, 1),
        currentDate: date,
        time: date.getFullYear() + "年" + (date.getMonth() + 1) + "月",
        showPicker:false,
        temp:undefined,//取消时间选择时存储下次选择时的初始值
        list: [],
		loading: false,
		finished: false,
		queryData:{
			pageNumber:0
		},
		totalPages:0
    },
    methods: {
        formatter(type, val) {
            if (type === 'year') {
                return `${val}年`;
            } else if (type === 'month') {
                return `${val}月`
            }
            return val;
        },
        format(dateString){
            return dateString.getFullYear() + "年" + (dateString.getMonth() + 1) + "月"
        },
        handleTimeConfirm(){
            this.time = this.format(this.currentDate);
            this.showPicker = false
            location.href=base+'/member/expandEarnings/earningsRecord.jhtml?date='+(this.currentDate.getFullYear()+"-"+(this.currentDate.getMonth() + 1)+"-"+this.currentDate.getDate())
        },
        handleTimeCancel(){
            this.currentDate = this.temp;
            this.showPicker = false
        },
        onLoad() {
            // 异步更新数据

        	var $this=this
            // 异步更新数据
           $this.queryData.pageNumber=$this.queryData.pageNumber+1
            	$.post(base+"/member/expandEarnings/details.jhtml",$this.queryData,function(data){
            		 // 加载状态结束
                    $this.loading = false;
            		if(data){
            			if(data.type=="success"){
            				if(data.data&&data.data.page&&data.data.page.content&&data.data.page.content.length>0){
            					 for (let i = 0; i < data.data.page.content.length; i++) {
            						 $this.list.push(data.data.page.content[i]);
                                 }
            				}
                            // 数据全部加载完成
                            if (data.data&&data.data.page) {
                            	$this.totalPages=data.data.page.totalPages
                            	if(data.data.page.pageNumber >= data.data.page.totalPages){
                            		$this.finished = true;
                            	}
                            }
            			}else{
            				$this.$toast({
                                message: data.content,
                                duration: 3000,
                                
                            });
            			}
            		}else{
            			$this.$toast({
                             message: '加载失败，请稍后再试',
                             duration: 3000,
                            
                         });
            		}
            	})
        
        }
    }
})