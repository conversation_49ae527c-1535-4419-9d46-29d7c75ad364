.ctrip_map_site_selection_page{
  padding-top: 0;
  background: #ffffff;
  overflow: initial;
}

.paddingB{
  padding-bottom: 0.3rem;
}
.van-nav-bar {
  height: 1.18rem;
  line-height: 1.18rem;
  background-color: #ffffff;
}
.van-hairline--bottom::after {
  border-bottom-width: 0;
}
.van-nav-bar__title {
  color: rgb(51,51,51);
  font-size: 0.48rem;
  font-weight: bold;
  letter-spacing: 0.1rem;
}
.textAlign_right{
  text-align: right;
}
.van-hairline--bottom::after {
  border-bottom-width: 0;
}
.van-nav-bar__title {
  color: rgb(51,51,51);
  font-size: 0.48rem;
  font-weight: bold;
  letter-spacing: 0.1rem;
}
.van-icon::before {
  font-size: 0.6rem;
  color: rgb(51,51,51);
}
.ctrip_map_site_selection_page .search_left{
  font-size: 0.2933rem;
  line-height: 0.32rem;
  height: 100%;
  color: #4993fa;
  font-weight: bold;
  border-right: 1px solid #999999;
  padding-right: 0.24rem;
}
.ctrip_map_site_selection_page .search_left p:first-of-type{
  margin-bottom: 0.06rem;

}
.ctrip_map_site_selection_page .search_left span{
  color: #999999;
  font-weight: normal;
  margin-right: 0.12rem;
}
.custom-overlay-class,
 .van-overlay{
  background-color: rgba(0, 0, 0, 0.2); /* 修改遮罩层的背景颜色 */
}
.ctrip_map_site_selection_page .van-sticky{
  position: fixed;
  z-index: 9999;
  width: 100%;
  background: #ffffff;
  padding: 0 0.32rem;
}
.select-time-popup{
  padding: 1.68rem 0.48rem 0;
  font-size: 0.32rem;
}
.select-time-popup .van-grid .van-grid-item__content{
  padding: 0;
}
.select-time-popup .room{
  border-top: 1px solid #f2f2f2;
  border-bottom: 1px solid #f2f2f2;
}
.custom-footer .content{
  padding: 0.12rem 0.32rem;
  color: #999999;
  font-size: 0.32rem;
}
.custom-footer .content  div.dis p:last-of-type{
color: #999999;
}
.custom-footer .content>div p:last-of-type {
  color: #333;
  font-size: 0.42rem;
  font-weight: bold;
}
.select-time-calendar  .van-calendar__footer{
  padding: 0.32rem;
  background: #fff;
}
.ctrip_map_site_selection_page .ctrip_map_search .van-field__left-icon{
display: none;
}
.ctrip_map_site_selection_page .ctrip_map_search .van-search__content{
  padding: 0.12rem 0.24rem;
  border: 1px solid #999999;
  background: #ffffff;
}
.ctrip_map_site_selection_page . .top-header{
  background: #fff;
}
.select-time-popup .fli_link_line{
  height: 1.2rem;
  padding: 0 0.32rem 0 0.12rem;
}
.select-time-popup .fli_link_line:after{
  right: -0.06rem;
  top: 0.5rem;
}
.select-time-popup .fli_link_line div p:first-of-type{
  color: #666;
  margin-bottom: 0.06rem;
  font-size: 0.32rem;
}
.select-time-popup .fli_link_line div p:last-of-type{
  color: #333;
  font-size: 0.42rem;
}
.select-time-popup .fli_link_line div p:last-of-type span{
  font-size: 0.32rem;
  margin-left: 0.12rem;
}
.select-time-popup .fli_link_line p{
  color: #333;
  font-size: 0.32rem;
}
.select-time-popup .fli_link_line.room{
  font-size: 0.42rem;
}
.select-time-popup .fli_link_line.room span{
  margin: 0 0.12rem;
}
.select-time-popup .van-button{
  margin-top: 0.32rem;
  margin-bottom: 0.32rem;
}
.check-conditions-popup  .content{
  border-top: 1px solid #f2f2f2;
  padding: 0 0.42rem;
}
.check-conditions-popup .item{
  height: 1.2rem;
}
.check-conditions-popup .item:last-of-type{
  border-top: 1px solid #f2f2f2;
}
.check-conditions-popup .tip{
  font-size: 0.3733rem;
  background: #f2f2f2;
  color: #333;
  padding: 0.32rem 0.42rem;
}
.check-conditions-popup .childen-content{
  padding: 0 0.82rem 0 0.42rem;
}
.check-conditions-popup .childen-content .fli_link_line:after{
  right: -0.42rem;
}
.ctrip_map_site_selection_page .main-content{
  /*padding-top: 2.68rem;*/
  padding-top: 2.21rem;
  height: 100%;
  overflow: hidden;
}
.ctrip_map_site_selection_page .xc_hotel_list .fixed_line{
  /*top: 1.58rem;*/
}
.main-content .myContainer{
  width: 100%;
  height: 6rem;
  background: #F2F2F2;
}

.custom-content-marker-img:after{
  position: absolute;
  content: '';
  display: inline-block;
  width: 0;
  height: 0;
  border-left: 0.14rem solid transparent;
  border-right: 0.14rem solid transparent;
  border-top:0.28rem solid #2471fe;
  left: 50%;
  transform: translateX(-50%);
  bottom: -0.25rem;
}
.custom-content-marker-img.active:after{
    border-top-color: #ff4343;
}
.custom-content-marker-img{
    font-size: 0.32rem;
    white-space: nowrap;
    background: #2471fe;
    color: #fff;
    padding: 0.04rem 0.22rem;
    border-radius: 0.32rem;
    position: relative;
    transform: translateX(-50%);
}

.custom-content-marker-img.active{
    background: #ff4343;
}
/*.custom-content-marker-img img{*/
  /*width: 0.62rem;*/
  /*position: relative;*/
  /*transform: translateY(50%);*/
/*}*/
.BigMap .xc_hotel_list ul.hotelUI.swiper-container{
  position: absolute;
  bottom: 0.32rem;
  width: 100%;
}
.BigMap .xc_hotel_list ul.hotelUI li.swiper-slide{
  border-radius: 0.32rem;

}
.xc_hotel_list ul.hotelUI li.active{
  border: 0.052rem solid  #2471fe;
  position: relative;
  border-radius: 0.19rem;
}
.xc_hotel_list ul.hotelUI li.active:after{
  content: "";
  position: absolute;
  right: 0;
  top: 0;
  width: 0.42rem;
  height: 0.42rem;
  background: url("./../img/icons/tap.png");
  background-size: cover;
}
.ctrip_map_site_selection_page .BigMap{
  height: 100%;
}
.ctrip_map_site_selection_page .BigMap .main-content{
  height: 100%;
  position: relative;
}
.ctrip_map_site_selection_page .BigMap .main-content .myContainer{
height: 100%;
}
.ctrip_map_site_selection_page .layui-m-layershade,
.ctrip_map_site_selection_page .layui-m-layermain{
  top: 2.1rem;
}
.ctrip_map_site_selection_page .van-search__action:active{
  background: initial;
}
.ctrip_map_site_selection_page #app {
  height: 100%;
  overflow: hidden;
}
.ctrip_map_site_selection_page .xc_hotel_list #hotelUI{
  height: calc(100% - 6.32rem);
  overflow: hidden;
  padding: 0.42rem 0.012rem 0;
  position: relative;
}
.ctrip_map_site_selection_page .xc_hotel_list #hotelUI:after{
  position: absolute;
  content: '';
  width: 1.1rem;
  height: 0.1rem;
  background: #f3f3f3;
  border-radius: 0.16rem;
  top: 0.16rem;
  left: 50%;
  transform: translateX(-50%);
}
/*.custom-content-marker-img.active img{*/
  /*width: 0.8rem;*/
/*}*/
.ctrip_map_site_selection_page .xc_hotel_list ul.hotelUI{
  height: 100%;
  overflow: hidden;
  position: initial;
}
.ctrip_map_site_selection_page .xc_hotel_list ul.hotelUI li .img{
  float: initial;
}
.ctrip_map_site_selection_page .xc_hotel_list ul.hotelUI .van-hotel{
  height:100%;
  overflow: scroll;
}
.ctrip_map_site_selection_page  .xc_hotel_list ul.hotelUI li .info{
  margin-left: 0;
  width: calc(100% - 3.8rem);
  height: auto;
}
.ctrip_map_site_selection_page  .top-header .search-list p{
  font-size: 0.32rem;
  color: #333;
  margin-top: 0.12rem;
}
.xc_hotel_list ul.hotelUI li .info .fixed_bottom .labels span.theme{
  margin-right: 0.12rem;
}
.ctrip_map_site_selection_page .hotel_city_time_selector .hotel_search_form{
  margin: 0 2.34rem 0 0;
}
.ctrip_map_site_selection_page .hotel-img{
  width: 100%;
}