.bocPage {
  padding-top: 0;
  background-image: url("../img/boc/index_bg.png");
  background-repeat: no-repeat;
  background-size: 100% 5rem;
}
.bocIcon {
  display: inline-block;
  width: 1.3rem;
  height: 1.3rem;
}
.van-nav-bar {
  height: 1.18rem;
  line-height: 1.18rem;
  background-color: transparent;
}
.van-hairline--bottom::after {
  border-bottom-width: 0;
}
.van-nav-bar__title {
  color: white;
  font-size: 0.48rem;
  font-weight: bold;
  letter-spacing: 0.1rem;
}
.van-icon::before {
  font-size: 0.6rem;
  color: white;
}
.bocBody {
  padding: 0.2667rem;
}
.bocContainer {
  background: #f5f5f5;
  display: -webkit-flex;
  /* Safari */
  display: flex;
  align-items: start;
  font-size: 0.3467rem;
  color: #333;
}
.bocCard {
  height: 4.8rem;
  background-image: linear-gradient(90deg, 
		#118eea 0%, 
		#dd98a9 99%), 
	linear-gradient(
		#ffffff, 
		#ffffff);
  border-radius: 0.16rem;
  box-shadow: 0 0.1rem 0.1rem #ced4dd;
  position: relative;
}
.bocCard .bocContent {
  width: 100%;
  height: 2.8rem;
  line-height: 2.8rem;
  border-radius: 0.16rem 0.16rem 0 0;
  padding: 0 0.5rem;
  display: flex;
  align-items: center;
}
.bocCard .bocContent::before {
  content: "";
  display: inline-block;
  height: 100%;
  width: 0;
  vertical-align: middle;
}
.bocTextAll{
  display: flex;
  justify-content: space-between;
  width: 100%;
}
.bocText {
  color: white;
  font-size: 0.45rem;
  margin-left: 0.225rem;
  letter-spacing: 0.05rem;
}
.cardNum {
  color: white;
  font-size: 0.45rem;
}
.bocFooter {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1.5rem;
  border-top: 2px solid rgba(204, 212, 222, 0.2);
  border-radius: 0 0 0.16rem 0.16rem;
  padding: 0 0.5rem;
  color: white;
}
.bocFooter div {
  line-height: 1.5rem;
  font-size: 0.33rem;
}
.bocFooter div:first-child {
  float: left;
}
.bocFooter div:last-child {
  float: right;
}
.bocFooter div span {
  font-weight: bold;
  font-size: 0.4rem;
  margin-left: 0.2rem;
}
.bocM::before {
  content: "￥";
  font-size: 0.6em;
}
.bocUse {
  background-color: white;
  margin-top: 0.25rem;
  border-radius: 0.16rem;
  padding: 0.5rem;
  position: relative;
}
.bocUse .rechargeBtn {
  width: 100%;
  height: 1.4rem;
  font-size: 0.45rem;
  border-radius: 0.16rem;
  letter-spacing: 0.08rem;
  background-color: #148ff0;
  margin: 0.35rem 0;
}
.bocUse h3 {
  font-size: 0.4rem;
  text-align: center;
  line-height: 2em;
  margin-top: 0.5rem;
}
.bocUse .useList {
  list-style-type: none;
  font-size: 0.36rem;
  line-height: 2em;
}
.bankScopde{
  width: 100%;
  padding: 0.4rem 1rem;
}
.bankList{
  width: 100%;
}
.bocDes{
  color: #973f0b;
  font-weight: 700;
  font-size: 1rem;
}
.bocOpen{
  width: 2rem;
  height: 2rem;
  position: absolute;
  z-index: 3;
  right: 1.6rem;
  top: 1rem;
}
.bocImg{
  width: 100%;
  height: 4.9rem;
  margin-bottom: -0.3rem;
}