.gasStationPage {
  padding-top: 0;
}
.paddingB{
  padding-bottom: 0.3rem;
}
.gasStationPage .headerTop,.consumeRecordDetailPage .headerTop{
  height: 2.64rem;
  background: rgb(20,143,240);
}
.consumeRecordDetailPage .gasStaionInfo .distance{
  display: inline-block;
  color: #ffffff;
  border-radius: 0.16rem;
  background: rgb(20,143,240);
  padding: 0.1rem 0.3rem;
}
.van-nav-bar {
  height: 1.18rem;
  line-height: 1.18rem;
  background-color: transparent;
}
.van-hairline--bottom::after {
  border-bottom-width: 0;
}
.van-nav-bar__title {
  color: white;
  font-size: 0.48rem;
  font-weight: bold;
  letter-spacing: 0.1rem;
}
.van-nav-bar .van-icon::before {
  font-size: 0.55rem;
  color: white;
}
.bocBody {
  padding: 0.2667rem;
}
.gasStationPage .search_section{
  width: 5rem;
}
.gasStationPage .addressName{
  width: calc(100% - 5rem - 0.66666667rem);
  height: 0.8rem;
  line-height: 0.8rem;
  border-radius: 0.4rem;
  background: rgb(0,0,0);
  color: rgb(255,255,255);
  font-size: 0.32rem;
  padding: 0 0.26666667rem;
}
.gasStationPage .searchBox{

  margin: 0.15rem 0.426666667rem 0;
}
.gasStationPage .addressName{
  margin-right: 0.66666667rem;
}
.gasStationPage .addressName .detailedAddress{
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
  margin-left: 0.16rem;
}
.gasStationPage.modify {
  padding-bottom: 1.62666667rem;
}
.gasStationPage  .search_section .van-cell{
  border-radius: 0.16rem;
  padding-top: 0.106666667rem;
  padding-bottom:  0.106666667rem;
}
.gasStationPage  .search_section .van-cell .van-field__right-icon .van-icon{
  color: rgb(20,143,240);
  font-size: 0.48rem;
  font-weight: bolder;
}
.gasStationPage .screen{
  position: relative;
  margin: -0.15rem 0.24rem 0.24rem;
}
.gasStationPage .screen .selectType1{
  position: absolute;
  z-index: 2;
  font-size: 0.32rem;
  left: 1.06667rem;
  top: 0.4rem;
}
.gasStationPage .screen .selectType2{
  position: absolute;
  z-index: 2;
  font-size: 0.32rem;
  left: 5.733333rem;
  top: 0.4rem;
}
.gasStationPage .screen .van-dropdown-menu .van-dropdown-menu__bar{
  border-radius: 0.16rem;
}
.gasStationPage .screen .oil{
  position: absolute;
  top: 50%;
  left: 1.06666667rem;
  transform: translateY(-50%);
  z-index: 1000;
  color: rgb(51,51,51);
  font-size: 0.32rem;
}
.gasStationPage .screen .brand{
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-0.2133333333rem,-50%);
  z-index: 1000;
  color:rgb(51,51,51);
  font-size: 0.32rem;
}
.gasStationPage .screen .van-dropdown-menu__title .van-ellipsis{
  color:rgb(153,153,153);
  font-size: 0.32rem;
  max-width: 3.5rem;
}
.gasStationPage .screen .oilBox,.gasStationPage .oilGun{
  margin:0.37333333rem 0.32rem 0rem;
  height: 4.26666667rem;
  flex-wrap: wrap;
  position: relative;
}
.gasStationPage .oilGun{
  height: auto;
  margin: 0.506666667rem 0 0.8rem;
}
.gasStationPage .screen .oilBox li,.gasStationPage .oilGun li{
  width: 23.5%;
  background: rgb(244,244,244);
  display: inline-block;
  height: 1.17333333rem;
  line-height: 1.17333333rem;
  color: rgb(51,51,51);
  font-size: 0.24rem;
  margin-bottom: 0.21333333rem;
  border-radius: 0.1066666rem;
  text-align: center;
  font-weight: bold;
}

.gasStationPage .screen .oilBox li:nth-child(4n-2),.gasStationPage .oilGun li:nth-child(4n-2){
  margin: 0 2%;
}
.gasStationPage .screen .oilBox li:nth-child(4n-1),.gasStationPage .oilGun li:nth-child(4n-1){
  margin-right: 2%;
}
.gasStationPage .screen .oilBox li.active,.gasStationPage .oilGun li.active{
  color: rgb(20,143,240);
  background: rgb(204,233,245);
  border: 0.02666667rem solid rgb(20,143,240);

}
.gasStationPage .screen .gasStationBox{
  /*margin: 0.66666667rem;*/
  flex-wrap: wrap;
  position: relative;
}
.gasStationPage .screen .oilBox .determineBox{
  text-align: right;
  width: 100%;
}
.gasStationPage .screen .oilBox .determineBtn{

  height: 0.8rem;
  border-radius: 0.16rem;
}
.gasStationList li,.gasStaionInfo{
  background: #ffffff;
  border-radius: 0.16rem;
  margin: 0 0.32rem 0.18666667rem;
  height: 2.8533333rem;
  padding: 0 0.69333333rem;
  position: relative;
}
.centerWith{
  width: 100%;
  text-align: center;
  color: rgb(184,184,184);
  font-size: 0.2666667rem;
}
.gasStaionInfo{
  height: auto;
  margin-top:-1.2rem;
}
.gasStaionInfo .oilType{
  color: rgb(20,143,240);
  font-weight: bold;
  vertical-align:bottom;
  display:table-cell;
  height: 0.74666667rem;
}
.gasStaionInfo .oilType img{
  width: 0.33333333rem;
}
.gasStaionInfo .oilType span{
  font-size: 0.64rem;
}
.consumeRecordDetailPage .gasStaionInfo .priceOne{
  color: rgb(255,62,62);
  font-weight: bold;
  margin-left: -0.07rem;
  height: 0.74666667rem;
  width: 100%;
  display: flex;
  align-items: flex-end;
  font-size: 0.2666667rem;
}
.gasStaionInfo .priceOne{
  color: rgb(255,62,62);
  font-weight: bold;
  margin-left: -0.07rem;
  height: 0.74666667rem;
  vertical-align:bottom;
  display:table-cell;
}
.gasStaionInfo .priceOne span{
  font-size: 0.426666667rem;
}
.gasStaionInfo .priceTwo{
  color: rgb(67,67,67);
  font-weight: bold;
  margin-left: -0.07rem;
  height: 0.74666667rem;
  vertical-align:bottom;
  display:table-cell;
}
.gasStaionInfo .MarginTB{
  margin: 0.2666666667rem 0 0.56rem;
}
.gasStaionInfo .priceTwo span{
  font-size: 0.426666667rem;
}
.gasStationList li .tag_recommond,.gasStaionInfo .tag_recommond{
  position: absolute;
  color: rgb(255,255,255);
  font-size: 0.24rem;
  background: rgb(255,62,62);
  padding: 0.08rem 0.10666667rem;
  border-radius: 0.1rem;
}
.contenTop{
  padding-top: 0.64rem;
}
.gasStationList li .gasInfo .gasType,.gasStaionInfo .gasInfo .gasType{
  background: rgb(184,184,184);
  color: rgb(255,255,255);
  font-size: 0.24rem;
  padding:0.08rem  0.10666667rem;
  border-radius: 0.1rem;
  margin-left: 0.32rem;
}
.gasStationList li .gasInfo .gasName,.gasStaionInfo .gasInfo .gasName{
  color: rgb(51,51,51);
  font-size: 0.37333333rem;
  font-weight: bold;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;

}

.gasStationList li .distance,.gasStaionInfo .distance{
  color: rgb(184,184,184);
  font-size: 0.32rem;
  text-align: right;
}
.gasStationList li .address{
  color: rgb(153,153,153);
  font-size: 0.32rem;
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
  /*margin:  0.30333333rem 0;*/
  height: 0.8rem;
  line-height: 0.8rem;
}
.gasStationList li .price {
  line-height: 16px;
}
.gasStaionInfo .address{
  color: rgb(153,153,153);
  font-size: 0.32rem;
  margin:  0.30333333rem 0;
  text-align: justify;
  word-break: break-all;
}
.gasStaionInfo .oilTypeText{
  color: rgb(184,184,184);
  font-size: 0.2666667rem;
}
.gasStationList li .price p{
  font-size: 0.21333333rem;
  color: rgb(255,62,62);
}
.gasStationList li .price p:nth-child(2){
  color: rgb(184,184,184);
  text-decoration: line-through;
}
.gasStationList li .price p span.priceOne{
  font-size: 0.37333333rem;
  font-weight: bold;
  margin-right: 0.48rem;
}
.gasStationList li .price p span.priceOne span{
  color: rgb(153,153,153);
  font-size: 0.32rem;
  font-weight: normal;
}
.gasStationList li .price p span.priceTwo{
  font-size: 0.32rem;
  font-weight: bold;
}
.gasStationPage .oilInfo{
  background: #ffffff;
  border-radius: 0.16rem;
  margin: 0 0.32rem 0.18666667rem;
  height: auto;
  padding: 0.56rem 0.32rem 2.4rem;
  position: relative;
}
.gasStationPage .selectItem{
  color: rgb(51,51,51);
  font-size: 0.3733333333rem;
  font-weight: bold;
  margin-left: 0.4rem;
}
.gasStationPage .oilsheetcontent .oilGun{
  margin: 0.506666667rem  0;
}
.gasStationPage .tips{
  color: rgb(184,184,184);
  font-size: 0.2666667rem;
  margin: 0.4rem
}
.gasStationPage .selectNum{
  color: rgb(20,143,240);
  font-size: 0.32rem;
}
.gasStationPage .refuelingAmount{
  background: rgb(245,245,245);
  border-radius: 0.16rem;
  margin-top: 0.64rem;
}
.gasStationPage .sumbitOption{
  width: 100%;
  position: fixed;
  bottom: 0;
  background: #ffffff;
  height: 1.62666667rem;
  padding: 0 0.50666667rem 0 0.72rem;
}
.gasStationPage .sumbitOption .moneyText{
  color: rgb(255,4,4);
  font-size: 0.266666667rem;
  font-weight: bold;
}
.gasStationPage .sumbitOption .moneyText .total{
  color: rgb(51,51,51);
  font-size: 0.32rem;
}
.gasStationPage .sumbitOption .moneyText .totalMoney{
  font-size: 0.37333333rem;
}
.gasStationPage .sumbitOption  .balance{
  color: rgb(102,102,102);
  font-size: 0.266666667rem;
  margin-top: 0.16rem;
}
.gasStationPage .sumbitOption .settlement{
  border-radius: 0.16rem;
}
.gasStationPage .oilsheetcontent{
  padding: 0.5066666667rem 0.32rem 0;
}
.gasStationPage .fixhome{
  box-shadow: 0px 0px 5px #aaa;
  position: fixed;
  right: 5PX;
  top: 350px;
  z-index: 200;
  width: 40px;
  height: 40px;
  background: #fff;
  border-radius: 25px;
  padding: 10px;
  opacity: 0.5;
  font-size: 20px;
  color: #333;
}
.gasStationPage .points{
  display: flex;
}
.gasStationPage .points span{
  width: 4px;
  height: 4px;
  background: #fff;
  display: block;
  border-radius: 2px;
  margin-right: 5px;
}

