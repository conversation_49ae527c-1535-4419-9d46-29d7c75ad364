.activity_index_page .van-nav-bar{
  background: #ffffff;
  /*background: -webkit-linear-gradient(to bottom, #297ff9, #4e95f8);*/
  /*background: linear-gradient(to bottom, #297ff9, #4e95f8);*/
  height: 1.18rem;
  line-height: 1.18rem;
}
.guide-corporate-code.activity_index_page .van-nav-bar{
  background: transparent;
}
.activity_index_page{
  /*background: #297ff9;*/
  /*background: -webkit-linear-gradient(to bottom, #297ff9, #8bb8f7, #f2f2f2);*/
  /*background: linear-gradient(to bottom, #297ff9,#8bb8f7, #f2f2f2);*/
  /*background-size: 100% 5.1rem;*/
  /*background-repeat: no-repeat;*/
}
.activity_index_page{
  padding-top: 0;
}
.activity_index_page .van-nav-bar .van-nav-bar__title{
  font-size: .48rem;
  font-weight: 700;
  letter-spacing: .1rem;
  /*color: #ffffff;*/
  color: #222022;
}
.activity_index_page .van-nav-bar .van-icon::before{
  font-size: 0.55rem;
  /*color: #ffffff;*/
  color: #222022;
}
.van-hairline--bottom::after {
  border-bottom-width: 0;
}
.activity_index_page{
  height: 100%;
  position: relative;
}
.activity_index_page #app{
  height: 100%;
}
.activity_index_page .content{
  background-color: #f5faea;
  border-radius: 0.2933rem;
  padding:0 0 0.46rem ;
}
.activity_index_page .content .main-content-text{
padding: 0 0.12rem;
}
.activity_index_page  .main-content{
  background: #ffffff;
  padding:0 0.4rem 1.533rem;
  font-size: 0.32rem;
  font-family: SourceHanSansSC-Bold;
  font-weight: bold;
  color: #666666;
  line-height: 0.48rem;
}
.activity_index_page .main-content .title{
  font-family: SourceHanSansSC-Bold;
  font-stretch: normal;
  line-height: 0.48rem;
  color: #f1702a;
  padding: 0 0.16rem ;
}
.activity_index_page .main-content .title p:last-of-type{
  text-indent: 2em;
}
.activity_index_page .main-content .title-item{
  height: 0.48rem;
  line-height: 0.48rem;
  font-family: SourceHanSansSC-Bold;
  margin: 0.06rem 0;
  padding: 0 0.16rem;
  text-indent: 2em;
  color: #222022;
  background: #f4d9bb;
  background: -webkit-linear-gradient(left, #f4d9bb, #f5f7e6);
  /* Safari 5.1 - 6.0 */
  background: -o-linear-gradient(left, #f4d9bb, #f5f7e6);
  /* Opera 11.1 - 12.0 */
  background: -moz-linear-gradient(left, #f4d9bb, #f5f7e6);
  /* Firefox 3.6 - 15 */
  background: linear-gradient(left, #f4d9bb, #f5f7e6);
  /* 标准的语法（必须放在最后） */
}
.activity_index_page .main-content .des{
  text-indent: 2em;
  padding: 0 0.16rem;
  text-align: justify;
}
.activity_index_page .content .banner-img{
  width: 100%;
  border-radius: 0.2933rem;
}
.activity_index_page .footer{
  text-align: right;
  padding-right: 0.16rem;
  margin-top: 0.24rem;
  color: #222022;
}
.activity_index_page .fixed_footer ul li a .icon_home{
  background-image: url("./../img/nanning/home.png");
}
.activity_index_page .fixed_footer ul li a .icon_cate{
  background-image: url("./../img/nanning/class.png");
}
.activity_index_page .fixed_footer ul li a .icon_user{
  background-image: url("./../img/nanning/my.png");
}
.activity_index_page .fixed_footer ul li a .icon_cart_yanjing{
  background-image: url("./../img/nanning/cart.png");
}