@charset "utf-8";
.movie-exchange-page {
    letter-spacing:0.02rem
}
.movie-exchange-page .banner{
    margin: 0.24rem 0.32rem;
}
.movie-exchange-page .banner img{
    width: 100%;
    border-radius: 0.16rem;
}
.movie-exchange-page .my-coupons{
    background: rgb(255,241,214);
    border-radius: 0.16rem;
    margin: 0 0.32rem 0.24rem;
    height: 1.2533rem;
    padding: 0 0.32rem 0 0.4533rem;
}
.movie-exchange-page .my-coupons .my-coupons-left{
    color: rgb(34,34,34);
    font-size: 0.2933rem;
}
.movie-exchange-page .my-coupons .my-coupons-left .number{
    color: rgb(34,34,34);
    font-size: 0.3733rem;
}
.movie-exchange-page .my-coupons .my-coupons-left .number span{
    color: rgb(238,7,7);
    font-weight: bold;
}
.movie-exchange-page .my-coupons .my-coupons-right{
    background: rgb(237,94,36);
    width: 1.6rem;
    height: 0.5867rem;
    text-align: center;
    line-height: 0.5867rem;
    color: rgb(255,255,255);
    font-size: 0.267rem;
    border-radius: 0.2933rem;
}
.movie-exchange-page .card-box {
    background: #ffffff;
    margin: 0 0.32rem 0.24rem;
    border-radius: 0.16rem;
    padding: 0.133rem 0.32rem 0.64rem;
    position: relative;
    height: 4.993rem;
}
.movie-exchange-page .card-box  .nav{
    height: 1.2rem;
    color: rgb(102,102,102);
    font-size:0.3733rem;
}
.movie-exchange-page .card-box  .nav li.active{
    font-weight: bold;
    color: rgb(34,34,34);
}
.movie-exchange-page .shop_nav_section_ul{
    overflow-x: scroll;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;
    height: 2.8rem;
}
.movie-exchange-page .shop_nav_section_ul::-webkit-scrollbar{
    display: none;
    height: 0;
}
.movie-exchange-page .scroll-bar-lists{
    position: absolute;
    left: 50%;
    bottom: 0.46rem;
    transform: translateX(-50%);
    height: 0.06rem;
    border-radius: 0.12rem;

}
.movie-exchange-page .scroll-bar-lists p.item{
    display: inline-block;
    background: #f5f5f5;
    width: 0.24rem;
    height: 0.06rem;
}
.movie-exchange-page .scroll-bar-lists p.bar{
    display: inline-block;
    width: 0.64rem;
    background: rgb(213,172,93);
    height: 0.06rem;
    position: absolute;
    left: 0rem;
    bottom: 0;
}
.movie-exchange-page .shop_nav_section_ul li{
    width: calc(33.33% - 0.16rem);
    /*height: 2.52rem;*/
    display: inline-block;
    color: rgb(181,124,15);
    font-size: 0.2933rem;
    background: rgb(255,241,214);
    border: 1px solid rgb(207,180,127);
    border-radius: 0.16rem;
    padding:  0.3467rem 0.24rem;
    position: relative;
}
.movie-exchange-page .shop_nav_section_ul li p.p-name{
    margin: 0 0 0.3733rem;
}
.movie-exchange-page .shop_nav_section_ul li p.p-bable{
    position: absolute;
    top: 0.8rem;
    text-align: center;
    font-family: PingFangSC-Regular;
    font-size: 0.2133rem;
    letter-spacing:0.013rem;
    color: #f6724b;
    /*width: 1.6267rem;*/
    height: 0.32rem;
    overflow: hidden; /* (文字长度超出限定宽度，则隐藏超出的内容） */
    white-space: nowrap; /* (（设置文字在一行显示，不能换行） */
    text-overflow: ellipsis;
    line-height: 0.32rem;
    background-color: #fbda9a;
    border-radius: 0.13rem;
    display: inline-block;
    padding: 0 0.12rem;
    max-width: 80%;
}
.movie-exchange-page .shop_nav_section_ul li p.p-attr{
    color: rgb(110,128,114);
    font-size: 0.24rem;
    margin-bottom: 0.18rem;
}
.movie-exchange-page .shop_nav_section_ul li p.p-attr span:first-child{
    color: rgb(181,124,15);
    font-size: 0.4533rem;
    font-weight: bold;
}
.movie-exchange-page .shop_nav_section_ul li div{
    color: rgb(250,76,68);
    font-size: 0.267rem;
}
.movie-exchange-page .shop_nav_section_ul li div span:first-child span{
    color: rgb(250,76,68);
    font-weight: bold;
    font-size: 0.3733rem;
    margin-right: 0.12rem;
}
.movie-exchange-page .shop_nav_section_ul li:not(:last-child){
    margin-right: 0.24rem;
}
.movie-exchange-page .shop_nav_section_ul li img{
    width: 0.4267rem;
    height: 0.4267rem;
}
.movie-exchange-page .movie-box{
    background: #ffffff;
    margin: 0 0.32rem 0.24rem;
    border-radius: 0.16rem;
    padding: 0.0533rem 0.32rem 0.24rem;
}
.movie-exchange-page .movie-box .movie-box-top{
    height: 1.2533rem;
}
.movie-exchange-page .movie-box .movie-box-top .left-content{
    color: rgb(34,34,34);
    font-size: 0.3733rem;
    font-weight: bold;
    height: 100%;
}
.movie-exchange-page .movie-box .movie-box-top .left-content img{
    width: 0.4533rem;
    height: 0.4533rem;
    margin-right: 0.1867rem;
}
.movie-exchange-page .movie-box .movie-box-top .right-content{
    color: rgb(153,153,153);
    font-size: 0.32rem;
    height: 100%;

}
.movie-exchange-page .movie-box img.movie-banner{
    width: 100%;
}